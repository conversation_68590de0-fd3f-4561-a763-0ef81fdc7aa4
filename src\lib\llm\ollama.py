from __future__ import annotations

import os
from typing import List, Dict, Any, Optional, Generator

try:
    from ollama import Client
except ImportError:
    raise ImportError(
        "The 'ollama' package is required to use OllamaLLM. "
        "Please install it with: pip install ollama"
    )

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .base import <PERSON><PERSON><PERSON>
from .cache import LLMCacheMixin
from .providers import LLMProvider

logger = QuepasaLogger().get_instance(__name__)

class OllamaLLM(BaseLLM, LLMCacheMixin):
    """Ollama LLM provider.
    
    This class provides access to Ollama's language models through
    their official client library.
    """
    
    def __init__(self):
        """Initialize Ollama LLM provider."""
        super().__init__()
        self.host = os.getenv('OLLAMA_HOST', 'http://localhost:11434')
        self.client = Client(host=self.host)

    @property
    def provider(self) -> LLMProvider:
        return LLMProvider.OLLAMA

    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a completion from Ollama API.
        
        Args:
            model_version: Model version (e.g., 'ollama:llama3.2')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Returns:
            Generated response text
        """
        try:
            response = self.client.chat(
                model=model_version,
                messages=prompt_list,
                options={
                    'num_predict': answer_prompt_size,
                    'temperature': 0.0,
                }
            )
            return response.message.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to get answer from Ollama: {str(e)}")
            return ""

    def get_streaming_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> Generator[str, None, None]:
        """Get a streaming completion from Ollama API.
        
        Args:
            model_version: Model version (e.g., 'ollama:llama3.2')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Yields:
            Generated response chunks
        """
        try:
            stream = self.client.chat(
                model=model_version,
                messages=prompt_list,
                options={
                    'num_predict': answer_prompt_size,
                    'temperature': 0.0,
                },
                stream=True
            )
            
            for chunk in stream:
                if chunk.message and chunk.message.content:
                    yield chunk.message.content
                    
        except Exception as e:
            logger.error(f"Failed to get streaming answer from Ollama: {str(e)}")
            yield ""
