import os

os.environ['ELASTICSEARCH_HOST'] = 'localhost'
os.environ['ELASTICSEARCH_PORT'] = '9200'
os.environ['ELASTICSEARCH_USERNAME'] = 'elastic'
os.environ['ELASTICSEARCH_PASSWORD'] = 'elastic123'

import pytest
from elasticsearch import Elasticsearch
from src.lib.utils import get_elasticsearch_config
import json
from configuration.main.cli_create_index import index_settings
import quepasa.indexer.tasks as indexer_tasks


@pytest.fixture(scope="module")
def es_client():
    """Create Elasticsearch client fixture"""
    es = Elasticsearch(**get_elasticsearch_config())
    yield es
    # Cleanup after tests
    test_indices = es.indices.get(index="test-*")
    for index in test_indices:
        es.indices.delete(index=index, ignore=[404])


@pytest.fixture(scope="function")
def test_index(es_client):
    """Create a test index fixture"""
    index_name = "test-search-document-v2"

    # Create index with explicit mapping for embedding field
    test_index_settings = {
        "settings": {
            "index": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        },
        "mappings": {
            "properties": {
                "id": {"type": "keyword"},
                "content": {"type": "text"},
                "embedding": {
                    "type": "dense_vector",
                    "dims": 384,
                    "index": True,
                    "similarity": "cosine"
                },
                "metadata": {
                    "type": "object",
                    "properties": {
                        "source": {"type": "keyword"},
                        "timestamp": {"type": "date"}
                    }
                }
            }
        }
    }

    es_client.indices.create(index=index_name, body=test_index_settings)
    yield index_name
    es_client.indices.delete(index=index_name, ignore=[404])


def test_index_creation(es_client, test_index):
    """Test index creation with correct settings"""
    assert es_client.indices.exists(index=test_index)

    # Verify mappings
    mappings = es_client.indices.get_mapping(index=test_index)
    props = mappings[test_index]["mappings"]["properties"]

    # Verify embedding field
    assert "embedding" in props
    assert props["embedding"]["type"] == "dense_vector"
    assert props["embedding"]["dims"] == 384


def test_document_indexing(es_client, test_index):
    """Test document indexing and retrieval"""
    # Test document with non-zero embedding
    doc = {
        "id": "test-doc-1",
        "content": "This is a test document",
        "embedding": [0.1] * 384,  # Non-zero embedding vector
        "metadata": {
            "source": "test",
            "timestamp": "2024-01-09T12:00:00"
        }
    }

    # Index document
    es_client.index(index=test_index, id=doc["id"], document=doc)
    es_client.indices.refresh(index=test_index)

    # Verify document exists
    assert es_client.exists(index=test_index, id=doc["id"])

    # Retrieve and verify document
    retrieved = es_client.get(index=test_index, id=doc["id"])
    assert retrieved["_source"]["content"] == doc["content"]
    assert len(retrieved["_source"]["embedding"]) == 384


def test_vector_search(es_client, test_index):
    """Test vector search functionality"""
    # Index multiple documents with different non-zero embeddings
    docs = [
        {
            "id": f"test-doc-{i}",
            "content": f"Test document {i}",
            "embedding": [(i + 1) / 10] * 384  # Non-zero embeddings
        }
        for i in range(3)
    ]

    for doc in docs:
        es_client.index(index=test_index, id=doc["id"], document=doc)
    es_client.indices.refresh(index=test_index)

    # Perform vector search with non-zero query vector using knn_vector query
    query = {
        "query": {
            "script_score": {
                "query": {"match_all": {}},
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                    "params": {"query_vector": [0.1] * 384}
                }
            }
        },
        "size": 2
    }

    results = es_client.search(index=test_index, body=query)
    assert len(results["hits"]["hits"]) == 2


def test_text_search(es_client, test_index):
    """Test text search functionality"""
    # Index test documents with non-zero embeddings
    docs = [
        {
            "id": "doc1",
            "content": "The quick brown fox jumps over the lazy dog",
            "embedding": [0.1] * 384
        },
        {
            "id": "doc2",
            "content": "The lazy cat sleeps all day",
            "embedding": [0.2] * 384
        }
    ]

    for doc in docs:
        es_client.index(index=test_index, id=doc["id"], document=doc)
    es_client.indices.refresh(index=test_index)

    # Test full-text search
    query = {
        "query": {
            "match": {
                "content": "lazy"
            }
        }
    }

    results = es_client.search(index=test_index, body=query)
    assert len(results["hits"]["hits"]) == 2  # Both documents contain "lazy"

    # Test phrase search
    query = {
        "query": {
            "match_phrase": {
                "content": "quick brown fox"
            }
        }
    }

    results = es_client.search(index=test_index, body=query)
    assert len(results["hits"]["hits"]) == 1  # Only first document matches


def test_indexer_propagates_product_metadata_to_es(es_client, monkeypatch):
    """
    The indexer should propagate the `metadata` field to both chunk and main product documents.
    This test runs the indexer function directly with mocks and verifies documents in real ES.
    """
    # Create a canonical-mapped index for the test
    index_name = "test-search-document-v2-products"
    es_client.indices.create(index=index_name, body=index_settings)

    try:
        client_id = 'test_client'
        batch_id = 'batch_1'
        domain = 'products'

        # Arrange: product doc as produced by data processor
        product_doc = {
            'id': 'TEST-SKU-123',
            'url': 'https://example.com/p/123',
            'title': 'Test Product',
            'type': 'product',
            'sku': 'TEST-SKU-123',
            'metadata': json.dumps({'title': 'Test Product', 'price': 29.99, 'color': 'red'}),
            'price_from': 19.99,
            'price_to': 29.99,
            'chunks': [
                {
                    'language': 'en',
                    'text': 'Product description text',
                    'keywords': 'product test',
                    'position': 0
                }
            ],
            'languages': ['en'],
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z'
        }

        # Patch storage read: return our product doc regardless of path
        monkeypatch.setattr(indexer_tasks, 'qp_files', indexer_tasks.QuepasaFiles())
        monkeypatch.setattr(indexer_tasks.qp_files, 'get_json_zlib', lambda path: product_doc)

        # Force index name
        class _H(indexer_tasks.IndexerHelper):
            def get_index_name(self) -> str:
                return index_name

        monkeypatch.setattr(indexer_tasks, 'IndexerHelper', _H)

        # Avoid external embedding/LLM calls
        monkeypatch.setattr(indexer_tasks, 'get_embedding_with_retry', lambda provider, model, text: [0.01] * 384)
        monkeypatch.setattr(indexer_tasks, 'get_llm_answer_with_retry',
                            lambda provider, model, messages, max_tokens: "summary")

        # Patch bulk to index documents directly without ingest pipeline requirement
        def _bulk(es, items):
            for item in items:
                es_client.index(index=item['_index'], id=item['_id'], document=item['_source'])
            es_client.indices.refresh(index=index_name)
            return (len(items), [])

        monkeypatch.setattr(indexer_tasks.helpers, 'bulk', _bulk)

        # Act: run indexing for our product
        indexer_tasks.upsert_document.run(client_id, batch_id, domain, 'ignored-path')

        # Assert: verify documents exist and contain metadata
        root_id = f"{client_id}:{domain}:{product_doc['id']}"

        # Main document
        main_doc = es_client.get(index=index_name, id=root_id)
        assert main_doc['_source']['type'] == 'product'
        assert main_doc['_source']['sku'] == 'TEST-SKU-123'
        assert main_doc['_source']['metadata'] == product_doc['metadata']

        # Chunk document id (first chunk index 0)
        chunk_doc = es_client.get(index=index_name, id=f"{root_id}:0")
        assert chunk_doc['_source']['type'] == 'product'
        assert chunk_doc['_source']['sku'] == 'TEST-SKU-123'
        assert chunk_doc['_source']['metadata'] == product_doc['metadata']
    finally:
        es_client.indices.delete(index=index_name, ignore=[404])
