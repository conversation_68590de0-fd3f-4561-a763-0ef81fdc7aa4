import os

os.environ['ELASTICSEARCH_HOST'] = 'localhost'
os.environ['ELASTICSEARCH_PORT'] = '9200'
os.environ['ELASTICSEARCH_USERNAME'] = 'elastic'
os.environ['ELASTICSEARCH_PASSWORD'] = 'elastic123'

import pytest
from elasticsearch import Elasticsearch
from src.lib.utils import get_elasticsearch_config

@pytest.fixture(scope="module")
def es_client():
    """Create Elasticsearch client fixture"""
    es = Elasticsearch(**get_elasticsearch_config())
    yield es
    # Cleanup after tests
    test_indices = es.indices.get(index="test-*")
    for index in test_indices:
        es.indices.delete(index=index, ignore=[404])

@pytest.fixture(scope="function")
def test_index(es_client):
    """Create a test index fixture"""
    index_name = "test-search-document-v2"
    
    # Create index with explicit mapping for embedding field
    index_settings = {
        "settings": {
            "index": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        },
        "mappings": {
            "properties": {
                "id": {"type": "keyword"},
                "content": {"type": "text"},
                "embedding": {
                    "type": "dense_vector",
                    "dims": 384,
                    "index": True,
                    "similarity": "cosine"
                },
                "metadata": {
                    "type": "object",
                    "properties": {
                        "source": {"type": "keyword"},
                        "timestamp": {"type": "date"}
                    }
                }
            }
        }
    }
    
    es_client.indices.create(index=index_name, body=index_settings)
    yield index_name
    es_client.indices.delete(index=index_name, ignore=[404])

def test_index_creation(es_client, test_index):
    """Test index creation with correct settings"""
    assert es_client.indices.exists(index=test_index)
    
    # Verify mappings
    mappings = es_client.indices.get_mapping(index=test_index)
    props = mappings[test_index]["mappings"]["properties"]
    
    # Verify embedding field
    assert "embedding" in props
    assert props["embedding"]["type"] == "dense_vector"
    assert props["embedding"]["dims"] == 384

def test_document_indexing(es_client, test_index):
    """Test document indexing and retrieval"""
    # Test document with non-zero embedding
    doc = {
        "id": "test-doc-1",
        "content": "This is a test document",
        "embedding": [0.1] * 384,  # Non-zero embedding vector
        "metadata": {
            "source": "test",
            "timestamp": "2024-01-09T12:00:00"
        }
    }
    
    # Index document
    es_client.index(index=test_index, id=doc["id"], document=doc)
    es_client.indices.refresh(index=test_index)
    
    # Verify document exists
    assert es_client.exists(index=test_index, id=doc["id"])
    
    # Retrieve and verify document
    retrieved = es_client.get(index=test_index, id=doc["id"])
    assert retrieved["_source"]["content"] == doc["content"]
    assert len(retrieved["_source"]["embedding"]) == 384

def test_vector_search(es_client, test_index):
    """Test vector search functionality"""
    # Index multiple documents with different non-zero embeddings
    docs = [
        {
            "id": f"test-doc-{i}",
            "content": f"Test document {i}",
            "embedding": [(i+1)/10] * 384  # Non-zero embeddings
        }
        for i in range(3)
    ]
    
    for doc in docs:
        es_client.index(index=test_index, id=doc["id"], document=doc)
    es_client.indices.refresh(index=test_index)
    
    # Perform vector search with non-zero query vector using knn_vector query
    query = {
        "query": {
            "script_score": {
                "query": {"match_all": {}},
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                    "params": {"query_vector": [0.1] * 384}
                }
            }
        },
        "size": 2
    }
    
    results = es_client.search(index=test_index, body=query)
    assert len(results["hits"]["hits"]) == 2

def test_text_search(es_client, test_index):
    """Test text search functionality"""
    # Index test documents with non-zero embeddings
    docs = [
        {
            "id": "doc1",
            "content": "The quick brown fox jumps over the lazy dog",
            "embedding": [0.1] * 384
        },
        {
            "id": "doc2",
            "content": "The lazy cat sleeps all day",
            "embedding": [0.2] * 384
        }
    ]
    
    for doc in docs:
        es_client.index(index=test_index, id=doc["id"], document=doc)
    es_client.indices.refresh(index=test_index)
    
    # Test full-text search
    query = {
        "query": {
            "match": {
                "content": "lazy"
            }
        }
    }
    
    results = es_client.search(index=test_index, body=query)
    assert len(results["hits"]["hits"]) == 2  # Both documents contain "lazy"
    
    # Test phrase search
    query = {
        "query": {
            "match_phrase": {
                "content": "quick brown fox"
            }
        }
    }
    
    results = es_client.search(index=test_index, body=query)
    assert len(results["hits"]["hits"]) == 1  # Only first document matches 