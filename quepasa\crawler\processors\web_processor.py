from src.lib.extraction import html_to_pages
from bs4 import BeautifulSoup
from .base import BaseProcessor
from ...utils import split_paragraphs
from ..exceptions import ProcessorError

class WebProcessor(BaseProcessor):
    def _process_impl(self, content: bytes, metadata: dict) -> dict:
        """Process HTML content and extract text."""
        try:
            html_content = content.decode('utf-8')
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract title from <title> tag
            title_tag = soup.find('title')
            title = title_tag.text.strip() if title_tag else None
            
            # Convert HTML to markdown pages
            pages = html_to_pages(html_content)['pages_v2']
            chunks = []
            
            for page in pages:
                raw_chunks = split_paragraphs(page['text'])
                for chunk in raw_chunks:
                    chunks.append({
                        'text': chunk,
                        'position': 'main content',
                    })

            return {
                'status': 'success',
                'url': metadata.get('url'),
                'chunks': chunks,
                'title': title or metadata.get('filename', ''),
                'filename': metadata.get('filename', '')
            }
        except Exception as e:
            raise ProcessorError(f"Error processing HTML content: {str(e)}")