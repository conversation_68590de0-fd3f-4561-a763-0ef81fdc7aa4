info:
  title: Cart Events API
  version: "1.0.0"
  description: |
    API to emit "add to cart" and "remove from cart" events for a given merchant.
    Secured using an `x-api-key` header.
paths:
  /api/v1/merchant/{merchant_id}/cart/add:
    post:
      tags: [Cart]
      summary: Add item to cart
      operationId: addToCart
      parameters:
        - $ref: "#/components/parameters/MerchantId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddToCartRequest"
      responses:
        "201":
          description: Item added event accepted
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Ack"
        "401":
          $ref: "#/components/responses/Unauthorized"
  /api/v1/merchant/{merchant_id}/cart/remove:
    post:
      tags: [Cart]
      summary: Remove item from cart
      operationId: removeFromCart
      parameters:
        - $ref: "#/components/parameters/MerchantId"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RemoveFromCartRequest"
      responses:
        "201":
          description: Item removal event accepted
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Ack"
        "401":
          $ref: "#/components/responses/Unauthorized"
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: x-api-key
      description: API key required in `x-api-key` header
  parameters:
    MerchantId:
      name: merchant_id
      in: path
      required: true
      description: Unique identifier of the merchant
      schema:
        type: string
  schemas:
    AddToCartRequest:
      type: object
      required: [user_id, product_id]
      properties:
        user_id:
          type: string
          description: Unique user identifier
        session_id:
          type: string
          description: Unique user session identifier
        collection:
          type: string
          description: Customer's collection name
        area:
          type: string
          description: Customer's area name
        product_id:
          type: string
          description: Product SKU/ID to add
        quantity:
          type: integer
          minimum: 1
          default: 1
          description: |
            Number of units of the product to add.
            Defaults to 1 if not specified.
        variants:
          type: object
          description: Variant selections for the product
          additionalProperties: true
          example: { color: "red", size: "M" }
    RemoveFromCartRequest:
      type: object
      required: [user_id, product_id]
      properties:
        user_id:
          type: string
          description: Unique user identifier
        session_id:
          type: string
          description: Unique user session identifier
        collection:
          type: string
          description: Customer's collection name
        area:
          type: string
          description: Customer's area name
        product_id:
          type: string
          description: Product SKU/ID to remove
        variants:
          type: object
          description: Variant selections used to identify the exact item
          additionalProperties: true
          example: { color: "red", size: "M" }
    Ack:
      type: object
      properties:
        status:
          type: string
          enum: [accepted]
          description: Indicates the event was accepted for processing
        event_id:
          type: string
          format: uuid
          description: Server-generated identifier for the published event
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error code
        message:
          type: string
          description: Human-readable error message
      required: [error, message]
  responses:
    Unauthorized:
      description: API key missing or invalid
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
          examples:
            invalidKey:
              value:
                error: unauthorized
                message: Invalid or missing API key
security:
  - ApiKeyAuth: []