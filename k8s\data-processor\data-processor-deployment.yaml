apiVersion: apps/v1
kind: Deployment
metadata:
  name: data-processor
  namespace: quepasa
spec:
  replicas: 1
  selector:
    matchLabels:
      app: data-processor
  strategy:
    type: Recreate  # Force recreation rather than rolling update
  template:
    metadata:
      labels:
        app: data-processor
    spec:
      containers:
      - name: data-processor
        image: qpreg.azurecr.io/quepasa/data_processor:v1.0.184
        imagePullPolicy: Always
        command: ["/bin/sh", "-c"]
        args:
          - |
            celery -A quepasa.data_processor.tasks worker -Q data-processor -n data-processor@%h --loglevel=info &
            wait
        env:
        - name: QUEPASA_EXECUTION_MODE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_EXECUTION_MODE
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: KUBERNETES_NAMESPACE
        - name: QUEPASA_IMAGE_VERSION
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_IMAGE_VERSION
        - name: MINIO_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_HOST
        - name: MINIO_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_PORT
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_BUCKET_NAME
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_BUCKET_NAME
        - name: MINIO_DEBUG
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_DEBUG
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: REDIS_PORT
        - name: CELERY_BROKER_URL
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: CELERY_BROKER_URL
        - name: CELERY_RESULT_BACKEND
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: CELERY_RESULT_BACKEND
        - name: QUEPASA_CRAWLER_IMAGE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_CRAWLER_IMAGE
        - name: QUEPASA_DATA_PROCESSOR_IMAGE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_DATA_PROCESSOR_IMAGE
        - name: QUEPASA_INDEXER_IMAGE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_INDEXER_IMAGE
        - name: PYTHONPATH
          value: /app
        envFrom:
        - configMapRef:
            name: shared-config
        - secretRef:
            name: shared-secrets
        volumeMounts:
        - name: data-processor-cache
          mountPath: /app/cache
        - name: config-volume
          mountPath: /app/configuration/main
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
      volumes:
      - name: data-processor-cache
        emptyDir:
          sizeLimit: 25Gi
      - name: config-volume
        configMap:
          name: quepasa-config 