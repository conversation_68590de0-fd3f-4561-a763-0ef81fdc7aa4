import pytest
from unittest.mock import patch, MagicMock
from quepasa.crawler.processors.web_processor import WebProcessor

@pytest.fixture
def mock_html_content():
    """Fixture for mock HTML content"""
    return """
    <html>
        <head><title>Test Page</title></head>
        <body>
            <h1>Main Title</h1>
            <p>Test paragraph</p>
        </body>
    </html>
    """.encode('utf-8')

@pytest.fixture
def sample_metadata():
    """Fixture for sample metadata"""
    return {
        'filename': 'test.html',
        'client_id': 'test_client',
        'url': 'http://example.com/test.html',
        'extension': 'html'
    }

@pytest.fixture
def mock_quepasa_extraction_lib():
    """Mock the quepasa_extraction_lib module"""
    mock = MagicMock()
    
    def mock_html_to_pages(content, minimal_number_of_words=8):
        return {
            "pages_v2": [{"text": "Sample HTML page content", "stripped_link": "sample link", "stripped_words_count": 10}]
        }
    
    mock.html_to_pages = mock_html_to_pages
    return mock

class TestWebProcessor:
    @patch('quepasa.crawler.processors.web_processor.html_to_pages')
    def test_process_valid_html(self, mock_html_to_pages, sample_metadata, mock_html_content, mock_quepasa_extraction_lib):
        mock_html_to_pages.return_value = {'pages_v2': [{"text": "Sample HTML page content"}]}
        
        processor = WebProcessor()
        result = processor.process(mock_html_content, sample_metadata)
        
        assert result is not None
        assert result['status'] == 'success'
        assert 'meta' in result
        assert 'result' in result
        assert isinstance(result['result']['chunks'], list)
        assert len(result['result']['chunks']) > 0
        
        # Verify chunk structure
        for chunk in result['result']['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'] == 'main content'
        
        assert result['result']['title'] == 'Test Page'
        assert result['result']['filename'] == sample_metadata['filename'] 