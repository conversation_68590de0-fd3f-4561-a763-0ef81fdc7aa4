import os
import re
from typing import Dict, Any, List, Union
from whoosh import fields as whoosh_fields, index as whoosh_index, qparser as whoosh_qparser, query as whoosh_query
from bs4 import BeautifulSoup
from Levenshtein import distance

from quepasa.searcher.models.request import QuepasaRequest
from .base_config import BaseConfig

from src.lib.logger import QuepasaLogger

# Logger
logger = QuepasaLogger().get_instance(__name__)

class QuepasaTypoFuzzyTerm(whoosh_query.FuzzyTerm):
    """Custom fuzzy term for typo detection with adaptive distance."""
    def __init__(self, fieldname, text, boost=1.0, maxdist=3, prefixlength=0, constantscore=True):
        if len(text) <= 4:
            maxdist = 1
        elif len(text) <= 6:
            maxdist = 2
        super().__init__(fieldname, text, boost, maxdist, prefixlength, constantscore)

class QuepasaFuzzyTerm(whoosh_query.FuzzyTerm):
    """Custom fuzzy term for general search with fixed parameters."""
    def __init__(self, fieldname, text, boost=1.0, maxdist=2, prefixlength=2, constantscore=True):
        super().__init__(fieldname, text, boost, maxdist, prefixlength, constantscore)

class SearchExtensionsConfig(BaseConfig):
    """Base configuration for Whoosh index management."""

    # Static cache for whoosh indexes by client_code
    _client_whoosh_index_misspellings = {}
    _client_whoosh_index_questions_extensions = {}
    _client_whoosh_index_legends = {}
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

        self._whoosh_index_dir = f"/app/cache/localdb/{self.client_code}" if self.client_code else None

        self.init_search_extensions()

    def _get_whoosh_index_misspellings(self) -> Union[whoosh_index.Index, None]:
        if self.client_code in self._client_whoosh_index_misspellings:
            return self._client_whoosh_index_misspellings[self.client_code]
        return None
    
    def _get_whoosh_index_questions_extensions(self) -> Union[whoosh_index.Index, None]:
        if self.client_code in self._client_whoosh_index_questions_extensions:
            return self._client_whoosh_index_questions_extensions[self.client_code]
        return None
    
    def _get_whoosh_index_legends(self) -> Union[whoosh_index.Index, None]:
        if self.client_code in self._client_whoosh_index_legends:
            return self._client_whoosh_index_legends[self.client_code]
        return None
        
    def init_search_extensions(self):
        """Initialize search indexes for extensions.
        
        Previously: init_extensions()
        """
        if not (self.get_misspellings() or self.get_question_expansions() or self.get_legend_list()):
            return
        
        try:
            # Create base directory
            if not os.path.exists(self._whoosh_index_dir):
                os.makedirs(self._whoosh_index_dir)
                
            # Initialize all indices
            self._init_search_misspellings_extensions_index()
            self._init_search_question_extensions_index()
            self._init_answer_legends_extensions_index()

        except (OSError, PermissionError) as e:
            logger.error(f"Error initializing search extensions: {e}")

    def _ensure_index_subdir(self, name: str) -> str:
        """Ensure a subdirectory under the index dir exists and return its path.
        Logs and returns empty string on failure.
        """
        try:
            subdir = os.path.join(self._whoosh_index_dir, name)
            if not os.path.exists(subdir):
                os.makedirs(subdir)
            return subdir
        except (OSError, PermissionError) as e:
            logger.error(f"Error creating {name} directory: {e}")
            return ""

    def _init_search_misspellings_extensions_index(self):
        """Initialize misspellings index."""
        if (
            self.client_code in self._client_whoosh_index_misspellings
            and self._client_whoosh_index_misspellings[self.client_code]
        ):
            return
        
        misspellings = self.get_misspellings()
        if not misspellings:
            return
        
        logger.info(f"Initializing misspellings index, for {len(misspellings)} misspellings")        
        try:
            whoosh_index_misspellings_dir = self._ensure_index_subdir("misspellings")
            if not whoosh_index_misspellings_dir:
                return
                
            self._client_whoosh_index_misspellings[self.client_code] = whoosh_index.create_in(
                whoosh_index_misspellings_dir,
                whoosh_fields.Schema(word=whoosh_fields.TEXT(stored=True))
            )
            
            writer = self._get_whoosh_index_misspellings().writer()
            for word in misspellings:
                if word and isinstance(word, str) and word.strip():
                    writer.add_document(word=word.strip())
                    logger.info(f"Added misspellings document: {word}")

            writer.commit()

        except (OSError, ValueError) as e:
            self._client_whoosh_index_misspellings.pop(self.client_code, None)
            logger.error(f"Error initializing misspellings index: {e}")
        
    def _init_search_question_extensions_index(self):
        """Initialize search index."""
        if (
            self.client_code in self._client_whoosh_index_questions_extensions
            and self._client_whoosh_index_questions_extensions[self.client_code]
        ):
            return
        
        expansions = self.get_question_expansions()
        if not expansions:
            return
            
        logger.info(f"Initializing question expansions index, for {len(expansions)} expansions")
        try:
            whoosh_index_question_extensions_dir = self._ensure_index_subdir("question_extensions")
            if not whoosh_index_question_extensions_dir:
                return
                
            self._client_whoosh_index_questions_extensions[self.client_code] = whoosh_index.create_in(
                whoosh_index_question_extensions_dir,
                whoosh_fields.Schema(
                    keywords=whoosh_fields.TEXT(stored=True),
                    use_all_parts=whoosh_fields.BOOLEAN(stored=True),
                    use_all_keywords=whoosh_fields.BOOLEAN(stored=True),
                    query=whoosh_fields.TEXT(stored=True)
                )
            )

            writer = self._get_whoosh_index_questions_extensions().writer()
            for item in expansions:
                if not isinstance(item, dict):
                    continue
                    
                query = item.get('query', '').strip()
                if not query:
                    continue

                is_splitted_keywords = (
                    'keywords' in item
                    and item['keywords'] != None
                    and isinstance(item['keywords'], list)
                    and len(item['keywords']) > 1
                )

                if is_splitted_keywords:
                    item['keywords'] = " | ".join(item['keywords'])

                has_keywords = (
                    'keywords' in item
                    and item['keywords'] != None
                    and item['keywords'].strip() != ""
                )

                writer.add_document(
                    keywords = (item['keywords'] if has_keywords else item['query']).lower(),
                    use_all_parts = is_splitted_keywords,
                    use_all_keywords = ('use_all_keywords' in item and item['use_all_keywords'] == True),
                    query = item['query']
                )
                logger.info(f"Added question expansion document: {item}")
                    
            writer.commit()
            
        except (OSError, ValueError) as e:
            self._client_whoosh_index_questions_extensions.pop(self.client_code, None)
            logger.error(f"Error initializing search index: {e}")
            
    def _init_answer_legends_extensions_index(self):
        """Initialize legends index."""
        if (
            self.client_code in self._client_whoosh_index_legends
            and self._client_whoosh_index_legends[self.client_code]
        ):
            return

        legends = self.get_legend_list()
        if not legends:
            return
                
        logger.info(f"Initializing legends index, for {len(legends)} legends")
        try:
            whoosh_index_legends_dir = self._ensure_index_subdir("legends")
            if not whoosh_index_legends_dir:
                return
                
            self._client_whoosh_index_legends[self.client_code] = whoosh_index.create_in(
                whoosh_index_legends_dir,
                whoosh_fields.Schema(
                    keywords=whoosh_fields.TEXT(stored=True),
                    use_all_parts=whoosh_fields.BOOLEAN(stored=True),
                    use_all_keywords=whoosh_fields.BOOLEAN(stored=True),
                    legend=whoosh_fields.TEXT(stored=True)
                )
            )
            
            writer = self._get_whoosh_index_legends().writer()
            for item in legends:
                if not isinstance(item, dict) or not item.get('legend', '').strip():
                    continue
                    
                keywords = item.get('keywords', [])
                if isinstance(keywords, list) and len(keywords) > 1:
                    keywords = " | ".join(keywords)
                elif not keywords:
                    keywords = item['legend']
                    
                writer.add_document(
                    keywords=keywords.lower(),
                    use_all_parts=isinstance(item.get('keywords'), list) and len(item.get('keywords', [])) > 1,
                    use_all_keywords=item.get('use_all_keywords', False),
                    legend=item['legend']
                )
                logger.info(f"Added legend document: {item}")

            writer.commit()

        except (OSError, ValueError) as e:
            self._client_whoosh_index_legends.pop(self.client_code, None)
            logger.error(f"Error initializing legends index: {e}")
    
    # Helper functions
    def _should_add_expansion(self, hit: Any, query: str) -> bool:
        """Check if a search hit should be added as query expansion or legend.
        
        Args:
            hit: Search result hit
            query: Original query text
            
        Returns:
            True if expansion should be added
        """
        if hit['use_all_parts']:
            # Check if all required parts match
            keywords_soup = BeautifulSoup(hit.highlights("keywords"), 'html.parser')
            matched_words = []
            for tag in keywords_soup.select('b'):
                for word in re.split(r'\s+', tag.text.strip()):
                    matched_words.append(word)
                
            if matched_words:
                return all(
                    any(word in matched_words for word in part.strip().split())
                    for part in hit['keywords'].split('|')
                )
            return False
            
        # Check if non-highlighted parts are in query
        highlight = re.sub(r'<b[^>]*>(.*?)</b>', '', hit.highlights("keywords")).strip()
        highlight_lc = re.sub(r'\W+', ' ', highlight.lower()).strip() if highlight else ""
        
        return (
            not hit['use_all_keywords']
            or not highlight 
            or highlight_lc and highlight_lc in query
        )

    def _find_spelling_variations(self, text: str) -> List[str]:
        """Find potential spelling variations of text using fuzzy matching.
        
        Previously: get_typos_for_text()
        
        Args:
            text: Text to find variations for
            
        Returns:
            List of potential spelling variations
        """
        logger.info(f"Finding spelling variations for: {text}")
        
        variations = []
        if not self._get_whoosh_index_misspellings():
            logger.info("No misspellings index found")
            return variations
        
        with self._get_whoosh_index_misspellings().searcher() as searcher:
            text_lc = re.sub(r'\W+', ' ', text.lower()).strip()
            for word_lc in re.split(r'\s+', text_lc):
                try:
                    query = whoosh_qparser.QueryParser(
                        "word",
                        schema=self._get_whoosh_index_misspellings().schema,
                        termclass=QuepasaTypoFuzzyTerm,
                        group=whoosh_qparser.OrGroup.factory(0.9)
                    ).parse(word_lc)
                    
                    sub_variations = []
                    for hit in searcher.search(query):
                        hit_word = hit['word']

                        # without word ending
                        word_lc_short = word_lc[0:-2] if len(hit_word) > 4 else word_lc
                        hit_word_short = hit_word[0:-2] if len(hit_word) > 4 else hit_word

                        sub_variations.append({
                            'short_distance': distance(word_lc_short, hit_word_short),
                            'distance': distance(word_lc, hit_word),
                            'word': hit['word'],
                        })

                    if len(sub_variations) > 0:
                        if len(sub_variations) > 1:
                            sub_variations = sorted(sub_variations, key = lambda t: (t['short_distance'], t['distance']))
                        variations.append(sub_variations[0]['word'])

                except Exception as e:
                    logger.error(f"Error finding spelling variations: {e}")

        logger.info(f"Found {len(variations)} spelling variations")
        return variations
            
    #
    # Configuration methods
    #
    def get_misspellings(self) -> List[str]:
        """Get list of misspellings to index.

        Example:
        [
            "word1",
            "word2"
        ]
        
        Previously: get_typos_list()
        
        Returns:
            List of misspelled words
        """
        return []
        
    def get_question_expansions(self) -> List[Dict[str, Any]]:
        """Get list of query expansion rules.

        Example:
        [
            # Match any of the keywords (OR condition)
            {
                "query": "query1",
                "keywords": "keyword1 keyword2"
            },
            
            # Match all keywords in this group (AND condition)
            {
                "query": "query2",
                "keywords": "keyword3 keyword4",
                "use_all_keywords": True
            },

            # Match any keyword from each group (multiple keyword groups with AND between groups)
            # This means the query will match if at least one keyword from each group is present
            # Example: ["keyword5 keyword6", "keyword7 keyword8"] matches if (keyword5 OR keyword6) AND (keyword7 OR keyword8)
            {
                "query": "query3",
                "keywords": ["keyword5 keyword6", "keyword7 keyword8"]
            }
        ]
        
        Previously: get_search_query_extensions_list()

        Returns:
            List of expansion objects with query and keywords
        """
        return []

    def get_legend_list(self) -> List[Dict[str, Any]]:
        """Get list of legends to index.

        Example:
        [
            {
                "legend": "legend1",
                "keywords": "keyword1 keyword2"
            },
        ]
        
        Previously: get_legend_list()
        
        Returns:
            List of legend strings
        """
        return [] 