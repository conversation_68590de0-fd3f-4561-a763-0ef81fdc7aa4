import os
import pytest
from unittest.mock import Mock, patch
from src.lib.llm.together import TogetherLLM
from src.lib.llm.providers import LLMProvider

@pytest.fixture
def llm(tmp_path):
    """Fixture that provides a Together LLM instance with mocked client."""
    with patch.dict('os.environ', {'TOGETHERAI_API_KEY': 'test-key'}):
        llm = TogetherLLM()
        llm._local_cache_dir = str(tmp_path / "cache")
        os.makedirs(llm._local_cache_dir, exist_ok=True)
        return llm

def test_provider(llm):
    """Test provider property."""
    assert llm.provider == LLMProvider.TOGETHER

def test_get_answer_success(llm):
    """Test successful answer generation."""
    test_response = Mock(choices=[Mock(message=Mock(content="Test response"))])
    prompts = [{"role": "user", "content": "test prompt"}]
    
    # Mock Together client response
    with patch('together.Complete.create', return_value=test_response) as mock_create:
        result = llm.get_answer("meta-llama/Meta-Llama-3-8B-Instruct-Turbo", prompts, 100)
    
    assert result == "Test response"
    mock_create.assert_called_once_with(
        model="meta-llama/Meta-Llama-3-8B-Instruct-Turbo",
        messages=prompts,
        max_tokens=100,
        temperature=0.0
    )

@pytest.mark.skip(reason="Together does not support JSON mode")
def test_get_answer_with_json_mode(llm):
    """Test answer generation with JSON mode enabled."""
    pass

def test_get_answer_failure(llm):
    """Test error handling in answer generation."""
    prompts = [{"role": "user", "content": "test prompt"}]
    
    # Mock Together client to raise an exception
    with patch('together.Complete.create', side_effect=Exception("API error")):
        result = llm.get_answer("meta-llama/Meta-Llama-3-8B-Instruct-Turbo", prompts, 100)
    
    assert result == ""

def test_missing_token():
    """Test error handling for missing API token."""
    with patch.dict('os.environ', clear=True):
        with pytest.raises(ValueError) as exc_info:
            TogetherLLM()
        assert " environment variable is not set" in str(exc_info.value)

def test_get_streaming_answer(llm):
    """Test getting streaming answer from Together."""
    test_chunks = [
        Mock(choices=[Mock(delta=Mock(content="Test"))]),
        Mock(choices=[Mock(delta=Mock(content=" response"))])
    ]
    prompts = [{"role": "user", "content": "test message"}]
    
    # Mock Together client response
    with patch('together.Complete.create', return_value=iter(test_chunks)) as mock_create:
        chunks = []
        for chunk in llm.get_streaming_answer("meta-llama/Meta-Llama-3-8B-Instruct-Turbo", prompts, 100):
            chunks.append(chunk)

    assert chunks == ["Test", " response"]
    mock_create.assert_called_once_with(
        model="meta-llama/Meta-Llama-3-8B-Instruct-Turbo",
        messages=prompts,
        max_tokens=100,
        temperature=0.0,
        stream=True
    )

def test_get_streaming_answer_failure(llm):
    """Test error handling in streaming answer generation."""
    prompts = [{"role": "user", "content": "test message"}]
    
    # Mock Together client to raise an exception
    with patch('together.Complete.create', side_effect=Exception("API error")):
        chunks = []
        for chunk in llm.get_streaming_answer("meta-llama/Meta-Llama-3-8B-Instruct-Turbo", prompts, 100):
            chunks.append(chunk)
    
    assert chunks == [""]  # Should yield empty string on error

def test_get_streaming_answer_empty_response(llm):
    """Test handling of empty streaming response."""
    test_chunks = [
        Mock(choices=[Mock(delta=Mock(content=None))]),
        Mock(choices=[Mock(delta=Mock(content=""))])
    ]
    prompts = [{"role": "user", "content": "test message"}]
    
    # Mock Together client response
    with patch('together.Complete.create', return_value=iter(test_chunks)) as mock_create:
        chunks = []
        for chunk in llm.get_streaming_answer("meta-llama/Meta-Llama-3-8B-Instruct-Turbo", prompts, 100):
            chunks.append(chunk)
    
    assert chunks == []  # Should yield no chunks 