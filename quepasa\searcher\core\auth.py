import os
from typing import Optional, Dict, Any, Tuple
from dataclasses import dataclass

from src.lib.logger import <PERSON><PERSON><PERSON>Log<PERSON>
from configuration.main.default import QuepasaConfigurationHub
from src.lib.constants import (
    AUTH_TYPE_BEARER, 
    AUTH_TYPE_EXTERNAL
)

logger = QuepasaLogger().get_instance(__name__)

@dataclass
class AuthResult:
    """Result of authentication check"""
    is_authorized: bool
    error: Optional[str] = None

class AuthManager:
    """Authentication and authorization manager"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        """Initialize auth manager"""
        self.config = config
        
    def authenticate_request(self, headers: Dict[str, str]) -> AuthResult:
        """Authenticate an incoming request
        
        Args:
            headers: Request headers
            
        Returns:
            AuthResult with authorization status and user info
        """
        try:
            # Check if auth is required
            auth_header = headers.get('Authorization')
            if not auth_header:
                return AuthResult(
                    is_authorized=False,
                    error="Missing Authorization header"
                )
                
            # Parse auth header
            auth_type, auth_token, external_user_token = self._parse_auth_header(auth_header)
            if not auth_type:
                return AuthResult(
                    is_authorized=False,
                    error="Invalid Authorization header format"
                )
                
            # Handle different auth types
            if auth_type == AUTH_TYPE_BEARER:
                return self._handle_bearer_auth(auth_token)
            
            elif auth_type == AUTH_TYPE_EXTERNAL:
                return self._handle_external_auth(auth_token, external_user_token)

            else:
                return AuthResult(
                    is_authorized=False,
                    error=f"Unsupported authorization type: {auth_type}"
                )
                
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return AuthResult(
                is_authorized=False,
                error=f"Authentication error: {str(e)}"
            )
            
    def _parse_auth_header(self, auth_header: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """Parse Authorization header into type and token
        
        Args:
            auth_header: Authorization header value
            
        Returns:
            Tuple of (auth_type, auth_token, external_user_token) or (None, None, None) if invalid
        """
        try:
            auth_parts = auth_header.split(' ', 1)
            auth_token = auth_parts[1].split(':')[-1]
            if len(auth_parts) == 2:
                return AUTH_TYPE_BEARER, auth_token, None
            
            elif len(auth_parts) == 3:
                external_user_token = auth_parts[2]
                return AUTH_TYPE_EXTERNAL, auth_token, external_user_token
            
        except:
            pass
        
        return None, None, None
            
    def _handle_bearer_auth(self, token: str) -> AuthResult:
        """Handle Bearer token authentication
        
        Args:
            token: Bearer token
            
        Returns:
            AuthResult with authorization status
        """
        if self.config.validate_auth_token(token):
            return AuthResult(is_authorized=True)
        
        else:
            return AuthResult(
                is_authorized=False,
                error="Invalid Bearer token"
            )
            
    def _handle_external_auth(self, token: str, external_user_token: str) -> AuthResult:
        """Handle external authentication
        
        Args:
            token: External auth token
            external_user_token: External user token
            
        Returns:
            AuthResult with authorization status
        """
        if self.config.validate_external_auth_token(token):
            if self.config.validate_external_auth_token(external_user_token):
                return AuthResult(is_authorized=True)
            
            else:
                return AuthResult(
                    is_authorized=False,
                    error="Invalid Bearer token"
                )
            
        else:
            return AuthResult(
                is_authorized=False,
                error="Invalid external token"
            ) 