import os
from typing import Optional, Dict, Any, Tuple
from dataclasses import dataclass

from quepasa.common.config_client import get_tenant_config
from quepasa.common.config_models import TenantConfig
from src.lib.logger import QuepasaLogger
from configuration.main.default import QuepasaConfigurationHub
from src.lib.constants import (
    AUTH_TYPE_BEARER, 
    AUTH_TYPE_EXTERNAL
)

logger = QuepasaLogger().get_instance(__name__)

@dataclass
class AuthResult:
    """Result of authentication check"""
    is_authorized: bool
    error: Optional[str] = None

class AuthManager:
    """Authentication and authorization manager"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        """Initialize auth manager"""
        self.config = config
        
    def authenticate_request(self, headers: Dict[str, str]) -> AuthResult:
        """Authenticate an incoming request
        
        Args:
            headers: Request headers
            
        Returns:
            AuthResult with authorization status and user info
        """
        try:
            # Check if auth is required
            auth_header = headers.get('Authorization')
            if not auth_header:
                return AuthResult(
                    is_authorized=False,
                    error="Missing Authorization header"
                )

            # Check if this is client-key authentication (has X-Customer-Id header)
            x_customer_id = headers.get('x-customer-id') or headers.get('X-Customer-Id')

            if x_customer_id and auth_header.lower().startswith('client-key '):
                # Handle client-key authentication
                # Extract key from the "client-key some-key" format
                auth_parts = auth_header.split(' ', 1)
                if len(auth_parts) == 2:
                    key = auth_parts[1]
                    return self._handle_client_key_auth(x_customer_id, key)
                else:
                    return AuthResult(
                        is_authorized=False,
                        error="Invalid Authorization header format"
                    )
                
            # Parse auth header
            auth_type, auth_token, external_user_token = self._parse_auth_header(auth_header)
            if not auth_type:
                return AuthResult(
                    is_authorized=False,
                    error="Invalid Authorization header format"
                )
                
            # Handle different auth types
            if auth_type == AUTH_TYPE_BEARER:
                return self._handle_bearer_auth(auth_token)
            
            elif auth_type == AUTH_TYPE_EXTERNAL:
                return self._handle_external_auth(auth_token, external_user_token)

            else:
                return AuthResult(
                    is_authorized=False,
                    error=f"Unsupported authorization type: {auth_type}"
                )
                
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return AuthResult(
                is_authorized=False,
                error=f"Authentication error: {str(e)}"
            )
            
    def _parse_auth_header(self, auth_header: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """Parse Authorization header into type and token
        
        Args:
            auth_header: Authorization header value
            
        Returns:
            Tuple of (auth_type, auth_token, external_user_token) or (None, None, None) if invalid
        """
        try:
            auth_parts = auth_header.split(' ', 1)
            auth_token = auth_parts[1].split(':')[-1]
            if len(auth_parts) == 2:
                return AUTH_TYPE_BEARER, auth_token, None
            
            elif len(auth_parts) == 3:
                external_user_token = auth_parts[2]
                return AUTH_TYPE_EXTERNAL, auth_token, external_user_token
            
        except:
            pass
        
        return None, None, None
            
    def _handle_bearer_auth(self, token: str) -> AuthResult:
        """Handle Bearer token authentication
        
        Args:
            token: Bearer token
            
        Returns:
            AuthResult with authorization status
        """
        if self.config.validate_auth_token(token):
            return AuthResult(is_authorized=True)
        
        else:
            return AuthResult(
                is_authorized=False,
                error="Invalid Bearer token"
            )
            
    def _handle_external_auth(self, token: str, external_user_token: str) -> AuthResult:
        """Handle external authentication
        
        Args:
            token: External auth token
            external_user_token: External user token
            
        Returns:
            AuthResult with authorization status
        """
        if self.config.validate_external_auth_token(token):
            if self.config.validate_external_auth_token(external_user_token):
                return AuthResult(is_authorized=True)
            
            else:
                return AuthResult(
                    is_authorized=False,
                    error="Invalid Bearer token"
                )
            
        else:
            return AuthResult(
                is_authorized=False,
                error="Invalid external token"
            )

    def _handle_client_key_auth(self, client_id: str, key: str) -> AuthResult:
        """Handle client-key authentication

        This method expects the client_id and key to be extracted from headers:
        - X-Customer-Id header becomes client_id
        - Authorization: "client-key some-key" becomes key

        Args:
            client_id: Client identifier from X-Customer-Id header
            key: Client key from Authorization header

        Returns:
            AuthResult with authorization status and appropriate error messages
        """
        # Validate client_id is not empty
        if not client_id or not client_id.strip():
            return AuthResult(
                is_authorized=False,
                error="Missing X-Customer-Id header"
            )

        # Validate key is not empty
        if not key or not key.strip():
            return AuthResult(
                is_authorized=False,
                error="Invalid or missing client key"
            )

        tenant_config: Optional[TenantConfig] = get_tenant_config(tenant_name=client_id)

        if tenant_config is None or not tenant_config.client_key_is_valid(client_key=key):
            return AuthResult(
                is_authorized=False,
                error="Invalid or missing client key"
            )

        return AuthResult(
            is_authorized=True
        )