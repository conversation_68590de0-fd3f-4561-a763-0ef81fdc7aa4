import os
from typing import Dict, Any, <PERSON>, Union
import re
from whoosh import qparser as whoosh_qparser
from quepasa.searcher.models.request import QuepasaRequest
from .search_extensions import QuepasaFuzzyTerm, QuepasaTypoFuzzyTerm
from .localization import LocalizationConfig
from .search_extensions import SearchExtensionsConfig
from src.lib.logger import QuepasaLogger

# Logger
logger = QuepasaLogger().get_instance(__name__)

class QuestionConfig(LocalizationConfig, SearchExtensionsConfig):
    """Base configuration for question processing."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)       
        
    def expand_question(self, source: str, question: str, language_code: str) -> str:
        """Expand search query with related terms and typo corrections.
        
        Previously: get_extended_question()
        
        Args:
            source: Source type
            question: Original question text
            
        Returns:
            Expanded question with related terms and spelling variations
        """
        logger.info(f"Original question: {question}")

        # Start with original query plus typo corrections
        expanded_terms = [question] + self._find_spelling_variations(question)
        logger.info(f"Expanded terms: {expanded_terms}")

        expanded_query = " ".join(expanded_terms)
        logger.info(f"Expanded query: {expanded_query}")
        
        # Add query expansions from rules
        if not self._get_whoosh_index_questions_extensions():
            logger.info("No question expansions index found")
            return expanded_query
            
        with self._get_whoosh_index_questions_extensions().searcher() as searcher:
            try:
                query_lc = re.sub(r'\W+', ' ', expanded_query.lower()).strip()
                logger.info(f"Expanded query lowercased: {query_lc}")

                query = whoosh_qparser.QueryParser(
                    "keywords",
                    schema=self._get_whoosh_index_questions_extensions().schema,
                    termclass=QuepasaFuzzyTerm,
                    group=whoosh_qparser.OrGroup.factory(0.9)
                ).parse(query_lc)

                for hit in searcher.search(query):
                    if self._should_add_expansion(hit, query_lc):
                        expanded_terms.append(hit['query'])

            except Exception as e:
                logger.error(f"Error expanding question: {e}")
                    
        return " ".join(expanded_terms)
    
    def get_misspellings(self) -> List[str]:
        """Get dictionary of known word misspellings.
        
        Previously: get_typos_list()
        
        Returns:
            Dict mapping words to their known misspellings

        Example:
        [
            "word1",
            "word2",
            "word3"
        ]
        """
        return []
        
    def get_question_expansions(self) -> List[Dict[str, Any]]:
        """Get rules for expanding search queries.
        
        Previously: get_search_query_extensions_list()
        
        Returns:
            List of dictionaries with terms and related terms for expansion

        Example:
        [
            {
                "keywords": "word1 word2 word3",
                "use_all_keywords": True,
                "query": "new word"
            }
        ]
        """
        return []
    
    def get_question_with_instruct(self, model: str, question: str) -> str:
        """Get question with instruct.
        
        Args:
            model: Model name
            question: Question text
            
        Returns:
            Question with instruct
        """
        return question
