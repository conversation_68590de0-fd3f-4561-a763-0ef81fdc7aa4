import os
import sys
import traceback
import types
import asyncio
import inspect
from typing import Dict, Any, Union, Optional, Generator, Iterable, AsyncGenerator
from flask import Flask, request, Response, jsonify, stream_with_context
import functions_framework

from quepasa.searcher.api.http import HTT<PERSON>Handler
from quepasa.searcher.api.telegram import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from quepasa.searcher.api.sse import SSEHandler
from quepasa.searcher.api.webhook import <PERSON>hookHand<PERSON>
from src.lib.logger import QuepasaLogger
from quepasa.searcher.models.request import QuepasaRequest
from configuration.main.default import QuepasaConfigurationHub
from quepasa.searcher.history.manager import HistoryManager
from src.lib.constants import (
    HTTP_PROTOCOL,
    SSE_PROTOCOL,
    WEBHOOK_PROTOCOL,
    TELEGRAM_PROTOCOL,
    ANSWER_STREAM_ENDPOINT,
    VALID_ENDPOINTS
)
from quepasa.searcher.models.response import QuepasaResponse

logger = QuepasaLogger().get_instance(__name__)

# Constants

# Initialize Flask app
app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for Kubernetes probes"""
    return jsonify({"status": "healthy"}), 200

def get_endpoint_from_path(path: str) -> Optional[str]:
    """Extract endpoint from path
    
    Args:
        path: Request path
        
    Returns:
        Optional[str]: Endpoint name or None if not found
    """
    # Remove query parameters and split path
    clean_path = path.split('?')[0].rstrip('/')
    parts = clean_path.split('/')
    
    # Get last non-empty part as endpoint
    for part in reversed(parts):
        if part:
            return part
    return None

@app.route('/', defaults={'path': ''}, methods=['GET', 'POST', 'OPTIONS'])
@app.route('/<path:path>', methods=['GET', 'POST', 'OPTIONS'])
def handle_request(path: str) -> Union[Response, tuple]:
    """Handle all incoming requests
    
    Args:
        path: Request path
        
    Returns:
        Union[Response, tuple]: Flask response object or tuple with response data
    """
    try:
        # Extract request information
        method = request.method
        url = request.url
        headers = dict(request.headers)
        endpoint = get_endpoint_from_path(path)

        # Handle OPTIONS request
        if method == 'OPTIONS':
            response = {
                'status': 'ok',
                'message': 'Options request handled successfully'
            }
            cors_headers = {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            }
            return jsonify(response), 200, cors_headers

        # Validate other endpoints
        if endpoint and endpoint not in VALID_ENDPOINTS:
            error_response = {
                'error': f'Unknown endpoint: {endpoint}',
                'status': 'error',
                'valid_endpoints': list(VALID_ENDPOINTS)
            }
            return jsonify(error_response), 404

        # Parse request data based on content type
        request_data = {}
        if request.is_json:
            request_data = request.get_json(silent=True) or {}
            logger.info(f"JSON request data: {request_data}")

        elif request.form:
            request_data = dict(request.form)
            logger.info(f"Form request data: {request_data}")

        elif request.data:
            try:
                request_data = request.get_json() or {}
                logger.info(f"Raw data request data: {request_data}")
            except Exception as e:
                logger.error(f"Error parsing request data: {str(e)}")
                pass

        # Extract client from Bearer token
        authorization_header = headers.get('Authorization')
        if authorization_header and authorization_header.startswith('Bearer '):
            authorization_parts = authorization_header.split(' ')
            full_client_token = authorization_parts[1]
            token_parts = full_client_token.split(':')
            if len(token_parts) == 2:
                request_data['client'] = token_parts[0]
                logger.info(f"Extracted client from token: {token_parts[0]}")

        # Create QuepasaRequest and initialize configuration
        quepasa_request = QuepasaRequest.from_dict(request_data)
        quepasa_request.protocol = request_data.get('protocol', SSE_PROTOCOL if endpoint == ANSWER_STREAM_ENDPOINT else HTTP_PROTOCOL)
        logger.info(f"Created QuepasaRequest with protocol: {quepasa_request.protocol}")

        # Initialize configuration hub
        config = QuepasaConfigurationHub.from_request(quepasa_request)

        # Handle based on protocol
        handler = None
        if config.request.protocol == TELEGRAM_PROTOCOL:
            handler = TelegramHandler(config)
        elif config.request.protocol == SSE_PROTOCOL:
            handler = SSEHandler(config)
        elif config.request.protocol == WEBHOOK_PROTOCOL:
            handler = WebhookHandler(config)
        else:
            handler = HTTPHandler(config)
        logger.info(f"Using handler: {handler.__class__.__name__}")

        response = handler.handle_request(method, url, headers)
        
        # If this is an SSE response (Flask Response object), return it directly
        if isinstance(response, Response):
            return response
            
        # For non-SSE responses, handle as before
        if isinstance(response, tuple) and len(response) > 0:
            try:
                # Convert the first element (response data)
                response_data = response[0]
                
                # If it's a generator, convert it
                if isinstance(response_data, QuepasaResponse):
                    converted_data = response_data.to_dict()

                else:
                    converted_data = response_data
                
                # Return JSON response
                return jsonify(converted_data), response[1], response[2]
                
            except Exception as conv_error:
                logger.error(f"Error during conversion: {str(conv_error)}")
                logger.error(traceback.format_exc())
                raise

        return response
        
    except Exception as e:
        # Enhanced error logging
        exc_type, exc_value, exc_traceback = sys.exc_info()
        tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)
        
        logger.error("Unexpected error in request handler:")
        logger.error(f"Error type: {exc_type.__name__}")
        logger.error(f"Error message: {str(e)}")
        logger.error("Traceback:")
        for line in tb_lines:
            logger.error(line.rstrip())
            
        error_response = {
            'error': 'Internal server error',
            'status': 'error',
            'details': str(e) if app.debug else None,
            'error_type': exc_type.__name__ if app.debug else None
        }
        return jsonify(error_response), 500

@functions_framework.http
def main(request) -> Union[Response, tuple]:
    """Cloud Function entry point
    
    Args:
        request: Flask request object
        
    Returns:
        Union[Response, tuple]: Flask response object or tuple with response data
    """
    # Forward request to Flask app
    with app.request_context(request.environ):
        return app.full_dispatch_request()

# Development server configuration
if __name__ == '__main__':
    # Get port from environment or use default
    port = int(os.environ.get('PORT', 8080))
    
    # Configure development server
    app.config['JSON_SORT_KEYS'] = False  # Preserve JSON key order
    app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True  # Pretty print JSON in development
    
    # Development configuration
    debug_mode = os.environ.get('DEBUG', 'false').lower() == 'true'
    if debug_mode:
        app.config['SECRET_KEY'] = 'dev'  # Set a secret key for development
        app.config['DEBUG'] = True
        logger.warning('Running in DEBUG mode - Do not use in production!')
    
    # Start development server
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug_mode,
        use_reloader=debug_mode
    ) 