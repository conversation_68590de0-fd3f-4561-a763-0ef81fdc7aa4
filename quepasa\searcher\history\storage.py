import os
import json
import time
from abc import ABC, abstractmethod
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Optional

from src.lib.files import QuepasaFiles
from src.lib.logger import QuepasaLogger
from configuration.main.default import QuepasaConfigurationHub
from .models import HistoryItem, HistoryFilter
from src.lib.constants import TELEGRAM_PROTOCOL, WEBHOOK_PROTOCOL

logger = QuepasaLogger().get_instance(__name__)
qp_files = QuepasaFiles()

class HistoryStorage(ABC):
    """Abstract base class for history storage implementations"""
    
    @abstractmethod
    def save_item(self, user_id: str, item: HistoryItem) -> None:
        """Save a history item for a user"""
        pass
        
    @abstractmethod
    def get_items(self, filter: HistoryFilter) -> List[HistoryItem]:
        """Get history items matching the filter criteria"""
        pass
        
    @abstractmethod
    def clear_history(self, user_id: str) -> None:
        """Clear all history for a user"""
        pass

class FileHistoryStorage(HistoryStorage):
    """File-based implementation of history storage"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        self.config = config
        
    def _get_history_path(self, user_id: str) -> str:
        """Get base history path for a user
        
        Args:
            user_id: User ID
            
        Returns:
            str: Base history path
        """
        pathes = [
            'history',
            self.config.get_client_code()
        ]
        
        # Add protocol-specific path if needed
        protocol = self.config.request.protocol
        if protocol in [TELEGRAM_PROTOCOL, WEBHOOK_PROTOCOL]:
            pathes.append(protocol)
            
        pathes.append(str(user_id))
        return os.path.join(*pathes)
        
    def _get_history_date_path(self, user_id: str, date: datetime) -> str:
        """Get history storage path for a specific date"""
        return f"{self._get_history_path(user_id)}/{date.strftime('%Y-%m-%d')}"
        
    def save_item(self, user_id: str, item: HistoryItem) -> None:
        """Save history item to file storage"""
        try:
            # Create date-based directory structure
            date_path = self._get_history_date_path(user_id, item.ts)
            
            # Generate filename using timestamp
            file_path = f"{date_path}/{time.time()}.json.zlib"
            
            # Convert item to dict for storage
            item_dict = {
                'request': item.request,
                'ts': item.ts.isoformat(),
                'role': item.role,
                'content': item.content,
                'tool_calls': item.tool_calls,
                'tool_call_id': item.tool_call_id,
                'name': item.name,
                'references': item.references
            }
            
            qp_files.set_json_zlib(file_path, item_dict)
            
        except Exception as e:
            logger.error(f"Failed to save history item: {str(e)}", exc_info=True)
            raise
            
    def get_items(self, filter: HistoryFilter) -> List[HistoryItem]:
        """Get history items from file storage"""
        items = []
        
        try:
            # Default to last 2 days if no time range specified
            if not filter.start_time:
                filter.start_time = datetime.now() - timedelta(days=2)

            if not filter.end_time:
                filter.end_time = datetime.now()
                
            # Get all files in date range
            current_date = filter.start_time

            logger.info(f"Get history items for filter: {filter}")
            while current_date.strftime('%Y-%m-%d') <= filter.end_time.strftime('%Y-%m-%d'):
                date_path = self._get_history_date_path(filter.user_id, current_date)
                logger.info(f"Get history items for date path: {date_path}")
                
                for file_path in qp_files.get_files(date_path):
                    logger.info(f"Read history item for path: {file_path}")
                    if file_path.endswith('.json.zlib'):
                        try:
                            item_dict = qp_files.get_json_zlib(file_path)
                            
                            # Convert stored dict back to HistoryItem
                            item = HistoryItem(
                                request=item_dict['request'],
                                ts=datetime.fromisoformat(item_dict['ts']),
                                role=item_dict['role'],
                                content=item_dict['content'],
                                tool_calls=item_dict.get('tool_calls'),
                                tool_call_id=item_dict.get('tool_call_id'),
                                name=item_dict.get('name'),
                                references=item_dict.get('references')
                            )
                            
                            # Apply filters
                            if (not filter.roles or item.role in filter.roles):
                                items.append(item)
                                
                        except Exception as e:
                            logger.error(f"Failed to read history file {file_path}: {str(e)}")
                            continue
                                
                current_date += timedelta(days=1)
                
            # Apply max items limit
            if filter.max_items and len(items) > filter.max_items:
                items = items[-filter.max_items:]
                
        except Exception as e:
            logger.error(f"Failed to get history items: {str(e)}", exc_info=True)
            raise
            
        return items
        
    def clear_history(self, user_id: str) -> None:
        """Clear all history for a user"""
        try:
            user_path = self._get_history_path(user_id)
            if qp_files.exists(user_path):
                for file_path in qp_files.get_files(user_path):
                    qp_files.delete_file(file_path)
                
        except Exception as e:
            logger.error(f"Failed to clear history: {str(e)}", exc_info=True)
            raise 