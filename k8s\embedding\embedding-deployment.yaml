apiVersion: apps/v1
kind: Deployment
metadata:
  name: embedding
  namespace: quepasa
  labels:
    app: embedding
spec:
  replicas: 2
  selector:
    matchLabels:
      app: embedding
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: embedding
    spec:
      containers:
      - name: embedding
        image: qpreg.azurecr.io/quepasa/embedding:v1.0.184
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        env:
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: REDIS_PORT
        - name: REDIS_DB
          value: "0"
        - name: REDIS_PREFIX
          value: "embedding:"
        - name: REDIS_EXPIRY
          value: "86400"
        - name: REPLICATE_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: REPLICATE_API_KEY
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: OPENAI_API_KEY
        - name: NEBIUS_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: NEBIUS_API_KEY
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ANTHROPIC_API_KEY
        - name: HUGGINGFACE_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: HUGGINGFACE_API_KEY
        - name: PRIVATE_EMBEDDING_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: PRIVATE_EMBEDDING_ENDPOINT
        - name: PRIVATE_EMBEDDING_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: PRIVATE_EMBEDDING_AUTH_TOKEN
        - name: PYTHONPATH
          value: "/app"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8080"
        - name: MODEL_CACHE_DIR
          value: "/app/models"
        - name: LOAD_MODELS_ON_DEMAND
          value: "true"
        - name: ACTIVE_MODEL_TYPE
          value: "qint8"
        volumeMounts:
        - name: embedding-cache
          mountPath: /app/cache
        - name: config-volume
          mountPath: /app/configuration/main
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 300
          periodSeconds: 60
          timeoutSeconds: 20
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 60
          timeoutSeconds: 20
          failureThreshold: 3
        resources:
          requests:
            cpu: "100m"
            memory: "1Gi"
          limits:
            cpu: "2"
            memory: "4Gi"
      volumes:
      - name: embedding-cache
        emptyDir:
          sizeLimit: 25Gi
      - name: config-volume
        configMap:
          name: quepasa-config 