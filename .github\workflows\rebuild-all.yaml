name: "Rebuild Services Dev"

on:
  workflow_dispatch:
    inputs:
      services:
        description: 'Services to rebuild (comma-separated, or "all" for all services). Available: api, crawler, data-processor, elasticsearch-init, embedding, indexer, searcher'
        required: false
        default: 'all'
        type: string

permissions:
  contents: read
  actions: write

jobs:
  trigger-workflows:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Get service workflows
      id: get-workflows
      run: |
        # Define only the service build workflows (exclude utility workflows)
        WORKFLOWS="api crawler data-processor elasticsearch-init embedding indexer searcher"
        echo "Service workflows: $WORKFLOWS"
        echo "WORKFLOWS=$WORKFLOWS" >> $GITHUB_ENV

    - name: Trigger selected workflows
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SERVICES_INPUT: ${{ github.event.inputs.services }}
      run: |

        set +e
        WORKFLOWS="${WORKFLOWS}"
        
        SERVICES_INPUT="${SERVICES_INPUT:-all}"
        
        if [ "$SERVICES_INPUT" = "all" ]; then
          SERVICES_TO_BUILD=$WORKFLOWS
        else
          SERVICES_TO_BUILD=$(echo "$SERVICES_INPUT" | tr ',' ' ')
        fi
    
        echo "🚀 Starting rebuild for services: $SERVICES_TO_BUILD"
        
        SUCCESS_COUNT=0
        FAILED_COUNT=0
        
        for service in $SERVICES_TO_BUILD; do
          workflow_file="${service}.yaml"
          echo "Triggering workflow: $workflow_file"
          
          # Check if workflow file exists
          if [ -f ".github/workflows/$workflow_file" ]; then
            response=$(curl -s -w "%{http_code}" -L \
              -X POST \
              -H "Accept: application/vnd.github+json" \
              -H "Authorization: Bearer $GITHUB_TOKEN" \
              -H "X-GitHub-Api-Version: 2022-11-28" \
              "https://api.github.com/repos/${{ github.repository }}/actions/workflows/$workflow_file/dispatches" \
              -d '{"ref":"main"}')
            
            http_code="${response: -3}"
            
            if [ "$http_code" = "204" ]; then
              echo "✅ Successfully triggered $service workflow"
              ((SUCCESS_COUNT++))
            else
              echo "❌ Failed to trigger $service workflow (HTTP: $http_code)"
              ((FAILED_COUNT++))
            fi
          else
            echo "⚠️  Workflow file $workflow_file not found"
            ((FAILED_COUNT++))
          fi
          
          echo ""
          # Add a small delay to avoid rate limiting
          sleep 2
        done
        
        echo ""
        echo "📊 Summary:"
        echo "  ✅ Successfully triggered: $SUCCESS_COUNT workflows"
        echo "  ❌ Failed: $FAILED_COUNT workflows"
        
        # Set summary for GitHub Actions
        {
          echo "## Rebuild Services Summary"
          echo ""
          echo "**Services:** $SERVICES_INPUT"
          echo "**Triggered by:** ${{ github.actor }}"
          echo ""
          echo "### Results:"
          echo "- ✅ Successfully triggered: $SUCCESS_COUNT workflows"
          echo "- ❌ Failed: $FAILED_COUNT workflows"
          echo ""
          echo "### Services processed:"
          for service in $SERVICES_TO_BUILD; do
            echo "- $service"
          done
        } >> $GITHUB_STEP_SUMMARY
        
        if [ $FAILED_COUNT -gt 0 ]; then
          echo "⚠️ Some workflows failed to trigger, but not failing the job."
        fi
