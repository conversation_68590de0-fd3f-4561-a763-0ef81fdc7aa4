import os
import json
import requests
from typing import List, Dict, Any, AsyncGenerator
from mistralai import <PERSON><PERSON><PERSON>

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .base import BaseLL<PERSON>
from .cache import LLMCacheMixin
from .providers import LLMProvider

logger = QuepasaLogger().get_instance(__name__)

class MistralLLM(BaseLLM, LLMCacheMixin):
    """Mistral AI LLM provider.
    
    This class provides access to Mistral's language models through both
    their REST API and official client library.
    """
    
    def __init__(self):
        """Initialize Mistral LLM provider."""
        super().__init__()

        # Check for API key
        self.api_key = os.getenv('MISTRAL_API_KEY')
        if not self.api_key:
            raise ValueError("MISTRAL_API_KEY environment variable is not set")
        self.client = Mistral(api_key=self.api_key)

    @property
    def provider(self) -> LLMProvider:
        return LLMProvider.MISTRAL

    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a completion from Mistral API.
        
        Args:
            model_version: Model version (e.g., 'mistral-tiny')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Returns:
            Generated response text
        """
        try:
            response = self.client.chat.complete(
                model=model_version,
                messages=prompt_list,
                max_tokens=answer_prompt_size,
                temperature=0.0,
                response_format={"type": "json_object"} if json_mode else None
            )
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Failed to get answer from Mistral: {str(e)}")
            raise

    async def get_streaming_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> AsyncGenerator[str, None]:
        """Get a streaming completion from Mistral API.
        
        Args:
            model_version: Model version (e.g., 'mistral-tiny')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Yields:
            Generated response chunks
        """
        try:
            stream = await self.client.chat.stream_async(
                model=model_version,
                messages=prompt_list,
                max_tokens=answer_prompt_size,
                temperature=0.0,
                response_format={"type": "json_object"} if json_mode else None
            )
            
            async for chunk in stream:
                if chunk.data.choices[0].delta.content:
                    yield chunk.data.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Failed to get streaming answer from Mistral: {str(e)}")
            raise 