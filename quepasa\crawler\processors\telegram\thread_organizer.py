import os
import json
import hashlib
import zlib
import re
import copy
import tiktoken
from typing import List, Dict, Set, Optional, Any, Tu<PERSON>
from rapidfuzz import process, fuzz
import numpy as np
from datetime import datetime
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ....utils import split_text

# Set up logger
logger = QuepasaLogger().get_instance(__name__)

class ThreadOrganizer:
    def __init__(self, external_username_mappings: Dict[str, Dict] = {}):
        self.message_cache: Dict[str, Dict] = {}
        self.reply_cache: Dict[str, List[str]] = {}
        self.username_mappings: Dict[str, Dict] = {}  # Store username to name mapping
        self.external_username_mappings: Dict[str, Dict] = {}  # Store username to name mapping
        self.chat_title: str = None  # Store chat title
        self.unique_urls: Set[str] = set()  # Store unique URLs

        for username, author in external_username_mappings.items():
            self.external_username_mappings[username.lower()] = author

        self._local_cache_dir = os.path.join(os.path.dirname(__file__), "../../../../cache/telegram")        
        self._threads_dir = f"{self._local_cache_dir}/threads"
        
    def set_chat_title(self, title: str) -> None:
        """Set the title for a specific chat"""
        if title:
            self.chat_title = title.strip()
        
    def _extract_urls(self, text: str) -> List[str]:
        """Extract URLs from text using regex"""
        # URL pattern that matches http(s), ftp, file URLs
        url_pattern = r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[^\s]*)?'
        return [url.rstrip(')') for url in re.findall(url_pattern, text)]
    
    def _get_message_text(self, message: Dict) -> str:
        """Get message text from a message dictionary"""
        if not message:
            return ""
        
        text = ""
            
        # Check for service messages with topic creation
        if message.get("_") == "MessageService":
            action = message.get("action", {})
            if isinstance(action, dict) and action.get("_") == "MessageActionTopicCreate":
                return action.get("title", "").strip()
                    
        # Get the main message text
        text = message.get("message", "").strip()
                    
        # Check for media with webpage info
        media = message.get("media", None)
        if media and isinstance(media, dict):
            webpage = media.get("webpage", {})
            if isinstance(webpage, dict):
                # Get title and description from webpage
                title = (webpage.get("title") or "").strip()
                description = (webpage.get("description") or "").strip()
                url = (webpage.get("url") or "").strip()
        
                alt = ""
                if title and description:
                    alt = f"{title}: {description}"
                elif title:
                    alt = title
                elif description:
                    alt = description

                if url and url in text:
                    text = text.replace(url, f"[{alt}]({url})")
                else:
                    text = f"{text}\n{alt}"

        # Check for transcription
        message_id = message.get('id')
        chat_id = None
        
        # Get chat ID from peer_id
        peer_id = message.get('peer_id', {})
        if isinstance(peer_id, dict):
            chat_id = str(peer_id.get('channel_id')) if peer_id.get('_') == 'PeerChannel' else None
            
        if chat_id and message_id:
            # Construct transcription file path
            script_dir = os.path.dirname(os.path.realpath(__file__))
            for media_type in ['audio', 'video']:
                transcription_path = os.path.join(
                    script_dir,
                    'output',
                    chat_id,
                    'transcriptions',
                    f'{media_type}-{chat_id}-{message_id}_transcription.txt'
                )
            
                if os.path.exists(transcription_path):
                    with open(transcription_path, 'r', encoding='utf-8') as f:
                        transcript = f.read().strip()
                        if transcript:
                            text = f"{text}\n{transcript}"
                            
        return text.strip()
    
    def _get_message_topic_id(self, message: Dict) -> str:
        """Get message topic ID from a message dictionary"""
        if not message:
            return None
        
        reply_to = message.get('reply_to', {})
        if reply_to and reply_to.get('forum_topic', False):  
            top_id = reply_to.get('reply_to_top_id', None)
            msg_id = reply_to.get('reply_to_msg_id', None)
            if top_id:
                return top_id
            return msg_id

        else:
            return None
    
    def _get_message_url(self, message: Dict) -> str:
        """Get message URL from a message dictionary"""
        if not message:
            return ""
        
        peer_id = message.get('peer_id', {})
        if not peer_id:
            return ""
            
        if peer_id.get('_') == 'PeerChannel':
            channel_id = str(peer_id.get('channel_id'))
        elif peer_id.get('_') == 'PeerChat':
            channel_id = str(peer_id.get('chat_id'))
        else:
            channel_id = None
            
        if not channel_id:
            return ""
        
        # Determine if private channel (no username)
        prefix = f"https://t.me/c/" if channel_id.isnumeric() else f"https://t.me/"
            
        # Get message ID and reply_to_top_id
        msg_id = message.get('id')
        top_id = self._get_message_topic_id(message)
        
        # Construct URL
        if top_id:
            return f"{prefix}{channel_id}/{top_id}/{msg_id}"
        return f"{prefix}{channel_id}/{msg_id}"
        
    def _get_message_reply_to(self, message: Dict) -> str:
        """Get message reply_to from a message dictionary"""
        if not message:
            return None
        
        reply_to = message.get('reply_to', {})
        if not reply_to:
            return None
        
        if reply_to.get('forum_topic', False) and not reply_to.get('reply_to_top_id', False):
            # This is a reply to a forum topic
            return None

        reply_to_msg_id = reply_to.get('reply_to_msg_id')
        if not reply_to_msg_id:
            return None        
        
        return reply_to_msg_id
    
    def _get_chat_id(self, message: Dict) -> str:
        """Get chat ID from a message dictionary"""
        if not message:
            return None
        
        peer_id = message.get('peer_id', {})
        return str(peer_id.get('channel_id')) if peer_id.get('_') == 'PeerChannel' else None
    
    def _get_message_author(self, message: Dict) -> Dict[str, Any]:
        """Get message author information from a message dictionary
        
        Returns:
            Dict with author id, name and username
        """
        # Get author ID
        author_id = None
        author_name = None
        username = None
        if message.get('fwd_from'):
            # Get author from forwarded message if available
            fwd_from = message.get('fwd_from')
            if fwd_from.get('from_id') and fwd_from['from_id'].get('user_id'):
                author_id = fwd_from['from_id'].get('user_id')

        elif message.get('from_id'):
            # Get author ID
            from_id = message.get('from_id')
            if from_id.get('user_id'):
                author_id = from_id.get('user_id')
            elif from_id.get('channel_id'):
                author_id = from_id.get('channel_id')
                                    
        # Get data from sender
        if message.get('sender'):
            sender = message.get('sender')
            if not author_id and sender.get('id'):
                author_id = sender.get('id')

            if not author_name:
                name_parts = []
                if sender.get('first_name'):
                    name_parts.append(sender.get('first_name'))
                if sender.get('last_name'):
                    name_parts.append(sender.get('last_name'))
                
                if name_parts:
                    author_name = ' '.join(name_parts)
                elif sender.get('title'):
                    author_name = sender.get('title')
                
            if not username and sender.get('username'):
                username = sender.get('username')

        return {
            'id': author_id,
            'name': author_name, 
            'username': username
        }

    def _get_message_data(self, message: Dict) -> str:
        """Get message data from a message dictionary"""
        if not message:
            return None

        data = {
            'is_system_message': message.get("_") in ["MessageService"],
            'id': message.get('id'),
            'chat_id': self._get_chat_id(message),
            'topic_id': self._get_message_topic_id(message),
            'reply_to': self._get_message_reply_to(message),
            'url': self._get_message_url(message),
            'author': self._get_message_author(message),
            'text': self._get_message_text(message),
            'created_at': message.get('date'),
            'updated_at': message.get('edit_date'),
        }

        if (
            not data['id']       # Skip messages with no ID
            or not data['text']  # Skip messages with no text
        ):
            return None
        
        # Convert IDs to strings
        data['id'] = str(data['id'])

        if data['topic_id']:
            data['topic_id'] = str(data['topic_id'])

        if data['reply_to']:
            data['reply_to'] = str(data['reply_to'])

        return data
    
    def _get_author_name_tuple(self, author: Dict) -> Tuple[str, str, str]:
        """Get author name and markdown from a dictionary"""
        author_username = author.get('username')
        author_name = author.get('name') or author_username
        author_url = f"https://t.me/{author_username}" if author_username else ''

        if author_username:
            author_username_lower = author_username.lower()
            if author_username_lower in self.external_username_mappings:
                author_name = self.external_username_mappings[author_username_lower].get('name', '')
                author_url = self.external_username_mappings[author_username_lower].get('url', '')
        
        author_markdown = f"[{author_name}]({author_url})" if author_url else author_name
        return author_name, author_username, author_markdown

    def organize_messages(self, messages: List[Dict[str, Any]]) -> List[Dict]:
        """Organize messages into threads"""
        # Reset caches
        self.message_cache = {}
        self.reply_cache = {}
        self.username_mappings = copy.deepcopy(self.external_username_mappings)
        self.unique_urls = set()
        # Don't reset chat_title as they are set externally
        
        logger.info(f"Organizing {len(messages)} messages into threads...")

        # First pass: collect all usernames, names and URLs
        for message in messages:
            if not message:  # Skip None messages
                continue

            message_data = self._get_message_data(message)
            if not message_data:
                continue
            
            # Collect author information
            author = message_data.get('author') or {}
            _, author_username, _ = self._get_author_name_tuple(author)
            
            if author_username:
                author_username_lower = author_username.lower()
                if author_username_lower not in self.username_mappings:
                    self.username_mappings[author_username_lower] = author
            
            # Extract URLs from text
            text = message_data.get('text')
            urls = self._extract_urls(text)
            self.unique_urls.update(urls)
        
        logger.info(f"Found {len(self.username_mappings)} unique usernames")
        logger.info(f"Found {len(self.unique_urls)} unique URLs")
        
        # Second pass: cache messages and build reply structure
        reply_count = 0
        root_count = 0
        for message in messages:
            if not message:  # Skip None messages
                continue
                
            message_data = self._get_message_data(message)
            if not message_data:
                continue

            message_id = message_data.get('id')
                
            # Cache message for later use
            self.message_cache[message_id] = message_data
            
            # Update reply structure
            reply_to = message_data.get('reply_to')
            if reply_to:
                reply_count += 1

                # Add message to reply cache
                if reply_to not in self.reply_cache:
                    self.reply_cache[reply_to] = []

                self.reply_cache[reply_to].append(message_id)

            else:
                root_count += 1
        
        logger.info(f"Found {root_count} root messages and {reply_count} reply messages")
        
        # Third pass: build threads
        threads = []
        processed_ids = set()
        
        # Find all root messages with replies directly to them
        for message_id, message_data in self.message_cache.items():
            # Skip if already processed
            if message_id in processed_ids:
                continue

            # Skip system messages
            if message_data.get('is_system_message'):
                continue
                
            # Skip if this is a reply to another message
            if message_data.get('reply_to'):
                continue
                
            # Now find all messages that reply to this one
            replies = self._get_replies_tree(message_id, max_nesting=50)
            
            # Collect all message IDs that form this thread
            thread_message_ids = [message_id] + self._flatten_replies(replies)
            processed_ids.update(thread_message_ids)
            
            # Format the thread
            thread_data = {
                'id': message_id,
                'url': message_data.get('url', ''),
                'topic_id': message_data.get('topic_id'),
                'author': message_data.get('author', {}),
                'text': message_data.get('text', ''),
                'created_at': message_data.get('created_at', ''),
                'updated_at': message_data.get('updated_at', ''),
                'topic': self._get_topic_name(message_data.get('topic_id')),
                'replies': replies
            }
            
            threads.append(thread_data)
        
        # Sort threads by creation date
        threads.sort(key=lambda x: x.get('created_at', 0) or 0)
        
        logger.info(f"Created {len(threads)} threads")
        return threads

    def _flatten_replies(self, replies: List[Dict]) -> List[str]:
        """Flatten a replies tree to a list of message IDs"""
        if not replies:
            return []
            
        result = []
        for reply in replies:
            if not reply:
                continue
                
            result.append(reply['id'])
            if reply.get('replies'):
                result.extend(self._flatten_replies(reply['replies']))
                
        return result

    def _get_topic_name(self, topic_id: str) -> str:
        """Get topic name from topic ID"""
        if not topic_id:
            return ''
            
        # Look for a message with this ID that might be a topic
        if topic_id in self.message_cache:
            message = self.message_cache[topic_id]
            if message.get('is_system_message'):
                return message.get('text', '')
                
        return ''
            
    def _get_replies_tree(self, message_id: str, max_nesting: int = 50, level: int = 0) -> List[Dict]:
        """Get all replies to a message in a tree structure, recursively"""
        if level >= max_nesting:
            logger.warning(f"Warning: Max nesting level reached for message {message_id}")
            return []
            
        if str(message_id) not in self.reply_cache:
            return []
            
        replies = []
        for reply_id in self.reply_cache[str(message_id)]:
            if reply_id in self.message_cache:
                reply_data = self.message_cache[reply_id]
                
                # Recursively get all replies to this reply
                nested_replies = self._get_replies_tree(reply_id, max_nesting, level + 1)
                
                # Format the reply
                formatted_reply = {
                    'id': reply_id,
                    'url': reply_data.get('url', ''),
                    'author': reply_data.get('author', {}),
                    'text': reply_data.get('text', ''),
                    'created_at': reply_data.get('created_at', ''),
                    'updated_at': reply_data.get('updated_at', ''),
                    'level': level + 1,
                    'replies': nested_replies
                }
                
                replies.append(formatted_reply)
        
        # Sort replies by creation date
        replies.sort(key=lambda x: x.get('created_at', 0) or 0)
        return replies
    
    def generate_documents(self, threads: List[Dict], chat_info: Dict) -> List[Dict]:
        """Generate documents from threads"""
        if not threads:
            return []
            
        documents = []

        # Save threads
        thread_topic_hash = {}
        for thread in threads:
            topic_id = thread.get('topic_id') or 'default'
            if topic_id not in thread_topic_hash:
                thread_topic_hash[topic_id] = []
            thread_topic_hash[topic_id].append(thread)

            # Save thread to QuePasa format
            has_replies = len(thread.get('replies', [])) > 0
            has_links = 'http://' in thread['text'] or 'https://' in thread['text'] or 'www.' in thread['text']
            has_normal_text = len(thread['text']) > 64

            # Save thread if it has replies, links or normal text
            if has_replies or has_links or has_normal_text or chat_info.get('type').lower() == 'channel':
                quepasa_doc = self._get_document(thread)
                if not quepasa_doc:
                    continue

                documents.append(quepasa_doc)

        # Make groups of threads
        for topic_id, topic_threads in thread_topic_hash.items():
            topic_documents = []
            for thread in topic_threads:
                quepasa_doc = self._get_document(thread)
                if not quepasa_doc:
                    continue

                topic_documents.append(quepasa_doc)

            # Split documents into chunks
            topic_chunks = self._chunk_documents(topic_documents)
            
            # Process each chunk
            for chunk_idx, chunk in enumerate(topic_chunks, 1):
                final_text = []
                first_url = chunk[0]['url']  # First message URL
                last_created_at = chunk[-1]['created_at']  # Last message timestamp
                first_title = chunk[0]['title']
                
                # Get common keywords across all documents in the chunk
                groupped_keywords = self._get_groupped_keywords(chunk)
                groupped_entities = self._get_groupped_entities(chunk)
                
                # Build the text content
                raw_text = ""
                for doc in chunk:
                    final_text.append(f"{doc['url']}:\n{doc['created_at']}\n{doc['text']}\n")
                    raw_text += f"{doc['text']}\n"

                if not raw_text:
                    continue

                # Construct the topic URL
                url = threads[0].get('url')
                slash_count = 5 if '/c/' in url else 4
                topic_url = '/'.join(url.split('/')[0:slash_count])
                if topic_id != 'default':
                    topic_url += f"/{topic_id}"
                topic_url += "#groupped"
                if len(topic_chunks) > 1:
                    topic_url += f":part{chunk_idx}"
            
                documents.append({
                    'id': topic_url,
                    'url': first_url,  # Use first message URL
                    'title': first_title,
                    'text': '\n\n'.join(final_text),
                    'keywords': ', '.join(sorted(groupped_keywords)),
                    'created_at': last_created_at,  # Use last message timestamp
                    'entities': groupped_entities,
                })

        # Split text into chunks and track line numbers by position
        final_documents = []
        for doc in documents:
            raw_text = doc['text']

            raw_chunks = split_text(raw_text)
            chunks = []
            current_pos = 0
            for chunk in raw_chunks:
                # Find start and end positions of chunk in full text
                chunk_start_pos = current_pos
                chunk_end_pos = current_pos + len(chunk)
                
                # Count newlines before start and end to get line numbers
                start_line = raw_text[:chunk_start_pos].count('\n') + 1
                end_line = raw_text[:chunk_end_pos].count('\n') + 1
                
                chunks.append({
                    'text': chunk,
                    'position': f"lines {start_line}-{end_line}"
                })
                current_pos = chunk_end_pos + 1  # +1 to skip newline
            
            if chunks:
                doc['chunks'] = chunks
                del doc['text']

                final_documents.append(doc)
        
        return final_documents

    def _chunk_documents(self, docs: List[Dict], max_tokens: int = 15000) -> List[List[Dict]]:
        """Split documents into chunks based on token limit"""
        if not docs:
            return []
            
        # Initialize tokenizer
        try:
            enc = tiktoken.encoding_for_model("gpt-3.5-turbo")
        except:
            enc = tiktoken.get_encoding("cl100k_base")
        
        chunks = []
        current_chunk = []
        previous_chunk = []
        current_tokens = 0
        
        for doc in docs:
            # Check how many tokens this document has (length of text)
            tokens = len(enc.encode(doc['text']))
            
            # If it's a single document that's too large, split it (simplified approach)
            # Note that each thread is already stored in a separate file
            # So we don't need to make a group out of a single thread
            if tokens > max_tokens:
                # If a single document is too large, split it (simplified approach)
                if current_chunk and len(current_chunk) > 1: # Store only groups of threads with more than one thread
                    chunks.append(current_chunk)
                    
                # If the document is too large, we skip it, because it is already in a separate thread
                # chunks.append([doc])

                previous_chunk = []
                current_chunk = []
                current_tokens = 0

            elif current_tokens + tokens > max_tokens:
                # Add overlap by keeping last document from previous chunk
                # Only add overlap if there is at least 5 threads in the previous chunk
                if len(previous_chunk) >= 5:
                    current_chunk.insert(0, previous_chunk[-1]) # Add last thread from previous chunk to the current chunk
                    
                if current_chunk and len(current_chunk) > 1: # Store only groups of threads with more than one thread
                    chunks.append(current_chunk)
                    
                previous_chunk = current_chunk.copy() # Store the current chunk as the previous chunk
                current_chunk = []
                current_tokens = 0

            else:
                current_chunk.append(doc)
                current_tokens += tokens
        
        if current_chunk:
            chunks.append(current_chunk)
            
        return chunks

    def _get_groupped_keywords(self, docs: List[Dict]) -> Set[str]:
        """Get keywords that appear in all documents in the chunk"""
        if not docs:
            return set()
            
        # Get keywords sets for each document
        keywords = set()
        for doc in docs:
            keywords.update(doc['keywords'].split(', '))
        return keywords

    def _get_groupped_entities(self, docs: List[Dict]) -> Set[str]:
        """Get entities that appear in all documents in the chunk"""
        if not docs:
            return set()
            
        # Get keywords sets for each document
        entity_values = set()
        for doc in docs:
            for entity in doc['entities']:
                entity_values.add(entity['value'])
        return [{'label': 'user', 'value': value} for value in entity_values]

    def _get_document(self, thread: Dict) -> Dict:
        """Convert a thread to QuePasa document format"""
        if not thread:
            return None
            
        # Get the main message text and username
        text = thread.get('text').strip()

        author = thread.get('author') or {}
        author_name, _, author_markdown = self._get_author_name_tuple(author)
        
        # Collect usernames for keywords
        names = set()
        entity_values = set()
        if author_name:
            names.add(author_name)
            entity_values.add(author_markdown)
        
        # Format main message as chat
        raw_text = []
        formatted_text = []
        if text:
            text = re.sub(r'\n+', ' ', text).strip()
            raw_text.append(text)

            # Replace @mentions with names
            text_lower = text.lower()
            for mention_username, mention_author in self.username_mappings.items():
                if f"@{mention_username}" in text_lower:
                    mention_name, _, mention_markdown = self._get_author_name_tuple(mention_author)
                    text = re.sub(f"@{mention_username}", mention_markdown, text, flags=re.IGNORECASE)
                    names.add(mention_name)
                    entity_values.add(mention_markdown)
            
            formatted_text.append(f"{author_markdown}: {text}")
        
        # If there are replies, append them with usernames
        replies = thread.get('replies') or []
        for reply in replies:
            if not reply:
                continue
                
            reply_text = reply.get('text')
            if reply_text.strip():
                reply_level = reply.get('level', 0)
                reply_text = re.sub(r'\n+', ' ', reply_text).strip()
                raw_text.append(reply_text)

                reply_author = reply.get('author') or {}
                reply_name, _, reply_markdown = self._get_author_name_tuple(reply_author)

                # Add reply author to names
                if reply_name:
                    names.add(reply_name)
                    entity_values.add(reply_markdown)
                
                # Replace @mentions with names
                for mention_username, mention_author in self.username_mappings.items():
                    if f"@{mention_username}" in reply_text:
                        mention_name, _, mention_markdown = self._get_author_name_tuple(mention_author)
                        reply_text = re.sub(f"@{mention_username}", mention_markdown, reply_text, flags=re.IGNORECASE)
                        names.add(mention_name)
                        entity_values.add(mention_markdown)

                formatted_text.append((reply_level * '  ') + f"{reply_markdown}: {reply_text}")
        
        if not raw_text:
            return None

        # Join all messages with newlines
        final_text = '\n'.join(formatted_text)
        
        # Get chat title
        title = self.chat_title or 'Untitled Chat'
        if thread.get('topic'):
            title = f"{title}: {thread.get('topic')}"
        
        # Create the document
        document = {
            'id': thread.get('url', ''),
            'url': thread.get('url', ''),
            'title': title,
            'text': final_text,
            'keywords': ', '.join(sorted(names)),  # Add usernames as keywords,
            'created_at': thread.get('created_at', ''),
            'entities': [{'label': 'user', 'value': value} for value in entity_values],
        }            

        if thread.get('updated_at'):
            document['updated_at'] = thread.get('updated_at')
            
        return document
