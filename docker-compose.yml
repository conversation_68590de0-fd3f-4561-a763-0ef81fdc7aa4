version: '3.8'

services:
  # Redis - Message broker and caching
  redis:
    image: redis:7.2-alpine
    container_name: quepasa-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # MinIO - Object storage
  minio:
    image: minio/minio:latest
    container_name: quepasa-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
      MINIO_DOMAIN: minio
      MINIO_BUCKET_NAME: ${MINIO_BUCKET_NAME}
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped

  # MinIO Bucket Initialization Service
  minio-init:
    image: python:3.11-slim
    container_name: quepasa-minio-init
    working_dir: /app
    volumes:
      - ./:/app
    environment:
      - MINIO_HOST=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME}
    command: >
      bash -c "pip install -r dev/minio_bucket_init_requirements.txt &&
               python dev/minio_bucket_init.py"
    depends_on:
      minio:
        condition: service_healthy
    restart: "no"

  # Elasticsearch - Search engine
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.12.2
    container_name: quepasa-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - cluster.routing.allocation.disk.watermark.low=85%
      - cluster.routing.allocation.disk.watermark.high=90%
      - cluster.routing.allocation.disk.watermark.flood_stage=95%
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # Elasticsearch Initialization Service
  elasticsearch-init:
    image: python:3.11-slim
    container_name: quepasa-elasticsearch-init
    working_dir: /app
    volumes:
      - ./:/app
    environment:
      - ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST}
      - ELASTICSEARCH_PORT=${ELASTICSEARCH_PORT}
      - ELASTICSEARCH_PASSWORD=${ELASTICSEARCH_PASSWORD}
      - PYTHONPATH=/app
    command: >
      bash -c "apt-get update && apt-get install -y curl &&
               echo 'Waiting for Elasticsearch to be ready...' &&
               until curl -s http://elasticsearch:9200 > /dev/null; do sleep 1; done &&
               echo 'Elasticsearch is ready, creating index...' &&
               pip install -r requirements.txt &&
               pip install -r quepasa/elasticsearch_init/requirements.txt &&
               python configuration/main/cli_create_index.py search-document-v2-default &&
               echo 'Index creation completed successfully!'"
    depends_on:
      elasticsearch:
        condition: service_healthy
    restart: "no"

  # Kibana - Elasticsearch management
  # kibana:
  #   image: docker.elastic.co/kibana/kibana:8.12.2
  #  container_name: quepasa-kibana
  #  environment:
  #    - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
  #    - ELASTICSEARCH_USERNAME=elastic
  #    - ELASTICSEARCH_PASSWORD=${ELASTICSEARCH_PASSWORD}
  #  ports:
  #    - "5601:5601"
  #   depends_on:
  #    elasticsearch:
  #      condition: service_healthy
  #  healthcheck:
  #    test: ["CMD", "curl", "-f", "http://localhost:5601/api/status"]
  #    interval: 30s
  #    timeout: 10s
  #    retries: 3
  #  restart: unless-stopped

  # Celery - Task queue
  # celery:
  #  image: python:3.11-slim
  #  container_name: quepasa-celery
  #  working_dir: /app
  #  volumes:
  #    - ./:/app
  #  environment:
  #    - CELERY_BROKER_URL=${CELERY_BROKER_URL}
  #    - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
  #  command: >
  #    bash -c "pip install -r requirements.txt &&
  #             celery -A src.lib.celery_app worker --loglevel=info"
  #  depends_on:
  #    redis:
  #      condition: service_healthy
  #  restart: unless-stopped

  # Celery Flower - Task monitoring
  # celery-flower:
  #  image: python:3.11-slim
  #  container_name: quepasa-celery-flower
  #  working_dir: /app
  #  volumes:
  #    - ./:/app
  #  environment:
  #    - CELERY_BROKER_URL=${CELERY_BROKER_URL} 
  #    - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
  #  command: >
  #    bash -c "pip install -r requirements.txt &&
  #             pip install flower &&
  #             celery -A src.lib.celery_app flower --port=5555"
  #  ports:
  #    - "5555:5555"
  #  depends_on:
  #    redis:
  #      condition: service_healthy
  #  restart: unless-stopped

  # API Service
  api:
    image: python:3.11-slim
    container_name: quepasa-api
    working_dir: /app
    volumes:
      - ./:/app
    environment:
      - API_HOST=${API_HOST}
      - API_PORT=${API_PORT}
      - SECRET_KEY=${SECRET_KEY}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME}
      - ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST}
      - ELASTICSEARCH_PORT=${ELASTICSEARCH_PORT}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - NEBIUS_API_KEY=${NEBIUS_API_KEY}
      - REPLICATE_API_KEY=${REPLICATE_API_KEY}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY}
    command: >
      bash -c "pip install -r requirements.txt &&
               pip install -r quepasa/api/requirements.txt &&
               pip install -r quepasa/crawler/requirements.txt &&
               uvicorn quepasa.api.app:app --host ${API_HOST} --port ${API_PORT} --reload"
    ports:
      - "${API_PORT}:${API_PORT}"
    depends_on:
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      minio:
        condition: service_healthy
      minio-init:
        condition: service_completed_successfully
    restart: unless-stopped

  # Searcher Service
  searcher:
    image: python:3.11-slim
    container_name: quepasa-searcher
    working_dir: /app
    volumes:
      - ./:/app
    ports:
      - "8080:8080"
    environment:
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST}
      - ELASTICSEARCH_PORT=${ELASTICSEARCH_PORT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - NEBIUS_API_KEY=${NEBIUS_API_KEY}
      - REPLICATE_API_KEY=${REPLICATE_API_KEY}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY}
      - SPD_API_URL=${SPD_API_URL}
    command: >
      bash -c "pip install -r requirements.txt &&
               pip install -r quepasa/searcher/requirements.txt &&
               python -m quepasa.searcher.main"
    depends_on:
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      minio:
        condition: service_healthy
      minio-init:
        condition: service_completed_successfully
    restart: unless-stopped

  # Crawler Service
  crawler:
    image: python:3.11-slim
    container_name: quepasa-crawler
    working_dir: /app
    volumes:
      - ./:/app
    environment:
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - NEBIUS_API_KEY=${NEBIUS_API_KEY}
      - REPLICATE_API_KEY=${REPLICATE_API_KEY}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY}
    command: >
      bash -c "pip install -r requirements.txt &&
               pip install -r quepasa/crawler/requirements.txt &&
               {
                 celery -A quepasa.crawler.tasks worker -Q crawler -n crawler@%h -c 3 --loglevel=info &
                 celery -A quepasa.crawler.tasks worker -Q crawler-telegram -n crawler-telegram@%h -c 1 --loglevel=info &
                 celery -A quepasa.crawler.tasks worker -Q crawler-save -n crawler-save@%h -c 4 --loglevel=info &
                 wait
               }"
    depends_on:
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      minio-init:
        condition: service_completed_successfully
    restart: unless-stopped

  # Data Processor Service
  data-processor:
    image: python:3.11-slim
    container_name: quepasa-data-processor
    working_dir: /app
    volumes:
      - ./:/app
    environment:
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - NEBIUS_API_KEY=${NEBIUS_API_KEY}
      - REPLICATE_API_KEY=${REPLICATE_API_KEY}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY}
    command: >
      bash -c "pip install -r requirements.txt &&
               pip install -r quepasa/data_processor/requirements.txt &&
               {
                 celery -A quepasa.data_processor.tasks worker -Q data-processor -n data-processor@%h --loglevel=info &
                 wait
               }"
    depends_on:
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      minio-init:
        condition: service_completed_successfully
    restart: unless-stopped

  # Indexer Service
  indexer:
    image: python:3.11-slim
    container_name: quepasa-indexer
    working_dir: /app
    volumes:
      - ./:/app
    environment:
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST}
      - ELASTICSEARCH_PORT=${ELASTICSEARCH_PORT}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - NEBIUS_API_KEY=${NEBIUS_API_KEY}
      - REPLICATE_API_KEY=${REPLICATE_API_KEY}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY}
    command: >
      bash -c "pip install -r requirements.txt &&
               pip install -r quepasa/indexer/requirements.txt &&
               {
                 celery -A quepasa.indexer.tasks worker -Q indexer -n indexer@%h -c 3 --loglevel=info &
                 celery -A quepasa.indexer.tasks worker -Q indexer-save -n indexer-save@%h -c 1 --loglevel=info &
                 wait
               }"
    depends_on:
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      minio:
        condition: service_healthy
      minio-init:
        condition: service_completed_successfully
      elasticsearch-init:
        condition: service_completed_successfully
    restart: unless-stopped

  # Embedding Service
  embedding:
    image: python:3.11-slim
    container_name: quepasa-embedding
    working_dir: /app
    volumes:
      - ./:/app
    ports:
      - "8001:8080"
    environment:
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - NEBIUS_API_KEY=${NEBIUS_API_KEY}
      - REPLICATE_API_KEY=${REPLICATE_API_KEY}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY}
    command: >
      bash -c "pip install -r requirements.txt &&
               pip install -r quepasa/embedding/requirements.txt &&
               python -m quepasa.embedding.app"
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  default:
    name: quepasa-network 
