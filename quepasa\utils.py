import re
from typing import List
from urllib.parse import urljoin
from langchain.text_splitter import RecursiveCharacterTextSplitter

from .config import VS_CHUNK_SIZE, VS_CHUNK_OVERLAP, MAX_FORMATED_CHUNK_SIZE

# Initialize text splitter
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=VS_CHUNK_SIZE,
    chunk_overlap=VS_CHUNK_OVERLAP,
)

def get_title_by_filename(filename: str) -> str:
    """Convert filename to readable title by removing extension and replacing separators with spaces"""
    title = '.'.join(filename.split('.')[0:-1])  # Remove extension
    return re.sub('[_\.]+', ' ', title)

def split_text(text: str) -> List[str]:
    """Split text using langchain's RecursiveCharacterTextSplitter."""
    return text_splitter.split_text(text)

def split_paragraphs(text: str, max_chunk_size: int = MAX_FORMATED_CHUNK_SIZE) -> List[str]:
    """Split text into paragraphs, preserving headers."""
    if len(text) > max_chunk_size:
        text_chunks = split_text(text)
        if len(text_chunks) > 1:
            header = ""
            if text_chunks[0].startswith('#'):
                header = text_chunks[0].split('\n')[0] + "\n\n"

            new_text_chunks = [text_chunks[0]]
            for text_chunk in text_chunks[1:]:
                new_text_chunks.append(header + text_chunk)
            return new_text_chunks

        return text_chunks
    return [text]

def convert_relative_urls_to_absolute(text: str, base_url: str) -> str:
    """Convert relative URLs in markdown to absolute URLs"""
    def parse_link(match):
        text = match.group(1)
        url = match.group(2)
        if (
            url.startswith("http://")
            or url.startswith("https://")
            or url.startswith("mailto:")
            or url.startswith("tel:")
        ):
            return f"[{text}]({url})"

        absolute_url = urljoin(base_url, url)
        return f"[{text}]({absolute_url})"

    link_pattern = re.compile(r'\[(.*?)\]\((.*?)\)')
    return link_pattern.sub(parse_link, text) 