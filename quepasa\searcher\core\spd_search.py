import os
from numbers import Number
from typing import Dict, Any, List, Optional

import requests

from configuration.main.default import QuepasaConfigurationHub
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>og<PERSON>
from ..models.request import QuepasaRequest
from ..models.spd_result import SPDSearchResult

logger = QuepasaLogger().get_instance(__name__)

_URL_ENV = "SPD_API_URL"


class SPDSearchManager:

    def __init__(self):
        """Initialize search engine"""
        self.base_url = os.getenv(_URL_ENV)
        if not self.base_url:
            raise EnvironmentError(f"{_URL_ENV} is not set")

    def search(self, request: QuepasaRequest) -> List[SPDSearchResult]:
        try:
            client = request.client
            if not client:
                raise ValueError("Client is not specified")

            logger.info(f"Starting search with request: {request}", extra={"client": client})

            collection = getattr(request, "collection", None)
            if not collection:
                raise ValueError(f"SPD collection is not configured for [{client}]")

            area = getattr(request, "area", None)
            if not area:
                raise ValueError(f"SPD area is not configured for [{client}]")

            config = QuepasaConfigurationHub.from_request(request)
            client_key = config.get_spd_client_code()
            if not client_key:
                raise ValueError(f"SPD client-key is not configured for [{client}]")

            filtering_attributes = config.get_spd_filtering_attributes()

            json_records = self._search(request,
                                        _build_filter_string(filtering_attributes),
                                        client,
                                        client_key,
                                        collection,
                                        area)

            logger.debug(f"Found {len(json_records)} records for query: [{request.question}]", extra={"client": client})

            search_results = []
            for rec in json_records:
                search_results.append(SPDSearchResult(
                    sku=rec.get("id") or rec.get("sku"),
                    title=rec.get("title"),
                    url=rec.get("url"),
                    metadata=rec
                ))
            return search_results
        except Exception as e:
            logger.error(f"Error searching SPD: {str(e)}", exc_info=True)
            raise Exception(f"Error searching SPD: {str(e)}")

    def _search(self,
                request: QuepasaRequest,
                pre_filter: str,
                client: str,
                client_key: str,
                collection: str,
                area: str) -> List[Dict[str, Any]]:
        """Perform search request to SPD API and return list of normalized json records"""
        query = request.question

        if (request.classification
                and hasattr(request.classification, 'query')
                and request.classification.query
                and request.classification.query.strip()):
            query = request.classification.query.strip()

        payload = {
            "query": query,
            "collection": collection,
            "area": area,
            "fields": ["*"],
            "refinements": [],
            "pageSize": 10,
            "skip": 0
        }
        if hasattr(request, "user_info") and request.user_info:
            if hasattr(request.user_info, "visitor_id") and request.user_info.visitor_id:
                payload["visitorId"] = request.user_info.visitor_id
            if hasattr(request.user_info, "session_id") and request.user_info.session_id:
                payload["sessionId"] = request.user_info.session_id
        if pre_filter:
            payload["preFilter"] = pre_filter
        if request.limit:
            payload["pageSize"] = request.limit

        headers = {
            'Authorization': f'client-key {client_key}',
            'Content-Type': 'application/json',
            'X-Groupby-Customer-Id': client
        }

        response = requests.post(self.base_url, headers=headers, json=payload, timeout=10)
        response.raise_for_status()
        response_json = response.json()

        return [
            rec
            for r in response_json.get("records", [])
            if (rec := _normalize_json_record(r)) is not None
        ]


def _normalize_json_record(record: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Normalize SPD JSON record to ensure consistent structure"""
    if not isinstance(record, dict):
        return None

    all_meta = record.get("allMeta")
    if not isinstance(all_meta, dict):
        return None

    all_meta["url"] = record.get("_u")

    labels = record.get("labels")
    all_meta["labels"] = labels if isinstance(labels, list) else []

    return all_meta


def _build_filter_string(filters: Optional[Dict[str, List[Any]]]) -> Optional[str]:
    """
    Convert a dict of filters into a SPD preFilter string.

    Args:
        filters (Optional[dict[str, list[Any]]]): A dictionary where each key is a attribute name and the value is a list of values.
            Values can be strings or numbers.
    Returns:
        Optional[str]: A string representing the filters in SPD preFilter format, or None if no valid filters are provided.
    Example:
        {"color": ["red", "blue"], "size": [10, 12]} ->
        'color:ANY("red","blue") AND size:ANY(10,12)'
    Raises:
        None.
    """
    if not filters:
        return None

    parts: List[str] = []

    for key, values in filters.items():
        if not key or not values:
            continue

        cleaned = [
            v for v in values
            if v is not None and (not isinstance(v, str) or v.strip() != "")
        ]
        if not cleaned:
            continue

        value_strs: List[str] = []
        for v in cleaned:
            if isinstance(v, Number) and not isinstance(v, bool):
                value_strs.append(str(v))
            else:
                value_strs.append(f'"{v}"')

        parts.append(f'{key}:ANY({",".join(value_strs)})')

    return " AND ".join(parts) if parts else None
