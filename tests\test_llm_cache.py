import json
import os
import pytest
from unittest.mock import Mock, patch, mock_open

from src.lib.llm.cache import LLMCacheMixin

class TestLLM(LLMCacheMixin):
    def __init__(self):
        super().__init__()
        self._test_response = "Test response"

    def get_answer(self, model, prompts, size, json_mode=False):
        return self._test_response

    async def get_streaming_answer(self, model, prompts, size):
        yield self._test_response

@pytest.fixture
def llm():
    """Fixture that provides a test LLM instance."""
    return TestLLM()

def test_get_cache_paths(llm):
    """Test cache path generation."""
    model = "test-model"
    prompts = [{"role": "user", "content": "test"}]
    size = 100
    
    local_path, remote_path = llm._get_cache_paths(model, prompts, size)
    assert local_path.endswith(".txt")
    assert remote_path.endswith(".txt")

def test_save_and_load_local_cache(llm):
    """Test saving and loading from local cache."""
    response = "Test response"
    cache_data = response
    cache_file = "../cache/llm/test.txt"
    os.makedirs(os.path.dirname(cache_file), exist_ok=True)
    
    with patch("builtins.open", mock_open()) as mock_file, \
         patch("src.lib.files.QuepasaFiles.upload_file") as mock_upload:
        llm._save_llm_answer(cache_data, str(cache_file), str(cache_file))
        mock_file.assert_called_once_with(str(cache_file), 'w')
        handle = mock_file()
        handle.write.assert_called_once_with("Test response")
        mock_upload.assert_called_once_with(str(cache_file), str(cache_file))

def test_get_cached_answer_from_local(llm):
    """Test getting a cached answer from local cache."""
    cached = "Cached response"
    
    with patch.object(llm, "_load_local_cache", return_value=cached), \
         patch.object(llm, "get_answer", return_value="New response"):
        response = llm.get_cached_answer("test", [], 100)
        assert response == cached

def test_get_cached_answer_from_remote(llm):
    """Test getting a cached answer from remote cache."""
    cached = "Remote cached response"
    
    with patch.object(llm, "_load_local_cache", return_value=None), \
         patch.object(llm, "_load_remote_cache", return_value=cached), \
         patch.object(llm, "get_answer", return_value="New response"):
        response = llm.get_cached_answer("test", [], 100)
        assert response == cached

def test_cache_error_handling(llm):
    """Test error handling in cache operations."""
    with patch("src.lib.files.QuepasaFiles.exists", return_value=True), \
         patch("src.lib.files.QuepasaFiles.download_file", side_effect=Exception("Remote error")):
        response = llm.get_answer("test", [], 100)
        assert response == "Test response" 