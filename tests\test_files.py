import pytest
import os
import json
import boto3
import hashlib
import zlib
from unittest.mock import patch, MagicMock

from src.lib.files import QuepasaFiles
from datetime import datetime

@pytest.fixture
def test_files_dir(tmp_path):
    """Create a temporary directory for test files."""
    return str(tmp_path)

@pytest.fixture(scope="session")
def storage():
    """Create a test QuepasaFiles instance."""
    # Create the bucket if it doesn't exist
    s3_client = boto3.client(
        's3',
        endpoint_url='http://localhost:9000',
        aws_access_key_id='minioadmin',
        aws_secret_access_key='minioadmin',
        region_name='us-east-1',
        verify=False
    )
    
    bucket_name = 'quepasa-files'
    try:
        s3_client.head_bucket(Bucket=bucket_name)
    except:
        s3_client.create_bucket(Bucket=bucket_name)
    
    # Create QuepasaFiles instance
    return QuepasaFiles(
        bucket_name=bucket_name,
        endpoint_url='http://localhost:9000',
        aws_access_key_id='minioadmin',
        aws_secret_access_key='minioadmin',
        debug_flag=True
    )

def test_create_local_dirs_for_path(storage, test_files_dir):
    """Test creating local directories."""
    test_path = os.path.join(test_files_dir, 'test/path/file.txt')
    storage.create_local_dirs_for_path(test_path)
    assert os.path.isdir(os.path.join(test_files_dir, 'test/path'))

def test_set_and_get_data(storage):
    """Test setting and getting binary data."""
    test_data = b'test data'
    storage.set_data('test.bin', test_data)
    assert storage.get_data('test.bin') == test_data

def test_set_and_get_text(storage):
    """Test setting and getting text data."""
    test_text = 'test text'
    storage.set_text('test.txt', test_text)
    assert storage.get_text('test.txt') == test_text

def test_set_and_get_json(storage):
    """Test setting and getting JSON data."""
    test_data = {'key': 'value'}
    storage.set_json('test.json', test_data)
    assert storage.get_json('test.json') == test_data

def test_set_and_get_json_zlib(storage):
    """Test setting and getting compressed JSON data."""
    test_data = {'key': 'value'}
    storage.set_json_zlib('test.json.zlib', test_data)
    assert storage.get_json_zlib('test.json.zlib') == test_data

def test_file_operations_with_properties(storage):
    """Test file operations with properties."""
    test_data = b'test data'
    test_file = 'test/path/test.bin'
    
    storage.set_data(test_file, test_data)
    assert storage.exists(test_file)
    
    props = storage.get_properties(test_file)
    assert props['content_length'] == len(test_data)
    
    storage.delete_file(test_file)
    assert not storage.exists(test_file)

def test_file_listing(storage):
    """Test file listing methods."""
    storage.debug_flag = True  # Enable debug output
    
    # Clean up any existing test files
    existing_files = storage.get_files('test/', recursive=True)
    for file in existing_files:
        storage.delete_file(file)
    
    test_files = [
        'test/a.txt',
        'test/b.txt',
        'test/sub/c.txt'
    ]
    
    for file in test_files:
        storage.set_text(file, 'content')
    
    # Test non-recursive listing
    files = storage.get_files('test/', recursive=False)
    assert len(files) == 2  # Should only get a.txt and b.txt
    
    # Test recursive listing
    files = storage.get_files('test/', recursive=True)
    assert len(files) == 3  # Should get all files
    
    # Clean up
    for file in test_files:
        storage.delete_file(file)

def test_upload_and_download_file(storage, test_files_dir):
    """Test uploading and downloading files."""
    # Create a test file
    local_file = os.path.join(test_files_dir, 'local.txt')
    with open(local_file, 'w') as f:
        f.write('test content')
    
    # Upload it
    storage.upload_file(local_file, 'remote.txt')
    assert storage.exists('remote.txt')
    
    # Download it to a new location
    download_file = os.path.join(test_files_dir, 'downloaded.txt')
    storage.download_file('remote.txt', download_file)
    
    with open(download_file, 'r') as f:
        assert f.read() == 'test content'

@pytest.mark.asyncio
async def test_upload_stream(storage):
    """Test uploading from a stream."""
    from io import BytesIO
    stream = BytesIO(b'test stream data')
    await storage.upload_stream(stream, 'stream.txt')
    assert storage.get_text('stream.txt') == 'test stream data'

def test_folder_metadata(storage):
    """Test folder metadata and updated_at timestamp."""
    storage.debug_flag = True
    
    # Create a nested directory structure
    test_files = [
        'metadata_test/a.txt',
        'metadata_test/sub1/b.txt',
        'metadata_test/sub1/sub2/c.txt'
    ]
    
    # Create files and check directory metadata
    for file in test_files:
        storage.set_text(file, 'content')
        
        # Check metadata for each parent directory
        dir_path = os.path.dirname(file)
        while dir_path:
            # Add trailing slash for directory marker
            dir_marker = dir_path + '/'
            
            # Verify directory exists
            assert storage.exists(dir_marker), f"Directory {dir_marker} should exist"
            
            # Get directory properties
            props = storage.get_properties(dir_marker)
            metadata = props.get('metadata', {})
            
            # Verify updated_at exists and is in ISO format
            assert 'updated_at' in metadata, f"Directory {dir_marker} should have updated_at metadata"
            updated_at = metadata['updated_at']
            try:
                datetime.fromisoformat(updated_at)
            except ValueError:
                pytest.fail(f"Invalid updated_at format in {dir_marker}: {updated_at}")
            
            # Verify last_modified exists
            assert props['last_modified'] is not None, f"Directory {dir_marker} should have last_modified"
            
            # Move up to parent directory
            dir_path = os.path.dirname(dir_path.rstrip('/'))
    
    # Verify directory structure
    files = storage.get_files('metadata_test/', recursive=True)
    assert len(files) == 3, "Should find all three files"
    assert all(f.endswith('.txt') for f in files), "All files should be .txt files"
    
    # Clean up
    for file in files:
        storage.delete_file(file)

def test_set_text_with_md5(storage):
    """Test setting text content with MD5 hash."""
    test_content = "Test content for MD5"
    content_md5 = hashlib.md5(test_content.encode('utf-8')).digest()
    test_path = "test/md5/test_text.txt"
    
    # Set content with MD5
    storage.set_text(test_path, test_content, content_md5)
    
    # Verify content and MD5
    properties = storage.get_properties(test_path)
    assert properties['content_md5'] == content_md5
    assert storage.get_text(test_path) == test_content
    
    # Clean up
    storage.delete_file(test_path)

def test_set_json_with_md5(storage):
    """Test setting JSON content with MD5 hash."""
    test_data = {"test": "data", "number": 123}
    json_str = json.dumps(test_data)
    content_md5 = hashlib.md5(json_str.encode('utf-8')).digest()
    test_path = "test/md5/test_json.json"
    
    # Set content with MD5
    storage.set_json(test_path, test_data, content_md5)
    
    # Verify content and MD5
    properties = storage.get_properties(test_path)
    assert properties['content_md5'] == content_md5
    assert storage.get_json(test_path) == test_data
    
    # Clean up
    storage.delete_file(test_path)

def test_set_json_zlib_with_md5(storage):
    """Test setting compressed JSON content with MD5 hash."""
    test_data = {"test": "data", "number": 123}
    json_str = json.dumps(test_data, ensure_ascii=False)
    compressed = zlib.compress(json_str.encode('utf-8'))
    content_md5 = hashlib.md5(compressed).digest()
    test_path = "test/md5/test_json.zlib.json"
    
    # Set content with MD5
    storage.set_json_zlib(test_path, test_data, content_md5)
    
    # Verify content and MD5
    properties = storage.get_properties(test_path)
    assert properties['content_md5'] == content_md5
    assert storage.get_json_zlib(test_path) == test_data
    
    # Clean up
    storage.delete_file(test_path)