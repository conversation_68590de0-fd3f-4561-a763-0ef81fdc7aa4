import json
import os
import pathlib
import sys
from unittest.mock import Mock, patch

import pytest

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from quepasa.searcher.core.spd_search import SPDSearchManager
from quepasa.searcher.models.request import QuepasaRequest, UserInfo

CUSTOMER_ID = "test-customer"


@pytest.fixture
def spd_env(monkeypatch):
    monkeypatch.setenv("SPD_API_URL", "https://search.example.groupbycloud.com/api/search")
    return "https://search.example.groupbycloud.com/api/search"


@pytest.fixture
def mock_config(mocker):
    """Mock config for SPD search tests"""
    mock = mocker.Mock()
    mock.get_spd_client_code.return_value = "xxx"
    mock.get_spd_filtering_attributes.return_value = {
        "colorFamilies": ["red", "crimson", "scarlet", "blue", "azure", "cyan", "brown", "yellow"],
        "size": ["S", "M", "L", "XL"]
    }

    def fake_unify_attribute(arg):
        if arg == "colorFamilies":
            return ["red", "crimson", "blue", "azure", "cyan"]
        elif arg == "size":
            return ["M", "L"]
        return []

    mock.unify_attribute = fake_unify_attribute

    return mock


@pytest.fixture
def spd_manager(spd_env, mock_config, mocker):
    """Create a SPD search manager with mocked config"""
    mocker.patch('configuration.main.default.QuepasaConfigurationHub.from_request', return_value=mock_config)
    return SPDSearchManager()


def request(query: str = None, **kwargs):
    req = QuepasaRequest(question="test query",
                         client=CUSTOMER_ID,
                         limit=10,
                         collection='productsEnriched',
                         area='Production',
                         user_info=UserInfo(id="John Doe", visitor_id='visitor-123', session_id='session-123'))
    req.classification = {"intent": "shopping", "attributes": {"colorFamilies": ["red-ish", "blue-ish"], "size": ["M/L"]}}
    if query:
        req.classification["query"] = query
    for k, v in kwargs.items():
        setattr(req, k, v)
    return req


@patch.dict(os.environ, {'SPD_API_URL': 'https://search.brah.groupbycloud.com/api/search'})
def test_init_with_env():
    assert SPDSearchManager().base_url == "https://search.brah.groupbycloud.com/api/search"


def test_search_missing_client(spd_manager):
    with pytest.raises(Exception) as context:
        spd_manager.search(QuepasaRequest(question="test"))
    assert "Client is not specified" in str(context.value)


def test_search_missing_client_key(spd_env, mocker):
    config = mocker.Mock()
    config.get_spd_client_code.return_value = ""
    mocker.patch('configuration.main.default.QuepasaConfigurationHub.from_request', return_value=config)

    with pytest.raises(Exception) as context:
        SPDSearchManager().search(request())
    assert f"SPD client-key is not configured for [{CUSTOMER_ID}]" in str(context.value)


@patch('requests.post')
def test_search_success(mock_post, mock_config, spd_manager):
    fixture_response = pathlib.Path(__file__).parent / "data" / "spd_response.json"
    mock_response = Mock()
    mock_response.json.return_value = json.loads(fixture_response.read_text(encoding="utf-8"))
    mock_response.raise_for_status.return_value = None
    mock_post.return_value = mock_response

    results = spd_manager.search(request())

    assert len(results) == 2
    assert results[0].sku == '1104787'
    assert results[0].title == 'Slim Fit Jeans Mediumstone L34'
    assert results[0].url == 'http://shoeby1productsEnriched.com/1104787'
    assert results[0].metadata['id'] == '1104787'
    assert results[0].metadata['primaryProductId'] == '1104787'
    assert results[0].metadata['title'] == 'Slim Fit Jeans Mediumstone L34'
    assert results[0].metadata['labels'] == ["BOOSTED", "PINNED"]
    assert len(results[0].metadata['variants']) == 2
    assert results[0].metadata['variants'][0]['id'] == '1104787-100-28'
    assert results[0].metadata['variants'][0]['primaryProductId'] == '1104787'
    assert results[0].metadata['variants'][0]['type'] == 'VARIANT'
    assert results[0].metadata['variants'][1]['id'] == '1104787-100-29'
    assert results[0].metadata['variants'][1]['primaryProductId'] == '1104787'
    assert results[0].metadata['variants'][1]['type'] == 'VARIANT'

    assert results[1].sku == '1104833'
    assert results[1].title == 'Slim Fit Jeans Mediumstone L30'
    assert results[1].url == 'http://shoeby1productsEnriched.com/1104833'
    assert results[1].metadata['id'] == '1104833'
    assert results[1].metadata['primaryProductId'] == '1104833'
    assert results[1].metadata['title'] == 'Slim Fit Jeans Mediumstone L30'
    assert results[1].metadata['labels'] == ["BURIED"]
    assert len(results[1].metadata['variants']) == 2
    assert results[1].metadata['variants'][0]['id'] == '1104833-100-28'
    assert results[1].metadata['variants'][0]['primaryProductId'] == '1104833'
    assert results[1].metadata['variants'][0]['type'] == 'VARIANT'
    assert results[1].metadata['variants'][1]['id'] == '1104833-100-29'
    assert results[1].metadata['variants'][1]['primaryProductId'] == '1104833'
    assert results[1].metadata['variants'][1]['type'] == 'VARIANT'

    mock_post.assert_called_once()
    call_args = mock_post.call_args
    assert call_args[0][0] == "https://search.example.groupbycloud.com/api/search"

    headers = call_args[1]['headers']
    assert headers['Authorization'] == f"client-key {mock_config.get_spd_client_code()}"
    assert headers['Content-Type'] == 'application/json'
    assert headers['X-Groupby-Customer-Id'] == CUSTOMER_ID

    payload = call_args[1]['json']
    assert payload['query'] == 'test query'
    assert payload['area'] == 'Production'
    assert payload['collection'] == 'productsEnriched'
    assert payload['preFilter'] == 'colorFamilies:ANY("red","crimson","blue","azure","cyan") AND size:ANY("M","L")'
    assert payload['pageSize'] == 10
    assert payload['visitorId'] == "visitor-123"
    assert payload['sessionId'] == "session-123"


@patch('requests.post')
def test_search_with_limit(mock_post, spd_manager, mock_config):
    mock_response = Mock()
    mock_response.json.return_value = {'records': []}
    mock_response.raise_for_status.return_value = None
    mock_post.return_value = mock_response

    spd_manager.search(request(limit=5))

    call_args = mock_post.call_args
    payload = call_args[1]['json']
    assert payload['pageSize'] == 5


@patch('requests.post')
def test_search_empty_records(mock_post, spd_manager):
    mock_response = Mock()
    mock_response.json.return_value = {'records': []}
    mock_response.raise_for_status.return_value = None
    mock_post.return_value = mock_response

    results = spd_manager.search(request())

    assert results == []


@patch('requests.post')
def test_search_http_error(mock_post, spd_manager):
    mock_response = Mock()
    mock_response.raise_for_status.side_effect = Exception("HTTP Error")
    mock_post.return_value = mock_response

    with pytest.raises(Exception) as context:
        spd_manager.search(request())
    assert "Error searching SPD" in str(context.value)


@patch('requests.post')
def test_search_request_exception(mock_post, spd_manager):
    mock_post.side_effect = Exception("Network Error")

    with pytest.raises(Exception) as context:
        spd_manager.search(request())
    assert "Error searching SPD" in str(context.value)


def test_search_payload_structure(spd_manager):
    with patch('requests.post') as mock_post:
        mock_response = Mock()
        mock_response.json.return_value = {'records': []}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        spd_manager.search(request(question="test jeans", limit=15))

        call_args = mock_post.call_args
        payload = call_args[1]['json']

        expected_payload = {
            "query": "test jeans",
            "area": "Production",
            "collection": "productsEnriched",
            "visitorId": "visitor-123",
            "sessionId": "session-123",
            "preFilter": 'colorFamilies:ANY("red","crimson","blue","azure","cyan") AND size:ANY("M","L")',
            "fields": ["*"],
            "refinements": [],
            "pageSize": 15,
            "skip": 0
        }

        assert payload == expected_payload


def test_search_extracted_query(spd_manager):
    with patch('requests.post') as mock_post:
        mock_response = Mock()
        mock_response.json.return_value = {'records': []}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response

        spd_manager.search(request(question="test jeans", limit=15, query="jeans"))

        call_args = mock_post.call_args
        payload = call_args[1]['json']

        expected_payload = {
            "query": "jeans",
            "area": "Production",
            "collection": "productsEnriched",
            "visitorId": "visitor-123",
            "sessionId": "session-123",
            "preFilter": 'colorFamilies:ANY("red","crimson","blue","azure","cyan") AND size:ANY("M","L")',
            "fields": ["*"],
            "refinements": [],
            "pageSize": 15,
            "skip": 0
        }

        assert payload == expected_payload
