import os
import sys
import json
import logging
import threading
from datetime import datetime

# Configure logging for common modules to reduce noise
QUIET_MODULES = [
    'urllib',
    'asyncio',
    'requests',
    'httpcore',
    'threading',
    'subprocess',
    'elasticsearch',
    'sentence_transformers',
]

for module in QUIET_MODULES:
    logging.getLogger(module).setLevel(logging.WARNING)
    logging.getLogger(module).propagate = False

class AnsiColor:
    RESET = "\033[0m"
    CYAN = "\033[36m"
    GREEN = "\033[32m"
    MAGENTA = "\033[35m"
    RED = "\033[31m"
    YELLOW = "\033[33m"

class PlainTextFormatter(logging.Formatter):
    """ANSI-colored log formatter, logback style:
    %cyan(%d{HH:mm:ss.SSS}) %green([%thread]) %highlight(%-5level) %magenta(%logger{36}) - %msg
    """

    LEVEL_COLORS = {
        "DEBUG": AnsiColor.CYAN,
        "INFO": AnsiColor.GREEN,
        "WARNING": AnsiColor.YELLOW,
        "ERROR": AnsiColor.RED,
        "CRITICAL": AnsiColor.RED,
    }

    def format(self, record: logging.LogRecord) -> str:
        ts = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        thread_name = threading.current_thread().name
        logger_name = record.name
        level = record.levelname
        message = record.getMessage()

        ts_colored = f"{AnsiColor.CYAN}{ts}{AnsiColor.RESET}"
        thread_colored = f"{AnsiColor.GREEN}[{thread_name}]{AnsiColor.RESET}"
        level_colored = f"{self.LEVEL_COLORS.get(level, '')}{level:<5}{AnsiColor.RESET}"
        logger_colored = f"{AnsiColor.MAGENTA}{logger_name}{AnsiColor.RESET}"

        line = f"{ts_colored} {thread_colored} {level_colored} {logger_colored} - {message}"

        if record.exc_info:
            exc_text = self.formatException(record.exc_info)
            line = f"{line}\n{exc_text}"

        return line

class JsonFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    def format(self, record):
        log_obj = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
        }
        
        # Add extra fields if present
        if hasattr(record, 'extra'):
            log_obj.update(record.extra)
            
        # Add exception info if present
        if record.exc_info:
            log_obj['exception'] = self.formatException(record.exc_info)
            
        return json.dumps(log_obj)

class QuepasaLogger:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(QuepasaLogger, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not QuepasaLogger._initialized:
            QuepasaLogger._initialized = True
            self._setup_logging()
    
    def _setup_logging(self):
        """Configure root logger with JSON formatting and STDOUT handler"""
        root_logger = logging.getLogger()
        
        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Create STDOUT handler
        handler = logging.StreamHandler(sys.stdout)
        formatter = PlainTextFormatter() if os.environ.get('LOG_FORMAT', '').lower() == 'plain' else JsonFormatter()
        handler.setFormatter(formatter)
        root_logger.addHandler(handler)
        
        # Set default level from environment or INFO
        log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
        root_logger.setLevel(getattr(logging, log_level))
    
    def get_instance(self, name=__name__):
        """
        Get a logger instance with the specified name
        
        Args:
            name: The logger name (typically __name__)
            
        Returns:
            logging.Logger: Configured logger instance
        """
        return logging.getLogger(name)
    
    @staticmethod
    def set_level(level):
        """
        Set logging level for root logger
        
        Args:
            level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        logging.getLogger().setLevel(level) 
