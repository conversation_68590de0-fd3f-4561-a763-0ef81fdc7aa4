import json

from src.lib.markdown_converter import _filter_product_for_metadata, METADATA_EXCLUDE_KEYS
from src.lib.markdown_converter import product_to_markdown
from src.lib.markdown_converter import products_to_documents


def assert_id_title(documents: list[dict]):
    assert len(documents) == 1
    doc = documents[0]
    assert doc['id'] == 'SKU123'
    assert doc.get('title', '') == 'Test Product'

    markdown = doc['chunks'][0]['text']
    assert "# Test Product" in markdown
    assert "**SKU:** SKU123" in markdown
    return markdown


class TestProductToMarkdown:
    """Test product_to_markdown function"""

    def test_minimal_product(self):
        """Test with minimal product data (only SKU)"""
        product = {"id": "SKU123"}
        markdown = product_to_markdown(product)
        assert "# SKU123" in markdown
        assert "**SKU:** SKU123" in markdown

    def test_product_with_title_and_description(self):
        """Test product with title and description"""
        product = {
            "id": "SKU123",
            "title": "Test Product",
            "description": "This is a test product description."
        }
        markdown = product_to_markdown(product)
        assert "# Test Product" in markdown
        assert "**SKU:** SKU123" in markdown
        assert "## Description" in markdown
        assert "This is a test product description." in markdown

    def test_product_with_attributes(self):
        """Test product with attributes"""
        product = {
            "id": "SKU123",
            "title": "Test Product",
            "attributes": {
                "color": "red",
                "size": ["S", "M", "L"]
            }
        }
        markdown = product_to_markdown(product)
        assert "## Attributes" in markdown
        assert "**color:** red" in markdown
        assert "**size:** S, M, L" in markdown

    def test_product_with_variants(self):
        """Test product with variants table"""
        product = {
            "id": "SKU123",
            "title": "Test Product",
            "variants": [
                {"id": "VAR1", "size": "S", "availability": "IN_STOCK"},
                {"id": "VAR2", "size": "M", "availability": "OUT_OF_STOCK"}
            ]
        }
        markdown = product_to_markdown(product)
        assert "## Variants" in markdown
        assert "| SKU | size | availability |" in markdown
        assert "| VAR1 | S | IN_STOCK |" in markdown
        assert "| VAR2 | M | OUT_OF_STOCK |" in markdown


class TestProductsDocuments:
    """Test products_to_documents function"""

    def test_minimal_product(self):
        """Test with minimal product data (only SKU)"""
        products = [{"id": "SKU123"}]
        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == 'SKU123'
        assert doc.get('title', '') == ''
        assert doc.get('url', '') == ''
        assert doc['sku'] == 'SKU123'
        assert 'metadata' in doc  # NEW: Verify metadata field exists

        # NEW: Verify metadata is valid JSON and contains the original product data
        metadata = json.loads(doc['metadata'])
        assert metadata['id'] == 'SKU123'

        assert len(doc['chunks']) == 1
        assert doc['chunks'][0]['language'] == 'en'

        markdown = doc['chunks'][0]['text']
        assert "# SKU123" in markdown
        assert "**SKU:** SKU123" in markdown

    def test_product_with_title_and_description(self):
        """Test product with title and description"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "description": "This is a test product description."
        }]
        documents = products_to_documents(products)

        markdown = assert_id_title(documents)
        assert "## Description" in markdown
        assert "This is a test product description." in markdown

    def test_product_with_availability(self):
        """Test product with availability status"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "availability": "IN_STOCK"
        }]
        documents = products_to_documents(products)

        markdown = documents[0]['chunks'][0]['text']
        assert "**Availability:** IN_STOCK" in markdown

    def test_product_with_attributes(self):
        """Test product with attributes"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "attributes": {
                "color": "red",
                "size": ["S", "M", "L"],
                "material": "cotton"
            }
        }]
        documents = products_to_documents(products)

        markdown = documents[0]['chunks'][0]['text']
        assert "## Attributes" in markdown
        assert "**color:** red" in markdown
        assert "**size:** S, M, L" in markdown
        assert "**material:** cotton" in markdown

    def test_product_with_link_and_images(self):
        """Test product with URI and images"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "uri": "https://example.com/product",
            "images": [
                {"uri": "https://example.com/image1.jpg"},
                {"uri": "https://example.com/image2.jpg"}
            ]
        }]
        documents = products_to_documents(products)

        doc = documents[0]
        assert doc.get('url') == 'https://example.com/product'

        markdown = doc['chunks'][0]['text']
        assert "[View product](https://example.com/product)" in markdown
        assert "![image](https://example.com/image1.jpg)" in markdown
        assert "![image](https://example.com/image2.jpg)" in markdown

    def test_product_with_images_as_strings(self):
        """Test product with images as string list (should be ignored as v2 script only handles dict images)"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "images": [
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg"
            ]
        }]
        documents = products_to_documents(products)

        markdown = documents[0]['chunks'][0]['text']
        # String images should be ignored, only dict images with "uri" key are processed
        assert "![image](" not in markdown

    def test_product_with_variants(self):
        """Test product with variants table"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "variants": [
                {"id": "VAR1", "size": "S", "availability": "IN_STOCK"},
                {"id": "VAR2", "size": "M", "availability": "OUT_OF_STOCK"},
                {"id": "VAR3", "size": "L", "availability": "IN_STOCK"}
            ]
        }]
        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == 'SKU123'
        assert doc.get('title', '') == 'Test Product'

        markdown = doc['chunks'][0]['text']
        assert "## Variants" in markdown
        assert "| SKU | size | availability |" in markdown
        assert "| VAR1 | S | IN_STOCK |" in markdown
        assert "| VAR2 | M | OUT_OF_STOCK |" in markdown
        assert "| VAR3 | L | IN_STOCK |" in markdown

    def test_product_with_extra_fields(self):
        """Test product with additional fields not in ignore list"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "brands": ["Brand A", "Brand B"],
            "categories": "apparel > tops > shirt",
            "priceInfo": {"originalPrice": 29.99, "price": 19.99}
        }]
        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == 'SKU123'
        assert doc.get('title', '') == 'Test Product'

        markdown = doc['chunks'][0]['text']
        assert "**brands:** Brand A, Brand B" in markdown
        assert "**categories:** apparel > tops > shirt" in markdown
        assert "**priceInfo:** originalPrice: 29.99, price: 19.99" in markdown

    def test_product_ignores_special_fields(self):
        """Test that special fields are ignored"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "_id": "internal_id",
            "indexables": ["field1", "field2"],
            "samplePrecision": 1,
            "type": "VARIANT",
            "name": "internal_name"
        }]
        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == 'SKU123'
        assert doc.get('title', '') == 'Test Product'

        markdown = doc['chunks'][0]['text']
        # These fields should not appear in the markdown
        assert "_id" not in markdown
        assert "indexables" not in markdown
        assert "samplePrecision" not in markdown
        assert "**type:**" not in markdown
        assert "**name:**" not in markdown

    def test_realistic_product(self):
        """Test with a realistic product structure similar to shoeby example"""
        products = [{
            "id": "1100351",
            "title": "Printed Tie Dye T-shirt Groen",
            "availability": "IN_STOCK",
            "brands": ["Shoeby"],
            "categories": ["apparel > tops > shirt > t-shirt"],
            "uri": "https://www.shoeby.nl/product.html",
            "images": [
                {"uri": "https://example.com/image1.jpg"}
            ],
            "description": "Print it! The Printed Tie Dye T-shirt has short sleeves.",
            "colorInfo": {
                "colorFamilies": ["Green"],
                "colors": ["green"]
            },
            "materials": ["cotton"],
            "priceInfo": {
                "originalPrice": 19.99,
                "price": 12.99
            },
            "attributes": {
                "special_offer": ["40% discount", "sale"],
                "style": ["tie dye", "trends"],
                "TargetAudience": ["boys", "kids"]
            },
            "variants": [
                {"id": "1100351-31-158/164", "sizes": ["158/164"], "availability": "IN_STOCK"},
                {"id": "1100351-31-134/140", "sizes": ["134/140"], "availability": "IN_STOCK"}
            ]
        }]

        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == '1100351'
        assert doc.get('title', '') == 'Printed Tie Dye T-shirt Groen'
        assert doc.get('url', '') == 'https://www.shoeby.nl/product.html'

        markdown = doc['chunks'][0]['text']

        # Check main sections
        assert "# Printed Tie Dye T-shirt Groen" in markdown
        assert "**SKU:** 1100351" in markdown
        assert "**Availability:** IN_STOCK" in markdown
        assert "**brands:** Shoeby" in markdown

        # Check description
        assert "## Description" in markdown
        assert "Print it! The Printed Tie Dye T-shirt has short sleeves." in markdown

        # Check attributes
        assert "## Attributes" in markdown
        assert "**special_offer:** 40% discount, sale" in markdown

        # Check variants table
        assert "## Variants" in markdown
        assert "| SKU | sizes | availability |" in markdown
        assert "| 1100351-31-158/164" in markdown

    def test_empty_product(self):
        """Test with empty product dictionary - should be skipped"""
        products = [{}]
        documents = products_to_documents(products)

        # Empty product without 'id' should be skipped
        assert len(documents) == 0

    def test_product_with_none_values(self):
        """Test product with None values for optional fields"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "availability": None,
            "attributes": None,
            "variants": None
        }]
        documents = products_to_documents(products)

        markdown = assert_id_title(documents)
        # None values should not create sections
        assert "## Description" not in markdown
        assert "## Attributes" not in markdown
        assert "## Variants" not in markdown

    def test_product_with_empty_collections(self):
        """Test product with empty attributes and variants"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "attributes": {},
            "variants": [],
            "images": []
        }]
        documents = products_to_documents(products)

        markdown = assert_id_title(documents)
        # Empty collections should not create sections
        assert "## Attributes" not in markdown
        assert "## Variants" not in markdown

    def test_multiple_products(self):
        """Test processing multiple products at once"""
        products = [
            {"id": "SKU001", "title": "Product 1", "description": "First product"},
            {"id": "SKU002", "title": "Product 2", "description": "Second product"}
        ]
        documents = products_to_documents(products)

        assert len(documents) == 2

        # Check first product
        assert documents[0]['id'] == 'SKU001'
        assert documents[0].get('title', '') == 'Product 1'
        assert "# Product 1" in documents[0]['chunks'][0]['text']
        assert "**SKU:** SKU001" in documents[0]['chunks'][0]['text']
        assert "First product" in documents[0]['chunks'][0]['text']

        # Check second product
        assert documents[1]['id'] == 'SKU002'
        assert documents[1].get('title', '') == 'Product 2'
        assert "# Product 2" in documents[1]['chunks'][0]['text']
        assert "**SKU:** SKU002" in documents[1]['chunks'][0]['text']
        assert "Second product" in documents[1]['chunks'][0]['text']

    def test_invalid_products_are_skipped(self):
        """Test that invalid products are skipped and valid ones are processed"""
        products = [
            {"id": "VALID1", "title": "Valid Product"},
            "invalid_string_product",  # Should be skipped
            {"title": "No ID"},  # Should be skipped - missing id
            {"id": "VALID2", "title": "Another Valid Product"}
        ]
        documents = products_to_documents(products)

        # Should only have 2 valid documents
        assert len(documents) == 2
        assert documents[0]['id'] == 'VALID1'
        assert documents[1]['id'] == 'VALID2'

    def test_empty_products_list(self):
        """Test handling of empty products list"""
        products = []
        documents = products_to_documents(products)

        assert len(documents) == 0
        assert documents == []


class TestFilterProductForMetadata:
    """Test _filter_product_for_metadata function"""

    def test_filter_excludes_specified_keys(self):
        """Test that filtering excludes only the keys defined in METADATA_EXCLUDE_KEYS"""
        product = {
            "id": "SKU123",
            "title": "Test Product",
            "_id": "internal_id",
            "indexables": ["field1", "field2"],
            "samplePrecision": 1,
            "dynamicFacets": ["color", "size"],
            "description": "Keep this",
            "priceInfo": {"price": 19.99},
            "attributes": {
                "color": "red",
                "sizes": ["S", "M", "L"],
                "nested": {"key": "value"}
            },
            "variants": [
                {"id": "VAR1", "size": "S"},
                {"id": "VAR2", "size": "M"}
            ],
        }

        filtered = _filter_product_for_metadata(product)

        # Should exclude keys in METADATA_EXCLUDE_KEYS
        for key in METADATA_EXCLUDE_KEYS:
            assert key not in filtered

        # Should keep everything else
        assert filtered["id"] == "SKU123"
        assert filtered["title"] == "Test Product"
        assert filtered["description"] == "Keep this"
        assert filtered["priceInfo"] == {"price": 19.99}
        assert filtered["attributes"] == {
            "color": "red",
            "sizes": ["S", "M", "L"],
            "nested": {"key": "value"}
        }
        assert len(filtered["variants"]) == 2
        assert filtered["variants"][0] == {"id": "VAR1", "size": "S"}

    def test_filter_with_empty_product(self):
        """Test filtering with empty product"""
        product = {}
        filtered = _filter_product_for_metadata(product)
        assert filtered == {}

    def test_products_to_documents_metadata_filtering(self):
        """Test that products_to_documents properly filters metadata using METADATA_EXCLUDE_KEYS"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "description": "Product description",
            # Keys that should be excluded from metadata
            "_id": "internal_id",
            "indexables": ["field1", "field2"],
            "samplePrecision": 1,
            "dynamicFacets": ["color", "size"],
            # Keys that should be kept in metadata
            "priceInfo": {"price": 29.99},
            "attributes": {"color": "red"},
            "categories": ["clothing"]
        }]

        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]

        # Verify the metadata field contains filtered data
        assert 'metadata' in doc
        metadata = json.loads(doc['metadata'])

        # Should exclude keys in METADATA_EXCLUDE_KEYS
        assert "_id" not in metadata
        assert "indexables" not in metadata
        assert "samplePrecision" not in metadata
        assert "dynamicFacets" not in metadata

        # Should keep other fields
        assert metadata["id"] == "SKU123"
        assert metadata["title"] == "Test Product"
        assert metadata["description"] == "Product description"
        assert metadata["priceInfo"] == {"price": 29.99}
        assert metadata["attributes"] == {"color": "red"}
        assert metadata["categories"] == ["clothing"]

    def test_products_to_documents_metadata_json_serialization(self):
        """Test that metadata field contains valid JSON"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "priceInfo": {"price": 29.99, "currency": "USD"},
            "attributes": {"colors": ["red", "blue"], "sizes": ["S", "M", "L"]},
            "variants": [
                {"id": "VAR1", "color": "red", "size": "S"},
                {"id": "VAR2", "color": "blue", "size": "M"}
            ]
        }]

        documents = products_to_documents(products)
        doc = documents[0]

        # Verify metadata is valid JSON
        metadata = json.loads(doc['metadata'])

        # Verify complex nested structures are properly serialized
        assert metadata["priceInfo"]["price"] == 29.99
        assert metadata["priceInfo"]["currency"] == "USD"
        assert "red" in metadata["attributes"]["colors"]
        assert len(metadata["variants"]) == 2
        assert metadata["variants"][0]["id"] == "VAR1"

    def test_products_to_documents_sku_field_populated(self):
        """Test that sku field is properly populated from preprocessed SKU"""
        products = [
            {"id": "12345", "title": "Numeric SKU"},
            {"id": "ABC-123", "title": "Alphanumeric SKU"},
        ]

        documents = products_to_documents(products)

        assert len(documents) == 2
        assert documents[0]['sku'] == '12345'  # Should be string
        assert documents[1]['sku'] == 'ABC-123'

    def test_multiple_products_with_filtered_metadata(self):
        """Ensure multiple products are converted with correct ids, type, sku, and filtered metadata"""
        products = [
            {
                "id": "PROD-001",
                "title": "Product One",
                "priceInfo": {"price": 19.99},
                "_id": "should_be_filtered_1"
            },
            {
                "id": "PROD-002",
                "title": "Product Two",
                "priceInfo": {"price": 29.99},
                "_id": "should_be_filtered_2"
            }
        ]

        documents = products_to_documents(products)
        assert len(documents) == 2

        for i, doc in enumerate(documents):
            expected_id = f"PROD-00{i + 1}"
            assert doc['id'] == expected_id
            assert doc['sku'] == expected_id

            metadata = json.loads(doc['metadata'])
            assert metadata['id'] == expected_id
            assert "_id" not in metadata

    def test_metadata_serialization_handles_unicode_and_nested(self):
        """Ensure JSON serialization handles nested structures and unicode content"""
        products = [{
            "id": "COMPLEX-123",
            "title": "Complex Product",
            "nested_structure": {
                "level1": {
                    "level2": ["item1", "item2", {"nested_dict": "value"}]
                }
            },
            "unicode_content": "Special chars: ñáéíóú中文🚀",
            "_id": "should_be_filtered"
        }]

        documents = products_to_documents(products)
        doc = documents[0]

        metadata = json.loads(doc['metadata'])
        assert metadata['nested_structure']['level1']['level2'][2]['nested_dict'] == 'value'
        assert metadata['unicode_content'] == "Special chars: ñáéíóú中文🚀"
        assert "_id" not in metadata
