from src.lib.markdown_converter import products_to_documents
from src.lib.markdown_converter import product_to_markdown


def assert_id_title(documents: list[dict]):
    assert len(documents) == 1
    doc = documents[0]
    assert doc['id'] == 'SKU123'
    assert doc.get('title', '') == 'Test Product'

    markdown = doc['chunks'][0]['text']
    assert "# Test Product" in markdown
    assert "**SKU:** SKU123" in markdown
    return markdown



class TestProductToMarkdown:
    """Test product_to_markdown function"""

    def test_minimal_product(self):
        """Test with minimal product data (only SKU)"""
        product = {"id": "SKU123"}
        markdown = product_to_markdown(product)
        assert "# SKU123" in markdown
        assert "**SKU:** SKU123" in markdown

    def test_product_with_title_and_description(self):
        """Test product with title and description"""
        product = {
            "id": "SKU123",
            "title": "Test Product",
            "description": "This is a test product description."
        }
        markdown = product_to_markdown(product)
        assert "# Test Product" in markdown
        assert "**SKU:** SKU123" in markdown
        assert "## Description" in markdown
        assert "This is a test product description." in markdown

    def test_product_with_attributes(self):
        """Test product with attributes"""
        product = {
            "id": "SKU123",
            "title": "Test Product",
            "attributes": {
                "color": "red",
                "size": ["S", "M", "L"]
            }
        }
        markdown = product_to_markdown(product)
        assert "## Attributes" in markdown
        assert "**color:** red" in markdown
        assert "**size:** S, M, L" in markdown

    def test_product_with_variants(self):
        """Test product with variants table"""
        product = {
            "id": "SKU123",
            "title": "Test Product",
            "variants": [
                {"id": "VAR1", "size": "S", "availability": "IN_STOCK"},
                {"id": "VAR2", "size": "M", "availability": "OUT_OF_STOCK"}
            ]
        }
        markdown = product_to_markdown(product)
        assert "## Variants" in markdown
        assert "| SKU | size | availability |" in markdown
        assert "| VAR1 | S | IN_STOCK |" in markdown
        assert "| VAR2 | M | OUT_OF_STOCK |" in markdown


class TestProductsDocuments:
    """Test products_to_documents function"""

    def test_minimal_product(self):
        """Test with minimal product data (only SKU)"""
        products = [{"id": "SKU123"}]
        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == 'SKU123'
        assert doc.get('title', '') == ''
        assert doc.get('url', '') == ''
        assert len(doc['chunks']) == 1
        assert doc['chunks'][0]['language'] == 'en'

        markdown = doc['chunks'][0]['text']
        assert "# SKU123" in markdown
        assert "**SKU:** SKU123" in markdown

    def test_product_with_title_and_description(self):
        """Test product with title and description"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "description": "This is a test product description."
        }]
        documents = products_to_documents(products)

        markdown = assert_id_title(documents)
        assert "## Description" in markdown
        assert "This is a test product description." in markdown

    def test_product_with_availability(self):
        """Test product with availability status"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "availability": "IN_STOCK"
        }]
        documents = products_to_documents(products)

        markdown = documents[0]['chunks'][0]['text']
        assert "**Availability:** IN_STOCK" in markdown

    def test_product_with_attributes(self):
        """Test product with attributes"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "attributes": {
                "color": "red",
                "size": ["S", "M", "L"],
                "material": "cotton"
            }
        }]
        documents = products_to_documents(products)

        markdown = documents[0]['chunks'][0]['text']
        assert "## Attributes" in markdown
        assert "**color:** red" in markdown
        assert "**size:** S, M, L" in markdown
        assert "**material:** cotton" in markdown

    def test_product_with_link_and_images(self):
        """Test product with URI and images"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "uri": "https://example.com/product",
            "images": [
                {"uri": "https://example.com/image1.jpg"},
                {"uri": "https://example.com/image2.jpg"}
            ]
        }]
        documents = products_to_documents(products)

        doc = documents[0]
        assert doc.get('url') == 'https://example.com/product'

        markdown = doc['chunks'][0]['text']
        assert "[View product](https://example.com/product)" in markdown
        assert "![image](https://example.com/image1.jpg)" in markdown
        assert "![image](https://example.com/image2.jpg)" in markdown

    def test_product_with_images_as_strings(self):
        """Test product with images as string list (should be ignored as v2 script only handles dict images)"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "images": [
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg"
            ]
        }]
        documents = products_to_documents(products)

        markdown = documents[0]['chunks'][0]['text']
        # String images should be ignored, only dict images with "uri" key are processed
        assert "![image](" not in markdown

    def test_product_with_variants(self):
        """Test product with variants table"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "variants": [
                {"id": "VAR1", "size": "S", "availability": "IN_STOCK"},
                {"id": "VAR2", "size": "M", "availability": "OUT_OF_STOCK"},
                {"id": "VAR3", "size": "L", "availability": "IN_STOCK"}
            ]
        }]
        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == 'SKU123'
        assert doc.get('title', '') == 'Test Product'

        markdown = doc['chunks'][0]['text']
        assert "## Variants" in markdown
        assert "| SKU | size | availability |" in markdown
        assert "| VAR1 | S | IN_STOCK |" in markdown
        assert "| VAR2 | M | OUT_OF_STOCK |" in markdown
        assert "| VAR3 | L | IN_STOCK |" in markdown

    def test_product_with_extra_fields(self):
        """Test product with additional fields not in ignore list"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "brands": ["Brand A", "Brand B"],
            "categories": "apparel > tops > shirt",
            "priceInfo": {"originalPrice": 29.99, "price": 19.99}
        }]
        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == 'SKU123'
        assert doc.get('title', '') == 'Test Product'

        markdown = doc['chunks'][0]['text']
        assert "**brands:** Brand A, Brand B" in markdown
        assert "**categories:** apparel > tops > shirt" in markdown
        assert "**priceInfo:** originalPrice: 29.99, price: 19.99" in markdown

    def test_product_ignores_special_fields(self):
        """Test that special fields are ignored"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "_id": "internal_id",
            "indexables": ["field1", "field2"],
            "samplePrecision": 1,
            "type": "VARIANT",
            "name": "internal_name"
        }]
        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == 'SKU123'
        assert doc.get('title', '') == 'Test Product'

        markdown = doc['chunks'][0]['text']
        # These fields should not appear in the markdown
        assert "_id" not in markdown
        assert "indexables" not in markdown
        assert "samplePrecision" not in markdown
        assert "**type:**" not in markdown
        assert "**name:**" not in markdown

    def test_realistic_product(self):
        """Test with a realistic product structure similar to shoeby example"""
        products = [{
            "id": "1100351",
            "title": "Printed Tie Dye T-shirt Groen",
            "availability": "IN_STOCK",
            "brands": ["Shoeby"],
            "categories": ["apparel > tops > shirt > t-shirt"],
            "uri": "https://www.shoeby.nl/product.html",
            "images": [
                {"uri": "https://example.com/image1.jpg"}
            ],
            "description": "Print it! The Printed Tie Dye T-shirt has short sleeves.",
            "colorInfo": {
                "colorFamilies": ["Green"],
                "colors": ["green"]
            },
            "materials": ["cotton"],
            "priceInfo": {
                "originalPrice": 19.99,
                "price": 12.99
            },
            "attributes": {
                "special_offer": ["40% discount", "sale"],
                "style": ["tie dye", "trends"],
                "TargetAudience": ["boys", "kids"]
            },
            "variants": [
                {"id": "1100351-31-158/164", "sizes": ["158/164"], "availability": "IN_STOCK"},
                {"id": "1100351-31-134/140", "sizes": ["134/140"], "availability": "IN_STOCK"}
            ]
        }]

        documents = products_to_documents(products)

        assert len(documents) == 1
        doc = documents[0]
        assert doc['id'] == '1100351'
        assert doc.get('title', '') == 'Printed Tie Dye T-shirt Groen'
        assert doc.get('url', '') == 'https://www.shoeby.nl/product.html'

        markdown = doc['chunks'][0]['text']

        # Check main sections
        assert "# Printed Tie Dye T-shirt Groen" in markdown
        assert "**SKU:** 1100351" in markdown
        assert "**Availability:** IN_STOCK" in markdown
        assert "**brands:** Shoeby" in markdown

        # Check description
        assert "## Description" in markdown
        assert "Print it! The Printed Tie Dye T-shirt has short sleeves." in markdown

        # Check attributes
        assert "## Attributes" in markdown
        assert "**special_offer:** 40% discount, sale" in markdown

        # Check variants table
        assert "## Variants" in markdown
        assert "| SKU | sizes | availability |" in markdown
        assert "| 1100351-31-158/164" in markdown

    def test_empty_product(self):
        """Test with empty product dictionary - should be skipped"""
        products = [{}]
        documents = products_to_documents(products)

        # Empty product without 'id' should be skipped
        assert len(documents) == 0

    def test_product_with_none_values(self):
        """Test product with None values for optional fields"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "availability": None,
            "attributes": None,
            "variants": None
        }]
        documents = products_to_documents(products)

        markdown = assert_id_title(documents)
        # None values should not create sections
        assert "## Description" not in markdown
        assert "## Attributes" not in markdown
        assert "## Variants" not in markdown

    def test_product_with_empty_collections(self):
        """Test product with empty attributes and variants"""
        products = [{
            "id": "SKU123",
            "title": "Test Product",
            "attributes": {},
            "variants": [],
            "images": []
        }]
        documents = products_to_documents(products)

        markdown = assert_id_title(documents)
        # Empty collections should not create sections
        assert "## Attributes" not in markdown
        assert "## Variants" not in markdown

    def test_multiple_products(self):
        """Test processing multiple products at once"""
        products = [
            {"id": "SKU001", "title": "Product 1", "description": "First product"},
            {"id": "SKU002", "title": "Product 2", "description": "Second product"}
        ]
        documents = products_to_documents(products)

        assert len(documents) == 2

        # Check first product
        assert documents[0]['id'] == 'SKU001'
        assert documents[0].get('title', '') == 'Product 1'
        assert "# Product 1" in documents[0]['chunks'][0]['text']
        assert "**SKU:** SKU001" in documents[0]['chunks'][0]['text']
        assert "First product" in documents[0]['chunks'][0]['text']

        # Check second product
        assert documents[1]['id'] == 'SKU002'
        assert documents[1].get('title', '') == 'Product 2'
        assert "# Product 2" in documents[1]['chunks'][0]['text']
        assert "**SKU:** SKU002" in documents[1]['chunks'][0]['text']
        assert "Second product" in documents[1]['chunks'][0]['text']

    def test_invalid_products_are_skipped(self):
        """Test that invalid products are skipped and valid ones are processed"""
        products = [
            {"id": "VALID1", "title": "Valid Product"},
            "invalid_string_product",  # Should be skipped
            {"title": "No ID"},  # Should be skipped - missing id
            {"id": "VALID2", "title": "Another Valid Product"}
        ]
        documents = products_to_documents(products)

        # Should only have 2 valid documents
        assert len(documents) == 2
        assert documents[0]['id'] == 'VALID1'
        assert documents[1]['id'] == 'VALID2'

    def test_empty_products_list(self):
        """Test handling of empty products list"""
        products = []
        documents = products_to_documents(products)

        assert len(documents) == 0
        assert documents == []
