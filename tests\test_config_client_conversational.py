import types
import builtins
import pytest

from quepasa.common import config_client as cc
from quepasa.common.config_client import get_tenant_config


def test_get_dynamic_config_populates_conversational_configs_full_refresh(monkeypatch):
    # Reset in-memory store before test
    cc._MERCHANT_STORE.tenants.clear()

    # Monkeypatch bearer and fetch to avoid network
    monkeypatch.setattr(cc, "login_get_bearer", lambda timeout=10.0: "token")

    tenants_payload = [
        {"id": 1, "name": "acme", "enabled": True, "settings": {"preferredLanguage": "en"}},
        {"id": 2, "name": "globex", "enabled": True, "settings": {"preferredLanguage": "en"}},
    ]

    projects_payload = [
        {"id": 10, "tenantId": 1, "collection": "products"},
        {"id": 11, "tenantId": 2, "collection": "products"},
    ]

    areas_payload = [
        {"id": 100, "tenantId": 1, "collectionId": 1000, "collectionName": "products"},
        {"id": 200, "tenantId": 2, "collectionId": 2000, "collectionName": "products"},
    ]

    attributes_payload = []

    conv_cfg_payload_initial = [
        {"id": 5001, "tenantId": 1, "collectionId": 1000, "areaId": 100, "interleaving": {"ragRatio": 0.5, "spdRatio": 0.5, "spdIncludeOnlyPromoted": False}},
        {"id": 5002, "tenantId": 2, "collectionId": 2000, "areaId": 200, "interleaving": {"ragRatio": 0.2, "spdRatio": 0.8, "spdIncludeOnlyPromoted": True}},
    ]

    def fake_fetch_dynamic_config(_token, timeout=15.0):
        return {
            "tenants": tenants_payload,
            "projectConfigurations": projects_payload,
            "areas": areas_payload,
            "attributes": attributes_payload,
            "rules": [],
            "conversationalSearchConfigs": conv_cfg_payload_initial,
        }

    monkeypatch.setattr(cc, "fetch_dynamic_config", fake_fetch_dynamic_config)

    # Full refresh
    cfg = cc.get_dynamic_config()
    assert cfg is not None

    acme = get_tenant_config("acme")
    globex = get_tenant_config("globex")
    assert acme is not None and globex is not None

    # Validate conversational configs populated
    assert len(acme.conversational_search_configs) == 1
    assert acme.conversational_search_configs[0].areaId == 100

    # Now change payload and perform another full refresh; entries should update accordingly
    new_payload = [
        {"id": 6001, "tenantId": 1, "collectionId": 1000, "areaId": 100, "interleaving": {"ragRatio": 0.7, "spdRatio": 0.3, "spdIncludeOnlyPromoted": False}},
    ]

    def fake_fetch_dynamic_config_updated(_token, timeout=15.0):
        return {
            "tenants": tenants_payload,
            "projectConfigurations": projects_payload,
            "areas": areas_payload,
            "attributes": attributes_payload,
            "rules": [],
            "conversationalSearchConfigs": new_payload,
        }

    monkeypatch.setattr(cc, "fetch_dynamic_config", fake_fetch_dynamic_config_updated)

    # Perform full refresh again
    cc.get_dynamic_config()

    acme_after_update = get_tenant_config("acme")
    assert acme_after_update is not None
    assert len(acme_after_update.conversational_search_configs) == 1
    assert acme_after_update.conversational_search_configs[0].id == 6001
