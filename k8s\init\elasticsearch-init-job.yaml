apiVersion: batch/v1
kind: Job
metadata:
  name: elasticsearch-init
  namespace: quepasa
spec:
  backoffLimit: 6
  template:
    spec:
      containers:
      - name: elasticsearch-init
        image: qpreg.azurecr.io/quepasa/elasticsearch_init:v1.0.184
        imagePullPolicy: Always
        command: ["/bin/bash"]
        args:
        - -c
        - |
          apt-get update && apt-get install -y curl &&
          echo 'Waiting for Elasticsearch to be ready...' &&
          until curl -s http://elasticsearch:9200 > /dev/null; do sleep 1; done &&
          echo 'Elasticsearch is ready, creating index...' &&
          python /app/configuration/main/cli_create_index.py search-document-v2-default
        env:
        - name: ELASTICSEARCH_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: ELASTICSEARCH_HOST
        - name: ELASTICSEARCH_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: ELASTICSEARCH_PORT
        - name: <PERSON>LA<PERSON><PERSON><PERSON>ARCH_USERNAME
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ELASTICSEARCH_USERNAME
        - name: <PERSON><PERSON><PERSON><PERSON><PERSON>ARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ELASTICSEARCH_PASSWORD
        - name: PYTHONPATH
          value: /app
        envFrom:
          - configMapRef:
              name: shared-config
          - secretRef:
              name: shared-secrets
        volumeMounts:
          - name: config-volume
            mountPath: /app/configuration/main
      restartPolicy: OnFailure
      volumes:
        - name: config-volume
          configMap:
            name: quepasa-config 