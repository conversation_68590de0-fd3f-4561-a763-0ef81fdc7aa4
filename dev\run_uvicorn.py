from pathlib import Path
import os
import uvicorn

try:
    from dotenv import load_dotenv  # type: ignore
except Exception:  # pragma: no cover
    def load_dotenv(*args, **kwargs):
        # If python-dotenv isn't installed, just skip loading files
        return False


def main() -> None:
    # Load base .env then override with local.env if present
    root = Path(__file__).resolve().parent.parent
    load_dotenv(root / ".env")
    load_dotenv(root / "local.env", override=True)

    host = os.getenv("API_HOST", "localhost")
    port = int(os.getenv("API_PORT", "8000"))

    uvicorn.run("quepasa.api.app:app", host=host, port=port, reload=True)


if __name__ == "__main__":
    main()
