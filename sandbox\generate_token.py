#!/usr/bin/env python3

import argparse
from configuration.main.default import QuepasaConfigurationHub

def generate_token(client_id: str, user_id: str = 'default') -> str:
    """Generate an authentication token for a client and user.
    
    Args:
        client_id: Client identifier
        user_id: API user identifier (defaults to 'default')
        
    Returns:
        Generated authentication token
    """
    # Initialize configuration
    config = QuepasaConfigurationHub.from_client_code(client_id)
    
    # Generate token
    token = config.create_auth_token(user_id)
    
    return token

def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Generate QuePasa authentication token')
    parser.add_argument('client_id', help='Client identifier')
    parser.add_argument('--user-id', default='default', help='API user identifier (default: default)')
    
    # Parse arguments
    args = parser.parse_args()
    
    try:
        # Generate token
        token = generate_token(args.client_id, args.user_id)
        
        # Print result
        print(f"\nGenerated token for client '{args.client_id}' and user '{args.user_id}':")
        print(f"\n{args.client_id}:{token}\n")
        print("Use this token in the Authorization header as:")
        print(f"Authorization: Bearer {args.client_id}:{token}\n")
        
    except Exception as e:
        print(f"Error generating token: {str(e)}")
        return 1
        
    return 0

if __name__ == '__main__':
    exit(main()) 