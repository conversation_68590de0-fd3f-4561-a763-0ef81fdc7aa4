import os
import requests
from typing import List, Dict, Any
from .base import BaseReranker
from .cache import RerankerCacheMixin

class TogetherReranker(RerankerCacheMixin, BaseReranker):
    """Together.ai reranker implementation"""
    
    def __init__(self):
        super().__init__()
        self.api_token = os.getenv("TOGETHERAI_API_KEY")
        if not self.api_token:
            raise ValueError("TOGETHERAI_API_KEY environment variable is not set")
            
    def get_results(self, model_version: str, query: str, documents: List[Dict[str, Any]], top_n: int = None) -> List[Dict[str, Any]]:
        """Get reranked documents using Together.ai API"""
        if not documents:
            return []
            
        response = requests.post(
            "https://api.together.xyz/v1/rerank",
            headers = {
                "accept": "application/json",
                "content-type": "application/json",
                "authorization": f"Bearer {self.api_token}"
            },
            json = {
                "model_version": model_version,
                "rank_fields": list(documents[0].keys()),
                "query": query,
                "documents": documents,
                "top_n": top_n or len(documents),
                "return_documents": True
            }
        )
        
        response.raise_for_status()
        result = response.json()
        
        return result.get("documents", []) 