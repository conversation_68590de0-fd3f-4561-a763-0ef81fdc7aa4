import pytest
from src.lib.llm.providers import LLMProvider

def test_provider_values():
    """Test provider enum values."""
    assert LLMProvider.OPENAI.value == 'openai'
    assert LLMProvider.MISTRAL.value == 'mistral'
    assert LLMProvider.ANTHROPIC.value == 'anthropic'
    assert LLMProvider.REPLICATE.value == 'replicate'
    assert LLMProvider.YANDEX.value == 'yandex'
    assert LLMProvider.OLLAMA.value == 'ollama'
    assert LLMProvider.TOGETHER.value == 'together'
    assert LLMProvider.NEBIUS.value == 'nebius'

def test_from_str_valid():
    """Test string to enum conversion with valid values."""
    assert LLMProvider.from_str('openai') == LLMProvider.OPENAI
    assert LLMProvider.from_str('mistral') == LLMProvider.MISTRAL
    assert LLMProvider.from_str('anthropic') == LLMProvider.ANTHROPIC
    assert LLMProvider.from_str('replicate') == LLMProvider.REPLICATE
    assert LLMProvider.from_str('yandex') == LLMProvider.YANDEX
    assert LLMProvider.from_str('ollama') == LLMProvider.OLLAMA
    assert LLMProvider.from_str('together') == LLMProvider.TOGETHER
    assert LLMProvider.from_str('nebius') == LLMProvider.NEBIUS

def test_from_str_case_insensitive():
    """Test case-insensitive string to enum conversion."""
    assert LLMProvider.from_str('OPENAI') == LLMProvider.OPENAI
    assert LLMProvider.from_str('MiStRaL') == LLMProvider.MISTRAL
    assert LLMProvider.from_str('AnThRoPiC') == LLMProvider.ANTHROPIC
    assert LLMProvider.from_str('RePLiCaTe') == LLMProvider.REPLICATE
    assert LLMProvider.from_str('YaNdEx') == LLMProvider.YANDEX
    assert LLMProvider.from_str('OlLaMa') == LLMProvider.OLLAMA
    assert LLMProvider.from_str('ToGeThEr') == LLMProvider.TOGETHER
    assert LLMProvider.from_str('NeBiUs') == LLMProvider.NEBIUS

def test_from_str_invalid():
    """Test error handling for invalid provider string."""
    with pytest.raises(ValueError) as exc_info:
        LLMProvider.from_str("invalid_provider")
    assert "Unknown LLM provider: invalid_provider" in str(exc_info.value)

def test_provider_uniqueness():
    """Test that all provider values are unique."""
    values = [provider.value for provider in LLMProvider]
    assert len(values) == len(set(values)) 