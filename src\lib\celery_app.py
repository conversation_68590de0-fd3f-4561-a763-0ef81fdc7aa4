import os
from celery import Celery
from kombu import Queue
from typing import Optional
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>ogger
from celery.signals import worker_ready

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

# Get Redis connection info from environment
redis_host = os.getenv('REDIS_HOST', 'redis')
redis_port = os.getenv('REDIS_PORT', '6379')
broker_url = os.getenv('CELERY_BROKER_URL', f'redis://{redis_host}:{redis_port}/0')
backend_url = os.getenv('CELERY_RESULT_BACKEND', f'redis://{redis_host}:{redis_port}/0')

# Create the Celery app
app = Celery('quepasa',
             broker=broker_url,
             backend=backend_url)

# Configure queues
app.conf.task_queues = (
    Queue('crawler', routing_key='crawler.#'),
    Queue('crawler-telegram', routing_key='crawler-telegram.#'),
    Queue('crawler-save', routing_key='crawler-save.#'),
    Queue('data-processor', routing_key='data-processor.#'),
    Queue('indexer', routing_key='indexer.#'),
    Queue('indexer-save', routing_key='indexer-save.#'),
)

# Define task routes based on explicit naming
app.conf.task_routes = {
    'quepasa.crawler.*': {'queue': 'crawler'},
    'quepasa.crawler_telegram.*': {'queue': 'crawler-telegram'},
    'quepasa.crawler_save.*': {'queue': 'crawler-save'},
    'quepasa.data_processor.*': {'queue': 'data-processor'},
    'quepasa.indexer.*': {'queue': 'indexer'},
    'quepasa.indexer_save.*': {'queue': 'indexer-save'},
}

# Common Celery configuration
app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,

    # General settings
    task_always_eager=False,
    broker_connection_retry_on_startup=True,

    # Add these settings for better connection handling
    broker_connection_timeout=10,
    broker_pool_limit=100,
    broker_heartbeat=5,

    # Result settings
    result_expires=86400,  # 1 day
    result_cache_max=1000,  # Maximum number of results to cache

    # Worker settings
    worker_prefetch_multiplier=1,  # don't prefetch tasks
    worker_cancel_long_running_tasks_on_connection_loss=False,

    # Task settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,

    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,

    task_time_limit=300,  # 5 minutes
    task_soft_time_limit=270,  # 4.5 minutes
    worker_max_tasks_per_child=1000,  # restart worker after 1000 tasks
    worker_max_memory_per_child=500000,  # or if exceeded 500 MB

    # Logging settings
    worker_send_task_events=True,
    task_track_started=True,
    task_send_sent_event=True,
)
@worker_ready.connect
def start_orchestrators(sender, **kwargs):
    """Run all orchestrators after worker startup"""
    try:
        logger.info("Starting crawler orchestrator after worker ready")
        from quepasa.crawler.orchestrator import CrawlerOrchestrator
        orchestrator = CrawlerOrchestrator()
        orchestrator.run() 

    except Exception as e:
        logger.error(f"Error starting crawler orchestrator: {str(e)}", exc_info=True)

    try:
        logger.info("Starting data processor orchestrator after worker ready")
        from quepasa.data_processor.orchestrator import DataProcessorOrchestrator
        orchestrator = DataProcessorOrchestrator()
        orchestrator.run() 

    except Exception as e:
        logger.error(f"Error starting data processor orchestrator: {str(e)}", exc_info=True)

    try:
        logger.info("Starting indexer orchestrator after worker ready")
        from quepasa.indexer.orchestrator import IndexerOrchestrator
        orchestrator = IndexerOrchestrator()
        orchestrator.run() 

    except Exception as e:
        logger.error(f"Error starting indexer orchestrator: {str(e)}", exc_info=True)

