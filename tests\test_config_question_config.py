import pytest
from configuration.base.question import QuestionConfig
from quepasa.searcher.models.request import QuepasaRequest

class TestQuestionConfig:
    def setup_method(self):
        self.question_config = QuestionConfig("test_client")
        self.request = QuepasaRequest(
            client="test_client",
            question="test question",
            source="telegram"
        )
        self.question_config.set_request(self.request)

    def test_expand_question_no_index(self):
        """Test question expansion without index"""
        result = self.question_config.expand_question(source=self.request.source, question="test question", language_code="en")
        assert isinstance(result, str)
        assert len(result) > 0

    def test_expand_question_with_variations(self):
        """Test question expansion with variations"""
        result = self.question_config.expand_question(source=self.request.source, question="test question with variations", language_code="en")
        assert isinstance(result, str)
        assert len(result) > 0
        assert "test question with variations" in result 