import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from quepasa.searcher.history.manager import <PERSON><PERSON><PERSON><PERSON>
from quepasa.searcher.history.models import <PERSON><PERSON><PERSON>, HistoryFilter
from quepasa.searcher.core.auth import Auth<PERSON><PERSON>ger, AuthResult
from quepasa.searcher.models.request import QuepasaRequest, UserInfo
from src.lib.constants import SOURCE_DOCUMENTS

@pytest.fixture
def mock_config():
    with patch('configuration.main.default.QuepasaConfigurationHub') as mock:
        instance = mock.return_value
        instance.get_history_storage_type.return_value = "memory"
        instance.get_telegram_access_token.return_value = "test_token"
        instance.get_language_code.return_value = "en"
        instance.get_history_config.return_value = Mock(
            forget_after_seconds=3600,
            use_roles=["user", "assistant"],
            max_last_messages=100
        )
        instance.request = QuepasaRequest(
            question="test query",
            protocol="http",
            source=SOURCE_DOCUMENTS,
            metadata={"client": "test_client"}
        )
        instance.get_client_code.return_value = "test_client"
        # Add validation methods
        instance.validate_auth_token = Mock(return_value=True)
        instance.validate_external_auth_token = Mock(return_value=True)
        yield instance

@pytest.fixture
def mock_storage():
    with patch('quepasa.searcher.history.storage.FileHistoryStorage') as mock:
        instance = mock.return_value
        instance.items = {}
        
        def save_item(user_id, item):
            if user_id not in instance.items:
                instance.items[user_id] = []
            instance.items[user_id].append(item)
            
        def get_items(filter_or_user_id):
            if isinstance(filter_or_user_id, str):
                return instance.items.get(filter_or_user_id, [])
            else:
                user_id = filter_or_user_id.user_id
                items = instance.items.get(user_id, [])
                if filter_or_user_id.start_time:
                    items = [i for i in items if i.ts >= filter_or_user_id.start_time]
                if filter_or_user_id.end_time:
                    items = [i for i in items if i.ts <= filter_or_user_id.end_time]
                if filter_or_user_id.roles:
                    items = [i for i in items if i.role in filter_or_user_id.roles]
                if filter_or_user_id.max_items:
                    items = items[-filter_or_user_id.max_items:]
                return items
            
        def clear_history(user_id):
            if user_id in instance.items:
                del instance.items[user_id]
                
        instance.save_item = Mock(side_effect=save_item)
        instance.get_items = Mock(side_effect=get_items)
        instance.clear_history = Mock(side_effect=clear_history)
        
        # Mock the file operations
        with patch('quepasa.searcher.history.storage.QuepasaFiles') as mock_files:
            files_instance = mock_files.return_value
            files_instance.set_json_zlib = Mock(return_value=None)
            files_instance.get_json_zlib = Mock(return_value=None)
            files_instance.exists = Mock(return_value=True)
            files_instance.get_files = Mock(return_value=[])
            yield instance

@pytest.fixture
def history_manager(mock_config, mock_storage):
    manager = HistoryManager(mock_config)
    manager.storage = mock_storage
    return manager

@pytest.fixture
def auth_manager(mock_config):
    return AuthManager(mock_config)

def test_history_save_message(history_manager, mock_storage):
    history_manager.save_message(
        user_id="test_user",
        role="user",
        content="test question"
    )
    
    # Verify item was saved
    items = mock_storage.get_items("test_user")
    assert len(items) == 1
    assert items[0].content == "test question"
    assert items[0].role == "user"

def test_history_get_conversation_history(history_manager, mock_storage):
    # Add multiple items
    for i in range(3):
        history_manager.save_message(
            user_id="test_user",
            role="user",
            content=f"test question {i}"
        )
    
    # Get conversation history
    items = history_manager.get_conversation_history("test_user")
    assert len(items) == 3
    assert all(item.role == "user" for item in items)

def test_history_delete(history_manager, mock_storage):
    # Add an item
    history_manager.save_message(
        user_id="test_user",
        role="user",
        content="test question"
    )
    
    # Get the item's ID
    items = mock_storage.get_items("test_user")
    assert len(items) == 1
    
    # Delete the item
    mock_storage.clear_history("test_user")
    assert len(mock_storage.get_items("test_user")) == 0

def test_history_clear(history_manager, mock_storage):
    # Add multiple items
    for i in range(3):
        history_manager.save_message(
            user_id="test_user",
            role="user",
            content=f"test question {i}"
        )
    
    assert len(mock_storage.get_items("test_user")) == 3
    
    # Clear history
    mock_storage.clear_history("test_user")
    assert len(mock_storage.get_items("test_user")) == 0

def test_auth_bearer(auth_manager, mock_config):
    """Test Bearer token authentication"""
    # Configure mock to reject this specific token
    mock_config.validate_auth_token.return_value = False
    
    headers = {
        "Authorization": "Bearer invalid_token"
    }
    result = auth_manager.authenticate_request(headers)
    assert not result.is_authorized
    assert "Invalid Bearer token" in result.error

def test_auth_invalid_header(auth_manager):
    """Test invalid authorization header"""
    headers = {
        "Authorization": "Invalid"  # Invalid format
    }
    result = auth_manager.authenticate_request(headers)
    assert not result.is_authorized
    assert "Invalid Authorization header format" in result.error 