import pytest
from unittest.mock import patch, MagicMock
from configuration.main.default import QuepasaConfigurationHub
from src.lib.constants import SOURCE_ALL, DOCUMENT_TYPE_DIALOG, DOCUMENT_TYPE_DOCUMENT
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.web import WebSearchResult

def test_quepasa_client_initialization():
    """Test basic initialization of QuepasaClient"""
    client = QuepasaConfigurationHub.from_client_code("test_client")
    assert client.client_code == "test_client"

def test_quepasa_client_mixin_inheritance():
    """Test that QuepasaClient inherits all mixin functionality"""
    client = QuepasaConfigurationHub.from_client_code("test_client")
    
    # Test Answer mixin
    assert hasattr(client, "get_SMTH_WENT_WRONG_constant")
    assert hasattr(client, "get_I_DONT_KNOW_constant")
    
    # Test Auth mixin
    assert hasattr(client, "get_telegram_access_token")
    assert hasattr(client, "get_telegram_bot_name")
    
    # Test Indexer mixin
    assert hasattr(client, "get_telegram_sources")
    assert hasattr(client, "get_pipeline_priority")
    
    # Test Question Classification mixin
    assert hasattr(client, "get_history_config")
    
    # Test Question mixin
    assert hasattr(client, "get_source_reference")
    assert hasattr(client, "get_source_reference_object")
    
    # Test Searcher mixin
    assert hasattr(client, "should_stream_telegram_responses")
    
    # Test Source Reference mixin
    assert hasattr(client, "get_source_reference")
    assert hasattr(client, "get_source_reference_object")
    
    # Test Telegram mixin
    assert hasattr(client, "allows_telegram_direct_questions")
    assert hasattr(client, "allows_telegram_direct_questions_in_topic")
    
    # Test Translation mixin
    assert hasattr(client, "get_history_config")
    
    # Test User History mixin
    assert hasattr(client, "get_history_config")

@pytest.mark.parametrize("method_name,expected_type", [
    ("get_SMTH_WENT_WRONG_constant", str),
    ("get_I_DONT_KNOW_constant", str),
    pytest.param("get_telegram_access_token", (str, type(None)), marks=pytest.mark.slow),
    pytest.param("get_telegram_bot_name", (str, type(None)), marks=pytest.mark.slow),
    ("get_telegram_sources", list),
    ("get_pipeline_priority", int),
    ("should_stream_telegram_responses", bool),
])
def test_quepasa_client_method_return_types(method_name, expected_type):
    """Test return types of various QuepasaClient methods"""
    client = QuepasaConfigurationHub.from_client_code("test_client")
    method = getattr(client, method_name)
    
    # Handle methods with different parameter requirements
    if method_name in ["get_SMTH_WENT_WRONG_constant", "get_I_DONT_KNOW_constant"]:
        request = QuepasaRequest(
            client="test_client",
            question="test question",
            source="telegram",
            language="en"
        )
        client.set_request(request)
        result = method(source=request.source, language_code=request.language)
    elif method_name == "get_history_config":
        result = method({})
    else:
        result = method()
    
    if isinstance(expected_type, tuple):
        assert isinstance(result, expected_type), f"{method_name} should return one of {expected_type}"
    else:
        assert isinstance(result, expected_type), f"{method_name} should return {expected_type}"

def test_quepasa_client_telegram_methods():
    """Test Telegram-specific functionality"""
    client = QuepasaConfigurationHub.from_client_code("test_client")
    
    # Test direct questions in regular chat
    assert client.allows_telegram_direct_questions({"chat": {"type": "private"}}) is False
    assert client.allows_telegram_direct_questions({"chat": {"type": "group"}}) is False
    
    # Test direct questions in topics
    assert client.allows_telegram_direct_questions_in_topic("General") is False
    assert client.allows_telegram_direct_questions_in_topic("Questions") is False
    
    # Test streaming behavior
    assert client.should_stream_telegram_responses() is True

def test_quepasa_client_source_reference():
    """Test source reference functionality"""
    client = QuepasaConfigurationHub.from_client_code("test_client")
    
    # Test dialog reference
    dialog_item = QuepasaDocument(
        root_id="1",
        id="1",
        chunk_index=0,
        client="test_client",
        domain="test_domain",
        provider="test_provider",
        type=DOCUMENT_TYPE_DIALOG,
        kind="dialog",
        level="info",
        url=None,
        language="en",
        title=None,
        keywords=[],
        text="Example",
        tokens=0,
        chunks=1,
        start_position=0,
        end_position=0,
        created_at="2024-01-08",
        updated_at=None,
        embeddings=None,
        score=0.8
    )
    dialog_ref = client.get_source_reference(SOURCE_ALL, dialog_item)
    assert "💬 Jan 8" in dialog_ref
    
    # Test document reference
    webpage_item = WebSearchResult(
        url="https://example.com",
        title="Example",
        snippet="Example",
        text="Example"
    )
    webpage_ref = client.get_source_reference(SOURCE_ALL, webpage_item)
    assert "🌐 example.com" in webpage_ref

@pytest.mark.parametrize("client_code", [
    "test_client",
    "",  # Empty string
    "123",  # Numeric string
    "test-client",  # With hyphen
    "test_client_with_long_name",  # Long name
])
def test_quepasa_client_various_client_codes(client_code):
    """Test QuepasaClient initialization with various client codes"""
    client = QuepasaConfigurationHub.from_client_code(client_code)
    assert client.client_code == client_code
    
    # Basic functionality should work with any client code
    assert isinstance(client.get_telegram_sources(), list)
    assert isinstance(client.get_pipeline_priority(), int)
    assert isinstance(client.should_stream_telegram_responses(), bool)

@pytest.mark.parametrize("source,language_code", [
    ("telegram", "en"),
    ("web", "en"),
    ("api", "es"),
    ("", ""),  # Edge case with empty values
    (None, None),  # Edge case with None values
])
def test_quepasa_client_answer_constants(source, language_code):
    """Test answer constants with various inputs"""
    client = QuepasaConfigurationHub.from_client_code("test_client")
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source=source or "telegram",
        language=language_code
    )
    client.set_request(request)
    
    # Test error message
    error_msg = client.get_SMTH_WENT_WRONG_constant(source=source, language_code=language_code)
    assert isinstance(error_msg, str)
    assert len(error_msg) > 0
    
    # Test don't know message
    dont_know_msg = client.get_I_DONT_KNOW_constant(source=source, language_code=language_code)
    assert isinstance(dont_know_msg, str)
    assert len(dont_know_msg) > 0

@pytest.mark.parametrize("message", [
    {"chat": {"type": "private"}},
    {"chat": {"type": "group"}},
    {"chat": {"type": "supergroup"}},
    {"chat": {"type": "channel"}},
    {"chat": {}},  # Empty chat
    {},  # Empty message
    None,  # None message
])
def test_quepasa_client_telegram_message_handling(message):
    """Test Telegram message handling with various message types"""
    client = QuepasaConfigurationHub.from_client_code("test_client")
    assert isinstance(client.allows_telegram_direct_questions(message), bool)

@pytest.mark.parametrize("item", [
    QuepasaDocument(
        root_id="1",
        id="1",
        chunk_index=0,
        client="test_client",
        domain="test_domain",
        provider="test_provider",
        type=DOCUMENT_TYPE_DIALOG,
        kind="dialog",
        level="info",
        url=None,
        language="en",
        title=None,
        keywords=[],
        text="Example",
        tokens=0,
        chunks=1,
        start_position=0,
        end_position=0,
        created_at="2024-01-08",
        updated_at=None,
        embeddings=None,
        score=0.8
    ),
    WebSearchResult(
        url="https://example.com",
        title="Example",
        snippet="Example",
        text="Example"
    ),
])
def test_quepasa_client_source_reference_handling(item):
    """Test source reference handling with various items"""
    client = QuepasaConfigurationHub.from_client_code("test_client")
    
    # Test reference string
    ref = client.get_source_reference(SOURCE_ALL, item)
    assert isinstance(ref, str)
    assert len(ref) > 0
    
    # Test reference object
    ref_obj = client.get_source_reference_object(SOURCE_ALL, item)
    assert hasattr(ref_obj, "label")
    assert hasattr(ref_obj, "source_type")

@pytest.mark.parametrize("data", [
    {},  # Empty data
    {"user_id": 123},  # With user ID
    {"chat_type": "private"},  # With chat type
    {"language": "en"},  # With language
    None,  # None data
])
def test_quepasa_client_history_config(data):
    """Test history configuration with various data"""
    client = QuepasaConfigurationHub.from_client_code("test_client")
    config = client.get_history_config()
    
    assert hasattr(config, "forget_after_seconds")
    assert hasattr(config, "max_last_messages")
    assert hasattr(config, "use_roles")
    assert isinstance(config.forget_after_seconds, int)
    assert isinstance(config.max_last_messages, int)
    assert isinstance(config.use_roles, list) 