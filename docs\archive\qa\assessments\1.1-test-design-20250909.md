# Test Design: Story 1.1 - Foundational Data Model and Storage Schema Update

Date: 2025-09-09  
Designer: <PERSON> (Test Architect)  
Story: 1.1 - Foundational Data Model and Storage Schema Update

## Test Strategy Overview

- **Total test scenarios**: 12
- **Unit tests**: 8 (67%)
- **Integration tests**: 4 (33%)
- **E2E tests**: 0 (0%) - Not needed for foundational model changes
- **Priority distribution**: P0: 6, P1: 4, P2: 2

**Testing Philosophy**: This foundational story focuses heavily on unit tests for model validation and integration tests for schema compatibility. No E2E tests are needed as changes are internal data model updates without user-facing functionality.

## Test Scenarios by Acceptance Criteria

### AC1: QuepasaDocument Model Updated with SKU and Metadata Fields

**Risk Mitigation**: Addresses TECH-002 (Critical Field Population Gap) from risk profile

#### Unit Test Scenarios

| ID            | Level | Priority | Test Description                                    | Justification                           |
|---------------|-------|----------|-----------------------------------------------------|-----------------------------------------|
| 1.1-UNIT-001  | Unit  | P0       | Validate QuepasaDocument with sku field present    | Critical field must be correctly added  |
| 1.1-UNIT-002  | Unit  | P0       | Validate QuepasaDocument with metadata field present | New metadata field validation          |
| 1.1-UNIT-003  | Unit  | P0       | Test QuepasaDocument serialization with new fields | Prevent serialization breaking changes |
| 1.1-UNIT-004  | Unit  | P1       | Test QuepasaDocument with None values for new fields | Default value behavior validation      |

#### Integration Test Scenarios

| ID            | Level       | Priority | Test Description                                    | Justification                           |
|---------------|-------------|----------|-----------------------------------------------------|-----------------------------------------|
| 1.1-INT-001   | Integration | P0       | Validate model instantiation in searcher context   | Ensure model works in actual service    |

### AC2: Elasticsearch Mapping Updated with Non-Indexed Metadata Field

**Risk Mitigation**: Addresses TECH-001 (Schema Migration Risk) and PERF-001 (Performance Impact)

#### Unit Test Scenarios

| ID            | Level | Priority | Test Description                                    | Justification                           |
|---------------|-------|----------|-----------------------------------------------------|-----------------------------------------|
| 1.1-UNIT-005  | Unit  | P0       | Validate metadata field in mapping configuration   | Ensure field correctly configured       |
| 1.1-UNIT-006  | Unit  | P0       | Verify metadata field has "index": false property  | Critical for performance - non-indexed |

#### Integration Test Scenarios

| ID            | Level       | Priority | Test Description                                    | Justification                           |
|---------------|-------------|----------|-----------------------------------------------------|-----------------------------------------|
| 1.1-INT-002   | Integration | P0       | Test Elasticsearch index creation with new mapping | Validate schema migration works         |
| 1.1-INT-003   | Integration | P1       | Verify metadata field cannot be searched directly  | Confirm non-indexed behavior            |

### AC3: Document Model Updated with Metadata Field

**Risk Mitigation**: Addresses DATA-001 (Model Serialization Compatibility)

#### Unit Test Scenarios

| ID            | Level | Priority | Test Description                                    | Justification                           |
|---------------|-------|----------|-----------------------------------------------------|-----------------------------------------|
| 1.1-UNIT-007  | Unit  | P1       | Validate Document model with metadata field        | Ensure API model correctly updated      |
| 1.1-UNIT-008  | Unit  | P2       | Test Document model JSON serialization             | Prevent API response breaking changes   |

### AC4: Existing Unit Tests Pass Without Regression

**Risk Mitigation**: Addresses TECH-003 (Test Regression Risk)

#### Integration Test Scenarios

| ID            | Level       | Priority | Test Description                                    | Justification                           |
|---------------|-------------|----------|-----------------------------------------------------|-----------------------------------------|
| 1.1-INT-004   | Integration | P1       | Execute full existing test suite for models        | Regression prevention validation        |

### AC5: New Unit Tests Added for Field Validation

**Coverage**: Covered by 1.1-UNIT-001 through 1.1-UNIT-008 above

## Detailed Test Specifications

### Critical Path Tests (P0)

#### 1.1-UNIT-001: Validate QuepasaDocument with SKU Field Present
```python
def test_quepasa_document_has_sku_field():
    """Test that QuepasaDocument model includes sku field"""
    doc = QuepasaDocument(
        id="test-123",
        sku="SKU-456",
        # ... other required fields
    )
    assert hasattr(doc, 'sku')
    assert doc.sku == "SKU-456"
    assert isinstance(doc.sku, (str, type(None)))
```

#### 1.1-UNIT-002: Validate QuepasaDocument with Metadata Field Present
```python
def test_quepasa_document_has_metadata_field():
    """Test that QuepasaDocument model includes metadata field"""
    metadata_json = '{"title": "Product", "price": 29.99}'
    doc = QuepasaDocument(
        id="test-123",
        metadata=metadata_json,
        # ... other required fields
    )
    assert hasattr(doc, 'metadata')
    assert doc.metadata == metadata_json
    assert isinstance(doc.metadata, (str, type(None)))
```

#### 1.1-UNIT-003: Test QuepasaDocument Serialization with New Fields
```python
def test_quepasa_document_serialization_includes_new_fields():
    """Test that new fields are included in serialization"""
    doc = QuepasaDocument(
        id="test-123",
        sku="SKU-456",
        metadata='{"test": "data"}',
        # ... other fields
    )
    serialized = doc.to_dict()
    assert 'sku' in serialized
    assert 'metadata' in serialized
    assert serialized['sku'] == "SKU-456"
    assert serialized['metadata'] == '{"test": "data"}'
```

#### 1.1-UNIT-005: Validate Metadata Field in Mapping Configuration
```python
def test_elasticsearch_mapping_has_metadata_field():
    """Test that Elasticsearch mapping includes metadata field"""
    from configuration.main.cli_create_index import index_settings
    
    properties = index_settings["mappings"]["properties"]
    assert "metadata" in properties
    
    metadata_field = properties["metadata"]
    assert metadata_field["type"] == "text"
    assert metadata_field["index"] == False
```

#### 1.1-INT-001: Validate Model Instantiation in Searcher Context
```python
def test_quepasa_document_instantiation_in_searcher():
    """Test QuepasaDocument works correctly in searcher service context"""
    # Mock searcher service context
    doc = QuepasaDocument(
        id="test-doc",
        sku="TEST-SKU",
        metadata='{"product": "data"}',
        type="product",
        text="Test content"
    )
    
    # Test that model works in expected searcher operations
    assert doc.get('sku') == "TEST-SKU"
    assert doc.get('metadata') == '{"product": "data"}'
    assert doc.to_dict()['sku'] == "TEST-SKU"
```

#### 1.1-INT-002: Test Elasticsearch Index Creation with New Mapping
```python
def test_elasticsearch_index_creation_with_metadata_field():
    """Test that index creation succeeds with new metadata field"""
    from configuration.main.cli_create_index import index_settings
    from elasticsearch import Elasticsearch
    
    # Use test Elasticsearch instance
    es_client = get_test_elasticsearch_client()
    test_index = "test-index-metadata"
    
    # Create index with new mapping
    result = es_client.indices.create(
        index=test_index,
        body=index_settings
    )
    
    assert result['acknowledged'] == True
    
    # Verify mapping includes metadata field
    mapping = es_client.indices.get_mapping(index=test_index)
    properties = mapping[test_index]["mappings"]["properties"]
    assert "metadata" in properties
    assert properties["metadata"]["index"] == False
    
    # Cleanup
    es_client.indices.delete(index=test_index)
```

### High Priority Tests (P1)

#### 1.1-UNIT-004: Test QuepasaDocument with None Values for New Fields
```python
def test_quepasa_document_with_none_values():
    """Test QuepasaDocument behaves correctly with None values for new fields"""
    doc = QuepasaDocument(
        id="test-123",
        sku=None,
        metadata=None,
        # ... other fields
    )
    assert doc.sku is None
    assert doc.metadata is None
    
    # Test serialization handles None values
    serialized = doc.to_dict()
    # None values should not be included in serialization
    assert 'sku' not in serialized or serialized['sku'] is None
    assert 'metadata' not in serialized or serialized['metadata'] is None
```

#### 1.1-INT-003: Verify Metadata Field Cannot Be Searched Directly
```python
def test_metadata_field_not_searchable():
    """Test that metadata field is not searchable (non-indexed)"""
    es_client = get_test_elasticsearch_client()
    test_index = "test-index-search"
    
    # Create index and add document with metadata
    es_client.indices.create(index=test_index, body=index_settings)
    es_client.index(
        index=test_index,
        id="test-doc",
        body={
            "id": "test-doc",
            "metadata": '{"searchable": "content", "price": 29.99}',
            "text": "Regular searchable content"
        }
    )
    es_client.indices.refresh(index=test_index)
    
    # Search should NOT find document by metadata content
    search_result = es_client.search(
        index=test_index,
        body={"query": {"match": {"metadata": "searchable"}}}
    )
    assert len(search_result["hits"]["hits"]) == 0
    
    # But should find by regular text field
    search_result = es_client.search(
        index=test_index,
        body={"query": {"match": {"text": "searchable"}}}
    )
    assert len(search_result["hits"]["hits"]) == 1
    
    # Cleanup
    es_client.indices.delete(index=test_index)
```

#### 1.1-INT-004: Execute Full Existing Test Suite for Models
```python
def test_existing_model_tests_still_pass():
    """Integration test to ensure existing tests pass with model changes"""
    import subprocess
    
    # Run existing model tests
    result = subprocess.run([
        "python", "-m", "pytest", 
        "tests/", 
        "-k", "test_document or test_quepasa",
        "-v"
    ], capture_output=True, text=True)
    
    assert result.returncode == 0, f"Existing tests failed: {result.stdout}\n{result.stderr}"
```

## Risk Coverage Mapping

| Risk ID   | Covered By Test Scenarios | Mitigation Level |
|-----------|---------------------------|------------------|
| TECH-002  | 1.1-UNIT-001, 1.1-UNIT-002, 1.1-INT-001 | High |
| TECH-001  | 1.1-UNIT-005, 1.1-UNIT-006, 1.1-INT-002 | High |
| DATA-001  | 1.1-UNIT-003, 1.1-UNIT-007, 1.1-UNIT-008 | Medium |
| PERF-001  | 1.1-UNIT-006, 1.1-INT-003 | Medium |
| TECH-003  | 1.1-INT-004, All unit tests | High |

## Test Environment Requirements

### Unit Tests
- **Framework**: pytest
- **Dependencies**: Mock objects for Elasticsearch clients
- **Data**: Minimal test data for model instantiation
- **Execution Time**: <5 seconds total

### Integration Tests
- **Framework**: pytest with testcontainers
- **Dependencies**: Test Elasticsearch instance (Docker or embedded)
- **Data**: Test index configurations, sample documents
- **Execution Time**: <30 seconds total

## Test Data Requirements

### Model Test Data
```python
# Test QuepasaDocument data
SAMPLE_QUEPASA_DOCUMENT = {
    "id": "test-doc-123",
    "root_id": "root-123",
    "type": "product",
    "sku": "TEST-SKU-456",
    "metadata": '{"title": "Test Product", "price": 29.99, "category": "electronics"}',
    "title": "Test Product Title",
    "text": "Test product description",
    "score": 0.8
}

# Test Document data
SAMPLE_DOCUMENT = {
    "id": "api-doc-123",
    "url": "https://example.com/product/123",
    "title": "API Document Title",
    "metadata": '{"api_version": "v1", "endpoint": "/products"}'
}
```

### Elasticsearch Test Data
```python
# Test mapping configuration
TEST_INDEX_SETTINGS = {
    "settings": {"number_of_shards": 1, "number_of_replicas": 0},
    "mappings": {
        "properties": {
            "id": {"type": "keyword"},
            "sku": {"type": "keyword"},
            "metadata": {"type": "text", "index": False},
            "text": {"type": "text"}
        }
    }
}
```

## Recommended Execution Order

### Phase 1: Critical Unit Tests (P0)
1. 1.1-UNIT-001 (QuepasaDocument sku field)
2. 1.1-UNIT-002 (QuepasaDocument metadata field)
3. 1.1-UNIT-005 (Elasticsearch mapping validation)
4. 1.1-UNIT-006 (Non-indexed configuration)

### Phase 2: Critical Integration Tests (P0)
5. 1.1-INT-001 (Model instantiation in context)
6. 1.1-INT-002 (Elasticsearch index creation)

### Phase 3: Serialization Tests (P0)
7. 1.1-UNIT-003 (QuepasaDocument serialization)

### Phase 4: Secondary Tests (P1)
8. 1.1-UNIT-004 (None value handling)
9. 1.1-UNIT-007 (Document model validation)
10. 1.1-INT-003 (Non-searchable verification)
11. 1.1-INT-004 (Regression testing)

### Phase 5: Nice-to-Have Tests (P2)
12. 1.1-UNIT-008 (Document serialization)

## Success Criteria

- **All P0 tests pass**: Critical for story acceptance
- **P1 tests pass**: Required for quality gate
- **Zero regressions**: Existing test suite continues to pass
- **Coverage targets**: >90% unit test coverage for new fields

## Test Maintenance Notes

- **Model Evolution**: Update tests when new fields added to models
- **Schema Changes**: Update Elasticsearch mapping tests if index structure changes
- **Performance Baseline**: Establish baseline for index creation time with new mapping
- **Documentation**: Keep test data examples in sync with actual implementation

## Quality Gate Integration

This test design supports the following quality gate decisions:

- **PASS**: All P0 and P1 tests pass, no regressions detected
- **CONCERNS**: P0 tests pass but some P1 tests fail or regressions found
- **FAIL**: Any P0 tests fail or critical regressions discovered

**Automation Requirements**: All tests should be automated and integrated into CI/CD pipeline for continuous validation.
