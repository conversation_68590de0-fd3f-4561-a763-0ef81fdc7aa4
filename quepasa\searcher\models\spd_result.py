from dataclasses import dataclass
from typing import Dict, List, Optional, Any


@dataclass
class SPDSearchResult:
    """SPD search result document"""
    sku: Optional[str]
    title: Optional[str]
    url: Optional[str]
    metadata: Dict[str, List[any]]
    text: Optional[str] = ""

    def get(self, key: str, default: Any = None) -> Any:
        """Get attribute value by key with default fallback"""
        return getattr(self, key, default)

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary for JSON serialization"""
        return self.metadata
