from dataclasses import dataclass
from typing import Dict, List, Optional, Any

_promoted_labels = ("PINNED", "BOOSTED", "SPONSORED")


@dataclass
class SPDSearchResult:
    """SPD search result document"""
    sku: Optional[str]
    title: Optional[str]
    url: Optional[str]
    metadata: Optional[Dict[str, List[Any]]]
    text: Optional[str] = ""

    def get(self, key: str, default: Any = None) -> Any:
        """Get attribute value by key with default fallback"""
        return getattr(self, key, default)

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary for JSON serialization"""
        return self.metadata

    def get_product_id(self) -> Optional[str]:
        """Extract product ID"""
        return self.sku

    def is_promoted(self) -> bool:
        """Check if the product is promoted"""
        return self.metadata and any(label in _promoted_labels for label in self.metadata.get("labels", []))
