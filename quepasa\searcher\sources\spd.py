from typing import Union, Generator

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.lib.markdown_converter import product_to_markdown
from .base import BaseSource
from .mixins import AnswerRetrieverMixin
from ..core.spd_search import SPDSearchManager
from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse, QuepasaStreamAnswer
from ..sources.base import STATUS_SUCCESS

logger = QuepasaLogger().get_instance(__name__)


class SPDSource(BaseSource, AnswerRetrieverMixin):
    """SPD source for retrieval augmented generation"""

    def __init__(self):
        """Initialize SPD source"""
        super().__init__()
        self.search_engine = SPDSearchManager()

    def search(self, request: QuepasaRequest) -> QuepasaResponse:
        """Execute search request
        
        Args:
            request: Search request
            
        Returns:
            Search response
        """
        try:
            # Get search results
            documents = self.search_engine.search(request)

            # Process search results using mixin
            return QuepasaResponse(
                status=STATUS_SUCCESS,
                data=documents
            )

        except Exception as e:
            return self._handle_error(request, f"Error searching: {str(e)}")

    def get_answer(self, request: QuepasaRequest, stream: bool = False) -> Union[
        QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """Get answer for request
        
        Args:
            request: Search request
            stream: Whether to stream the response
            
        Returns:
            Search response or generator of stream responses
        """
        try:
            # Get search results
            documents = self.search_engine.search(request)
            
            # add $.text as markdown formatted product description
            for i, doc in enumerate(documents):
                doc.text = product_to_markdown(doc.metadata)

            # Process search results using mixin
            return self.retrieve_answer(request, request.source, documents, stream)

        except Exception as e:
            return self._handle_error(request, f"Error getting answer: {str(e)}", stream)
