import pytest
import json
import uuid
from unittest.mock import patch, MagicMock

from quepasa.common.bearer import RequestAuthenticatorManager
from quepasa.searcher.api.http import HTTPHandler
from quepasa.searcher.models.request import QuepasaRequest, UserInfo
from configuration.main.default import QuepasaConfigurationHub


@pytest.fixture
def mock_config():
    """Mock configuration fixture"""
    config = MagicMock(spec=QuepasaConfigurationHub)
    config.request = QuepasaRequest(
        question="test question",
        client="test_client",
        protocol="http",
        request_id="test-request-id-123"
    )
    config.validate_auth_token.return_value = True
    return config


@pytest.fixture
def http_handler(mock_config):
    """HTTP handler fixture"""
    with patch('quepasa.searcher.api.base.HistoryManager'), \
         patch('quepasa.searcher.api.base.AuthManager'), \
         patch('quepasa.searcher.api.base.RequestAuthenticatorManager'), \
         patch('quepasa.searcher.api.base.SourceFactory'):
        handler = HTTPHandler(mock_config)
        return handler


class TestConversationEndpoint:
    """Test conversation endpoint functionality"""

    def test_conversation_with_valid_client_key_headers_success(self, http_handler):
        """Test /conversation with valid X-Customer-Id and client-key headers -> success"""
        from quepasa.searcher.models.response import QuepasaResponse, QuepasaAnswer
        
        # Mock RequestAuthenticatorManager to return client_id for tenant configuration
        with patch.object(http_handler.request_auth_manager, 'authenticate') as mock_auth:
            mock_auth.return_value = MagicMock(
                is_authorized=True,
                client_id='test_client'
            )
            
            # Mock source factory response
            mock_answer = QuepasaAnswer(
                text="Hello! How can I help you today?",
                markdown="**Hello!** How can I help you today?"
            )
            mock_response = QuepasaResponse(status="success", data=mock_answer)
            http_handler.source_factory.get_answer.return_value = mock_response
            
            chat_request = {
                'message': 'Hello, I need help',
                'sessionId': 'test-session-123',
                'collection': 'products',
                'area': 'shopping',
                'visitorId': 'visitor-123'
            }
            
            http_handler.config.request.body = json.dumps(chat_request)
            
            # Simulate client being set by authentication process
            http_handler.config.request.client = 'test_client'
            
            # Act
            result = http_handler._handle_conversation()
            
            # Assert
            assert result[1] == 200  # Status code
            response_data = result[0]
            assert response_data['sessionId'] == 'test-session-123'
            assert response_data['visitorId'] == 'visitor-123'
            assert 'responseId' in response_data
            assert response_data['stream'] is False
            assert response_data['status'] == 'COMPLETE'
            assert 'timestamp' in response_data
            assert isinstance(response_data['components'], list)
            assert isinstance(response_data['actions'], list)
            
            # Verify domain was set correctly from client + collection
            assert hasattr(http_handler.config.request, 'domain')
            assert http_handler.config.request.domain == 'test_client_products'

    def test_conversation_with_missing_customer_id_unauthorized(self, http_handler, mock_config):
        """Test /conversation with missing X-Customer-Id -> 401 error"""
        # Arrange
        headers = {
            'Authorization': 'client-key test-key-123',
            'Content-Type': 'application/json'
        }
        
        # Mock authentication failure
        with patch.object(http_handler.request_auth_manager, 'authenticate') as mock_auth:
            mock_auth.return_value = MagicMock(
                is_authorized=False,
                error='Missing X-Customer-Id header'
            )
            
            # Act
            result = http_handler.handle_request('POST', '/conversation', headers)
            
            # Assert
            assert result[1] == 401
            error_response = result[0]
            assert 'error' in error_response

    def test_conversation_with_missing_authorization_header_unauthorized(self, http_handler):
        """Test /conversation with missing Authorization header -> 401 error"""
        # Arrange
        headers = {
            'X-Customer-Id': 'test_client',
            'Content-Type': 'application/json'
        }
        
        # Mock authentication failure
        with patch.object(http_handler.request_auth_manager, 'authenticate') as mock_auth:
            mock_auth.return_value = MagicMock(
                is_authorized=False,
                error='Missing Authorization header'
            )
            
            # Act
            result = http_handler.handle_request('POST', '/conversation', headers)
            
            # Assert
            assert result[1] == 401
            error_response = result[0]
            assert 'error' in error_response

    def test_bearer_auth_detects_auth_type_based_on_headers(self, http_handler):
        """Test that RequestAuthenticatorManager automatically detects auth type based on headers"""

        # Test client-key detection
        headers_client_key = {
            'X-Customer-Id': 'test_client',
            'Authorization': 'client-key test-key-123'
        }
        
        # Mock the tenant config and client key validation
        mock_tenant_config = MagicMock()
        mock_tenant_config.client_key_is_valid.return_value = True
        
        with patch('quepasa.common.bearer.get_tenant_config', return_value=mock_tenant_config):
            auth = RequestAuthenticatorManager()
            result = auth.authenticate(headers_client_key)
            assert result.is_authorized is True
            assert result.client_id == 'test_client'
        
        # Test Bearer token detection (would need valid token in real scenario)
        headers_bearer = {
            'Authorization': 'Bearer client_id:token123'
        }
        
        with patch('quepasa.common.bearer.QuepasaConfigurationHub.from_client_code') as mock_config:
            mock_config.return_value.validate_auth_token.return_value = True
            auth = RequestAuthenticatorManager()
            result = auth.authenticate(headers_bearer)
            assert result.client_id == 'client_id'

    def test_successful_minimal_chat_response_structure(self, http_handler):
        """Test successful minimal ChatResponse structure"""
        from quepasa.searcher.models.response import QuepasaResponse, QuepasaAnswer
        
        # Mock source factory response
        mock_answer = QuepasaAnswer(
            text="This is a test response",
            markdown="This is a **test** response"
        )
        mock_response = QuepasaResponse(status="success", data=mock_answer)
        http_handler.source_factory.get_answer.return_value = mock_response
        
        chat_request = {
            'message': 'Test message',
            'sessionId': 'session-123',
            'collection': 'products',
            'area': 'shopping'
        }
        
        http_handler.config.request.body = json.dumps(chat_request)
        
        # Act
        result = http_handler._handle_conversation()
        
        # Assert
        assert result[1] == 200
        response_data = result[0]
        
        # Check required fields
        required_fields = ['sessionId', 'responseId', 'timestamp', 'content', 'stream']
        for field in required_fields:
            assert field in response_data, f"Missing required field: {field}"
        
        # Check field types and values
        assert isinstance(response_data['sessionId'], str)
        assert isinstance(response_data['responseId'], str)
        assert isinstance(response_data['timestamp'], str)
        assert isinstance(response_data['content'], str)
        assert isinstance(response_data['stream'], bool)
        assert response_data['status'] == 'COMPLETE'

    def test_conversation_missing_required_fields(self, http_handler):
        """Test conversation request missing required fields"""
        # Test missing 'message' field
        incomplete_request = {
            'sessionId': 'session-123',
            'collection': 'products',
            'area': 'shopping'
        }
        
        http_handler.config.request.body = json.dumps(incomplete_request)
        
        result = http_handler._handle_conversation()
        
        assert result[1] == 400
        error_response = result[0]
        assert 'error' in error_response
        assert 'Missing required parameters: message' in error_response['error']['message']

    def test_conversation_multiple_missing_required_fields(self, http_handler):
        """Test conversation request with multiple missing required fields"""
        # Test missing 'message' and 'area' fields
        incomplete_request = {
            'sessionId': 'session-123',
            'collection': 'products'
            # Missing 'message' and 'area'
        }
        
        http_handler.config.request.body = json.dumps(incomplete_request)
        
        result = http_handler._handle_conversation()
        
        assert result[1] == 400
        error_response = result[0]
        assert 'error' in error_response
        error_message = error_response['error']['message']
        assert 'Missing required parameters:' in error_message
        assert 'message' in error_message
        assert 'area' in error_message
    
    def test_conversation_invalid_json_body(self, http_handler):
        """Test conversation request with invalid JSON body"""
        http_handler.config.request.body = "invalid json {"
        
        result = http_handler._handle_conversation()
        
        assert result[1] == 400
        error_response = result[0]
        assert 'error' in error_response
        assert 'Invalid JSON' in error_response['error']['message']

    def test_conversation_missing_body(self, http_handler):
        """Test conversation request with missing body"""
        http_handler.config.request.body = None
        
        result = http_handler._handle_conversation()
        
        assert result[1] == 400
        error_response = result[0]
        assert 'error' in error_response
        assert 'Missing request body' in error_response['error']['message']

    def test_visitor_id_generation(self, http_handler):
        """Test that visitorId is generated when not provided"""
        from quepasa.searcher.models.response import QuepasaResponse, QuepasaAnswer
        
        # Mock source factory response
        mock_answer = QuepasaAnswer(
            text="Test response",
            markdown="**Test** response"
        )
        mock_response = QuepasaResponse(status="success", data=mock_answer)
        http_handler.source_factory.get_answer.return_value = mock_response
        
        chat_request = {
            'message': 'Test message',
            'sessionId': 'session-123',
            'collection': 'products',
            'area': 'shopping'
            # No visitorId provided
        }
        
        http_handler.config.request.body = json.dumps(chat_request)
        
        result = http_handler._handle_conversation()
        
        assert result[1] == 200
        response_data = result[0]
        assert 'visitorId' in response_data
        assert len(response_data['visitorId']) > 0
        # Should be a valid UUID format
        try:
            uuid.UUID(response_data['visitorId'])
        except ValueError:
            pytest.fail("Generated visitorId is not a valid UUID")

    def test_error_response_schema_compliance(self, http_handler):
        """Test that error responses match ErrorResponse schema"""
        http_handler.config.request.body = None
        
        result = http_handler._handle_conversation()
        
        assert result[1] == 400
        error_response = result[0]
        
        # Check ErrorResponse schema compliance
        assert 'error' in error_response
        assert 'code' in error_response['error']
        assert 'message' in error_response['error']
        assert 'timestamp' in error_response
        assert 'requestId' in error_response
        
        # Check error code mapping
        assert error_response['error']['code'] == 'INVALID_REQUEST'
        
        # Check timestamp format (should be ISO 8601)
        timestamp = error_response['timestamp']
        assert timestamp.endswith('Z')

    def test_user_name_extraction_from_context(self, http_handler):
        """Test that user name is extracted from context.userInfo"""
        chat_request = {
            'message': 'Hello',
            'sessionId': 'session-123',
            'collection': 'products',
            'area': 'shopping',
            'context': {
                'userInfo': {
                    'name': 'John Smith'
                }
            }
        }
        
        http_handler.config.request.body = json.dumps(chat_request)
        
        # Call _set_conversation_user_info method directly to test it
        http_handler._set_conversation_request_context()
        
        # Verify user_info is set correctly
        assert http_handler.config.request.user_info is not None
        assert http_handler.config.request.user_info.id == 'session-123'
        assert http_handler.config.request.user_info.session_id == 'session-123'
        assert http_handler.config.request.user_info.name == 'John Smith'

    def test_source_factory_integration_with_agentic(self, http_handler):
        """Test /conversation endpoint integrates with source factory using 'agentic' source"""
        from quepasa.searcher.models.response import QuepasaResponse, QuepasaAnswer, ProductItem
        
        # Mock successful source factory response with products
        mock_product = ProductItem(
            id="test-product-123",
            title="Test Product",
            url="https://example.com/product/123",
            collection="products",
            allMeta={
                "description": "Test product description",
                "price": 29.99,
                "variants": [{"size": "M", "inStock": True}]
            }
        )
        
        mock_answer = QuepasaAnswer(
            text="Here are some products for you",
            markdown="Here are some **products** for you",
            products=[mock_product]
        )
        
        mock_response = QuepasaResponse(
            status="success",
            data=mock_answer
        )
        
        http_handler.source_factory.get_answer.return_value = mock_response
        
        chat_request = {
            'message': 'Show me some products',
            'sessionId': 'session-123',
            'collection': 'products',
            'area': 'shopping'
        }
        
        http_handler.config.request.body = json.dumps(chat_request)
        
        result = http_handler._handle_conversation()
        
        # Verify successful response
        assert result[1] == 200
        response_data = result[0]
        
        # Check that source was set to "agentic"
        assert http_handler.config.request.source == "agentic"
        
        # Check that question was set from message
        assert http_handler.config.request.question == 'Show me some products'
        
        # Verify source factory was called with stream=False
        http_handler.source_factory.get_answer.assert_called_once_with(stream=False)
        
        # Check ChatResponse structure
        assert response_data['content'] == "Here are some **products** for you"
        assert response_data['stream'] is False
        assert response_data['status'] == 'COMPLETE'
        assert 'components' in response_data
        assert len(response_data['components']) == 1
        assert response_data['components'][0]['componentType'] == 'PRODUCT_SET'

    def test_response_format_switching_markdown_to_plaintext(self, http_handler):
        """Test response format switching between markdown and plain_text"""
        from quepasa.searcher.models.response import QuepasaResponse, QuepasaAnswer
        
        # Mock response with both markdown and text content
        mock_answer = QuepasaAnswer(
            text="Here are some products for you",
            markdown="Here are some **products** for you"
        )
        
        mock_response = QuepasaResponse(
            status="success",
            data=mock_answer
        )
        
        http_handler.source_factory.get_answer.return_value = mock_response
        
        chat_request = {
            'message': 'Show me products',
            'sessionId': 'session-123',
            'collection': 'products',
            'area': 'shopping'
        }
        
        http_handler.config.request.body = json.dumps(chat_request)
        
        # Test with format=plain_text (simulate URL: /conversation?format=plain_text)
        http_handler._current_url = '/conversation?format=plain_text'
        
        result = http_handler._handle_conversation()
        
        assert result[1] == 200
        response_data = result[0]
        
        # Should return plain text content, not markdown
        assert response_data['content'] == "Here are some products for you"

    def test_source_factory_error_handling(self, http_handler):
        """Test error handling for source factory failures"""
        from quepasa.searcher.models.response import QuepasaResponse
        
        # Mock source factory error response
        mock_error_response = QuepasaResponse(
            status="error",
            error="Failed to process query"
        )
        
        http_handler.source_factory.get_answer.return_value = mock_error_response
        
        chat_request = {
            'message': 'Test query',
            'sessionId': 'session-123',
            'collection': 'products',
            'area': 'shopping'
        }
        
        http_handler.config.request.body = json.dumps(chat_request)
        
        result = http_handler._handle_conversation()
        
        # Should return proper error response
        assert result[1] == 500
        error_response = result[0]
        assert 'error' in error_response
        assert error_response['error']['code'] == 'INTERNAL_SERVER_ERROR'
        assert 'Failed to process query' in error_response['error']['message']

    def test_chatrequest_model_validation(self, http_handler):
        """Test ChatRequest model parsing and validation"""
        from quepasa.searcher.models.response import ChatRequest
        
        # Test valid request
        valid_data = {
            'message': 'Hello',
            'sessionId': 'session-123',
            'collection': 'products',
            'area': 'shopping',
            'visitorId': 'visitor-456',
            'context': {
                'userInfo': {
                    'name': 'John Smith'
                }
            },
            'options': {
                'includeHistory': True,
                'debug': False
            }
        }
        
        chat_request = ChatRequest.from_dict(valid_data)
        
        assert chat_request.message == 'Hello'
        assert chat_request.sessionId == 'session-123'
        assert chat_request.collection == 'products'
        assert chat_request.area == 'shopping'
        assert chat_request.visitorId == 'visitor-456'
        assert chat_request.context.userInfo.name == 'John Smith'
        assert chat_request.options.includeHistory is True

    def test_chatresponse_schema_compliance(self, http_handler):
        """Test ChatResponse schema compliance with required fields"""
        from quepasa.searcher.models.response import QuepasaResponse, QuepasaAnswer
        
        mock_answer = QuepasaAnswer(
            text="Test response",
            markdown="**Test** response"
        )
        
        mock_response = QuepasaResponse(
            status="success",
            data=mock_answer
        )
        
        http_handler.source_factory.get_answer.return_value = mock_response
        
        chat_request = {
            'message': 'Test',
            'sessionId': 'session-123',
            'collection': 'products',
            'area': 'shopping'
        }
        
        http_handler.config.request.body = json.dumps(chat_request)
        
        result = http_handler._handle_conversation()
        
        assert result[1] == 200
        response_data = result[0]
        
        # Check all required ChatResponse fields
        required_fields = ['sessionId', 'responseId', 'timestamp', 'content', 'stream']
        for field in required_fields:
            assert field in response_data, f"Missing required field: {field}"
        
        # Check field types and formats
        assert isinstance(response_data['stream'], bool)
        assert response_data['timestamp'].endswith('Z')  # ISO 8601 format
        assert len(response_data['responseId']) > 0

    def test_client_domain_configuration(self, http_handler):
        """Test that domain is correctly set from client + collection"""
        from quepasa.searcher.models.response import QuepasaResponse, QuepasaAnswer
        
        # Mock source factory response
        mock_answer = QuepasaAnswer(
            text="Test response",
            markdown="**Test response**"
        )
        mock_response = QuepasaResponse(status="success", data=mock_answer)
        http_handler.source_factory.get_answer.return_value = mock_response
        
        chat_request = {
            'message': 'Test message',
            'sessionId': 'session-123',
            'collection': 'test_collection',
            'area': 'shopping'
        }
        
        http_handler.config.request.body = json.dumps(chat_request)
        
        # Test with client set
        http_handler.config.request.client = 'my_client'
        result = http_handler._handle_conversation()
        
        # Verify domain was created correctly
        assert result[1] == 200
        assert http_handler.config.request.domain == 'my_client_test_collection'
        
        # Test without client (should not set domain)
        http_handler.config.request.client = None
        http_handler.config.request.domain = None  # Reset domain
        result = http_handler._handle_conversation()
        
        assert result[1] == 200
        assert http_handler.config.request.domain is None
