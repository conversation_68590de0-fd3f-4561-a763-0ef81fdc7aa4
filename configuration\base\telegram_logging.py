from typing import Dict, Any, Optional, Union
from quepasa.searcher.models.request import QuepasaRequest
from .base_config import BaseConfig

class TelegramLoggingConfig(BaseConfig):
    """Base configuration for Telegram logging."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def get_telegram_log_channel_id(self) -> Optional[str]:
        """Get complete Telegram logging configuration.
        
        Args:
            data: Request data containing user info

        Previously: get_log_channel_id()
            
        Returns:
            Telegram log channel ID or None if not configured
        """
        return None
    
    def should_show_full_telegram_user_info_in_logs(self) -> bool:
        """Check if full user info should be shown in logs.
        
        Previously: is_show_full_user_info()
        
        Args:
            data: Request data containing user info
            
        Returns:
            True if full user info should be shown
        """
        return False
