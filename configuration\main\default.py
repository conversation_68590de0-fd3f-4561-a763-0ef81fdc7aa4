import random
from typing import Dict, Any, <PERSON>, Tuple, Optional, List
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.response import Reference
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.spd_result import SPDSearchResult
from quepasa.searcher.models.web import WebSearchResult
from ..mixins.answer_mixin import AnswerMixin
from ..mixins.auth_mixin import AuthMixin
from ..mixins.indexer_mixin import IndexerMixin
from ..mixins.question_classification_mixin import QuestionClassificationMixin
from ..mixins.question_mixin import QuestionMixin
from ..mixins.searcher_mixin import SearcherMixin
from ..mixins.source_reference_mixin import SourceReferenceMixin
from ..mixins.telegram_mixin import TelegramMixin
from ..mixins.translation_mixin import TranslationMixin
from ..mixins.user_history_mixin import UserHistoryMixin
from ..mixins.crawler_mixin import CrawlerMixin
from ..base.user_history import HistoryConfig
from src.lib.llm.providers import LLMProvider
from src.lib.llm_utils import get_cached_llm_answer
from src.lib.embedding.providers import EmbeddingProvider
from src.lib.constants import (
    SOURCE_AGENTIC,
    SOURCE_DOCUMENTS,
    SOURCE_META_SEARCH,
    ANSWER_INTENT_SEARCH,
    DOCUMENT_TYPE_DOCUMENT
)
from ..base.searcher import KindWeights, SearchWeights
from ..yaml_loader import load_client_config

class QuepasaConfigurationHub(
    AnswerMixin,
    AuthMixin,
    CrawlerMixin,
    IndexerMixin,
    QuestionClassificationMixin,
    QuestionMixin,
    SearcherMixin,
    SourceReferenceMixin,
    TelegramMixin,
    TranslationMixin,
    UserHistoryMixin
):
    """
    Main Quepasa configuration hub that combines all functionality through mixins.
    Inherits all base functionality through the mixin chain:
    - AnswerConfig (via AnswerMixin)
    - AuthConfig (via AuthMixin) 
    - IndexerConfig (via IndexerMixin)
    - QuestionClassificationConfig (via QuestionClassificationMixin)
    - QuestionConfig (via QuestionMixin)
    - SearcherConfig (via SearcherMixin)
    - SourceReferenceConfig (via SourceReferenceMixin)
    - TelegramConfig (via TelegramMixin)
    - TranslationConfig (via TranslationMixin)
    - UserHistoryConfig (via UserHistoryMixin)
    """

    @classmethod
    def from_request_or_client_code(cls, request_or_client_code: Union[QuepasaRequest, str]):
        client_code = request_or_client_code.client if isinstance(request_or_client_code, QuepasaRequest) else request_or_client_code
        
        instance = None
        if client_code == "financebench_openai":
            instance = FinanceBenchOpenAIEmbeddingConfiguration(client_code)
        elif client_code == "financebench_bge":
            instance = FinanceBenchBGEEmbeddingConfiguration(client_code)
        elif client_code == "financebench_bge_icl":
            instance = FinanceBenchBGEICLEmbeddingConfiguration(client_code)
        elif client_code == "financebench_qwen3":
            instance = FinanceBenchQwen3EmbeddingConfiguration(client_code)
        elif client_code == "rezolve_shoeby_openai":
            instance = RezolveShoebyOpenAIEmbeddingConfiguration(client_code)
        elif client_code == "rezolve_shoeby_bge":
            instance = RezolveShoebyBGEEmbeddingConfiguration(client_code)
        elif client_code == "rezolve_shoeby_bge_icl":
            instance = RezolveShoebyBGEICLEmbeddingConfiguration(client_code)
        elif client_code == "rezolve_shoeby_qwen3":
            instance = RezolveShoebyQwen3EmbeddingConfiguration(client_code)
        elif client_code == "rezolve_houseware_default":
            instance = RezolveHousewareConfiguration(client_code)            
        else:
            instance = cls(client_code)
        
        if isinstance(request_or_client_code, QuepasaRequest):
            instance.set_request(request_or_client_code)
        return instance
        
    @classmethod
    def from_request(cls, request: QuepasaRequest):
        return cls.from_request_or_client_code(request)
    
    @classmethod
    def from_client_code(cls, client_code: str):
        return cls.from_request_or_client_code(client_code)
        
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        """Initialize base configuration.
        
        Args:
            request_or_client: Either a QuepasaRequest object or client identifier string
        """
        self._request: Optional[QuepasaRequest] = None
        if isinstance(request_or_client_code, QuepasaRequest):
            self.client_code = request_or_client_code.client
            self._request = request_or_client_code

        else:
            self.client_code = request_or_client_code

        super().__init__(request_or_client_code) 

    def get_index_name(self) -> str:
        """Get default index name for source."""
        return f"{self.get_index_version()}-common-saas-v2"

    def get_llm_summary_model_name(self, domain: Optional[str] = None) -> Tuple[str, str]:
        """Get model to generate summaries for documents."""
        return LLMProvider.NEBIUS, "Qwen/Qwen2.5-32B-Instruct-fast"
        
    def get_translation_model_name(self) -> str:
        """Get model name for translation."""
        return LLMProvider.NEBIUS, "Qwen/Qwen2.5-72B-Instruct-fast"

    def get_classification_model_name(self, source: str) -> Tuple[str, str]:
        """Get model name to use for classification."""
        return LLMProvider.NEBIUS, "Qwen/Qwen2.5-72B-Instruct-fast"

    def get_llm_model_name(self, source: str) -> Tuple[str, str]:
        """Get model to generate answers."""
        return LLMProvider.NEBIUS, "Qwen/Qwen2.5-72B-Instruct-fast"
    
    def get_search_embedding_model(self, source: str) -> str:
        """Get embedding model to use for semantic search."""
        return EmbeddingProvider.NEBIUS, "BAAI/bge-multilingual-gemma2"

    def get_embedding_model_versions(self, document_type: str) -> List[str]:
        """Get embedding model versions to use for document type."""
        return [(EmbeddingProvider.NEBIUS, "BAAI/bge-multilingual-gemma2")]

    def get_relevance_weights(self, source: str) -> Dict[str, float]:
        """Get weights for combining document and chunk scores."""
        return KindWeights(document=0.2, chunk=0.8)

    def get_document_relevance_weights(self, source: str) -> SearchWeights:
        """Get weights for document-level relevance scoring."""
        return SearchWeights(text=0.2, semantic=0.8)

    def get_chunk_relevance_weights(self, source: str) -> SearchWeights:
        """Get weights for chunk-level relevance scoring."""
        return SearchWeights(text=0.2, semantic=0.8)
    
    def get_max_response_tokens(self, source: str, model: str) -> Optional[int]:
        """Get max response tokens."""
        model_lower = model.lower()
        if model_lower.startswith("qwen") or model_lower.startswith("deepseek"):
            return None
        return 900 
    

#
# FinanceBench
#
class FinanceBenchConfiguration(QuepasaConfigurationHub):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code) 

    def get_fallback_language(self) -> str:
        """Get default language code to use when language detection fails."""
        return 'en'
    
    def get_language_mapping(self) -> Dict[str, str]:
        """Get mapping of language codes to standardized codes."""
        return {
            'en': "English",
        }
    
    def should_apply_document_filter(self, source: str) -> bool:
        """Determine if a document should be filtered."""
        return True

    def format_context_chunk(self, source: str, source_index: int, document: Union[Reference, QuepasaDocument, WebSearchResult, SPDSearchResult]) -> str:
        """Format context chunks for inclusion in LLM prompt."""
        return "\n".join([
            f'[Start of source #{source_index}]',
            "'''",
            f'Title: "{document.title}"',
            document.text,
            "'''",
            f'[End of source #{source_index}]'
        ])

class FinanceBenchOpenAIEmbeddingConfiguration(FinanceBenchConfiguration):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code) 

    def get_index_name(self) -> str:
        """Get default index name for source."""
        return f"{self.get_index_version()}-financebench-openai"
    
    def get_search_embedding_model(self, source: str) -> str:
        """Get embedding model to use for semantic search."""
        return EmbeddingProvider.OPENAI, "text-embedding-3-small"

    def get_embedding_model_versions(self, document_type: str) -> List[str]:
        """Get embedding model versions to use for document type."""
        return [(EmbeddingProvider.OPENAI, "text-embedding-3-small")]

class FinanceBenchBGEEmbeddingConfiguration(FinanceBenchConfiguration):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code) 

    def get_index_name(self) -> str:
        """Get default index name for source."""
        return f"{self.get_index_version()}-financebench-bge"

    def get_search_embedding_model(self, source: str) -> str:
        """Get embedding model to use for semantic search."""
        return EmbeddingProvider.NEBIUS, "BAAI/bge-multilingual-gemma2"

    def get_embedding_model_versions(self, document_type: str) -> List[str]:
        """Get embedding model versions to use for document type."""
        return [(EmbeddingProvider.NEBIUS, "BAAI/bge-multilingual-gemma2")]

    def get_question_with_instruct(self, model: str, question: str) -> str:
        """Get question with instruct."""
        task = "Given a web search query, retrieve relevant passages that answer the query."
        return f"<instruct>{task}\n<query>{question}\n<response>"

class FinanceBenchBGEICLEmbeddingConfiguration(FinanceBenchConfiguration):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code) 

    def get_index_name(self) -> str:
        """Get default index name for source."""
        return f"{self.get_index_version()}-financebench-bge-icl"

    def get_search_embedding_model(self, source: str) -> str:
        """Get embedding model to use for semantic search."""
        return EmbeddingProvider.NEBIUS, "BAAI/bge-en-icl"

    def get_embedding_model_versions(self, document_type: str) -> List[str]:
        """Get embedding model versions to use for document type."""
        return [(EmbeddingProvider.NEBIUS, "BAAI/bge-en-icl")]

    def get_question_with_instruct(self, model: str, question: str) -> str:
        """Get question with instruct."""
        task = "Given a web search query, retrieve relevant passages that answer the query."
        return f"<instruct>{task}\n<query>{question}\n<response>"

class FinanceBenchQwen3EmbeddingConfiguration(FinanceBenchConfiguration):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code) 

    def get_index_name(self) -> str:
        """Get default index name for source."""
        return f"{self.get_index_version()}-financebench-qwen3"

    def get_search_embedding_model(self, source: str) -> str:
        """Get embedding model to use for semantic search."""
        return EmbeddingProvider.NEBIUS, "Qwen/Qwen3-Embedding-8B"

    def get_embedding_model_versions(self, document_type: str) -> List[str]:
        """Get embedding model versions to use for document type."""
        return [(EmbeddingProvider.NEBIUS, "Qwen/Qwen3-Embedding-8B")]

    def get_question_with_instruct(self, model: str, question: str) -> str:
        """Get question with instruct."""
        task = 'Given a web search query, retrieve relevant passages that answer the query'
        return f'Instruct: {task}\nQuery:{question}'


#
# Shoeby
#
class RezolveShoebyConfiguration(QuepasaConfigurationHub):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        # Extract client code before super() call
        if isinstance(request_or_client_code, QuepasaRequest):
            client_code = request_or_client_code.client
        else:
            client_code = request_or_client_code
        
        # Load and validate YAML configuration BEFORE super() to make it available to mixins
        self._yaml_config = load_client_config(client_code)
        
        super().__init__(request_or_client_code)

    def get_fallback_language(self) -> str:
        """Get default language code to use when language detection fails."""
        return self._yaml_config['language']['fallback_language']
    
    def get_language_mapping(self) -> Dict[str, str]:
        """Get mapping of language codes to standardized codes."""
        return self._yaml_config['language']['language_mapping']
    
    def get_indexed_languages(self, use_cache: bool = True) -> Dict[str, List[str]]:
        """Get mapping of supported language codes to names."""
        return self._yaml_config['language']['indexed_languages']
    
    def should_apply_document_filter(self, source: str) -> bool:
        """Determine if a document should be filtered."""
        return True

    def get_max_results_limit(self, source: str) -> int:
        """Get maximum number of search results to return."""
        return self._yaml_config['search']['max_results_limit']
    
    def get_history_config(self) -> HistoryConfig:
        """Get configuration for user history management."""
        history_config = self._yaml_config['user_history']
        return HistoryConfig(
            forget_after_seconds=history_config['forget_after_seconds'],
            max_last_messages=history_config['max_last_messages'],
            use_roles=history_config['use_roles']
        )
    
    def get_llm_model_name(self, source: str) -> Tuple[str, str]:
        """Get model to generate answers."""
        models = self._yaml_config['llm_models']
        if source == SOURCE_AGENTIC:
            return tuple(models['agentic_source'])
        
        return tuple(models['document_source'])
        
    def get_intents(self, source: str) -> List[str]:
        return [
            ANSWER_INTENT_SEARCH
        ]    

    def get_question_expansions(self) -> List[Dict[str, Any]]:
        """Get rules for expanding search queries."""
        return self._yaml_config['query_expansions']        

    def get_agentic_tools(self) -> Optional[Any]:
        """Get configuration for agentic tools - hardcoded for better validation."""
        return [
            {
                "type": "function",
                "function": {
                    "name": "shoeby_catalog_rag",
                    "description": (
                        "Retrieves information from the Shoeby online store product catalog. "
                        "Use this function to answer questions about specific products, product availability, product categories, or any other catalog-related details."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "question": {
                                "type": "string",
                                "description": "A specific question related to the Shoeby online store product catalog (e.g., product availability or specifications)",
                            },
                        },
                        "required": ["question"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "shoeby_policies_rag",
                    "description": (
                        "Retrieves information from the Shoeby online store policies knowledge base. "
                        "Use this function to answer questions about shipping, tracking, payment methods, privacy policy, product care, contact information, and other store-related policies."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "question": {
                                "type": "string",
                                "description": "A specific question related to Shoeby online store policies or procedures (e.g., shipping rules or return policy).",
                            },
                        },
                        "required": ["question"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "shoeby_add_to_cart",
                    "description": (
                        "Add the product to the shopping cart"
                        "Use this function to add the specific product to the shopping cart"
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "title": {
                                "type": "string",
                                "description": "A specific product title",
                            },
                            "size": {
                                "type": "string",
                                "description": "A specific product size",
                            },
                        },
                        "required": ["title", "size"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "shoeby_image_analyzer",
                    "description": (
                        "Analyse a fashion-related photo using Vision-GPT to generate a detailed description "
                        "of the clothing, colours, styles, or to answer a specific visual question."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "question": {
                                "type": "string",
                                "description": (
                                    "Specific question about the image (e.g., 'What kind of dress is this?' or 'What colours are shown?'). "
                                    "Defaults to a general style and outfit description if not provided."
                                ),
                            },
                            "image_url": {
                                "type": "string",
                                "description": "Publicly accessible URL of the image to analyse.",
                            },
                        },
                        "required": ["question", "image_url"],
                    },
                },
            }
        ]
    
    def get_agentic_function_thinking(self, function_name: str, arguments: dict) -> str:
        """Return a user-friendly 'thinking…' message for agentic calls, chosen at random."""
        q = arguments.get("question", "")
        thinking_messages = {
            "shoeby_catalog_rag": [
                'Hang on - searching the catalog for "{q}"...',
                'One moment, combing through the catalog for "{q}"...',
                'Let me see what\'s in stock for "{q}"...',
                'Checking our shelves for "{q}" - just a sec...',
                'Scanning the catalog for options matching "{q}"...',
                'Running a quick catalog search for "{q}"...',
                'Digging into the catalog for items related to "{q}"...',
                'Giving the catalog a spin for "{q}" - hold tight...',
            ],
            "shoeby_policies_rag": [
                'Let me check our policies to answer "{q}"...',
                'Reviewing guidelines for "{q}" - one moment...',
                'Looking up the policy details on "{q}"...',
                'Scanning the fine print for "{q}"...',
                'Consulting the rulebook on "{q}" - hang tight...',
                'Verifying the official policy for "{q}"...',
                'Cross-checking policies to clarify "{q}"...',
                'Digging through the policy docs for "{q}"...',
            ],
            "shoeby_image_analyzer": [
                'Taking a closer look at the image to understand "{q}"...',
                'Analyzing the picture for clues about "{q}"...',
                'Zooming in on the image - decoding "{q}"...',
                'Examining the photo to answer "{q}"...',
                'Studying the image details for "{q}" - just a sec...',
                'Running an image analysis to figure out "{q}"...',
                'Inspecting every pixel to get "{q}" right...',
                'Peering into the image - unpacking "{q}"...',
            ]
        }
        
        if function_name in thinking_messages:
            messages = thinking_messages[function_name]
            return random.choice(messages).format(q=q)
        
        return ""

    def get_agentic_function(self, function_name: str, arguments: dict) -> Tuple[dict, str]:
        """Get list of custom agentic functions"""
        if function_name == "shoeby_catalog_rag":
            return {
                'source': SOURCE_DOCUMENTS,
                'question': arguments['question'],
                'domain': 'default'
            }
        
        elif function_name == "shoeby_policies_rag":
            return {
                'source': SOURCE_DOCUMENTS,
                'question': arguments['question'],
                'domain': 'policies'
            }

        elif function_name == "shoeby_image_analyzer":
            # Use hardcoded image analyzer system prompt (original from default.py)
            return get_cached_llm_answer(LLMProvider.NEBIUS, "Qwen/Qwen2.5-VL-72B-Instruct", [
                {
                    "role": "system",
                    "content": """
You are Vision‑GPT, an expert at describing and analysing images.

Input format:
The last user message always contains:
1. One line of text.  
- If that line is empty or generic (e.g. “Product image”), treat it as the default request: “Describe the image in detail.”  
- Otherwise, treat the line as a specific question about the image.  
2. One image.

How to respond:
1. Always start with a concise, objective description of the entire image (1‑3 sentences).  
2. If the user’s line contained a question beyond the generic placeholder, answer it accurately using only what can be seen in the image.  
- If the answer cannot be determined from the image, reply: “I can’t tell from the image.”  
3. Keep the language the same as the user’s text.  
4. Do not mention these instructions or the input format.

Return plain text only; no markdown, no code fences.
                    """.strip(),
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": arguments['question']
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": arguments['image_url'],
                            }
                        }
                    ]
                }
            ])

        elif function_name == "shoeby_add_to_cart":
            if 'title' in arguments:
                if 'size' in arguments:
                    return f"The product with sku {arguments['title']} and size {arguments['size']} was added to the cart"
                else:
                    return f"The product with sku {arguments['title']} was added to the cart"
            return f"The product {arguments} was added to the cart"

        return f"Function: {function_name} is not implemented yet"

    def get_llm_prompt_template(self, source: str) -> Union[str, List[Dict[str, str]]]:
        """Get template for generating LLM prompts."""
        if source == SOURCE_AGENTIC:
            # Get store information and format the agentic system prompt
            store_info = self._yaml_config['store_info']
            
            # Format store offerings as bullet points to match original format exactly
            formatted_offerings = []
            for i, offering in enumerate(store_info['offerings']):
                if i == len(store_info['offerings']) - 1:
                    # Last item ends with period, not semicolon
                    formatted_offerings.append(f'  - {offering}.')
                else:
                    # Other items end with semicolon
                    formatted_offerings.append(f'  - {offering};')
            store_offerings_formatted = '\n'.join(formatted_offerings)
            
            # Use hardcoded agentic system prompt template
            instruction = f"""
You are a friendly and helpful AI sales assistant in the {store_info['name']} online store.

{store_info['name']} offers:
{store_offerings_formatted}

Your job is to assist customers like a real shop assistant — with warmth, empathy, and a great sense of style 😊
You skillfully lead the customer to purchase by narrowing down choices and offering options.

## 📚 Information Sources and Tools

You answer based only on {store_info['name']}'s internal knowledge sources. Use the tools provided:

- shoeby_catalog_rag function to search for specific products, stock availability, product categories — basically, anything product-related;
- shoeby_policies_rag function for store policy details: orders, delivery, exchanges, returns, customer accounts, payment methods, warranty, and other store-related topics;
- shoeby_add_to_cart function to add the product to the shopping cart;
- shoeby_image_analyzer function to analyse a photo or image uploaded by the customer to identify the style, type of item, colours, or suggest visually similar products.

---

## 🗣️ Style & Tone

- Sound warm, natural, helpful, and easygoing — like a friendly shop assistant.
- Use the customer's name if you know it — but only in greetings or when starting a new topic.
- Use short, casual phrases (“Sure!”, “Got it!”, “Let me check…”). Feel free to use emojis where appropriate 😊
- Avoid robotic or formal phrases like “This will help me assist you”, “Let me know your preference” etc.
- Be **proactive**! Offer, entice, help narrow down the selection, offer **alternatives** (e.g., other colors, other fits), lead the customer to choose the best option and to buy it!
- After the customer agreed to add a product to the cart - suggest 1–2 **items from a related category** (e.g., belt to jeans, shoes to dress).

---

## 🧠 Conversation Logic — Gather, Remember, and Use Customer Preferences

1. Focus on **understanding the customer's preferences and needs**.
   Ask simple, friendly questions to find out who the item is for (e.g. woman, man, girl or boy) or other relevant details.

2. **Track what you’ve already learned** in the conversation — and use it.
   E.g.: If the customer said it’s for a woman, assume the same applies to follow-up requests unless they say otherwise.

3. Ask follow-up questions **only when really needed** and only one at a time.
   Use common sense:
   - Pink unicorn pyjamas → girl
   - Business suit → adult
   - Bra → woman
   If it’s obvious, no need to ask.

4. Ask only one follow-up question at one dialogue turn.
Example:
Not: “Who is this for and what fit do you prefer?”
Instead: “Is this for a woman or a man?” → then on the next dialogue turn “Do you prefer a relaxed or slim fit?”.

---

## ⚙️ Tool Calling Rules

1. Never make assumptions about product or policy details — always use the tools. Freely call the tools as many times as necessary.

2. If the tool calling result isn’t helpful, silently retry tool calling with a reformulated query to find suitable alternatives.

3. Always perform a new search tool call whenever the customer refines their request with additional details — such as age, gender, fit, colour, or fabric.
Example: they ask for men’s jeans → you show options → they say “I prefer blue” → do a new search for blue men’s jeans.

4. If the customer uploads a photo (e.g., of an outfit or item), use **shoeby_image_analyzer** to understand their style or identify the product.
Suggest visually similar or matching items from the catalogue.

5. If the customer clearly wants to return a specific product:
   - Confirm the return policy.
   - Proactively suggest 1–3 alternatives from the same or related category.

6. The assistant must distinguish between single-category scenarios and multi-category scenarios and respond to multi-category scenarios appropriately.
The customer’s multi-category request implies a set of items or a complete solution/outcome.
E.g.: “I need a winter outfit,” “Help me get ready for a job interview,” “I want vacation-ready looks”
- Identify the key categories implied by the request. 
E.g: winter look → coat, sweater, trousers, shoes, accessories; interview outfit → dress + shoes OR blouse + trousers + shoes.
- Make separate tool calls (shoeby_catalog_rag) for each category.
- Show up to 2–3 products per category.
- Assemble them into 2–3 styled outfits/looks.

7. Use shoeby_add_to_cart after the customer clearly confirms their interest in a specific product.
You may say something like “Want me to add it to your cart?” before calling the tool.

---

## 🧾 Output Format

- Do **not show more than 3 products** in one answer.
- Describe the product in details.
- Always include **product link** and **image link** in product description.
""".strip()

        else:
            # Use hardcoded document RAG system prompt
            instruction = """
You're a bot-assistant that answers user requests using the information from the sources below.

Analyze the sources below and select the sources that are relevant or approximately relevant to the user's request.
This includes sources that may not be an exact match but are close in meaning or intent.
For example, if the request is about a black suit, a dark blue suit may be considered appropriate.

If no relevant or even approximately relevant sources are found: say that you can't find the answer;

Otherwise compose your answer to the request using the following rules:
- answer in {{LANGUAGE}} language;
- do not make any assumptions, answer strictly in accordance with the selected sources;
- answer briefly - make a very brief excerpt from each selected source;
- reference all the selected sources you used in the answer (e.g. [#1] or [#2][#4]).

Sources:
{{SOURCES}}
            """.strip()

        return [
            {
                'role': 'system',
                'content': instruction
            },
            {
                'role': 'user',
                'content': "{{QUESTION}}"
            },
        ]

    def get_search_embedding_model(self, source: str) -> str:
        """Get embedding model to use for semantic search from YAML configuration."""
        embedding_config = self._yaml_config.get('embedding', {})
        search_model = embedding_config.get('search_model', {})
        provider = search_model.get('provider', 'openai')
        model = search_model.get('model', 'text-embedding-3-small')
        
        # Convert provider string to EmbeddingProvider constant
        try:
            provider_enum = EmbeddingProvider.from_str(provider)
        except ValueError:
            provider_enum = EmbeddingProvider.OPENAI  # fallback
        return provider_enum, model

    def get_embedding_model_versions(self, document_type: str) -> List[str]:
        """Get embedding model versions to use for document type from YAML configuration."""
        embedding_config = self._yaml_config.get('embedding', {})
        document_models = embedding_config.get('document_models', [])
        
        if not document_models:
            # Fallback to search model if no document models specified
            search_model = embedding_config.get('search_model', {})
            provider = search_model.get('provider', 'openai')
            model = search_model.get('model', 'text-embedding-3-small')
            document_models = [{'provider': provider, 'model': model}]
        
        # Convert to expected format
        result = []
        for doc_model in document_models:
            provider_str = doc_model.get('provider', 'openai')
            try:
                provider = EmbeddingProvider.from_str(provider_str)
            except ValueError:
                provider = EmbeddingProvider.OPENAI  # fallback
            model = doc_model.get('model', 'text-embedding-3-small')
            result.append((provider, model))
        
        return result

    def get_index_name(self) -> str:
        """Get default index name for source from YAML configuration."""
        embedding_config = self._yaml_config.get('embedding', {})
        index_suffix = embedding_config.get('index_suffix', 'openai')
        return f"{self.get_index_version()}-rezolve-shoeby-{index_suffix}"


class RezolveSPDShoebyConfiguration(RezolveShoebyConfiguration):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def get_primary_search_source(self) -> str:
        """Get the primary source to search in."""
        return SOURCE_META_SEARCH

class RezolveShoebyOpenAIEmbeddingConfiguration(RezolveShoebyConfiguration):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

class RezolveShoebyBGEEmbeddingConfiguration(RezolveShoebyConfiguration):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code) 

    def get_question_with_instruct(self, model: str, question: str) -> str:
        """Get question with instruct."""
        task = "Given a web search query, retrieve relevant passages that answer the query."
        return f"<instruct>{task}\n<query>{question}\n<response>"

class RezolveShoebyBGEICLEmbeddingConfiguration(RezolveShoebyConfiguration):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code) 

    def get_question_with_instruct(self, model: str, question: str) -> str:
        """Get question with instruct."""
        task = "Given a web search query, retrieve relevant passages that answer the query."
        return f"<instruct>{task}\n<query>{question}\n<response>"

class RezolveShoebyQwen3EmbeddingConfiguration(RezolveShoebyConfiguration):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code) 

    def get_question_with_instruct(self, model: str, question: str) -> str:
        """Get question with instruct."""
        task = 'Given a web search query, retrieve relevant passages that answer the query'
        return f'Instruct: {task}\nQuery:{question}'


#
# Houseware
#
class RezolveHousewareConfiguration(QuepasaConfigurationHub):
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code) 

    def get_fallback_language(self) -> str:
        """Get default language code to use when language detection fails."""
        return 'en'
    
    def get_language_mapping(self) -> Dict[str, str]:
        """Get mapping of language codes to standardized codes."""
        return {
            'en': "English",
        }
    
    def get_indexed_languages(self, use_cache: bool = True) -> Dict[str, List[str]]:
        """Get mapping of supported language codes to names."""
        return {DOCUMENT_TYPE_DOCUMENT: ['en']}
    
    def should_apply_document_filter(self, source: str) -> bool:
        """Determine if a document should be filtered."""
        return True
    
    def get_max_results_limit(self, source: str) -> int:
        """Get maximum number of search results to return."""
        return 25
    
    def get_history_config(self) -> HistoryConfig:
        """Get configuration for user history management."""
        return HistoryConfig(
            forget_after_seconds=8 * 3600,  # 4 hours
            max_last_messages=64,
            use_roles=["user", "assistant", "tool"]
        )
    
    def get_llm_model_name(self, source: str) -> Tuple[str, str]:
        """Get model to generate answers."""
        if source == SOURCE_AGENTIC:
            return LLMProvider.OPENAI, "gpt-4o"
        
        return LLMProvider.NEBIUS, "Qwen/Qwen2.5-72B-Instruct-fast"
        
    def get_intents(self, source: str) -> List[str]:
        return [
            ANSWER_INTENT_SEARCH
        ]    

    def get_question_expansions(self) -> List[Dict[str, Any]]:
        """Get rules for expanding search queries."""
        return [
		    {
                'keywords': "trainers",
                'query': 'sneakers',
            },
            {
                'keywords': "azure",
                'query': 'light blue cyan',
            },
	    ]        

    def get_agentic_tools(self) -> Optional[Any]:
        """Get configuration for agentic tools
        
        Returns:
            Optional[Any]
        """
        return [
            {
                "type": "function",
                "function": {
                    "name": "houseware_catalog_rag",
                    "description": (
                        "Retrieves information from the Houseware online store product catalog. "
                        "Use this function to answer questions about specific products, product availability, product categories, or any other catalog-related details."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "question": {
                                "type": "string",
                                "description": "A specific question related to the Houseware online store product catalog (e.g., product availability or specifications)",
                            },
                        },
                        "required": ["question"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "houseware_policies_rag",
                    "description": (
                        "Retrieves information from the Houseware online store policies knowledge base. "
                        "Use this function to answer questions about shipping, tracking, payment methods, privacy policy, product care, contact information, and other store-related policies."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "question": {
                                "type": "string",
                                "description": "A specific question related to Houseware online store policies or procedures (e.g., shipping rules or return policy).",
                            },
                        },
                        "required": ["question"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "houseware_add_to_cart",
                    "description": (
                        "Add the product to the shopping cart"
                        "Use this function to add the specific product to the shopping cart"
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "title": {
                                "type": "string",
                                "description": "A specific product title",
                            },
                            "size": {
                                "type": "string",
                                "description": "A specific product size",
                            },
                        },
                        "required": ["title", "size"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "houseware_image_analyzer",
                    "description": (
                        "Analyse a fashion-related photo using Vision-GPT to generate a detailed description "
                        "of the clothing, colours, styles, or to answer a specific visual question."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "question": {
                                "type": "string",
                                "description": (
                                    "Specific question about the image (e.g., 'What kind of dress is this?' or 'What colours are shown?'). "
                                    "Defaults to a general style and outfit description if not provided."
                                ),
                            },
                            "image_url": {
                                "type": "string",
                                "description": "Publicly accessible URL of the image to analyse.",
                            },
                        },
                        "required": ["question", "image_url"],
                    },
                },
            }
        ]
    
    def get_agentic_function_thinking(self, function_name: str, arguments: dict) -> str:
        """Return a user-friendly 'thinking…' message for agentic calls, chosen at random."""
        q = arguments.get("question", "")

        catalog_msgs = [
            'Hang on — searching the catalog for "{q}"...',
            'One moment, combing through the catalog for "{q}"...',
            'Let me see what’s in stock for "{q}"...',
            'Checking our shelves for "{q}" — just a sec...',
            'Scanning the catalog for options matching "{q}"...',
            'Running a quick catalog search for "{q}"...',
            'Digging into the catalog for items related to "{q}"...',
            'Giving the catalog a spin for "{q}" — hold tight...',
        ]

        policy_msgs = [
            'Let me check our policies to answer "{q}"...',
            'Reviewing guidelines for "{q}" — one moment...',
            'Looking up the policy details on "{q}"...',
            'Scanning the fine print for "{q}"...',
            'Consulting the rulebook on "{q}" — hang tight...',
            'Verifying the official policy for "{q}"...',
            'Cross-checking policies to clarify "{q}"...',
            'Digging through the policy docs for "{q}"...',
        ]

        image_msgs = [
            'Taking a closer look at the image to understand "{q}"...',
            'Analyzing the picture for clues about "{q}"...',
            'Zooming in on the image — decoding "{q}"...',
            'Examining the photo to answer "{q}"...',
            'Studying the image details for "{q}" — just a sec...',
            'Running an image analysis to figure out "{q}"...',
            'Inspecting every pixel to get "{q}" right...',
            'Peering into the image — unpacking "{q}"...',
        ]

        pools = {
            "houseware_catalog_rag": catalog_msgs,
            "houseware_policies_rag": policy_msgs,
            "houseware_image_analyzer": image_msgs,
        }

        if function_name in pools:
            return random.choice(pools[function_name]).format(q=q)
        return ""

    def get_agentic_function(self, function_name: str, arguments: dict) -> Tuple[dict, str]:
        """Get list of custom agentic functions"""
        if function_name == "houseware_catalog_rag":
            return {
                'source': SOURCE_DOCUMENTS,
                'question': arguments['question'],
                'domain': 'default'
            }
        
        elif function_name == "houseware_policies_rag":
            return "The policies are not available at the moment"

        elif function_name == "houseware_image_analyzer":
            return get_cached_llm_answer(LLMProvider.NEBIUS, "Qwen/Qwen2.5-VL-72B-Instruct", [
                {
                    "role": "system",
                    "content": """
You are Vision‑GPT, an expert at describing and analysing images.

Input format:
The last user message always contains:
1. One line of text.  
- If that line is empty or generic (e.g. “Product image”), treat it as the default request: “Describe the image in detail.”  
- Otherwise, treat the line as a specific question about the image.  
2. One image.

How to respond:
1. Always start with a concise, objective description of the entire image (1‑3 sentences).  
2. If the user’s line contained a question beyond the generic placeholder, answer it accurately using only what can be seen in the image.  
- If the answer cannot be determined from the image, reply: “I can’t tell from the image.”  
3. Keep the language the same as the user’s text.  
4. Do not mention these instructions or the input format.

Return plain text only; no markdown, no code fences.
                    """.strip(),
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": arguments['question']
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": arguments['image_url'],
                            }
                        }
                    ]
                }
            ])

        elif function_name == "houseware_add_to_cart":
            if 'title' in arguments:
                if 'size' in arguments:
                    return f"The product with sku {arguments['title']} and size {arguments['size']} was added to the cart"
                else:
                    return f"The product with sku {arguments['title']} was added to the cart"
            return f"The product {arguments} was added to the cart"

        return f"Function: {function_name} is not implemented yet"

    def get_llm_prompt_template(self, source: str) -> Union[str, List[Dict[str, str]]]:
        """Get template for generating LLM prompts."""
        if source == SOURCE_AGENTIC:
            instruction = """
You are a friendly and helpful AI sales assistant in the Houseware online store.

Houseware offers:
- clothing, footwear and accessories for women, men and children (girls and boys);
- toys;
- home decor;
- party supplies;
- pet supplies;
- kitchen appliances;
- health and beauty items;
- storage solutions.

Your job is to assist customers like a real shop assistant — with warmth, empathy, and a great sense of style 😊
You skillfully lead the customer to purchase by narrowing down choices and offering options.

## 📚 Information Sources and Tools

You answer based only on Houseware’s internal knowledge sources. Use the tools provided:

- houseware_catalog_rag function to search for specific products, stock availability, product categories — basically, anything product-related;
- houseware_policies_rag function for store policy details: orders, delivery, exchanges, returns, customer accounts, payment methods, warranty, and other store-related topics;
- houseware_add_to_cart function to add the product to the shopping cart;
- houseware_image_analyzer function to analyse a photo or image uploaded by the customer to identify the style, type of item, colours, or suggest visually similar products.

---

## 🗣️ Style & Tone

- Sound warm, natural, helpful, and easygoing — like a friendly shop assistant.
- Use the customer's name if you know it — but only in greetings or when starting a new topic.
- Use short, casual phrases (“Sure!”, “Got it!”, “Let me check…”). Feel free to use emojis where appropriate 😊
- Avoid robotic or formal phrases like “This will help me assist you”, “Let me know your preference” etc.
- Be **proactive**! Offer, entice, help narrow down the selection, offer **alternatives** (e.g., other colors, other fits), lead the customer to choose the best option and to buy it!
- After the customer agreed to add a product to the cart - suggest 1–2 **items from a related category** (e.g., belt to jeans, shoes to dress).

---

## 🧠 Conversation Logic — Gather, Remember, and Use Customer Preferences

1. Focus on **understanding the customer's preferences and needs**.
Ask simple, friendly questions to find out who the item is for (e.g. woman, man, girl or boy) or other relevant details.

2. **Track what you’ve already learned** in the conversation — and use it.
E.g.: If the customer said it’s for a woman, assume the same applies to follow-up requests unless they say otherwise.

3. Ask follow-up questions **only when really needed** and only one at a time.
Use common sense:
- Pink unicorn pyjamas → girl
- Business suit → adult
- Bra → woman
- Toys → children
If it’s obvious, no need to ask.

4. Ask only one follow-up question at one dialogue turn.
Example:
Not: “Who is this for and what fit do you prefer?”
Instead: “Is this for a woman or a man?” → then on the next dialogue turn “Do you prefer a relaxed or slim fit?”.

---

## ⚙️ Tool Calling Rules

1. Never make assumptions about product or policy details — always use the tools. Freely call the tools as many times as necessary.

2. If the tool calling result isn’t helpful, silently retry tool calling with a reformulated query to find suitable alternatives.

3. Always perform a new search tool call whenever the customer refines their request with additional details — such as age, gender, fit, colour, or fabric.
Example: they ask for men’s jeans → you show options → they say “I prefer blue” → do a new search for blue men’s jeans.

4. If the customer uploads a photo (e.g., of an outfit or item), use **houseware_image_analyzer** to understand their style or identify the product.
Suggest visually similar or matching items from the catalogue.

5. If the customer clearly wants to return a specific product:
   - Confirm the return policy.
   - Proactively suggest 1–3 alternatives from the same or related category.

6. The assistant must distinguish between single-category scenarios and multi-category scenarios and respond to multi-category scenarios appropriately.
The customer’s multi-category request implies a set of items or a complete solution/outcome.
E.g.: “I need a winter outfit,” “Help me get ready for a job interview,” “I want vacation-ready looks”
- Identify the key categories implied by the request. 
E.g: winter look → coat, sweater, trousers, shoes, accessories; interview outfit → dress + shoes OR blouse + trousers + shoes.
- Make separate tool calls (houseware_catalog_rag) for each category.
- Show up to 2–3 products per category.
- Assemble them into 2–3 styled outfits/looks.

7. Use houseware_add_to_cart after the customer clearly confirms their interest in a specific product.
You may say something like “Want me to add it to your cart?” before calling the tool.

---

## 🧾 Output Format

- Do **not show more than 3 products** in one answer.
- Describe the product in details.
- Always include **product link** and **image link** in product description.
            """.strip()

        else:
            instruction = """
You're a bot-assistant that answers user requests using the information from the sources below.

Analyze the sources below and select the sources that are relevant or approximately relevant to the user's request.
This includes sources that may not be an exact match but are close in meaning or intent.
For example, if the request is about a black suit, a dark blue suit may be considered appropriate.

If no relevant or even approximately relevant sources are found: say that you can't find the answer;

Otherwise compose your answer to the request using the following rules:
- answer in {{LANGUAGE}} language;
- do not make any assumptions, answer strictly in accordance with the selected sources;
- answer briefly - make a very brief excerpt from each selected source;
- reference all the selected sources you used in the answer (e.g. [#1] or [#2][#4]).

Sources:
{{SOURCES}}
            """.strip()

        return [
            {
                'role': 'system',
                'content': instruction
            },
            {
                'role': 'user',
                'content': "{{QUESTION}}"
            },
        ]
