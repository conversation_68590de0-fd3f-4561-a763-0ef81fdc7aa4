import pytest
from unittest.mock import Mock, patch
import numpy as np

from src.lib.embedding.sbert import SBERT<PERSON>mbedding, DEFAULT_MODEL
from src.lib.embedding.providers import EmbeddingProvider
from src.lib.embedding.factory import EmbeddingFactory

def test_sbert_provider():
    """Test that SBERT provider is correctly identified."""
    embedding = SBERTEmbedding()
    assert embedding.provider == EmbeddingProvider.SBERT

@pytest.mark.integration
def test_sbert_embedding_real():
    """Integration test with real SBERT model."""
    embedding = SBERTEmbedding()
    text = "This is a test sentence"
    
    # Test with default model
    result = embedding.get_embedding(None, text)
    assert isinstance(result, list)
    assert len(result) > 0
    assert all(isinstance(x, float) for x in result)
    
    # Test with explicit default model
    result2 = embedding.get_embedding(DEFAULT_MODEL, text)
    assert isinstance(result2, list)
    assert len(result2) > 0
    assert np.allclose(result, result2)  # Should be identical

@patch('src.lib.embedding.sbert.SentenceTransformer')
def test_sbert_model_caching(mock_sbert):
    """Test that models are properly cached."""
    embedding = SBERTEmbedding()
    
    mock_model = Mock()
    mock_sbert.return_value = mock_model
    mock_model.encode.return_value = np.array([0.1, 0.2, 0.3])
    
    # First call should create new model
    embedding._get_model(DEFAULT_MODEL)
    assert mock_sbert.call_count == 1
    
    # Second call should use cached model
    embedding._get_model(DEFAULT_MODEL)
    assert mock_sbert.call_count == 1  # No additional calls

@patch('src.lib.embedding.sbert.SentenceTransformer')
def test_sbert_model_fallback(mock_sbert):
    """Test fallback to default model when specified model fails."""
    embedding = SBERTEmbedding()
    
    mock_model = Mock()
    def side_effect(model_name):
        if model_name == "invalid-model":
            raise ValueError("Model not found")
        return mock_model
    
    mock_sbert.side_effect = side_effect
    mock_model.encode.return_value = np.array([0.1, 0.2, 0.3])
    
    # Should fallback to default model
    model = embedding._get_model("invalid-model")
    assert mock_sbert.call_count == 2  # One failed attempt + one fallback
    assert model is not None

def test_factory_creates_sbert():
    """Test that factory correctly creates SBERT provider."""
    embedding = EmbeddingFactory.get_embedding('sbert')
    assert isinstance(embedding, SBERTEmbedding)
    assert embedding.provider == EmbeddingProvider.SBERT

@patch('src.lib.embedding.sbert.SentenceTransformer')
def test_embedding_none_handling(mock_sbert):
    """Test handling of None/empty inputs."""
    embedding = SBERTEmbedding()
    
    mock_model = Mock()
    mock_sbert.return_value = mock_model
    mock_model.encode.return_value = np.array([0.1, 0.2, 0.3])
    
    # Test empty string
    result = embedding.get_embedding(DEFAULT_MODEL, "")
    assert result is None
    
    # Test whitespace string
    result = embedding.get_embedding(DEFAULT_MODEL, "   ")
    assert result is None
    
    # Test None text
    result = embedding.get_embedding(DEFAULT_MODEL, None)
    assert result is None 