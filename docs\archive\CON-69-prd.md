# CON-69 Brownfield Enhancement PRD

## Project Analysis and Context

### 1.1 Existing Project Overview

**Analysis Source:** Fresh analysis based on the provided `/docs/CON-69-req.md`, and `/docs/CON-69-tests.md`.

**Current Project State:** The project, "Quepasa," is a microservices-based RAG (Retrieval-Augmented Generation) system. Its primary purpose is to manage the entire lifecycle of documents: ingestion, content extraction, transformation, indexing into Elasticsearch, and providing intelligent search capabilities through various APIs.

### 1.2 Available Documentation Analysis

Based on my analysis of the provided repomix-output.xml, here is the status of the existing project documentation:

- [x] Tech Stack Documentation
- [x] Source Tree/Architecture
- [x] Coding Standards (inferred from code)
- [x] API Documentation (inferred from API handlers)

**Assessment:** The project has good technical documentation, which is sufficient to proceed with architectural planning for this enhancement.

### 1.3 Enhancement Scope Definition

**Enhancement Type:**
- [x] New Feature Addition
- [x] Major Feature Modification

**Enhancement Description:** To implement storage-based structured product metadata. This will enhance the conversational API by returning structured JSON product data in a new products array in the response, in addition to the existing markdown text.

**Impact Assessment:**
- [x] Significant Impact (substantial existing code changes) - This enhancement will affect the data model, ingestion pipeline, data processing tasks, Elasticsearch mapping, RAG search logic, and the final API response models.

### 1.4 Goals and Background Context

**Goals:**
- Enhance API responses to include a products array containing structured JSON metadata for product-type documents.
- Maintain backward compatibility for existing API consumers.
- Introduce a new document type, product, to differentiate catalog items from other content types.
- Ensure product metadata is efficiently stored in Elasticsearch and retrieved during search operations.

**Background Context:** Currently, the API returns product information formatted as markdown within the text and markdown fields. While useful for text-based displays, this format is difficult for client applications to parse and use for structured UI components (e.g., product carousels). This enhancement introduces a dedicated, machine-readable products array to provide rich, structured data, making the API more versatile for modern e-commerce frontends.

### 1.5 Change Log

| Change | Date | Version | Description | Author |
|--------|------|---------|-------------|--------|
| Created | 2025-09-09 | 1.0 | Initial draft of Brownfield PRD for CON-69. | John (PM) |

## Requirements

### 2.1 Functional Requirements

**FR1:** The API response for conversational queries will be enhanced to include a products array, containing structured ProductItem objects, when product-type documents are retrieved.

**FR2:** A new document type with the value "product" will be introduced to differentiate product catalog items from other content types (e.g., document, dialog).

**FR3:** During ingestion, a new metadata field will be added to product type documents. This field will store a JSON string of the filtered, raw product data.

**FR4:** The data processing pipeline will populate the sku and metadata fields into the QuepasaDocument model during search retrieval from Elasticsearch.

**FR5:** The meta_search source will correctly process both RAG (QuepasaDocument) and SPD (SPDSearchResult) results, converting them into ProductItem objects for the final products array.

### 2.2 Non-Functional Requirements

**NFR1:** The metadata field in the Elasticsearch index must be configured as non-indexed ("index": false) to prevent performance degradation and unnecessary index bloat.

**NFR2:** The filtering logic for the metadata JSON string must only exclude a minimal set of internal-use keys (METADATA_EXCLUDE_KEYS), preserving all other business-relevant product data.

### 2.3 Compatibility Requirements

**CR1:** The API enhancement must be fully backward compatible. Existing API consumers who do not read the new products array must continue to function without any changes.

**CR2:** The QuepasaDocument data model in quepasa/searcher/models/document.py must be updated to include the sku and metadata fields to fix a gap from a previous implementation.

## Technical Constraints and Integration Requirements

### 3.1 Existing Technology Stack

- **Languages:** Python 3.9 / 3.11
- **Frameworks:** FastAPI, Celery, Flask
- **Database:** Elasticsearch 8.12.2, Redis 7.2
- **Infrastructure:** Docker, Kubernetes (k8s), MinIO
- **External Dependencies:** OpenAI, Anthropic, Mistral, Nebius, Replicate, HuggingFace LLM/Embedding services.

### 3.2 Integration Approach

**Database Integration Strategy:** The Elasticsearch mapping in configuration/main/cli_create_index.py must be updated to include a new non-indexed text field named metadata.

**API Integration Strategy:** The QuepasaAnswer model in quepasa/searcher/models/response.py requires the addition of a new optional field products: Optional[List[ProductItem]]. This change is non-breaking.

**Data Model Integration:** The Document model in quepasa/api/handlers/document_handler.py and the QuepasaDocument model in quepasa/searcher/models/document.py must be enhanced to include sku: Optional[str] and metadata: Optional[str] fields.

**Processing Pipeline Integration:** The process_document_upsert task in quepasa/data_processor/tasks.py must be updated to include the metadata field in the cleaned document object that is saved for indexing.

### 3.3 Code Organization and Standards

**File Structure Approach:** New logic for metadata filtering should be located in src/lib/markdown_converter.py. Changes to data models and services must be made within their existing respective files to maintain the current microservice architecture.

**Coding Standards:** All new code must adhere to the existing patterns, including the use of dataclasses for models and maintaining the separation of concerns between handlers, core logic, and utility functions.

### 3.4 Risk Assessment and Mitigation

**Technical Risks:** The primary technical risk is ensuring the new metadata field is correctly populated during ingestion and successfully passed through the entire data pipeline (crawler -> data-processor -> indexer -> searcher) without being dropped or corrupted. A critical gap from a previous implementation, where the sku field was not added to the QuepasaDocument model, must be rectified for both sku and metadata to prevent search-time failures.

**Integration Risks:** The API response modification must be strictly additive to ensure backward compatibility. Incorrect modification of the Elasticsearch mapping could lead to indexing failures or data loss, requiring a re-index.

**Mitigation Strategies:**
- Implement comprehensive unit and integration tests as outlined in CON-69-tests.md to validate the data flow from end to end.
- The Elasticsearch index mapping change should be carefully tested in a development environment before deployment.
- The QuepasaDocument model update is critical and must be completed before other search-related changes.

## Epic and Story Structure

### 4.1 Epic: Implement Product Metadata in API Responses

**Epic Goal:** To enhance the Quepasa conversational API by providing structured product data in responses, enabling richer client-side integrations while maintaining full backward compatibility. This involves updating the entire data pipeline to handle a new product document type with a dedicated metadata field.

#### Story 1.1: Foundational Data Model and Storage Schema Update

**As a** System Architect,  
**I want to** update the core data models and Elasticsearch schema to support product-specific metadata,  
**so that** the system has a foundational structure for storing and retrieving structured product information.

**Acceptance Criteria:**
- The QuepasaDocument model in quepasa/searcher/models/document.py is updated to include sku: Optional[str] and metadata: Optional[str].
- The Elasticsearch mapping in configuration/main/cli_create_index.py is updated to include a non-indexed text field named metadata.
- The Document model in quepasa/api/handlers/document_handler.py is updated to include the metadata: Optional[str] field.
- All existing unit tests for the data models pass without regression.
- New unit tests are added to validate the presence and type of the new sku and metadata fields.

#### Story 1.2: Enhance Ingestion and Data Processing Pipeline for Product Metadata

**As a** Data Engineer,  
**I want to** modify the ingestion and data processing pipeline to correctly identify, process, and store product documents with their structured metadata,  
**so that** product data is accurately captured and prepared for indexing.

**Acceptance Criteria:**
- A new filtering method _filter_product_for_metadata is implemented in src/lib/markdown_converter.py that excludes only the keys defined in METADATA_EXCLUDE_KEYS: { "_id", "indexables", "samplePrecision", "dynamicFacets" }.
- The products_to_documents function is updated to set the document type to "product" and populate the metadata field with the filtered JSON string.
- The process_document_upsert task in quepasa/data_processor/tasks.py is updated to handle and persist the new metadata field.
- Unit tests for products_to_documents are updated to verify the new type and metadata fields.
- An integration test confirms that an ingested product record results in a file in MinIO with the correct type and metadata JSON string.

#### Story 1.3: Update Search Service to Retrieve Product Metadata

**As a** Backend Developer,  
**I want to** enhance the RAG search service to retrieve the new sku and metadata fields from Elasticsearch,  
**so that** structured product data is available for API responses.

**Acceptance Criteria:**
- The RAG search logic in quepasa/searcher/core/rag_search.py is updated to query for and retrieve the sku and metadata fields from Elasticsearch.
- The search logic correctly populates the sku and metadata attributes of the QuepasaDocument objects it returns.
- Existing search functionality for non-product documents remains unchanged and performant.
- Integration tests are updated to confirm that a search for a product returns QuepasaDocument objects with the sku and metadata fields populated correctly from the mock Elasticsearch response.

#### Story 1.4: Implement ProductItem Model and Enhance API Response

**As a** Backend Developer,  
**I want to** introduce a ProductItem model and update the final API response to include a products array,  
**so that** client applications can receive structured, type-safe product data.

**Acceptance Criteria:**
- A new ProductItem dataclass is defined in quepasa/searcher/models/response.py as specified in the PRD.
- The QuepasaAnswer model in the same file is updated with a new optional field: products: Optional[List[ProductItem]].
- The meta_search source (quepasa/searcher/sources/meta.py) and/or AnswerRetrieverMixin is updated to convert QuepasaDocument objects of type "product" and SPDSearchResult objects into ProductItem objects.
- The final API response correctly populates the products array with ProductItem objects when products are retrieved.
- The enhancement is fully backward compatible; API responses for non-product queries are unchanged.
- API-level tests are created to validate the structure of the new products array in the response.
