from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from datetime import datetime
from src.lib.constants import (
    HTTP_PROTOCOL,
    VALID_PROTOCOLS,
    VALID_SOURCES,
    SOURCE_GPT,
)

@dataclass
class UserInfo:
    """User information for request"""
    id: str
    name: Optional[str] = None
    user_name: Optional[str] = None
    chat_id: Optional[int] = None
    message_id: Optional[int] = None
    session_id: Optional[str] = None
    visitor_id: Optional[str] = None

    def get(self, key: str, default: Any = None) -> Any:
        """Get attribute value by key name"""
        return getattr(self, key, default)

@dataclass
class UserHistoryItem:
    """User information for request"""
    role: str
    content: str
    request: Optional[Dict[str,Any]] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    name: Optional[str] = None
    references: Optional[Dict[str, Any]] = None

    def get(self, key: str, default: Any = None) -> Any:
        """Get attribute value by key name"""
        return getattr(self, key, default)

@dataclass
class WebhookConfig:
    """Webhook configuration"""
    endpoint: str
    meta: Optional[Dict[str, Any]] = None

    def get(self, key: str, default: Any = None) -> Any:
        """Get attribute value by key name"""
        return getattr(self, key, default)

@dataclass
class Classification:
    """Classification information for the request"""
    intent: str
    language_code: Optional[str] = None
    related: Optional[List[str]] = None
    date: Optional[str] = None
    query: Optional[str] = None

@dataclass
class Constants:
    """Constants for the request"""
    smth_went_wrong: Optional[str] = None
    i_dont_know: Optional[str] = None

@dataclass
class QuepasaRequest:
    """Quepasa request model"""
    question: str
    client: Optional[str] = None
    protocol: str = HTTP_PROTOCOL
    source: Optional[str] = None
    stream: bool = False
    classification: Optional[Classification] = None
    metadata: Optional[Dict[str, Any]] = None

    # Optional fields
    engine: str = "answer"  # search, answer, train, wiki, chunks
    filter: Optional[List[Dict[str, Any]]] = None
    country: Optional[str] = None
    language: Optional[str] = None
    llm: Optional[str] = None
    answer_llm: Optional[str] = None
    agent_llm: Optional[str] = None
    prompt: Optional[Union[str, List[Dict[str, str]]]] = None
    kind: Optional[str] = None  # text, summary, all
    domain: Optional[Union[str, List[str]]] = None

    # Authentication
    bearer_token: Optional[str] = None
    client_token: Optional[str] = None
    external_user_token: Optional[str] = None
    
    # User meta data
    history: Optional[List[UserHistoryItem]] = None
    user_info: Optional[UserInfo] = None

    # Request metadata
    webhook: Optional[WebhookConfig] = None
    constants: Optional[Constants] = None
    command: Optional[str] = None
    
    # Search parameters
    maximum_chunks_per_document: Optional[int] = None
    limit: Optional[int] = None
    documents: Optional[List[Dict[str, Any]]] = None

    # SPD specific
    collection: Optional[str] = None
    area: Optional[str] = None

    # Response configuration
    show_query: bool = False
    show_price: bool = False
    show_prompt: bool = False
    show_all_references: bool = False
    show_text: bool = False
    show_keywords: bool = False
    show_embedding: bool = False

    # Waterfall
    waterfall: Optional[List[Dict[str, Any]]] = None
    waterfall_step_name: Optional[str] = None
    
    # Timestamps
    ts: Optional[int] = None
    now: Optional[str] = None

    def __getitem__(self, key: str) -> Any:
        """Support dictionary-like access to request fields"""
        return self.to_dict()[key]

    def __contains__(self, key: str) -> bool:
        """Support 'in' operator for dictionary-like access"""
        return key in self.to_dict()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QuepasaRequest':
        """Create QuepasaRequest from dictionary with enhanced validation
        
        Args:
            data: Request data dictionary
            
        Returns:
            QuepasaRequest instance
            
        Raises:
            ValueError: If validation fails
        """
        # Validate required fields
        if not data.get('question'):
            raise ValueError("Question is required")
        if not isinstance(data.get('question'), str):
            raise ValueError(f"Question must be a string: {data.get('question')}")
            
        # Validate protocol
        protocol = data.get('protocol', HTTP_PROTOCOL)
        if protocol not in VALID_PROTOCOLS:
            raise ValueError(f"Invalid protocol: {protocol}. Must be one of: {VALID_PROTOCOLS}")
            
        # Validate engine
        engine = data.get('engine', 'answer')
        valid_engines = ['search', 'answer', 'train', 'wiki', 'chunks']
        if engine not in valid_engines:
            raise ValueError(f"Invalid engine: {engine}. Must be one of: {valid_engines}")
        
        # Validate filter
        filter = data.get('filter', None)
        if filter is not None and not isinstance(filter, list):
            raise ValueError(f"Filter must be a list: {filter}")
            
        # Validate optional fields
        if 'stream' in data and not isinstance(data['stream'], bool):
            raise ValueError(f"Stream must be a boolean: {data['stream']}")
            
        if 'metadata' in data and data['metadata'] is not None and not isinstance(data['metadata'], dict):
            raise ValueError(f"Metadata must be a dictionary: {data['metadata']}")
            
        if 'documents' in data:
            if not isinstance(data['documents'], list):
                raise ValueError(f"Documents must be a list: {data['documents']}")
            for doc in data['documents']:
                if not isinstance(doc, dict):
                    raise ValueError(f"Each document must be a dictionary: {doc}")
                    
        # Validate numeric fields
        numeric_fields = ['maximum_chunks_per_document', 'limit']
        for field in numeric_fields:
            if field in data and not isinstance(data[field], (int, type(None))):
                raise ValueError(f"{field} must be an integer: {data[field]}")
                
        # Validate boolean fields
        boolean_fields = ['show_query', 'show_price', 'show_prompt', 'show_all_references', 'show_text']
        for field in boolean_fields:
            if field in data and not isinstance(data[field], bool):
                raise ValueError(f"{field} must be a boolean: {data[field]}")
                
        # Validate nested objects
        if 'user_info' in data and data['user_info']:
            if not isinstance(data['user_info'], dict):
                raise ValueError(f"user_info must be a dictionary: {data['user_info']}")
            if 'id' not in data['user_info']:
                raise ValueError(f"user_info.id is required: {data['user_info']}")
                
        if 'webhook' in data and data['webhook']:
            if not isinstance(data['webhook'], dict):
                raise ValueError(f"webhook must be a dictionary: {data['webhook']}")
            if 'endpoint' not in data['webhook']:
                raise ValueError(f"webhook.endpoint is required: {data['webhook']}  ")
                
        # Create instance with validated data
        return cls(**{
            k: v for k, v in data.items() 
            if k in cls.__annotations__
        })

    def to_dict(self) -> Dict[str, Any]:
        return {
            'question': self.question,
            'client': self.client,
            'protocol': self.protocol,
            'source': self.source,
            'stream': self.stream,
            'classification': self.classification,
            'metadata': self.metadata
        }

    def __post_init__(self):
        # Clean up question
        if isinstance(self.question, str):
            self.question = self.question.strip()

        # Handle country code normalization
        if self.country:
            self.country = self.country.lower()

        if self.llm:
            if not self.answer_llm:
                self.answer_llm = self.llm

            if not self.agent_llm:
                self.agent_llm = self.llm

        # Extract history if present
        if self.history:
            if isinstance(self.history, list):
                self.history = [UserHistoryItem(**item) for item in self.history]

        # Extract user info if present
        if self.user_info:
            if isinstance(self.user_info, dict):
                self.user_info = UserInfo(**self.user_info)

        # Extract webhook config if present
        if self.webhook:
            if isinstance(self.webhook, dict):
                self.webhook = WebhookConfig(**self.webhook)

        # Extract constants if present
        if self.constants:
            if isinstance(self.constants, dict):
                self.constants = Constants(**self.constants)

        # Set timestamps if not provided
        if self.ts is None:
            self.ts = int(datetime.now().timestamp())
        if self.now is None:
            self.now = datetime.now().isoformat()

        # Validate protocol
        if self.protocol not in VALID_PROTOCOLS:
            raise ValueError(f"Invalid protocol: {self.protocol}")

        # Validate question
        if not self.question or not isinstance(self.question, str):
            raise ValueError("Question must be a non-empty string")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = {
            'question': self.question,
            'client': self.client,
            'protocol': self.protocol,
            'source': self.source,
            'stream': self.stream,
            'classification': self.classification,
            'metadata': self.metadata
        }

        # Add optional fields if present
        if self.engine:
            data['engine'] = self.engine
        if self.filter:
            data['filter'] = self.filter
        if self.country:
            data['country'] = self.country
        if self.language:
            data['language'] = self.language
        if self.llm:
            data['llm'] = self.llm
        if self.answer_llm:
            data['answer_llm'] = self.answer_llm
        if self.agent_llm:
            data['agent_llm'] = self.agent_llm
        if self.prompt:
            data['prompt'] = self.prompt
        if self.kind:
            data['kind'] = self.kind
        if self.domain:
            data['domain'] = self.domain
        if self.waterfall:
            data['waterfall'] = self.waterfall
        if self.waterfall_step_name:
            data['waterfall_step_name'] = self.waterfall_step_name
        if self.bearer_token:
            data['bearer_token'] = self.bearer_token
        if self.client_token:
            data['client_token'] = self.client_token
        if self.external_user_token:
            data['external_user_token'] = self.external_user_token
        if self.history:
            data['history'] = [
                {
                    'role': item.role,
                    'content': item.content,
                    'request': item.request,
                }
                for item in self.history
            ]
        if self.user_info:
            data['user_info'] = {
                'id': self.user_info.id,
                'name': self.user_info.name,
                'user_name': self.user_info.user_name,
                'chat_id': self.user_info.chat_id,
                'message_id': self.user_info.message_id
            }
        if self.webhook:
            data['webhook'] = {
                'endpoint': self.webhook.endpoint,
                'meta': self.webhook.meta
            }
        if self.constants:
            data['constants'] = {
                'smth_went_wrong': self.constants.smth_went_wrong,
                'i_dont_know': self.constants.i_dont_know
            }
        if self.command:
            data['command'] = self.command
        if self.maximum_chunks_per_document:
            data['maximum_chunks_per_document'] = self.maximum_chunks_per_document
        if self.limit:
            data['limit'] = self.limit
        if self.documents:
            data['documents'] = self.documents
        if self.show_query:
            data['show_query'] = self.show_query
        if self.show_price:
            data['show_price'] = self.show_price
        if self.show_prompt:
            data['show_prompt'] = self.show_prompt
        if self.show_all_references:
            data['show_all_references'] = self.show_all_references
        if self.show_text:
            data['show_text'] = self.show_text
        if self.ts:
            data['ts'] = self.ts
        if self.now:
            data['now'] = self.now

        return data 
