import os
import pytest
import requests
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
import json
import responses

from quepasa.searcher.sources.web import WebSource
from quepasa.searcher.core.web_search import WebSearchManager
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.web import WebSearchResult
from quepasa.searcher.models.response import QuepasaResponse
from quepasa.searcher.sources.base import STATUS_SUCCESS

@pytest.fixture
def mock_bing_response():
    # Create a mock response structure similar to Bing search API
    return {
        "webPages": {
            "value": [
                {
                    "url": "https://example.com/1",
                    "name": "Example Page 1",
                    "snippet": "This is a snippet from page 1"
                },
                {
                    "url": "https://example.com/2",
                    "name": "Example Page 2",
                    "snippet": "This is a snippet from page 2"
                }
            ],
            "totalEstimatedMatches": 2
        }
    }

@pytest.fixture
def web_manager():
    """Fixture for WebSearchManager"""
    manager = WebSearchManager("dummy_key")
    return manager

@pytest.fixture
def web_source():
    """Fixture for WebSource"""
    source = WebSource()
    return source

@responses.activate
def test_web_source_search():
    """Test successful web search with mocked API response"""
    # Set up mock response
    mock_response = {
        "webPages": {
            "value": [
                {
                    "url": "https://example.com/1",
                    "name": "Example Page 1",
                    "snippet": "This is a snippet from page 1"
                },
                {
                    "url": "https://example.com/2",
                    "name": "Example Page 2",
                    "snippet": "This is a snippet from page 2"
                }
            ],
            "totalEstimatedMatches": 2
        }
    }
    
    # Set up responses mock
    responses.add(
        responses.GET,
        "https://api.bing.microsoft.com/v7.0/search",
        json=mock_response,
        status=200
    )
    
    # Set up environment variables
    with patch.dict(os.environ, {"BING_SUBSCRIPTION_API_KEY": "dummy_key"}):
        # Create searcher and perform search
        web_source = WebSource()
        
        # Mock WebSearchManager.search to return expected results
        with patch.object(WebSearchManager, 'search') as mock_search:
            expected_results = [
                WebSearchResult(
                    url="https://example.com/1",
                    title="Example Page 1",
                    snippet="This is a snippet from page 1",
                    text="Example Page 1\nThis is a snippet from page 1"
                ),
                WebSearchResult(
                    url="https://example.com/2",
                    title="Example Page 2",
                    snippet="This is a snippet from page 2",
                    text="Example Page 2\nThis is a snippet from page 2"
                )
            ]
            mock_search.return_value = expected_results
            
            request = QuepasaRequest(question="test query")
            response = web_source.search(request)
            
            # Verify results
            assert response.status == STATUS_SUCCESS
            assert len(response.data) == 2
            assert response.data[0].url == "https://example.com/1"
            assert response.data[0].title == "Example Page 1"
            assert response.data[0].snippet == "This is a snippet from page 1"

@responses.activate
def test_web_source_no_results(web_source):
    """Test web search with no results"""
    # Set up environment variables
    with patch.dict(os.environ, {"BING_SUBSCRIPTION_API_KEY": "dummy_key"}):
        # Mock WebSearchManager.search to return empty results
        with patch.object(WebSearchManager, 'search') as mock_search:
            mock_search.return_value = []
            
            request = QuepasaRequest(question="test query")
            response = web_source.search(request)
            
            # Verify results
            assert response.status == STATUS_SUCCESS
            assert len(response.data) == 0

@patch.dict(os.environ, {})
def test_api_key_error():
    """Test web search without API key"""
    # Make sure the environment variable is not set
    if "BING_SUBSCRIPTION_API_KEY" in os.environ:
        del os.environ["BING_SUBSCRIPTION_API_KEY"]
    
    # Create web search manager without API key
    web_search = WebSearchManager()
    
    # Test search with no API key
    with pytest.raises(ValueError) as exc_info:
        request = QuepasaRequest(question="test query")
        web_search.search(request)
    
    # Check the error message
    assert "Bing API key not configured" in str(exc_info.value)

def test_web_search_basic(web_manager, mock_bing_response):
    """Test basic web search functionality"""
    with patch('requests.get') as mock_get:
        mock_get.return_value.json.return_value = mock_bing_response
        mock_get.return_value.raise_for_status = Mock()
        
        request = QuepasaRequest(question="test query")
        results = web_manager.search(request)
        
        assert len(results) == 2
        assert isinstance(results[0], WebSearchResult)
        assert results[0].url == 'https://example.com/1'
        assert results[0].title == 'Example Page 1'
        assert results[0].snippet == 'This is a snippet from page 1'

def test_web_search_with_country(web_manager, mock_bing_response):
    """Test web search with country code"""
    with patch('requests.get') as mock_get:
        mock_get.return_value.json.return_value = mock_bing_response
        mock_get.return_value.raise_for_status = Mock()
        
        request = QuepasaRequest(question="test query", country="us")
        web_manager.search(request)
        
        # Verify country code was added
        call_args = mock_get.call_args[1]
        assert call_args['params']['cc'] == 'US'

def test_web_search_special_countries(web_manager, mock_bing_response):
    """Test web search with special country codes"""
    with patch('requests.get') as mock_get:
        mock_get.return_value.json.return_value = mock_bing_response
        mock_get.return_value.raise_for_status = Mock()
        
        # Test Ireland
        request = QuepasaRequest(question="test query", country="irl")
        web_manager.search(request)
        call_args = mock_get.call_args[1]
        assert call_args['params']['cc'] == 'IE'
        
        # Test Montenegro
        request = QuepasaRequest(question="test query", country="mne")
        web_manager.search(request)
        call_args = mock_get.call_args[1]
        assert call_args['params']['cc'] == 'ME'

def test_web_search_api_error(web_manager):
    """Test web search API error handling"""
    with patch('requests.get') as mock_get:
        mock_get.side_effect = requests.exceptions.RequestException("API Error")
        
        request = QuepasaRequest(question="test query")
        with pytest.raises(Exception) as exc_info:
            web_manager.search(request)
        assert "Error searching web: API Error" in str(exc_info.value)

def test_web_source_get_answer(web_source):
    """Test get_answer method of WebSource"""
    with patch.dict(os.environ, {"BING_SUBSCRIPTION_API_KEY": "dummy_key"}):
        # Mock WebSearchManager.search to return expected results
        with patch.object(WebSearchManager, 'search') as mock_search:
            expected_results = [
                WebSearchResult(
                    url="https://example.com/1",
                    title="Example Page 1",
                    snippet="This is a snippet from page 1",
                    text="Example Page 1\nThis is a snippet from page 1"
                )
            ]
            mock_search.return_value = expected_results
            
            # Mock retrieve_answer method
            with patch.object(web_source, 'retrieve_answer') as mock_retrieve:
                expected_response = QuepasaResponse(
                    status=STATUS_SUCCESS,
                    data={"answer": "This is the answer"}
                )
                mock_retrieve.return_value = expected_response
                
                request = QuepasaRequest(question="test query")
                response = web_source.get_answer(request)
                
                # Verify answer is retrieved
                assert response.status == STATUS_SUCCESS
                assert response.data["answer"] == "This is the answer" 