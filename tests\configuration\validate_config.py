#!/usr/bin/env python3
"""
Configuration Validation Script

Run this script to validate that all client YAML configurations are complete
and properly formatted before deploying or starting the application.

Usage:
    python configuration/validate_config.py
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from configuration.yaml_loader import YAMLConfigurationLoader, ConfigurationValidationError


def main():
    """Validate all client configurations."""
    print("🔍 Validating YAML configurations...\n")
    
    loader = YAMLConfigurationLoader()
    
    # Get all available client configurations
    available_clients = loader.get_available_clients()
    
    if not available_clients:
        print("❌ No client configuration files found!")
        print(f"   Expected location: {loader.config_root}")
        return 1
    
    print(f"Found {len(available_clients)} client configuration(s):")
    for client in available_clients:
        print(f"  - {client}")
    print()
    
    # Validate each configuration
    all_valid = True
    results = {}
    
    for client_code in available_clients:
        print(f"Validating {client_code}...", end=" ")
        
        try:
            config = loader.load_client_config(client_code)
            print("✅ VALID")
            results[client_code] = {"status": "valid", "config": config}
            
        except ConfigurationValidationError as e:
            print("❌ INVALID")
            print(f"   Error: {e}")
            results[client_code] = {"status": "invalid", "error": str(e)}
            all_valid = False
            
        except Exception as e:
            print("💥 ERROR")
            print(f"   Unexpected error: {e}")
            results[client_code] = {"status": "error", "error": str(e)}
            all_valid = False
    
    print("\n" + "="*60)
    
    if all_valid:
        print("🎉 All configurations are VALID!")
        print("\nConfiguration summary:")
        
        for client_code, result in results.items():
            if result["status"] == "valid":
                config = result["config"]
                print(f"\n{client_code}:")
                print(f"  - Domain: {config.get('domain', 'N/A')}")
                print(f"  - Store: {config.get('store_info', {}).get('name', 'N/A')}")
                print(f"  - Query expansions: {len(config.get('query_expansions', []))}")
                print(f"  - Agentic tools: {len(config.get('agentic_tools', []))}")
                print(f"  - Languages: {', '.join(config.get('language', {}).get('indexed_languages', {}).get('document', []))}")
        
        return 0
    else:
        print("❌ Some configurations are INVALID!")
        print("\n⚠️  Please fix the errors above before starting the application.")
        return 1


def test_shoeby_integration():
    """Test Shoeby configuration integration with the actual configuration class."""
    print("\n🧪 Testing Shoeby configuration integration...\n")
    
    try:
        # Import here to avoid circular imports
        from configuration.main.default import RezolveShoebyConfiguration
        
        print("Creating RezolveShoebyConfiguration instance...", end=" ")
        config = RezolveShoebyConfiguration("rezolve_shoeby_openai")
        print("✅ SUCCESS")
        
        # Test key methods
        tests = [
            ("Language fallback", lambda: config.get_fallback_language()),
            ("Language mapping", lambda: config.get_language_mapping()),
            ("Max results limit", lambda: config.get_max_results_limit("documents")),
            ("Query expansions", lambda: config.get_question_expansions()),
            ("History config", lambda: config.get_history_config()),
            ("LLM model (agentic)", lambda: config.get_llm_model_name("agentic")),
            ("LLM model (documents)", lambda: config.get_llm_model_name("documents")),
            ("Agentic tools", lambda: config.get_agentic_tools()),
        ]
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                print(f"✅ {test_name}: {type(result).__name__}")
                if hasattr(result, '__len__') and not isinstance(result, str):
                    print(f"   Count: {len(result)}")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                return False
        
        # Test thinking messages
        try:
            thinking_msg = config.get_agentic_function_thinking("shoeby_catalog_rag", {"question": "test"})
            print(f"✅ Thinking messages: {len(thinking_msg)} chars")
        except Exception as e:
            print(f"❌ Thinking messages: ERROR - {e}")
            return False
        
        print("\n🎉 All integration tests passed!")
        return True
        
    except Exception as e:
        print(f"💥 ERROR - {e}")
        return False


if __name__ == "__main__":
    # Validate configurations
    result = main()
    
    # Test integration if validation passed
    if result == 0:
        integration_success = test_shoeby_integration()
        if not integration_success:
            result = 1
    
    sys.exit(result)