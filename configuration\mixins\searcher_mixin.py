from typing import Dict, Any, Union
from quepasa.searcher.models.request import QuepasaRequest
from ..base.searcher import SearcherConfig

class SearcherMixin(SearcherConfig):
    """Mixin that implements search functionality."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def update_chunk_data(self, source: str, data: Dict[str, Any], document: Dict[str, Any], default_item: Dict[str, Any]) -> Dict[str, Any]:
        """Format document in results.
        
        Previously: get_document_item()
        """
        text = default_item['text']
        if (
            'title' in document
            and document['title'] != None
            and document['title'].strip() != ""
        ):
            text = f"# {document['title']}\n" + text

        default_item['text'] = text.strip()
        return default_item