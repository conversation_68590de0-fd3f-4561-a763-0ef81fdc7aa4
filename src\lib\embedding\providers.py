from enum import Enum, auto

class EmbeddingProvider(Enum):
    """Enum for supported embedding providers.
    
    Values:
        AZURE_OPENAI: Azure OpenAI embeddings service
        OPENAI: Regular OpenAI embeddings service
        ASKROBOT: AskRobot embeddings service
        SBERT: Local Sentence-BERT embeddings
        NEBIUS: Nebius embeddings service
        MWS: MWS embeddings service
        HUGGINGFACE: Huggingface embeddings service
        REPLICATE: Replicate embeddings service
        PRIVATE: Private GPU-based embeddings service
    """
    AZURE_OPENAI = 'azure-openai'
    OPENAI = 'openai'
    ASKROBOT = 'askrobot'
    SBERT = 'sbert'
    NEBIUS = 'nebius'
    MWS = 'mws'
    HUGGINGFACE = 'huggingface'
    REPLICATE = 'replicate'
    PRIVATE = 'private'
    
    @classmethod
    def from_str(cls, value: str) -> 'EmbeddingProvider':
        """Convert string to EmbeddingProvider enum.
        
        Args:
            value: String value to convert. For backward compatibility,
                  'openai' is mapped to OPENAI.
            
        Returns:
            Corresponding EmbeddingProvider enum value
            
        Raises:
            ValueError: If value is not a valid provider
        """
        value = value.lower()
        try:
            return cls(value)

        except ValueError:
            raise ValueError(f"Unknown embedding provider: {value}. "
                           f"Supported providers: {[e.value for e in cls]}") 