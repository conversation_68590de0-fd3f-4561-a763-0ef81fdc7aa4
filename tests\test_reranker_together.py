import os
import pytest
from unittest.mock import patch, MagicMock
from src.lib.reranker.together import TogetherReranker

@pytest.fixture
def reranker():
    with patch.dict(os.environ, {"TOGETHERAI_API_KEY": "test-token"}):
        return TogetherReranker()

@pytest.fixture
def test_data():
    return {
        "model_version": "Salesforce/Llama-Rank-v1",
        "query": "test query",
        "documents": [
            {"id": 1, "text": "first doc"},
            {"id": 2, "text": "second doc"},
            {"id": 3, "text": "third doc"}
        ]
    }

def test_init_missing_token():
    """Test initialization with missing API token"""
    with patch.dict(os.environ, {}, clear=True):
        with pytest.raises(ValueError) as exc_info:
            TogetherReranker()
        assert "TOGETHERAI_API_KEY environment variable is not set" in str(exc_info.value)

def test_get_results_empty_documents(reranker):
    """Test behavior with empty documents list"""
    result = reranker.get_results("test-model", "query", [])
    assert result == []

@patch("requests.post")
def test_get_results_success(mock_post, reranker, test_data):
    """Test successful API call"""
    # Mock response
    mock_response = MagicMock()
    mock_response.json.return_value = {
        "documents": test_data["documents"][:2]
    }
    mock_post.return_value = mock_response
    
    result = reranker.get_results(
        test_data["model_version"],
        test_data["query"],
        test_data["documents"],
        top_n=2
    )
    
    # Verify result
    assert result == test_data["documents"][:2]
    
    # Verify API call
    mock_post.assert_called_once()
    call_args = mock_post.call_args
    
    # Check URL
    assert call_args[0][0] == "https://api.together.xyz/v1/rerank"
    
    # Check headers
    headers = call_args[1]["headers"]
    assert headers["authorization"] == "Bearer test-token"
    assert headers["content-type"] == "application/json"
    
    # Check request body
    body = call_args[1]["json"]
    assert body["query"] == test_data["query"]
    assert body["documents"] == test_data["documents"]
    assert body["model_version"] == test_data["model_version"]
    assert body["top_n"] == 2
    assert body["rank_fields"] == ["id", "text"]

@patch("requests.post")
def test_get_results_api_error(mock_post, reranker, test_data):
    """Test API error handling"""
    # Mock error response
    mock_response = MagicMock()
    mock_response.raise_for_status.side_effect = Exception("API Error")
    mock_post.return_value = mock_response
    
    with pytest.raises(Exception) as exc_info:
        reranker.get_results(
            test_data["model_version"],
            test_data["query"],
            test_data["documents"]
        )
    assert "API Error" in str(exc_info.value)

@patch("requests.post")
def test_get_results_empty_response(mock_post, reranker, test_data):
    """Test handling of empty API response"""
    # Mock empty response
    mock_response = MagicMock()
    mock_response.json.return_value = {}
    mock_post.return_value = mock_response
    
    result = reranker.get_results(
        test_data["model_version"],
        test_data["query"],
        test_data["documents"]
    )
    assert result == [] 