import requests
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

def get_language(text: str, default: str = 'en', allowed_languages: List[str] = None) -> Optional[str]:
    """
    Detect language using the embedding service API.
    
    Args:
        text: The text to detect language for
        default: The default language to return if detection fails
        allowed_languages: The list of allowed languages to return
        
    Returns:
        A dictionary with 'language' and 'confidence' keys, or None if detection failed
        
    Example response:
        {'language': 'en', 'confidence': 0.9109769463539124}
    """
    if not text or not text.strip():
        return None
        
    try:
        service_url = "http://embedding:8080"
        response = requests.post(
            f"{service_url}/api/detect-language",
            json={
                "text": text,
                "allowed_languages": allowed_languages
            },
            timeout=10
        )
        
        response.raise_for_status()
        result = response.json()
        
        # Create a compatible object that mimics cld3 response format
        return result.get('language', default)
        
    except Exception as e:
        logger.error(f"Language detection error: {str(e)}")
        return None 