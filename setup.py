from setuptools import setup, find_packages

setup(
    name="quepasa",
    version="0.1.0",
    packages=find_packages() + ['configuration', 'configuration.base'],
    install_requires=[
        "celery",
        "requests",
        "beautifulsoup4",
        "pytubefix",
        "youtube_transcript_api",
        "pdfplumber",
        "python-docx",
        "openpyxl",
        "python-pptx",
        "markdown",
        "langchain_community",
        "pymupdf",
        "bs4",
    ],
    extras_require={
        "dev": [
            "pytest",
            "pytest-cov",
            "pytest-mock",
            "black",
            "flake8",
            "mypy",
        ],
        "test": [
            "pytest",
            "pytest-cov",
            "pytest-mock",
            "coverage",
        ],
    },
    python_requires=">=3.8",
) 