import os
import re
import csv
import asyncio
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple
from telethon.tl.types import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PeerChannel, InputPeerChannel
from telethon.tl.functions.channels import JoinChannelRequest, GetFullChannelRequest
from telethon.tl.functions.messages import (
    ImportChatInviteRequest,
    CheckChatInviteRequest,
    GetFullChatRequest
)
from telethon.tl.functions.users import GetFullUserRequest
import json
import zlib
import logging
from pprint import pprint

# Import QuepasaTelegramClient instead of direct Telethon client
from src.lib.telegram.client import QuepasaTelegramClient
from src.lib.files import QuepasaFiles
from src.lib.logger import QuepasaLogger

# Set up logger
logger = QuepasaLogger().get_instance(__name__)
qp_files = QuepasaFiles()

# Convert bytes objects to strings
def convert_message_to_serialized(obj):
    if isinstance(obj, bytes):
        return obj.hex()
    elif isinstance(obj, datetime):
        return obj.strftime('%Y-%m-%dT%H:%M:%SZ')
    elif isinstance(obj, dict):
        return {k: convert_message_to_serialized(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_message_to_serialized(i) for i in obj]
    return obj

class TelegramMessageDownloader:
    BATCH_SIZE = 10000

    def __init__(self, client_id: str, phone_number: str, files=None):
        self.client_id = client_id
        self.phone_number = phone_number
        self.client = None
        self.qp_files = files or qp_files

        self._local_cache_dir = os.path.join(os.path.dirname(__file__), "../../../../cache/telegram")        
        self._raw_dir = f"{self._local_cache_dir}/raw"

    async def connect(self) -> None:
        """Establish connection to Telegram"""
        if self.client:
            return

        # Create a QuepasaTelegramClient instead of direct Telethon client
        qp_telegram_client = QuepasaTelegramClient(self.client_id, self.phone_number)

        # Get the Telegram client from the QuepasaTelegramClient
        self.client = await qp_telegram_client.get_telegram_client()

    async def disconnect(self) -> None:
        """Safely disconnect from Telegram"""
        if self.client:
            await self.client.disconnect()
            self.client = None

    async def download_video(self, message: Any, output_dir: str) -> Optional[str]:
        """Download video from a message and return the path to the downloaded file"""
        if not message or not isinstance(message, dict):
            return None
            
        try:
            # Handle video messages
            media = message.get('media')
            if not media:
                return None
                
            doc = media.get('document')
            if not doc:
                return None
                
            # Check if it's a video file
            mime_type = doc.get('mime_type', '')
            if not mime_type.startswith('video/'):
                return None
            
            logger.info(f"Mime type: {mime_type}")
                
            # Convert output_dir to absolute path
            output_dir = os.path.abspath(output_dir)
            os.makedirs(output_dir, exist_ok=True)
            
            # Get chat ID from peer_id or from_id
            peer_id = message.get('peer_id', {})
            chat_id = None
            
            # Try different ways to get chat ID
            if peer_id.get('_') == 'PeerChannel':
                chat_id = peer_id.get('channel_id')
            elif message.get('to_id', {}).get('_') == 'PeerChannel':
                chat_id = message.get('to_id', {}).get('channel_id')
            elif message.get('chat_id'):
                chat_id = abs(message.get('chat_id'))  # Remove negative sign if present
                
            if not chat_id:
                logger.error(f"Invalid chat ID for message {message.get('id')}")
                return None
            
            # Generate filename
            filename = f"video-{chat_id}-{message.get('id')}.mp4"
            filepath = os.path.join(output_dir, filename)
            
            # Download the file if it doesn't exist
            if not os.path.exists(filepath):
                try:
                    # Get the channel entity to get access hash
                    channel = await self.client.get_entity(int(chat_id))
                    if not channel:
                        logger.error(f"Could not get channel entity for {chat_id}")
                        return None
                    
                    # Create input peer for the channel
                    input_peer = InputPeerChannel(
                        channel_id=int(chat_id),
                        access_hash=channel.access_hash
                    )
                    
                    # Get the original message
                    original_message = await self.client.get_messages(
                        input_peer,
                        ids=message.get('id')
                    )
                    
                    if not original_message:
                        logger.error(f"Could not get original message {message.get('id')}")
                        return None
                    
                    # Download using the original message
                    file = await self.client.download_media(
                        original_message.media,
                        filepath,
                        progress_callback=lambda received, total: logger.info(f"Downloading: {received}/{total} bytes")
                    )
                    
                    if not file or not os.path.exists(filepath):
                        logger.error(f"Failed to download video: file not created at {filepath}")
                        return None
                    
                    logger.info(f"Successfully downloaded video to {filepath}")
                    
                except Exception as e:
                    logger.error(f"Error downloading video: {str(e)}")
                    if os.path.exists(filepath):
                        os.remove(filepath)  # Clean up partial download if it exists
                    return None
                    
            return filepath
            
        except Exception as e:
            logger.error(f"Error processing video message: {str(e)}")
            return None

    async def download_audio(self, message: Any, output_dir: str) -> Optional[str]:
        """Download audio from a message and return the path to the downloaded file"""
        if not message or not isinstance(message, dict):
            return None
            
        try:
            # Handle voice messages and audio files
            media = message.get('media')
            if not media:
                return None
                
            doc = media.get('document')
            if not doc:
                return None
                
            # Check if it's an audio file
            mime_type = doc.get('mime_type', '')
            if not mime_type.startswith('audio/'):
                return None
            
            logger.info(f"Mime type: {mime_type}")
                
            # Convert output_dir to absolute path
            output_dir = os.path.abspath(output_dir)
            os.makedirs(output_dir, exist_ok=True)
            
            # Get chat ID from peer_id
            peer_id = message.get('peer_id', {})
            chat_id = peer_id.get('channel_id') if peer_id.get('_') == 'PeerChannel' else None
            if not chat_id:
                logger.error(f"Invalid chat ID for message {message.get('id')}")
                return None
            
            # Generate filename
            filename = f"audio-{chat_id}-{message.get('id')}.ogg"
            filepath = os.path.join(output_dir, filename)
            
            # Download the file if it doesn't exist
            if not os.path.exists(filepath):
                try:
                    # Get the channel entity to get access hash
                    channel = await self.client.get_entity(int(chat_id))
                    if not channel:
                        logger.error(f"Could not get channel entity for {chat_id}")
                        return None
                    
                    # Create input peer for the channel
                    input_peer = InputPeerChannel(
                        channel_id=int(chat_id),
                        access_hash=channel.access_hash
                    )
                    
                    # Get the original message
                    original_message = await self.client.get_messages(
                        input_peer,
                        ids=message.get('id')
                    )
                    
                    if not original_message:
                        logger.error(f"Could not get original message {message.get('id')}")
                        return None
                    
                    # Download using the original message
                    file = await self.client.download_media(
                        original_message.media,
                        filepath,
                        progress_callback=lambda received, total: logger.info(f"Downloading: {received}/{total} bytes")
                    )
                    
                    if not file or not os.path.exists(filepath):
                        logger.error(f"Failed to download audio: file not created at {filepath}")
                        return None
                        
                    logger.info(f"Successfully downloaded audio to {filepath}")
                    
                except Exception as e:
                    logger.error(f"Failed to download media: {str(e)}")
                    if os.path.exists(filepath):
                        os.remove(filepath)  # Clean up partial download if it exists
                    return None
            
            return filepath
            
        except Exception as e:
            logger.error(f"Failed to download audio from message {message.get('id')}: {str(e)}")
            return None

    async def download_messages(self, url: str, start_id: int = 0, chat_info: dict = None) -> List[Dict[str, Any]]:
        """Download messages from a chat starting from start_id"""
        try:
            # Use provided chat_info or get it from URL
            if chat_info:
                entity = await self.client.get_entity(chat_info['id'])

            else:
                # Extract chat ID or invite hash from URL
                chat_path = url.split('/')[-1]
                
                entity = None
                if chat_path.startswith('+'):
                    # Handle invite link
                    invite_hash = chat_path[1:]  # Remove the + prefix
                    try:
                        # Try to join using the invite link
                        updates = await self.client(ImportChatInviteRequest(invite_hash))
                        entity = updates.chats[0]

                    except Exception as e:
                        logger.error(f"Failed to join chat via invite link {url}: {str(e)}")

                        # Try to get info about the invite link
                        invite_result = await self.client(CheckChatInviteRequest(invite_hash))
                        if hasattr(invite_result, 'chat'):
                            entity = invite_result.chat

                        else:
                            raise Exception(f"Could not access invite link {url}")
                        
                else:
                    # Handle regular chat ID
                    chat_id = int('-100' + chat_path) if chat_path.isdigit() and '/c/' in url else chat_path
                    try:
                        entity = await self.client.get_entity(chat_id)

                    except Exception as e1:
                        # If that fails, try without -100 prefix
                        try:
                            if isinstance(chat_id, int):
                                chat_id = int(str(chat_id).replace('-100', '', 1))
                                entity = await self.client.get_entity(chat_id)
                        except Exception as e2:
                            raise Exception(f"Could not access chat {chat_path}: {str(e2)}")
            
            if not entity:
                raise Exception(f"Could not access chat {url}")
            
            # Try to get messages
            last_messages = await self.client.get_messages(entity, limit=1)
            if not last_messages:
                return []
            
            last_message_id = last_messages[0].id
            logger.info(f"Found {last_message_id} total messages in chat")
            
            messages = []
            retry_count = 0
            max_retries = 3

            # Download all messages in reverse order
            while True:
                try:
                    logger.info("Downloading messages...")
                    async for msg in self.client.iter_messages(entity, min_id=start_id, max_id=last_message_id+1, reverse=True):
                        message = convert_message_to_serialized(msg.to_dict())
                        if msg.sender:
                            message['sender'] = convert_message_to_serialized(msg.sender.to_dict())
                        messages.append(message)
                    break
                    
                except Exception as e:
                    if "flood wait" in str(e).lower():
                        # If we hit a rate limit, wait and retry
                        wait_time = int(str(e).split('(')[1].split(')')[0])
                        logger.info(f"Rate limit hit, waiting for {wait_time} seconds")
                        await asyncio.sleep(wait_time)
                        retry_count += 1
                        if retry_count >= max_retries:
                            raise Exception(f"Failed to download messages after {max_retries} retries")
                        continue
                    raise Exception(f"Failed to download messages: {str(e)}")

            # Sort messages by ID
            messages.sort(key=lambda x: int(x['id']))
            logger.info(f"Successfully downloaded {len(messages)} messages")

            # Save messages to a JSON file
            if messages:
                self.save_messages(messages, chat_info)

            return messages

        except Exception as e:
            raise Exception(f"Failed to download messages: {str(e)}")

    def save_messages(self, messages: List[Dict[str, Any]], chat_info: dict) -> str:
        """Save messages to a JSON file using QuepasaFiles"""
        # Remove original message objects before saving
        messages_to_save = []
        for msg in messages:
            msg_copy = msg.copy()
            msg_copy.pop('_original_message', None)
            messages_to_save.append(msg_copy)
        
        # Format current date for both filename and path
        date_str = datetime.now().strftime('%Y-%m-%d')
        time_str = datetime.now().strftime('%H_%M_%S')
        filename = f"messages-{date_str}__{time_str}.jsonl"
                
        # Construct the remote path
        remote_path = f"prod/storage/custom/telegram/{self.client_id}/{chat_info['id']}/{date_str}/{filename}"
        
        # Convert messages to JSONL format
        jsonl_content = "\n".join(json.dumps(msg) for msg in messages_to_save)
        
        # Save using QuepasaFiles
        self.qp_files.set_text(remote_path, jsonl_content)

    async def get_chat_info(self, chat_url: str) -> Optional[Dict]:
        """Get chat information including title"""
        try:
            logger.info(f"Getting chat info for: {chat_url}")
            
            chat = None

            # Extract chat ID or invite hash from URL
            chat_path = chat_url.split('/')[-1]
            
            if chat_path.startswith('+'):
                # Handle invite link
                invite_hash = chat_path[1:]  # Remove the + prefix
                retry_count = 0
                max_retries = 3
                
                while True:
                    try:
                        # Try to join using the invite link
                        updates = await self.client(ImportChatInviteRequest(invite_hash))
                        chat = updates.chats[0]
                        chat_type = 'chat'
                        break

                    except Exception as e:
                        if "a wait of" in str(e).lower() and "seconds is required" in str(e).lower():
                            # If we hit a rate limit, wait and retry
                            wait_time = int(re.search(r'A wait of (\d+) seconds is required', str(e)).group(1))
                            logger.info(f"Rate limit hit for invite link, waiting for {wait_time} seconds")
                            await asyncio.sleep(wait_time)

                            retry_count += 1
                            if retry_count >= max_retries:
                                logger.error(f"Failed to join chat via invite link {chat_url} after {max_retries} retries")

                                # Try to get info about the invite link without joining
                                invite_result = await self.client(CheckChatInviteRequest(invite_hash))
                                if hasattr(invite_result, 'chat'):
                                    chat = invite_result.chat

                                else:
                                    return None
                                break
                            continue

                        else:
                            logger.error(f"Failed to join chat via invite link {chat_url}: {str(e)}")

                            # Try to get info about the invite link
                            invite_result = await self.client(CheckChatInviteRequest(invite_hash))
                            if hasattr(invite_result, 'chat'):
                                chat = invite_result.chat
                            else:
                                return None
                            break

            else:
                entity_ids = []
                if chat_path.isdigit() and '/c/' in chat_url:
                    for chat_id in [int(chat_path), int('-100' + chat_path)]:
                        entity_ids.append(chat_id)
                else:
                    entity_ids.append(chat_path)

                entities = []
                for entity_id in entity_ids:
                    if isinstance(entity_id, int):
                        try:
                            entities.append(PeerChat(entity_id))
                        except Exception as e:
                            logger.error(f"Failed to create PeerChat for ID: {entity_id}: {str(e)}")

                    try:
                        entities.append(PeerUser(entity_id))
                    except Exception as e:
                        logger.error(f"Failed to create PeerUser for ID: {entity_id}: {str(e)}")

                    try:
                        entities.append(PeerChannel(entity_id))
                    except Exception as e:
                        logger.error(f"Failed to create PeerChannel for ID: {entity_id}: {str(e)}")

                for entity in entities:
                    try:
                        logger.info(f"Getting chat entity for ID: {entity}")
                        chat = await self.client.get_entity(entity)
                        break

                    except Exception as e:
                        logger.error(f"Failed to get chat entity for ID: {entity}: {str(e)}")
                        continue

                if not chat:
                    for entity_id in entity_ids:
                        try:
                            chat = await self.client.get_entity(entity_id)
                            break

                        except Exception as e:
                            logger.error(f"Failed to get chat entity for ID: {entity_id}: {str(e)}")
                            continue

            if not chat:
                raise Exception(f"Could not access chat {chat_path}")
                                        
            # Extract relevant information
            chat_info = {
                'id': chat.id,
                'title': getattr(chat, 'title', None),
                'username': getattr(chat, 'username', None),
                'type': chat.__class__.__name__
            }
            logger.info(f"Chat info: {chat_info}")
            
            return chat_info
            
        except Exception as e:
            logger.error(f"Error getting chat info for {chat_url}: {str(e)}")
            return None 