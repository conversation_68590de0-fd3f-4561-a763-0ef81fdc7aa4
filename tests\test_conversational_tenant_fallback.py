import pytest

from quepasa.common.config_models import TenantConfig, TenantModel, ConversationalSearchConfigModel


def make_cfg(id, tenantId, collectionId=None, areaId=None):
    return ConversationalSearchConfigModel(
        id=id,
        tenantId=tenantId,
        collectionId=collectionId,
        areaId=areaId,
        interleaving=ConversationalSearchConfigModel.Interleaving(ragRatio=0.1, spdRatio=0.9, spdIncludeOnlyPromoted=False),
    )


def test_both_area_and_collection_fall_back_to_tenant_level_when_no_match():
    tenant = TenantModel(id=1, name="acme", enabled=True, settings={})
    tenant_level = make_cfg(1, 1, None, None)
    # unrelated configs
    other_collection = make_cfg(2, 1, 2000, None)
    other_area = make_cfg(3, 1, 1000, 123)

    tc = TenantConfig(
        tenant=tenant,
        conversational_search_configs=[tenant_level, other_collection, other_area],
    )

    cfg = tc.get_conversational_config(area_id=999, collection_id=555)
    assert cfg is not None
    assert cfg.id == 1


def test_only_collection_prefers_collection_no_area_then_any_area_then_tenant():
    tenant = TenantModel(id=1, name="acme", enabled=True, settings={})
    tenant_level = make_cfg(10, 1, None, None)
    collection_any_area = make_cfg(11, 1, 1000, 55)
    collection_no_area = make_cfg(12, 1, 1000, None)

    tc = TenantConfig(
        tenant=tenant,
        conversational_search_configs=[tenant_level, collection_any_area, collection_no_area],
    )

    cfg = tc.get_conversational_config(collection_id=1000)
    assert cfg is not None
    # should return the area-agnostic one first
    assert cfg.id == 12

    # If we remove the area-agnostic, should return any area match
    tc.conversational_search_configs = [tenant_level, collection_any_area]
    cfg2 = tc.get_conversational_config(collection_id=1000)
    assert cfg2 is not None and cfg2.id == 11

    # If no collection matches, fall back to tenant-level
    tc.conversational_search_configs = [tenant_level]
    cfg3 = tc.get_conversational_config(collection_id=1000)
    assert cfg3 is not None and cfg3.id == 10
