import pytest
from searcher_example import (
    create_test_documents, 
    search_documents,
    get_index_name,
    es
)

@pytest.fixture(autouse=True)
def cleanup_index():
    """Clean up test index before and after each test"""
    index_name = get_index_name()
    
    # Delete index if it exists
    if es.indices.exists(index=index_name):
        es.indices.delete(index=index_name)
    yield
    if es.indices.exists(index=index_name):
        es.indices.delete(index=index_name)

def test_create_documents():
    """Test document creation"""
    docs = create_test_documents()
    assert len(docs) == 2
    assert docs[0].title == "Python Programming"
    assert docs[1].title == "Machine Learning"

def test_index_and_search():
    """Test indexing and searching documents"""
    # Create and index documents
    docs = create_test_documents()
    # index_documents(docs)
    
    # Search for Python
    results = search_documents("What is Python?")
    assert len(results) > 0
    python_doc = next((doc for doc in results if doc.title == "Python Programming"), None)
    assert python_doc is not None
    assert "Python" in python_doc.text
    assert "programming language" in python_doc.text
    
    # Search for Machine Learning
    results = search_documents("Tell me about machine learning")
    assert len(results) > 0
    ml_doc = next((doc for doc in results if doc.title == "Machine Learning"), None)
    assert ml_doc is not None
    assert "machine learning" in ml_doc.text.lower()
    assert "artificial intelligence" in ml_doc.text.lower() 