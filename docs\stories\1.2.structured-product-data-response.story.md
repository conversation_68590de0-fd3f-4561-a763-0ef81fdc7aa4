# Story 1.2: Structured Product Data Response

## Status
DONE

## Story
**As a** developer,  
**I want to** implement the logic to fetch and shape product data into the new structured format using the existing agentic source,  
**so that** the /conversation endpoint can return rich, detailed product information as specified in the OpenAPI contract.

## Acceptance Criteria
1. The existing or new service can be called from the /conversation handler.
2. The service logic correctly uses source: agentic.
3. The service fetches product data and includes all variant attributes.
4. The final response object matches the ChatResponse schema defined in conversational_shopping_assistant_v1.yaml for a non-streaming request.
5. The response format can be switched between markdown and plain_text using a query parameter.
6. A visitorId is generated and returned if one is not provided in the request.

## Tasks / Subtasks

- [x] Task 1: Refactor user processing logic (AC: 6)
  - [x] Move visitorId generation logic from `_handle_conversation` to `_set_conversation_request_context` in base.py
  - [x] Enhance `_set_conversation_request_context` to handle visitorId generation when not provided
  - [x] Update user_info model to include visitorId field properly
  - [x] Test that user processing works correctly with and without visitorId (covered by existing tests)

- [x] Task 2: Integrate with source factory for actual conversation processing (AC: 1, 2)
  - [x] Replace hardcoded response in `_handle_conversation` with `source_factory.get_answer(stream=False)` call
  - [x] Ensure `source: "agentic"` is set correctly in request configuration before calling source factory
  - [x] Handle source factory response and error cases appropriately
  - [x] Update request configuration with conversation-specific fields (message, collection, area)

- [x] Task 3: Create request models and enhance conversation request processing (AC: 1, 2, 6)
  - [x] Create ChatRequest model class matching OpenAPI schema (message, sessionId, collection, area, visitorId, etc.)
  - [x] Create nested models: Context, UserInfo, Attachment, Options for ChatRequest
  - [x] Add ChatRequest model validation and parsing methods
  - [x] Parse ChatRequest body fields using the model and map to internal request structure
  - [x] Set request.question from chat_request.message using model
  - [x] Set request.source to "agentic" as required by PRD
  - [x] Pass collection and area parameters appropriately to source factory
  - [x] Handle conversation history integration if options.includeHistory is true
  - [x] Test ChatRequest model parsing with sample data

- [x] Task 4: Create response data models (AC: 4)
  - [x] Create ChatResponse model class with required fields (sessionId, responseId, timestamp, content, stream)
  - [x] Verify existing ProductItem model matches OpenAPI schema (reuse from response.py)
  - [x] Create ProductSet and ReferenceItem component models with componentType discrimination
  - [x] Create ActionItem model for cart operations (type, payload)
  - [x] Add model validation and serialization methods for new response models
  - [x] Test response model creation and validation with sample data

- [x] Task 5: Create response shaping logic (AC: 3, 4, 5)
  - [x] Create response transformation function to convert QuepasaResponse to ChatResponse schema
  - [x] Add query parameter parsing for `?format=` parameter (markdown/plain_text)
  - [x] Implement response format transformation logic to switch between markdown and plain_text
  - [x] Ensure default format is "markdown" when parameter not specified
  - [x] Extract product data from search results and format according to ProductItem schema
  - [x] Structure components array with ProductSet and ReferenceItem objects as needed  
  - [x] Map search result metadata to allMeta field including variant attributes
  - [x] Ensure response includes required fields: sessionId, responseId, timestamp, content, stream
  - [x] Handle empty/error responses gracefully with appropriate ChatResponse structure

- [x] Task 6: Add only essential non-superficial unit tests (AC: 1, 2, 3, 4, 5, 6)
  - [x] Test successful conversation processing with actual source factory integration
  - [x] Test response format switching between markdown and plain_text
  - [x] Test ChatResponse schema compliance for structured response
  - [x] Test error handling for source factory failures
  - [x] Test integration with source: "agentic" parameter

## Dev Notes

### Previous Story Insights
**[Source: Story 1.1 Dev Agent Record]**
- ✅ Authentication infrastructure complete with BearerAuth integration
- ✅ Request ID generation working with UUID v4 format  
- ✅ User info mapping established: sessionId → user_info.id for history integration
- ⚠️ **Critical Gap**: Current conversation handler returns hardcoded "Hello!" message
- ✅ Enhanced request models include `body` field and `request_id` generation
- ✅ Backward compatibility maintained for existing endpoints

**Key Technical Foundation Available:**
- BearerAuth working for /conversation endpoint with dual-header validation
- Request body processing established in main.py and base.py
- User context mapping from sessionId to user_info.id implemented
- Request ID generation integrated into response pipeline

### Architecture Context and Requirements

**[Source: docs/CON-76-architecture.md#3.2]**

**Searcher Core RAG Integration:**
- Must reuse existing SourceFactory.get_answer() method entirely without modification
- Call with hardcoded `source: "agentic"` parameter as specified in PRD FR3
- Core business logic resides in `/quepasa/searcher/sources/factory.py`
- Factory returns QuepasaResponse objects that must be transformed

**Response Shaper Component (New):**
- **Analysis**: Existing `/retrieve/answer/stream` mixes data transformation with streaming.
- **Implementation**: Check if we can reuse existing data transformation logic or New dedicated response transformation module required
- Must transform QuepasaResponse → ChatResponse format per OpenAPI spec
- Handle markdown vs plain_text content formatting based on ?format parameter
- Structure products with variant attributes, actions, and references per schema

**[Source: docs/api/conversational_shopping_assistant_v1.yaml]**

**ChatResponse Schema Requirements:**
- **Required Fields**: sessionId, responseId, timestamp, content, stream
- **Optional Fields**: visitorId, status, components, actions
- **Product Structure**: ProductSet components with ProductItem array
- **Variant Data**: Include all variant attributes in allMeta field
- **Content Format**: Support both "markdown" (default) and "plain_text" via ?format parameter

**ProductItem Schema:**
```yaml
required: [id, url, title, collection, allMeta]
properties:
  allMeta: 
    type: object
    additionalProperties: true
    description: "All metadata including images, pricing, attributes, categories, brands, variants"
```

**[Source: docs/CON-76-architecture.md#3.3.1]**

**Non-Streaming Request Flow:**
1. Conversation Handler validates request and extracts sessionId
2. HistoryManager retrieves conversation history (sessionId as user_id)
3. Searcher Core RAG invoked with user query + history + source: agentic
4. **Response Shaper transforms result into ChatResponse JSON structure**
5. History service saves user query + assistant response
6. Single 200 OK response returned with full JSON payload

### Data Models and Response Transformation

**[Source: quepasa/searcher/models/response.py analysis]**

**Existing QuepasaResponse Structure:**
- Contains `data` field with search results including products/references
- Products have metadata fields that map to ChatResponse ProductItem schema
- Need transformation layer: QuepasaResponse → ChatResponse format

**User Processing Enhancement:**
- Current `_set_conversation_user_info` in base.py handles sessionId → user_info.id mapping
- Must extend to handle visitorId generation when not provided
- Generate UUID v4 for missing visitorId and include in user_info

**[Source: quepasa/searcher/sources/factory.py lines 148-183]**

**Source Factory Integration:**
- `get_answer(stream=False)` returns QuepasaResponse or error
- Must set `self.config.request.source = "agentic"` before calling
- Handle both success and error responses appropriately
- Transform QuepasaResponse.data to ChatResponse components structure

**[Source: quepasa/searcher/models/response.py]**

**Existing Models Available for Reuse:**
- ✅ **ProductItem** (lines 45-61): Already has id, title, url, collection, allMeta fields matching OpenAPI schema
- ✅ **Reference** (lines 17-43): Available for ReferenceItem component mapping  
- ✅ **QuepasaResponse/QuepasaAnswer**: Existing response format that needs transformation to ChatResponse
- ❌ **Missing Models**: ChatRequest, ChatResponse, ProductSet, ActionItem, Context, UserInfo, Attachment, Options

### File Locations for Implementation

**[Source: Story 1.1 File List + Architecture]**
- **Main Logic**: `/quepasa/searcher/api/http.py` (_handle_conversation method)
- **User Processing**: `/quepasa/searcher/api/base.py` (_set_conversation_user_info method)  
- **Response Models**: Extend `/quepasa/searcher/models/response.py` if needed
- **Source Integration**: Use existing `/quepasa/searcher/sources/factory.py`
- **Test Extension**: Add to `/tests/test_searcher_conversation.py`

### Testing Requirements

**[Source: Story 1.1 QA Results - Anti-Superficial Testing Principles]**
- Focus on conversation-specific business logic, not framework behavior
- Test actual source factory integration with "agentic" source type
- Validate ChatResponse schema compliance with structured product data
- Test response format switching (markdown vs plain_text)
- Test visitorId generation logic when not provided
- Mock external dependencies (SourceFactory, HistoryManager) appropriately

**Test File Location**: `/tests/test_searcher_conversation.py`
- Extend existing 12-test foundation with new integration tests
- Add tests for response transformation and format switching
- Include error handling tests for source factory failures

### Technical Constraints

**[Source: docs/CON-76-prd.md NFR5 + Architecture]**
- RequestId must remain UUID v4 format and be used as responseId
- All timestamps must be ISO 8601 format with 'Z' suffix
- Response format defaults to "markdown" unless ?format=plain_text specified  
- Must use source: "agentic" when calling searcher core (hardcoded requirement)
- Backward compatibility: existing /retrieve endpoints must remain unaffected

### Integration Points

**Source Factory Call Pattern:**
```python
self.config.request.source = "agentic" 
self.config.request.question = chat_request['message']
response = self.source_factory.get_answer(stream=False)
```

**Response Transformation Pattern:**
```python
# Transform QuepasaResponse → ChatResponse
chat_response = {
    'sessionId': chat_request['sessionId'],
    'responseId': self.config.request.request_id,
    'timestamp': datetime.utcnow().isoformat() + 'Z',
    'content': format_content(response.data, format_type),
    'stream': False,
    'visitorId': user_info.visitor_id,
    'components': extract_components(response.data),
    'actions': extract_actions(response.data)
}
```

## Testing

### Test File Location
- `/tests/test_searcher_conversation.py`

### Test Standards  
- Extend existing 12-test foundation with integration tests
- Use pytest framework with async support
- Follow anti-superficial testing principles from Story 1.1
- Mock external dependencies appropriately (SourceFactory responses)
- Test both success and error scenarios
- Validate ChatResponse schema compliance

### Testing Frameworks and Patterns
- pytest with pytest-asyncio for async test support
- Use existing fixtures and MockRequest patterns from current tests
- Add fixtures for mock QuepasaResponse objects
- Test response transformation logic with various product data structures
- Validate format switching works for both markdown and plain_text

## QA Results

### Test Design Analysis - 2025-01-11

**Test Architect**: Quinn  
**Design Document**: `docs/qa/assessments/1.2-test-design-20250911.md`

**Test Strategy Summary:**
- **Total Scenarios**: 12 tests with anti-superficial focus
- **Distribution**: 8 Unit (67%) | 3 Integration (25%) | 1 E2E (8%)
- **Priority Focus**: 8 P0 tests target revenue-critical paths (product data, API compliance)

**Key Test Coverage:**
- ✅ **AC1-2**: Source factory integration with "agentic" parameter validation  
- ✅ **AC3**: Product data extraction and variant attributes mapping to allMeta
- ✅ **AC4**: ChatResponse schema compliance with required fields validation
- ✅ **AC5**: Format switching logic between markdown and plain_text
- ✅ **AC6**: VisitorId generation when not provided in request

**Risk Mitigation:**
- **DATA-001**: Response transformation errors → Unit tests 1.2-UNIT-003/004/005
- **INTEGRATION-001**: Source factory communication → Integration tests 1.2-INT-001/002
- **BUSINESS-001**: Product data loss → Unit test 1.2-UNIT-003/004
- **USER-001**: Missing visitor tracking → Unit test 1.2-UNIT-009

**Anti-Superficial Testing Applied:**
- Avoided framework behavior testing (FastAPI internals)
- Focused on business logic: source integration, product transformation, schema compliance
- Avoided mock validation and trivial getter/setter tests
- Prioritized by business risk: P0 for revenue-critical, P1 for UX features

**Execution Recommendation**: Run P0 tests first (fail fast), then P1, with P2 E2E as final validation.

### Review Date: 2025-09-12

### Reviewed By: Quinn (Test Architect)

### Code Quality Assessment

**EXCELLENT** - This implementation demonstrates high-quality engineering with comprehensive coverage across all acceptance criteria. The code exhibits clean separation of concerns, proper error handling, and production-ready patterns.

**Architecture Compliance:**
- ✅ **Source Factory Integration**: Correctly reuses existing SourceFactory.get_answer() without modification
- ✅ **Response Transformation**: Well-structured transformation from QuepasaResponse to ChatResponse format
- ✅ **Authentication**: Proper BearerAuth integration with dual-header validation
- ✅ **User Context**: Correct sessionId mapping to user_info.id for history integration

### Refactoring Performed

No refactoring was needed - the implementation is already following best practices and architectural patterns.

### Compliance Check

- Coding Standards: ✅ Follows project conventions, proper constant usage, consistent naming
- Project Structure: ✅ Files placed in correct locations, proper separation of concerns
- Testing Strategy: ✅ Anti-superficial testing applied, 17 comprehensive tests covering all ACs
- All ACs Met: ✅ All 6 acceptance criteria fully implemented and tested

### Improvements Checklist

All items were already properly implemented by the development team:

- [x] Comprehensive conversation endpoint implementation (quepasa/searcher/api/http.py:103-177)
- [x] Source factory integration with "agentic" parameter validation
- [x] Product data extraction and variant attribute mapping to allMeta field
- [x] ChatResponse schema compliance with all required fields
- [x] Format switching logic between markdown and plain_text via query parameter
- [x] Visitor ID generation when not provided in request with secure UUID v4
- [x] Comprehensive test suite with 17 tests covering all business scenarios
- [x] Proper error handling with structured error responses
- [x] Security measures with BearerAuth and input validation

### Security Review

**PASS** - Comprehensive security measures implemented:
- ✅ BearerAuth integration with dual-header validation (X-Customer-Id + Authorization)
- ✅ Input validation with JSON parsing error handling
- ✅ Secure visitor ID generation using UUID v4
- ✅ No sensitive information exposed in error messages
- ✅ Structured error responses following OpenAPI schema
- ⚡ **Future consideration**: Rate limiting for production hardening (infrastructure-level)

### Performance Considerations

**PASS** - Efficient implementation patterns:
- ✅ Reuses existing SourceFactory without duplication
- ✅ Proper cache-control headers prevent caching of sensitive data
- ✅ Fast-fail validation for missing required fields
- ✅ Clean memory management with proper object cleanup
- ⚡ **Future consideration**: Extract transformation logic to separate ResponseShaper when complexity increases

### Files Modified During Review

No files were modified during review - implementation quality was already excellent.

### Gate Status

Gate: **PASS** → docs/qa/gates/1.2-structured-product-data-response.yml
Quality Score: 95/100 (Excellent implementation with minor future optimizations noted)

### Test Results

**ALL TESTS PASSING: 17/17** ✅
- Conversation endpoint integration tests
- Source factory "agentic" parameter validation
- Response format switching (markdown ↔ plain_text)
- ChatResponse schema compliance validation
- Visitor ID generation and user context processing
- Comprehensive error handling scenarios
- Authentication and authorization flows

### Recommended Status

**✅ Ready for Done** - Implementation exceeds quality standards with comprehensive test coverage and production-ready security measures. All acceptance criteria fully satisfied.
