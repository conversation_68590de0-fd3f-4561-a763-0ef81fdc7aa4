import pytest
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

from quepasa.searcher.models.answer import AnswerFormatter, FormattedAnswer
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.response import AnswerType
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.web import WebSearchResult
from src.lib.constants import DOCUMENT_TYPE_DOCUMENT, DOCUMENT_TYPE_DIALOG
from configuration.main.default import QuepasaConfigurationHub

@pytest.fixture
def mock_config():
    config = Mock()
    # Create a mock source reference object with label
    source_ref = Mock(
        label="🌐 example.com",
        reference_id="example.com",
        source_type="web",
        url="https://example.com",
        created_at="2024-01-01",
        occurrence=1
    )
    # Mock source reference methods
    config.get_source_reference.return_value = "🌐 example.com"
    config.get_source_reference_object.return_value = source_ref
    return config

@pytest.fixture
def sample_request():
    return QuepasaRequest(
        client="test_client",
        question="test question",
        source="web"
    )

class TestAnswerFormatter:
    def test_format_answer_basic(self, sample_request, mock_config):
        """Test basic answer formatting without links"""
        with patch('quepasa.searcher.models.answer.QuepasaConfigurationHub') as mock_hub:
            mock_hub.from_request.return_value = mock_config
            
            formatter = AnswerFormatter()
            result = formatter.format_answer(
                request=sample_request,
                text="This is a test answer",
                source_hash={}
            )
            
            assert isinstance(result, FormattedAnswer)
            assert result.text == "This is a test answer"
            assert result.markdown == "This is a test answer"
            assert result.type == AnswerType.WEAK
            assert len(result.references) == 0
        
    def test_format_answer_with_links(self, sample_request, mock_config):
        """Test answer formatting with links"""
        with patch('quepasa.searcher.models.answer.QuepasaConfigurationHub') as mock_hub:
            mock_hub.from_request.return_value = mock_config
            
            formatter = AnswerFormatter()
            
            source_hash = {
                1: QuepasaDocument(
                    root_id="1",
                    id="1",
                    chunk_index=0,
                    client="test_client",
                    domain="example.com",
                    provider="test_provider",
                    type=DOCUMENT_TYPE_DOCUMENT,
                    kind="document",
                    level="info",
                    url="https://example.com",
                    language="en",
                    title=None,
                    keywords=[],
                    text="Example",
                    tokens=0,
                    chunks=1,
                    start_position=0,
                    end_position=0,
                    created_at="2024-01-01",
                    updated_at=None,
                    embeddings=None,
                    score=0.8
                )
            }
            
            text = "Check this source [1] for more info"
            result = formatter.format_answer(
                request=sample_request,
                text=text,
                source_hash=source_hash
            )
            
            assert result.text == text
            assert "🌐 example.com" in result.markdown
            assert len(result.references) == 1
            assert result.references[1].url == 'https://example.com'
            assert result.references[1].type == DOCUMENT_TYPE_DOCUMENT
            assert result.type == AnswerType.WEAK
        
    def test_format_answer_with_multiple_links(self, sample_request, mock_config):
        """Test formatting with multiple links"""
        with patch('quepasa.searcher.models.answer.QuepasaConfigurationHub') as mock_hub:
            mock_hub.from_request.return_value = mock_config
            
            # Configure mock for multiple references
            mock_config.get_source_reference.side_effect = [
                "🌐 example1.com",
                "🌐 example1.com #2",
                "🌐 example2.com"
            ]
            mock_config.get_source_reference_object.side_effect = [
                Mock(label="🌐 example1.com", source_type="web", url="https://example1.com"),
                Mock(label="🌐 example1.com #2", source_type="web", url="https://example1.com"),
                Mock(label="🌐 example2.com", source_type="web", url="https://example2.com")
            ]
            
            formatter = AnswerFormatter()
            
            source_hash = {
                1: QuepasaDocument(
                    root_id="1",
                    id="1",
                    chunk_index=0,
                    client="test_client",
                    domain="example1.com",
                    provider="test_provider",
                    type=DOCUMENT_TYPE_DOCUMENT,
                    kind="document",
                    level="info",
                    url="https://example1.com",
                    language="en",
                    title=None,
                    keywords=[],
                    text="Example",
                    tokens=0,
                    chunks=1,
                    start_position=0,
                    end_position=0,
                    created_at="2024-01-01",
                    updated_at=None,
                    embeddings=None,
                    score=0.8
                ),
                2: QuepasaDocument(
                    root_id="2",
                    id="2",
                    chunk_index=0,
                    client="test_client",
                    domain="example1.com",
                    provider="test_provider",
                    type=DOCUMENT_TYPE_DOCUMENT,
                    kind="document",
                    level="info",
                    url="https://example1.com",
                    language="en",
                    title=None,
                    keywords=[],
                    text="Example",
                    tokens=0,
                    chunks=1,
                    start_position=0,
                    end_position=0,
                    created_at="2024-01-01",
                    updated_at=None,
                    embeddings=None,
                    score=0.8
                ),
                3: QuepasaDocument(
                    root_id="3",
                    id="3",
                    chunk_index=0,
                    client="test_client",
                    domain="example2.com",
                    provider="test_provider",
                    type=DOCUMENT_TYPE_DOCUMENT,
                    kind="document",
                    level="info",
                    url="https://example2.com",
                    language="en",
                    title=None,
                    keywords=[],
                    text="Example",
                    tokens=0,
                    chunks=1,
                    start_position=0,
                    end_position=0,
                    created_at="2024-01-01",
                    updated_at=None,
                    embeddings=None,
                    score=0.8
                )
            }
            
            text = "Sources: [1], [2], and [3]"
            result = formatter.format_answer(
                request=sample_request,
                text=text,
                source_hash=source_hash
            )
            
            assert len(result.references) == 3
            assert "example1.com" in result.markdown
            assert "example2.com" in result.markdown
            assert result.type == AnswerType.WEAK
        
    def test_format_answer_different_sources(self, sample_request, mock_config):
        """Test formatting with different source types"""
        with patch('quepasa.searcher.models.answer.QuepasaConfigurationHub') as mock_hub:
            # Test dialogs source
            sample_request.source = "dialogs"
            
            # Create a mock source reference object for dialogs
            dialog_ref = Mock(
                label="💬 01.01.2024",
                reference_id="01.01.2024",
                source_type="dialog",
                url="https://example.com",
                created_at="2024-01-01"
            )
            
            # Update mock config for dialog source
            mock_config.get_source_reference.return_value = "💬 01.01.2024"
            mock_config.get_source_reference_object.return_value = dialog_ref
            mock_hub.from_request.return_value = mock_config
            
            formatter = AnswerFormatter()
            
            source_hash = {
                1: QuepasaDocument(
                    root_id="1",
                    id="1",
                    chunk_index=0,
                    client="test_client",
                    domain="example.com",
                    provider="test_provider",
                    type=DOCUMENT_TYPE_DIALOG,
                    kind="dialog",
                    level="info",
                    url="https://example.com",
                    language="en",
                    title=None,
                    keywords=[],
                    text="Example",
                    tokens=0,
                    chunks=1,
                    start_position=0,
                    end_position=0,
                    created_at="2024-01-01",
                    updated_at=None,
                    embeddings=None,
                    score=0.8
                )
            }
            
            text = "Check [1] for more info"
            result = formatter.format_answer(
                request=sample_request,
                text=text,
                source_hash=source_hash
            )
            
            assert "💬 01.01.2024" in result.markdown
