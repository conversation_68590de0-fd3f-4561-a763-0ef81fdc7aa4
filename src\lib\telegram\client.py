import os
from typing import Dict, Any, Optional
from telethon import TelegramClient
from src.lib.files import QuepasaFiles
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)

qp_files = QuepasaFiles()

class QuepasaTelegramClient:
    def __init__(self, client_id: str, phone_number: str, files=None):
        self.client_id = client_id
        self.phone_number = phone_number
        self.client = None
        
        self.qp_files = files or qp_files

        self.api_id = '24434318'
        self.api_hash = '5d4f88431bdfa00fa6d77bb7acb244d7'

        self._cache_dir = "external/telegram/sessions"
        self._local_cache_dir = os.path.join(os.path.dirname(__file__), "../../../cache/telegram/sessions")

        # Ensure directory exists
        os.makedirs(self._local_cache_dir, exist_ok=True)

        self.phone_metadata_path = QuepasaTelegramClient.get_phone_metadata_file(self.client_id)

    def _get_session_filename(self):
        return f"{self.client_id}.{self.phone_number.lstrip('+')}.session"

    def _get_local_session_file(self):
        return f"{self._local_cache_dir}/{self._get_session_filename()}"

    def _get_remote_session_file(self):
        return f"{self._cache_dir}/{self._get_session_filename()}"
    
    async def get_telegram_client(self):
        if self.client:
            return self.client
        
        # Load session file
        local_session_file = self._get_local_session_file()
        remote_session_file = self._get_remote_session_file()
        
        if not os.path.isfile(local_session_file):
            if self.qp_files.exists(remote_session_file):
                self.qp_files.download_file(remote_session_file, local_session_file)

        # Create a new Telegram client
        client = TelegramClient(
            local_session_file,
            self.api_id,
            self.api_hash
        )

        # Connect to the Telegram server
        await client.start(self.phone_number)
        await client.connect()

        # Set the client
        self.client = client
        return self.client

    async def send_code_request(self):
        """Request authentication code from Telegram"""
        try:
            local_session_file = self._get_local_session_file()
            self.client = TelegramClient(
                local_session_file,
                self.api_id,
                self.api_hash
            )
            await self.client.connect()
            
            if not await self.client.is_user_authorized():
                sent_code = await self.client.send_code_request(self.phone_number)

                # Extract the phone_code_hash string from the SentCode object
                phone_code_hash = sent_code.phone_code_hash
                
                return {
                    "status": "success",
                    "message": "Authentication code sent to your phone",
                    "data": {
                        "phone_code_hash": phone_code_hash
                    }
                }
            
            else:
                # User is already authorized
                await self.save_session()
                return {
                    "status": "success", 
                    "message": "User is already authorized"
                }
                
        except Exception as e:
            logger.error(f"Error sending code request: {str(e)}", exc_info=True)
            return {"status": "error", "message": str(e)}
        
        finally:
            if self.client and self.client.is_connected():
                await self.client.disconnect()

    async def sign_in(self, code: str, phone_code_hash: str = None, password: str = None):
        """Complete authentication with received code"""
        try:
            local_session_file = self._get_local_session_file()
            self.client = TelegramClient(
                local_session_file,
                self.api_id,
                self.api_hash
            )
            await self.client.connect()
            
            # Sign in with the code
            if not await self.client.is_user_authorized():
                try:
                    # Check if we have a phone_code_hash and use it if provided
                    if phone_code_hash:
                        # Use keyword arguments to avoid positional argument issues
                        await self.client.sign_in(
                            phone=self.phone_number,
                            code=code,
                            phone_code_hash=phone_code_hash
                        )
                    else:
                        # Basic sign in without phone_code_hash
                        await self.client.sign_in(
                            phone=self.phone_number,
                            code=code
                        )

                except Exception as e:
                    if "Two-steps verification" in str(e):
                        # Handle 2FA
                        if not password:
                            return {"status": "error", "message": "Two-factor authentication required"}
                        await self.client.sign_in(password=password)

                    else:
                        raise e
            
            # Save the session to MinIO
            await self.save_session()
            
            return {"status": "success", "message": "Authentication successful"}
        
        except Exception as e:
            logger.error(f"Error signing in: {str(e)}", exc_info=True)
            return {"status": "error", "message": str(e)}
        
        finally:
            if self.client and self.client.is_connected():
                await self.client.disconnect()

    async def save_session(self):
        """Save the session file to MinIO"""
        try:
            local_session_file = self._get_local_session_file()
            remote_session_file = self._get_remote_session_file()
            
            if os.path.isfile(local_session_file):
                self.qp_files.upload_file(local_session_file, remote_session_file)
                logger.info(f"Remote session updated at {remote_session_file}")

                await self._save_phone_metadata()
                return True
            
            else:
                logger.error("Local session file not found")
                return False
            
        except Exception as e:
            logger.error(f"Error saving session: {str(e)}", exc_info=True)
            return False

    @staticmethod
    def get_phone_metadata_file(client_id: str) -> str:
        return f"external/telegram/sessions/{client_id}.metadata.json"

    @staticmethod
    def get_phone_metadata(client_id: str, files = None) -> Dict[str, Any]:
        """Get phone metadata for future use"""
        local_qp_files = files or qp_files

        try:
            # Check if metadata already exists
            existing_metadata = {}
            phone_metadata_path = QuepasaTelegramClient.get_phone_metadata_file(client_id)
            if local_qp_files.exists(phone_metadata_path):
                try:
                    existing_metadata = local_qp_files.get_json(phone_metadata_path)

                except Exception as e:
                    logger.warning(f"Could not read existing metadata: {str(e)}")

            return existing_metadata

        except Exception as e:
            logger.error(f"Error getting phone metadata: {str(e)}", exc_info=True)
            return None


    @staticmethod
    def get_external_username_mappings_file(client_id: str) -> str:
        return f"external/telegram/external-username-mappings/{client_id}.json"

    @staticmethod
    def set_external_username_mappings(client_id: str, external_username_mappings: Dict[str, Dict[str, Any]], files = None):
        """Set external username mappings for future use"""
        local_qp_files = files or qp_files
        
        try:
            external_username_mappings_path = QuepasaTelegramClient.get_external_username_mappings_file(client_id)
            local_qp_files.set_json(external_username_mappings_path, external_username_mappings)

        except Exception as e:
            logger.error(f"Error setting external username mappings: {str(e)}", exc_info=True)

    @staticmethod
    def get_external_username_mappings(client_id: str, files = None) -> Dict[str, Dict[str, Any]]:
        """Get external username mappings for future use"""
        local_qp_files = files or qp_files
        
        try:
            # Check if metadata already exists
            external_username_mappings = {}
            external_username_mappings_path = QuepasaTelegramClient.get_external_username_mappings_file(client_id)
            if local_qp_files.exists(external_username_mappings_path):
                try:
                    external_username_mappings = local_qp_files.get_json(external_username_mappings_path)

                except Exception as e:
                    logger.warning(f"Could not read existing external username mappings: {str(e)}")

            return external_username_mappings

        except Exception as e:
            logger.error(f"Error getting external username mappings: {str(e)}", exc_info=True)
            return None
                        

    async def _save_phone_metadata(self) -> None:
        """Save phone number to metadata for future use"""
        try:
            # Check if metadata already exists
            existing_metadata = {}
            if self.qp_files.exists(self.phone_metadata_path):
                try:
                    existing_metadata = self.qp_files.get_json(self.phone_metadata_path)

                except Exception as e:
                    logger.warning(f"Could not read existing metadata: {str(e)}")
            
            # Initialize or update phone_numbers list
            if 'phone_numbers' not in existing_metadata:
                existing_metadata['phone_numbers'] = []
            
            # Add the phone number if it's not already in the list
            if self.phone_number not in existing_metadata['phone_numbers']:
                existing_metadata['phone_numbers'].append(self.phone_number)
            
            # Keep the most recently used phone number as the default
            existing_metadata['default_phone'] = self.phone_number
            
            # Save updated metadata
            self.qp_files.set_json(self.phone_metadata_path, existing_metadata)
            logger.info(f"Saved phone metadata for client {self.client_id}")

        except Exception as e:
            logger.error(f"Error saving phone metadata: {str(e)}", exc_info=True)
            # Don't raise - this is a helper function
