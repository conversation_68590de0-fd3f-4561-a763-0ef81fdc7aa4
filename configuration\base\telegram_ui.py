from typing import Optional, List, Dict, Any, Tuple, Union
from telegram import InlineKeyboardButton
from quepasa.searcher.models.request import QuepasaRequest
from .telegram_config import TelegramConfig
from .telegram_utils import TelegramUtils

class TelegramUIConfig(TelegramUtils):
    """Base configuration for Telegram UI elements."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def get_telegram_first_time_welcome_message(self, format: str = 'text') -> Optional[str]:
        """Get welcome message for first-time users.
        
        Previously: get_welcome_chat() and get_welcome_chat_markdown()
        
        Args:
            format: Message format ('text' or 'markdown')
        """
        return None
    
    def get_telegram_start_command_response(self) -> Optional[str]:
        """Get response for /start command.
        
        Previously: get_start()
        """
        return self._get_telegram_command_text('start')

    def get_telegram_empty_ask_command_response(self) -> Optional[str]:
        """Get help text for /ask command.
        
        Previously: get_default_ask()
        """
        return self._get_telegram_command_text('ask')
    
    def get_telegram_available_commands(self) -> List[Dict[str, str]]:
        """Get list of available bot commands and their descriptions.
        
        Previously: get_command_list()
        """
        return list(self.get_telegram_command_descriptions().keys())
    
    def get_telegram_command_descriptions(self) -> Dict[str, str]:
        """Get descriptions for Telegram bot commands.
        
        Previously: get_command_setup()
        """
        return {
            'start': "Bot info",
            'ask':   "Ask a question",
        }

    def get_telegram_empty_command_response(self) -> str:
        """Get response for commands used without parameters.
        
        Previously: get_command_empty_reply()
        """
        if not self.request:
            return "Just ask me a question."
            
        command = self.request.command
        if command == 'reset':
            return "Chat history has been reset!"
        return "Just ask me a question."

    def get_telegram_positive_feedback_response(self, language_code: str, format: str = 'text') -> Optional[str]:
        """Get response for positive feedback (thumbs up).
        
        Previously: get_feedback_positive_message_markdown()
        """
        return None

    def get_telegram_negative_feedback_response(self, language_code: str, format: str = 'text') -> Optional[str]:
        """Get response for negative feedback (thumbs down).
        
        Previously: get_feedback_negative_message_markdown()
        """
        return None

    def get_telegram_response_buttons(self, source: str, response: Dict[str, Any], buttons: List[Any]) -> List[Any]:
        """Get buttons to add to bot response.
        
        Previously: get_chat_buttons()
        """
        return buttons 

    def get_no_results_message(self, language_code: str, source: Optional[str] = None,
                               recommended_channels: Optional[List[str]] = None) -> Tuple[str, List[Any]]:
        """Get message to show when no good results found.
        
        Previously: get_poor_quality_answer_markdown()
        
        Args:
            language_code: Language code for the message
            source: Source type that was searched
            recommended_channels: List of recommended channel IDs
            
        Returns:
            Tuple of (message, buttons)
        """
        return "I couldn't find an answer to your question.", []

    def should_show_preview_links(self) -> bool:
        """Whether to show preview links to dialog website.
        
        Previously: use_dialog_preview_url()
        """
        return False

    def should_show_webapp_preview(self) -> bool:
        """Whether to show answers in webapp preview.
        
        Previously: is_show_preview()
        """
        return True
