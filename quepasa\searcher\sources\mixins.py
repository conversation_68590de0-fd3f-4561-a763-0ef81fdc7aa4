import re
from typing import Dict, Any, List, Optional, Union, Generator
import time
import traceback

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>og<PERSON>
from src.lib.constants import ANSWER_TYPE_STRICT, ANSWER_TYPE_WEAK
from src.lib.llm_utils import get_cached_streaming_chunks

from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse, QuepasaStreamAnswer
from ..models.answer import AnswerFormatter
from configuration.main.default import QuepasaConfigurationHub
from ..models.spd_result import SPDSearchResult

from ..utils import (
    SOURCES_PLACEHOLDER,
    LANGUAGE_PLACEHOLDER,
    QUESTION_PLACEHOLDER,
    get_prepared_response_dict,
    replace_placeholders_in_prompt_messages,
    get_encoding,
    is_unique_document,
    get_serializable_request,
    get_llm_model_name,
    get_llm_prompt_template,
    get_think_and_answer_from_content
)
from src.lib.utils import ALLOWED_LANGUAGES

from ..models.document import QuepasaDocument
from ..models.web import WebSearchResult
from .base import STATUS_SUCCESS, STATUS_ERROR, ERROR_MESSAGE_NO_RELEVANT_ANSWER

logger = QuepasaLogger().get_instance(__name__)

class AnswerRetrieverMixin:
    """Mixin for retrieving answers from search results"""
    
    def retrieve_answer(
        self,
        request: QuepasaRequest,
        source: str,
        documents: List[Union[QuepasaDocument, WebSearchResult, SPDSearchResult]],
        stream: bool = False
    ) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """Retrieve answer from search results.
        
        Args:
            request: Original search request
            source: Source identifier (e.g. SOURCE_WEB, SOURCE_DOCUMENTS)
            documents: List of search result documents
            stream: Whether to stream the response
            
        Returns:
            Response with answer or error
        """
        try:
            # Initialize common components
            config = QuepasaConfigurationHub.from_request(request)
            formatter = AnswerFormatter()
            
            # Get LLM configuration
            provider, model = get_llm_model_name(request.answer_llm) if request.answer_llm else config.get_llm_model_name(source)
            logger.info(f"Retrieved LLM configuration: provider: {provider}, model: {model}, request.answer_llm: {request.answer_llm}")

            llm_params = config.get_llm_parameters(source)
            
            # Process documents and build prompt
            prompt_data = self._prepare_prompt_data(request, source, documents, config)
            if not prompt_data['source_hash']:
                return self._handle_error(request, ERROR_MESSAGE_NO_RELEVANT_ANSWER, stream)
            
            # Get answer based on streaming preference
            if stream:
                return self._handle_streaming_answer(
                    request=request,
                    source=source,
                    prompt_data=prompt_data,
                    config=config,
                    formatter=formatter,
                    provider=provider,
                    model=model,
                    llm_params=llm_params
                )
            
            else:
                return self._handle_nonstreaming_answer(
                    request=request,
                    source=source,
                    prompt_data=prompt_data,
                    config=config,
                    formatter=formatter,
                    provider=provider,
                    model=model,
                    llm_params=llm_params
                )
                
        except Exception as e:
            logger.error(f"Error processing search results: {str(e)}")
            logger.error("Traceback:")
            for line in traceback.format_exc().splitlines():
                logger.error(line)
            return self._handle_error(request, f"Error processing search results: {str(e)}", stream)

    def _prepare_prompt_data(
        self,
        request: QuepasaRequest,
        source: str,
        documents: List[Union[QuepasaDocument, WebSearchResult, SPDSearchResult]],
        config: QuepasaConfigurationHub
    ) -> Dict[str, Any]:
        """Prepare prompt data from documents.
        
        Args:
            request: Search request
            source: Source identifier
            documents: List of documents
            config: Configuration hub
            
        Returns:
            Dictionary containing prompt data and metadata
        """
        # Get LLM model
        _, model = get_llm_model_name(request.answer_llm) if request.answer_llm else config.get_llm_model_name(source)

        language_name = ALLOWED_LANGUAGES[config.get_fallback_language()]
        if (
            request.classification
            and hasattr(request.classification, 'language_code')
            and request.classification.language_code in ALLOWED_LANGUAGES
        ):
            language_name = ALLOWED_LANGUAGES[request.classification.language_code]

        # Initialize tokenizer
        encoding = get_encoding(model)
        
        # Get prompt template and size limits
        prompt_messages = get_llm_prompt_template(request.prompt) if request.prompt else config.get_llm_prompt_template(source)

        # Add history
        if request.history:
            # Append befor last message
            prompt_messages[-1:-1] = [
                {
                    'role': history_item.role,
                    'content': history_item.content,
                }
                for history_item in request.history
                if history_item.role in ["user", "assistant"]
            ]
            
        max_prompt_tokens = config.get_max_prompt_tokens(source)
        
        # Initialize tracking for unique documents
        sources = ""
        skipped_documents = []
        source_hash = {}
        source_index = 1
        seen_keys = set()
        
        # Process each document
        for document in documents:                
            # Skip empty or duplicate documents
            if (
                config.should_deduplicate_results(source)
                and not is_unique_document(document, seen_keys)
            ):
                skipped_documents.append(document)
                continue
                
            # Get formatted context chunk
            append_prompt = config.format_context_chunk(source, source_index, document) + "\n\n"
            
            # Check if adding this document would exceed prompt limits
            full_prompt = replace_placeholders_in_prompt_messages(
                prompt_messages,
                {
                    SOURCES_PLACEHOLDER: sources + append_prompt,
                    LANGUAGE_PLACEHOLDER: language_name,
                    QUESTION_PLACEHOLDER: request.question
                }
            )
            
            prompt_tokens = sum(len(encoding.encode(msg['content'])) for msg in full_prompt)
            if prompt_tokens > max_prompt_tokens:
                skipped_documents.append(document)
                continue
            
            # Add document to sources
            sources += append_prompt
            source_hash[source_index] = document
            source_index += 1

        # Build final prompt
        final_prompt = replace_placeholders_in_prompt_messages(
            prompt_messages,
            {
                SOURCES_PLACEHOLDER: sources,
                LANGUAGE_PLACEHOLDER: language_name,
                QUESTION_PLACEHOLDER: request.question
            }
        )
        
        return {
            'prompt_messages': prompt_messages,
            'final_prompt': final_prompt,
            'sources': sources,
            'source_hash': source_hash,
            'skipped_documents': skipped_documents
        }

    def _handle_streaming_answer(
        self,
        request: QuepasaRequest,
        source: str,
        prompt_data: Dict[str, Any],
        config: QuepasaConfigurationHub,
        formatter: AnswerFormatter,
        provider: str,
        model: str,
        llm_params: Dict[str, Any]
    ) -> Generator[QuepasaStreamAnswer, None, None]:
        """Handle streaming answer generation.
        
        Args:
            request: Search request
            source: Source identifier
            prompt_data: Prepared prompt data
            config: Configuration hub
            formatter: Answer formatter
            provider: LLM provider
            model: Model name
            llm_params: LLM parameters
            
        Yields:
            Stream of answer chunks
        """
        # Initial state
        yield QuepasaStreamAnswer(
            type=None,
            text=None,
            markdown=None,
            references=None,
            loading=True,
            streaming=False,
            created_at=int(time.time())
        )
                
        # Get streaming chunks
        collected_answer = []
        for chunk in get_cached_streaming_chunks(
            provider,
            model,
            prompt_data['final_prompt'],
            config.get_max_response_tokens(source, model),
            llm_params.get('json_mode', False)
        ):
            collected_answer.append(chunk)
            
            # Process answer
            think, answer = get_think_and_answer_from_content(''.join(collected_answer))

            source_hash = prompt_data['source_hash']
            answer, source_hash = config.process_answer(source, True, answer, source_hash)

            formatted_answer = formatter.format_answer(request, answer, source_hash)
            yield QuepasaStreamAnswer(
                type=f"{formatted_answer.type.value}.{source}",
                think=think,
                text=formatted_answer.text,
                markdown=formatted_answer.markdown,
                references=formatted_answer.references,
                loading=False,
                streaming=True,
                created_at=int(time.time())
            )
        
        # Process answer
        think, answer = get_think_and_answer_from_content(''.join(collected_answer))
        logger.info(f"Think: {think}")    
        logger.info(f"Answer: {answer}")

        source_hash = prompt_data['source_hash']
        answer, source_hash = config.process_answer(source, False, answer, source_hash)

        # Final response
        formatted_answer = formatter.format_answer(request, answer, source_hash)
        response_dict = self._prepare_response_dict(
            request=request,
            source=source,
            formatted_answer=formatted_answer,
            prompt_data=prompt_data,
            think=think,
            provider=provider,
            model=model,
            llm_params=llm_params
        )

        processed_response = config.process_response(response_dict)
        response_dict = processed_response.to_dict() if hasattr(processed_response, 'to_dict') else processed_response
        del response_dict['metadata']
        
        yield QuepasaStreamAnswer(
            loading=False,
            streaming=False,
            created_at=int(time.time()),
            **response_dict
        )

    def _handle_nonstreaming_answer(
        self,
        request: QuepasaRequest,
        source: str,
        prompt_data: Dict[str, Any],
        config: QuepasaConfigurationHub,
        formatter: AnswerFormatter,
        provider: str,
        model: str,
        llm_params: Dict[str, Any]
    ) -> QuepasaResponse:
        """Handle non-streaming answer generation.
        
        Args:
            request: Search request
            source: Source identifier
            prompt_data: Prepared prompt data
            config: Configuration hub
            formatter: Answer formatter
            provider: LLM provider
            model: Model name
            llm_params: LLM parameters
            
        Returns:
            Complete answer response
        """
        # Get complete answer
        collected_answer = []
        for chunk in get_cached_streaming_chunks(
            provider,
            model,
            prompt_data['final_prompt'],
            config.get_max_response_tokens(source, model),
            llm_params.get('json_mode', False)
        ):
            collected_answer.append(chunk)
            
        # Process answer
        think, answer = get_think_and_answer_from_content(''.join(collected_answer))
        logger.info(f"Think: {think}")
        logger.info(f"Answer: {answer}")

        source_hash = prompt_data['source_hash']
        answer, source_hash = config.process_answer(source, False, answer, source_hash)

        # Format answer
        formatted_answer = formatter.format_answer(request, answer, source_hash)
        
        # Prepare response
        response_dict = self._prepare_response_dict(
            request=request,
            source=source,
            formatted_answer=formatted_answer,
            prompt_data=prompt_data,
            think=think,
            provider=provider,
            model=model,
            llm_params=llm_params
        )

        # Process and return
        processed_response = config.process_response(response_dict)
        response_dict = processed_response.to_dict() if hasattr(processed_response, 'to_dict') else processed_response
        
        return QuepasaResponse(
            status=STATUS_SUCCESS,
            data=response_dict
        )

    def _prepare_response_dict(
        self,
        request: QuepasaRequest,
        source: str,
        formatted_answer: Any,
        prompt_data: Dict[str, Any],
        think: Optional[str] = None,
        provider: str = None,
        model: str = None,
        llm_params: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Prepare response dictionary.
        
        Args:
            request: Search request
            source: Source identifier
            formatted_answer: Formatted answer object
            prompt_data: Prompt data dictionary
            provider: LLM provider
            model: Model name
            llm_params: LLM parameters
            
        Returns:
            Response dictionary
        """
        return get_prepared_response_dict(
            request=request,
            source=source,
            formatted_answer=formatted_answer,
            prompt_data=prompt_data,
            think=think,
            provider=provider,
            model=model,
            llm_params=llm_params
        )

    def _handle_error(
        self,
        request: QuepasaRequest,
        error_message: str,
        stream: bool
    ) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """Handle error responses.
        
        Args:
            request: Search request
            error_message: Error message
            stream: Whether to stream the response
            
        Returns:
            Error response
        """
        if stream:
            def error_generator():
                yield QuepasaStreamAnswer(
                    type=STATUS_ERROR,
                    text=error_message,
                    loading=False,
                    streaming=False,
                    created_at=int(time.time())
                )
            return error_generator()
            
        return QuepasaResponse(
            status="error",
            error=error_message
        )
