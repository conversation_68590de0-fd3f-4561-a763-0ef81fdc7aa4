# CON-69: Product Metadata Storage Implementation - PRD

## Overview

Implement storage-based structured product metadata to enhance conversational API responses with JSON product data alongside existing markdown text.

## Requirements

### Core Functionality

#### 1. Enhanced Response Structure

**Current Response:**
```json
{
  "status": "success",
  "data": {
    "type": "answer",
    "text": "Here are some great t-shirts...",
    "markdown": "# Printed Tie Dye T-shirt\n**SKU:** 1100351...",
    "references": {}
  }
}
```

**Required Response:**
```json
{
  "status": "success", 
  "data": {
    "type": "answer",
    "text": "Here are some great t-shirts...",
    "markdown": "# Printed Tie Dye T-shirt\n**SKU:** 1100351...",
    "references": {},
    "products": [
      {
        "sku": "1100351",
        "title": "Printed Tie Dye T-shirt Groen",
        "url": "https://www.shoeby.nl/...",
        "metadata": {
          "id": "1100351",
          "title": "Printed Tie Dye T-shirt Groen",
          "description": "Een t-shirt met een print in tie dye stijl...",
          "brands": ["Shoeby"],
          "categories": ["apparel > tops > shirt > t-shirt"],
          "availability": "IN_STOCK",
          "priceInfo": {"price": 12.99, "originalPrice": 19.99},
          "variants": [...],
          "images": [...]
        }
      }
    ]
  }
}
```

#### 2. Document Type Enhancement

**Add New Document Type:**
- Type: `product` (alongside existing `document`, `dialog`, etc.)
- Distinguishes products from policies/documents/URLs
- Products populate `products[]` array in response
- Non-products populate `references[]` array in response (doesn't change existing behavior)

#### 3. Metadata Storage Requirements

**Storage Location:** Document-level `metadata` field
**Content:** Filtered product JSON using new separate method for metadata filtering
**Format:** JSON string/text containing cleaned product data

**Filtering Logic:**
- **For Markdown**: Keep existing `_delete_unnecessary_fields()` method unchanged
- **For JSON Metadata**: Use new separate filtering method with minimal exclusions
- **Metadata Exclusion List**: Only remove `METADATA_EXCLUDE_KEYS = { "_id", "indexables", "samplePrecision", "dynamicFacets" }`
- **Preserve**: All other product information including id, title, description, price, variants, images, attributes, etc.

### Data Model Changes

#### 1. Document Model Extension

**Current Document Model:**
```python
@dataclass
class Document:
    id: str
    url: str
    sku: Optional[str] = None
    price_from: Optional[float] = None
    price_to: Optional[float] = None
    # ... existing fields
```

**Required Enhancement:**
```python
@dataclass  
class Document:
    id: str
    url: str
    sku: Optional[str] = None
    price_from: Optional[float] = None
    price_to: Optional[float] = None
    metadata: Optional[str] = None  # NEW: JSON string of filtered product data
    # ... existing fields
```

#### 2. QuepasaAnswer Model Extension

**Current Model:**
```python
@dataclass
class QuepasaAnswer:
    type: Optional[str] = None
    text: Optional[str] = None
    references: Optional[Dict[str, Reference]] = None
    markdown: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
```

**Required Enhancement:**
```python
@dataclass
class QuepasaAnswer:
    type: Optional[str] = None
    text: Optional[str] = None
    references: Optional[Dict[str, Reference]] = None
    markdown: Optional[str] = None
    products: Optional[List[QuepasaProductResponse]] = field(default=None)  # NEW
    metadata: Dict[str, Any] = field(default_factory=dict)
```

#### 3. QuepasaProductResponse Model

**New API Response Model:**
```python
@dataclass
class QuepasaProductResponse:
    sku: str
    title: str
    url: str
    metadata: Dict[str, Any]
```

#### 4. Document Type Classification

**Type Field Values:**
- `document` - Policies, documentation, general content
- `product` - Product catalog items
- `dialog` - Conversation history
- Other existing types remain unchanged

### Implementation Requirements

#### 1. Product Ingestion Enhancement
**File:** `src/lib/markdown_converter.py`

**High-Level Changes:**
1. Add `_filter_product_for_metadata()` method with `METADATA_EXCLUDE_KEYS`
2. Update `products_to_documents()` to store filtered JSON in `metadata` field
3. Set document `type` to `product`

#### 2. Document Model Enhancement
**Files:** `quepasa/api/handlers/document_handler.py`, `quepasa/searcher/models/document.py`

**High-Level Changes:**
1. Add `metadata` field to `Document` class
2. Create `QuepasaProductResponse` class for API responses (per CON-69 requirement #4)

#### 3. Search Enhancement
**File:** `quepasa/searcher/core/rag_search.py`

**High-Level Changes:**
1. Retrieve `metadata` field from Elasticsearch documents
2. Add metadata to `QuepasaDocument` objects (add `metadata` field to class)

**Note:** `QuepasaProductResponse` objects should NOT be returned from search - keep existing `QuepasaDocument`/`SPDSearchResult` types to maintain compatibility with meta search mixing algorithm.

#### 4. Meta Search Enhancement
**File:** `quepasa/searcher/sources/meta.py`

**High-Level Changes:**
1. Combine RAG results (with stored metadata) and SNPD results (with live metadata)
2. Both populate same `products[]` array structure

#### 5. Response Generation Enhancement
**File:** `quepasa/searcher/sources/mixins.py`

**High-Level Changes:**
1. Process mixed document types from search results
2. Convert `QuepasaDocument` (type="product") → `QuepasaProductResponse` → `products[]`
3. Convert `SPDSearchResult` → `QuepasaProductResponse` → `products[]`
4. Maintain `references[]` for non-product documents

#### 6. Storage Schema

#### 6. Storage Schema
**Elasticsearch Mapping:** ✅ `type` field already exists (confirmed in mapping)
**New Field:** Add `metadata` field as text/non-indexed

## Key Implementation Notes

### 1. Elasticsearch Mapping
- Add `metadata` field as text/non-indexed

### 2. Meta Search Integration
- Current meta search combines RAG + SNPD results in `get_answer()`
- Both result types should populate same `products[]` array structure
- RAG results: use stored metadata from ingestion
- SNPD results: use live metadata from API

### 3. CON-69 Requirement #4 Compliance
- Create `QuepasaProductResponse` class with `sku`, `title`, `url`, `metadata` fields
- Use for **API response processing only** (not in search results)
- Convert from both `QuepasaDocument` (type="product") and `SPDSearchResult` during response generation
- ✅ **SNPD Compatibility**: `SPDSearchResult` has exact matching fields for easy conversion

## Success Criteria

1. **Functional Requirements:**
   - Products return structured metadata in `products[]` array
   - Existing markdown responses unchanged
   - Document type classification works correctly

2. **Data Integrity:**
   - Filtered product metadata stored correctly
   - JSON parsing/serialization works reliably
   - No data loss during ingestion process

3. **API Compatibility:**
   - Existing API consumers continue working
   - New `products[]` field available for enhanced consumers
   - Backward compatibility maintained

4. **System Integration:**
   - Product ingestion pipeline enhanced successfully
   - Search results include metadata
   - Response generation populates new fields correctly
