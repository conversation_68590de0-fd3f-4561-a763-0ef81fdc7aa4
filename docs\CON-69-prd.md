# CON-69: Product Metadata Storage Implementation - PRD

## Overview

Implement storage-based structured product metadata to enhance conversational API responses with JSON product data alongside existing markdown text.

## Requirements

### Core Functionality

#### 1. Enhanced Response Structure

**Current Response:**
```json
{
  "status": "success",
  "data": {
    "type": "answer",
    "text": "Here are some great t-shirts...",
    "markdown": "# Printed Tie Dye T-shirt\n**SKU:** 1100351...",
    "references": {}
  }
}
```

**Required Response:**
```json
{
  "status": "success", 
  "data": {
    "type": "answer",
    "text": "Here are some great t-shirts...",
    "markdown": "# Printed Tie Dye T-shirt\n**SKU:** 1100351...",
    "references": {},
    "products": [
      {
        "sku": "1100351",
        "title": "Printed Tie Dye T-shirt Groen",
        "url": "https://www.shoeby.nl/...",
        "metadata": {
          "id": "1100351",
          "title": "Printed Tie Dye T-shirt Groen",
          "description": "Een t-shirt met een print in tie dye stijl...",
          "brands": ["Shoeby"],
          "categories": ["apparel > tops > shirt > t-shirt"],
          "availability": "IN_STOCK",
          "priceInfo": {"price": 12.99, "originalPrice": 19.99},
          "variants": [...],
          "images": [...]
        }
      }
    ]
  }
}
```

#### 2. Document Type Enhancement

**Add New Document Type:**
- Type: `product` (alongside existing `document`, `dialog`, etc.)
- Distinguishes products from policies/documents/URLs
- Products populate `products[]` array in response
- Non-products populate `references[]` array in response (doesn't change existing behavior)

#### 3. Metadata Storage Requirements

**Storage Location:** Document-level `metadata` field
**Content:** Filtered product JSON using new separate method for metadata filtering
**Format:** JSON string/text containing cleaned product data

**Filtering Logic:**
- **For Markdown**: Keep existing `_delete_unnecessary_fields()` method unchanged
- **For JSON Metadata**: Use new separate filtering method with minimal exclusions
- **Metadata Exclusion List**: Only remove `{ "_id", "indexables", "samplePrecision", "dynamicFacets" }`
- **Preserve**: All other product information including id, title, description, price, variants, images, attributes, etc.

### Data Model Changes

#### 1. Document Model Extension

**Current Document Model:**
```python
@dataclass
class Document:
    id: str
    url: str
    sku: Optional[str] = None
    price_from: Optional[float] = None
    price_to: Optional[float] = None
    # ... existing fields
```

**Required Enhancement:**
```python
@dataclass  
class Document:
    id: str
    url: str
    sku: Optional[str] = None
    price_from: Optional[float] = None
    price_to: Optional[float] = None
    metadata: Optional[str] = None  # NEW: JSON string of filtered product data
    # ... existing fields
```

#### 2. QuepasaAnswer Model Extension

**Current Model:**
```python
@dataclass
class QuepasaAnswer:
    type: Optional[str] = None
    text: Optional[str] = None
    references: Optional[Dict[str, Reference]] = None
    markdown: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
```

**Required Enhancement:**
```python
@dataclass
class QuepasaAnswer:
    type: Optional[str] = None
    text: Optional[str] = None
    references: Optional[Dict[str, Reference]] = None
    markdown: Optional[str] = None
    products: List[Dict[str, Any]] = field(default_factory=list)  # NEW
    metadata: Dict[str, Any] = field(default_factory=dict)
```

#### 3. Document Type Classification

**Type Field Values:**
- `document` - Policies, documentation, general content
- `product` - Product catalog items
- `dialog` - Conversation history
- Other existing types remain unchanged

### Implementation Requirements

#### 1. Product Ingestion Enhancement

**File:** `src/lib/markdown_converter.py`
**Function:** `products_to_documents()`

**Required Changes:**
1. Add new metadata filtering method with minimal exclusions
2. Store filtered product JSON in `metadata` field
3. Set document `type` to `product`
4. Preserve existing markdown generation logic (keep `_delete_unnecessary_fields()` unchanged)

**New Method Required:**
```python
METADATA_EXCLUDE_KEYS = {"_id", "indexables", "samplePrecision", "dynamicFacets"}

def _filter_product_for_metadata(product: Dict[str, Any]) -> Dict[str, Any]:
    """Filter product data for JSON metadata storage with minimal exclusions"""
    return {k: v for k, v in product.items() if k not in METADATA_EXCLUDE_KEYS}
```

**Implementation:**
```python
def products_to_documents(products: List[Dict[str, Any]], ...):
    documents = []
    for product in products:
        # Apply existing filtering for markdown (unchanged)
        md_ready_product = _preprocess_product(product, exclude_keys, exclude_attr_keys, exclude_var_keys)

        # Apply minimal filtering for metadata storage (new)
        metadata_product = _filter_product_for_metadata(product)

        # Create document with metadata
        document_data = {
            'id': md_ready_product['SKU'],
            'url': md_ready_product.get('uri', ''),
            'title': md_ready_product.get('title', ''),
            'sku': str(md_ready_product['SKU']),
            'type': 'product',  # NEW
            'metadata': json.dumps(metadata_product),  # NEW - uses minimal filtering
            'price_from': price_from,
            'price_to': price_to,
            'chunks': chunks
        }
        documents.append(document_data)
```

#### 2. Search Result Enhancement

**File:** `quepasa/searcher/core/rag_search.py`
**Class:** `RagSearchManager`

**Required Changes:**
1. Retrieve `metadata` field from stored documents
2. Parse JSON metadata for product-type documents
3. Return metadata alongside existing document fields

#### 3. Response Generation Enhancement

**File:** `quepasa/searcher/sources/mixins.py`
**Class:** `AnswerRetrieverMixin`

**Required Changes:**
1. Identify product-type documents in search results
2. Extract and parse metadata from product documents
3. Populate `products[]` array in `QuepasaAnswer`
4. Maintain existing `references[]` for non-product documents

**Logic:**
```python
def process_search_results(documents):
    products = []
    references = {}
    
    for doc in documents:
        if doc.type == 'product' and doc.metadata:
            # Parse stored metadata
            product_data = json.loads(doc.metadata)
            products.append({
                'sku': doc.sku,
                'title': doc.title,
                'url': doc.url,
                'metadata': product_data
            })
        else:
            # Add to references as before
            references[str(len(references) + 1)] = {
                'title': doc.title,
                'url': doc.url
            }
    
    return products, references
```

#### 4. Storage Schema Updates

**Elasticsearch Mapping:**
```json
{
  "mappings": {
    "properties": {
      "type": {"type": "keyword"},
      "metadata": {"type": "text", "index": false}
    }
  }
}
```

**MinIO Storage:**
- No changes required - existing document storage handles new fields

### API Compatibility

#### Backward Compatibility
- Existing `references[]` field maintained for non-product documents
- Existing `markdown` and `text` fields unchanged
- New `products[]` field added without breaking existing consumers

#### Forward Compatibility
- `products[]` array structure allows for future enhancements
- `metadata` field can accommodate additional product data
- Document type system supports new types without schema changes

### Validation Requirements

#### Input Validation
- Ensure product JSON contains required fields (`id`, `title`)
- Validate metadata JSON structure before storage
- Handle malformed product data gracefully

#### Output Validation
- Ensure `products[]` array contains valid product objects
- Validate metadata JSON parsing during response generation
- Handle missing or corrupted metadata gracefully

### Configuration

#### Feature Flags
- Enable/disable structured metadata per client
- Gradual rollout capability
- Fallback to existing behavior if disabled

#### Client Configuration
- Maintain existing exclusion key configurations
- Support client-specific metadata filtering rules
- Preserve existing product ingestion settings

## Success Criteria

1. **Functional Requirements:**
   - Products return structured metadata in `products[]` array
   - Existing markdown responses unchanged
   - Document type classification works correctly

2. **Data Integrity:**
   - Filtered product metadata stored correctly
   - JSON parsing/serialization works reliably
   - No data loss during ingestion process

3. **API Compatibility:**
   - Existing API consumers continue working
   - New `products[]` field available for enhanced consumers
   - Backward compatibility maintained

4. **System Integration:**
   - Product ingestion pipeline enhanced successfully
   - Search results include metadata
   - Response generation populates new fields correctly
