import os
import re
import hashlib
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
from urllib.parse import urlparse

from src.lib.files import QuepasaFiles
from src.lib.logger import QuepasaLogger

import ssl
import tempfile
import tiktoken
from src.lib.constants import (
    DEFAULT_TOKENIZER_MODEL,
    DEPLOYMENT_STAGE_DEV
)


# Initialize QuepasaFiles with default values
qp_files = QuepasaFiles()

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)


stage = os.getenv('DEPLOYMENT_STAGE', DEPLOYMENT_STAGE_DEV)
INDEX_NAME_PREFIX = "search-document"
if stage != DEPLOYMENT_STAGE_DEV:
    INDEX_NAME_PREFIX = f"{INDEX_NAME_PREFIX}-{stage}"

ALLOWED_LANGUAGES = {
    'en': "English",
    'ru': "Russian",
    'he': "Hebrew",
    'iw': "Hebrew",
    'es': "Spanish",
    'zh': "Chinese",
    'fr': "French",
    'de': "German",
    'hi': "Hindi",
    'ja': "Japanese",
    'pt': "Portuguese",
    'ar': "Arabic",
    'ko': "Korean",
    'it': "Italian",
    'tr': "Turkish",
    'el': "Greek",
    'nl': "Dutch",
}


#
# Tokenizer
#
token_encoding = None
def get_tokenizer():
    global token_encoding
    if token_encoding is None:
        token_encoding = tiktoken.encoding_for_model(DEFAULT_TOKENIZER_MODEL)
    return token_encoding


#
# Embedding Model Token Length
#
DEFAULT_EMBEDDING_MODEL_TOKEN_LENGTH = 8192
MAX_EMBEDDING_MODEL_TOKEN_LENGTH = {
    'BAAI/bge-multilingual-gemma2': 4096,
    'BAAI/bge-en-icl': 4096,
}

def get_max_embedding_model_token_length(model_name: str) -> int:
    if model_name in MAX_EMBEDDING_MODEL_TOKEN_LENGTH:
        return MAX_EMBEDDING_MODEL_TOKEN_LENGTH[model_name]
    return DEFAULT_EMBEDDING_MODEL_TOKEN_LENGTH


#
# Telegram
#
def escape_telegram_markdown(text):
    # In all other places characters '_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!' must be escaped with the preceding character '\'.
    for char in ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']:
        text = text.replace(char, "\\" + char)
    return text

def unescape_telegram_markdown(text):
    # In all other places characters '_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!' must be escaped with the preceding character '\'.
    for char in ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']:
        text = text.replace("\\" + char, char)
    return text

#
# Get Elasticsearch config
#
def get_elasticsearch_config():
    # Get credentials from environment variables or use defaults
    es_host = os.getenv('ELASTICSEARCH_HOST', 'elasticsearch')
    es_port = os.getenv('ELASTICSEARCH_PORT', '9200')
    es_cloud_id = os.getenv('ELASTICSEARCH_CLOUD_ID', '')
    es_ca_certs_string = os.getenv('ELASTICSEARCH_CA_CERTS_STRING', '')
    es_user = os.getenv('ELASTICSEARCH_USERNAME', 'elastic')
    es_pass = os.getenv('ELASTICSEARCH_PASSWORD', 'elastic123')

    es_config = {
        'basic_auth': (es_user, es_pass),
        'request_timeout': 30,
        'max_retries': 5,
        'retry_on_timeout': True,
        'http_compress': True,
    }

    if es_cloud_id:
        es_config['cloud_id'] = es_cloud_id
    elif es_host.startswith('http://') or es_host.startswith('https://'):
        es_config['hosts'] = [es_host]
    else:
        es_config['hosts'] = [f"http://{es_host}:{es_port}"]

    if es_ca_certs_string:
        # Create a temporary file to store the CA certificate
        with tempfile.NamedTemporaryFile(delete=False, suffix=".crt") as temp_ca_cert:
            temp_ca_cert.write(es_ca_certs_string.encode("utf-8"))
            temp_ca_cert_path = temp_ca_cert.name  # Get the file path

        # Create an SSL context
        ssl_context = ssl.create_default_context(cafile=temp_ca_cert_path)
        es_config['ssl_context'] = ssl_context 

    else:
        es_config['verify_certs'] = False  # For development only
    return es_config


#
# Document Validation
#
def has_str_field_in_dict(field, dictionary):
    """Check if a field exists in dictionary and is a non-empty string."""
    return (
        field in dictionary 
        and dictionary[field] is not None
        and isinstance(dictionary[field], str)
        and dictionary[field].strip() != ""
    )

def is_document_valid(doc):
    """Validate document structure and required fields."""
    # Check required keys
    for key in ['id', 'url']:
        if not has_str_field_in_dict(key, doc):
            return False

    # Check required lists
    if (
        'pages' in doc
        and doc['pages'] is not None
        and isinstance(doc['pages'], list)
        and len(doc['pages']) > 0
    ):
        # Check pages
        for page in doc['pages']:
            for key in ['language', 'text']:
                if not has_str_field_in_dict(key, page):
                    return False
    else:
        for key in ['language']:
            if not has_str_field_in_dict(key, doc):
                return False

        flag = False
        for key in ['text', 'html', 'markdown']:
            if has_str_field_in_dict(key, doc):
                flag = True
                break

        if not flag:
            return False

    # Check dates
    for key in ['created_at', 'updated_at']:
        if (
            key in doc
            and doc[key] is not None
            and (
                not isinstance(doc[key], str)
                or not re.search(r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$", doc[key])
            )
        ):
            return False

    return True

def create_document(client_id, doc_id, url, text, doc_type='webpage', title=None, metadata=None):
    """Create a new document with required fields."""
    document = {
        'id': doc_id,
        'url': url,
        'text': text,
        'type': doc_type,
        'created_at': datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ"),
        'client_id': client_id
    }
    
    if title:
        document['title'] = title
    if metadata:
        document['metadata'] = metadata
        
    return document

def get_filename_id(id):
    """Generate a filename-safe ID using MD5 hash."""
    return hashlib.md5(str(id).encode('utf-8')).hexdigest()

def get_basepath(id, offset=0):
    """Generate a hierarchical path from an ID.
    Example: 'abcdef' -> 'a/ab/abc/abcdef'
    """
    id_str = str(id)
    filename_parts = []
    for i in range(offset, offset + 3):
        if i + 1 <= len(id_str):
            filename_parts.append(id_str[0:i + 1])
    filename_parts.append(id_str)
    return '/'.join(filename_parts)

#
# Link Index Generation
#
def get_link_index_from_date(language_code: str, date_str: str) -> str:
    """Generate a link index from a date string.
    
    Args:
        language_code: Language code for date formatting
        date_str: Date string in ISO format
        
    Returns:
        Formatted date string (e.g. "Jan 8")
    """
    try:
        date = datetime.strptime(date_str, "%Y-%m-%d")
        # Use day without leading zeros in a cross-platform way
        # %-d is not supported on Windows, so format with %d and strip leading zero.
        day_str = date.strftime("%d").lstrip('0')
        return f"{date.strftime('%b')} {day_str}"

    except (ValueError, TypeError):
        return date_str

def get_link_index_from_url(url: str) -> str:
    """Generate a link index from a URL.
    
    Args:
        url: URL string
        
    Returns:
        Domain name from URL
    """
    try:
        parsed = urlparse(url)
        return parsed.netloc
    
    except (ValueError, AttributeError):
        return url


#
# Tool Call Dictionary List
#
def get_tool_call_dict_list(tool_calls: list) -> List[Dict[str,Any]]:
    tool_call_dict_list = []
    if tool_calls:
        tool_call_dict_list = [
            {
                "id": getattr(tool_call, "id", None),
                "type": getattr(tool_call, "type", None),
                "function": {
                    "name": getattr(tool_call.function, "name", None),
                    "arguments": getattr(tool_call.function, "arguments", None),
                } if hasattr(tool_call, "function") else None,
            }
            for tool_call in tool_calls
        ]
    return tool_call_dict_list

#
# LLM Reasoning and Answer Extraction
#
def get_think_and_answer_from_content(content: str) -> Tuple[Optional[str], Optional[str]]:
    """Get think and content from answer"""
    think = None
    answer = content

    match = re.search(r'<think>(.*?)(?:</think>|$)(.*)', content, re.DOTALL)
    if match:
        think = match.group(1).strip()
        answer = match.group(2).strip()
    return think, answer
