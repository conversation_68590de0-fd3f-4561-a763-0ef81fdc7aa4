# CON-76: Conversational Shopping Assistant <PERSON><PERSON> Enhancement PRD

## Intro Project Analysis and Context

Based on my analysis of the provided documentation and requirements, here is the initial context:
- Enhancement brief: `/docs/CON-76.md`
- OpenAPI spec: `/docs/api/conversational_shopping_assistant_v1.yaml`
- API request/response examples: `/docs/api/request_example.md`, `/docs/api/response_example.md`
- Existing Searcher API: `/docs/api/openapi-searcher.yaml`

**Existing Project Overview:** The project is an existing Python-based "searcher" service built with the FastAPI framework. Its core function is to provide product search results through /retrieve/answer and /retrieve/answer/stream endpoints.

**Available Documentation Analysis:** I have analyzed the available documentation. The OpenAPI specs are very helpful. I have inferred the tech stack and architecture from the repomix-output.xml file. Explicit documents on coding standards or technical debt were not found, which is acceptable for now.

**Enhancement Scope:**

- **Type:** New Feature Addition & Integration with New Systems.
- **Description:** The project will introduce a new /conversation endpoint to the searcher service. This endpoint will feature a new response format with structured product metadata, support a new authorization mechanism (X-Customer-Id and client-key headers), handle streaming Server-Sent Events (SSE), and use session-based conversation history.
- **Impact:** I assess this as a Significant Impact, as it requires adding new logic, refactoring parts of the existing service to support the new data flow, and integrating a new authorization scheme.

**Goals and Background Context:**

**Goals:**
- Provide a richer, structured response format including detailed product metadata.
- Implement a more robust, customer-specific authorization model.
- Improve user experience by providing real-time, streamed responses.
- Enable persistent, session-based conversations for context-aware interactions.

**Background:** The current endpoints provide simple text-based answers. To support more advanced conversational commerce applications, a new endpoint is needed that can deliver structured data for rendering rich product cards and interactive elements on the client side. This enhancement (CON-76) is the foundational step towards that goal.

## Change Log

| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- |
| 2025-09-11 | 1.0 | Initial draft of Brownfield PRD | John (PM) |
| 2025-09-11 | 1.1 | Added technical constraints for auth reuse. | John (PM) |
| 2025-09-11 | 1.2 | Added Epic and Story Structure. | John (PM) |
| 2025-09-11 | 1.3 | Refined stories for history reuse, testing scope, and streaming. Added requestId NFR. | John (PM) |
| 2025-09-11 | 1.4 | Updated testing story to emphasize avoiding superficial tests. | John (PM) |

## Requirements

### Functional

**FR1:** The service MUST expose a new POST /conversation endpoint as defined in the conversational_shopping_assistant_v1.yaml OpenAPI specification.

**FR2:** The /conversation endpoint MUST implement a new authorization mechanism requiring both X-Customer-Id and Authorization: client-key <key> headers for access.

**FR3:** The internal request handling for /conversation MUST use source: agentic.

**FR4:** The /conversation endpoint MUST support both markdown and plain_text response formats, selectable via a query parameter.

**FR5:** The service MUST return product data in the new, structured format defined in the OpenAPI spec, including all variant attributes.

**FR6:** The /conversation endpoint MUST support streaming Server-Sent Events (SSE) when triggered by ?stream=true or an Accept: text/event-stream header.

**FR7:** Streaming responses MUST emit chunks in the ChatResponse shape, with a consistent responseId and a final event with status: COMPLETE.

**FR8:** The service MUST use the sessionId from the request body to manage and retrieve conversation history for the new endpoint.

**FR9:** If a visitorId is not provided in the request, a new one MUST be generated and returned in the response.

**FR10:** Essential unit and integration tests MUST be implemented for the new /conversation endpoint and its associated logic.

### Non Functional

**NFR1:** The new endpoint must be designed for high availability and low latency, consistent with the performance of existing service endpoints.

**NFR2:** The implementation must include sufficient logging to trace a request from entry to response, including interactions with downstream services.

**NFR3:** The new authorization logic must be secure and prevent unauthorized access between different customers.

**NFR4:** The refactoring of shared components must not introduce performance degradation to existing endpoints.

**NFR5:** A unique requestId MUST be generated for every inbound request to the /conversation endpoint. This ID will be used for logging, debugging, and will be returned as the responseId in streaming responses.

### Compatibility Requirements

**CR1:** The existing /retrieve/answer and /retrieve/answer/stream endpoints MUST continue to function without any breaking changes.

**CR2:** The existing authentication/authorization mechanisms for the old endpoints MUST remain fully functional.

**CR3:** Any refactoring of shared services or utilities must be backward compatible and not alter the behavior of the existing search functionality.

**CR4:** The reuse of the conversation history service for the new endpoint must not disrupt the existing history tracking that uses user_id.

## Technical Constraints and Integration Requirements

### Existing Patterns and Code to Reuse

**Authentication:** There is a working client-key authentication implementation in the products_handler via the BearerAuth class. The searcher service should reuse this existing, proven component.

- `/quepasa/auth/bearer.py`
- `/quepasa/api/auth.py`

## Epic and Story Structure

### Epic Approach

**Epic Structure Decision:** This enhancement will be managed as a single, comprehensive epic. This approach is chosen because all the requirements are tightly coupled and deliver a single, cohesive piece of functionality—the new conversational endpoint. A single epic ensures that the work is tracked as one valuable deliverable and simplifies dependency management.

### Epic 1: Implement Conversational Shopping Assistant Endpoint

**Epic Goal:** To deliver a new, fully functional /conversation endpoint that provides structured, streamable product data with enhanced authorization and session management, while ensuring zero regression to existing service functionality.

#### Story 1.1: Foundational Endpoint and Authorization

**As a** developer,  
**I want to** create the basic POST /conversation endpoint and integrate the new X-Customer-Id and client-key authorization by reusing the existing BearerAuth class,  
**so that** the fundamental security and routing for the new feature are in place and testable.

**Acceptance Criteria:**
- A new POST /conversation route exists in the searcher service.
- The endpoint is protected by an authorization scheme that requires both X-Customer-Id and Authorization: client-key headers.
- The existing BearerAuth class and related authentication logic from products_handler are successfully reused.
- Unauthorized requests are rejected with a 401 Unauthorized error.
- Authorized requests receive a hardcoded, non-streaming 200 OK response for initial validation.
- The old endpoints (/retrieve/answer) remain fully functional with their original auth.

#### Story 1.2: Structured Product Data Response

**As a** developer,  
**I want to** refactor the AnswerService and implement the logic to fetch and shape product data into the new structured format,  
**so that** the /conversation endpoint can return rich, detailed product information as specified in the OpenAPI contract.

**Acceptance Criteria:**
- The AnswerService (or a new, related service) can be called from the /conversation handler.
- The service logic correctly uses source: agentic.
- The service fetches product data and includes all variant attributes.
- The final response object matches the ChatResponse schema defined in conversational_shopping_assistant_v1.yaml for a non-streaming request.
- The response format can be switched between markdown and plain_text using a query parameter.
- A visitorId is generated and returned if one is not provided in the request.

#### Story 1.3: Streaming SSE Response

**As a** developer,  
**I want to** add Server-Sent Events (SSE) streaming capabilities to the /conversation endpoint,  
**so that** clients can receive real-time, progressive updates for an improved user experience.

**Acceptance Criteria:**
- The endpoint correctly initiates an SSE stream when ?stream=true is in the URL or the Accept header is text/event-stream.
- The implementation reuses as much of the existing underlying streaming logic as possible.
- A unique requestId is generated at the start of the request and used as the constant responseId for all events in that stream.
- All events sent on the stream conform to the ChatResponse shape.
- Interim events have a status of IN_PROGRESS.
- The final event in the stream has a status of COMPLETE.
- The SSE stream closes correctly after the COMPLETE event is sent.
- The non-streaming functionality remains the default behavior.

#### Story 1.4: Integrate Session-Based Conversation History

**As a** developer,  
**I want to** integrate the /conversation endpoint with the existing conversation history management service,  
**so that** the assistant can maintain context across multiple turns in a conversation.

**Acceptance Criteria:**
- The existing conversation history service is reused for the /conversation endpoint.
- The sessionId from the request body is passed as the user_id parameter to the existing history service.
- If includeHistory is true, previous messages from the session are correctly retrieved and factored into the response.
- The current user message and the assistant's response are correctly saved to the history via the existing service.
- The integration handles new sessions and existing sessions correctly without modification to the core history service logic.

#### Story 1.5: Essential Integration and Regression Testing

**As a** developer,  
**I want to** create a focused suite of integration and regression tests that avoid superficial tests,  
**so that** we can ensure the new endpoint is functionally correct and does not break existing features.

**Acceptance Criteria:**
- Integration tests are created for the /conversation endpoint's critical path, covering a successful non-streaming and streaming request.
- Test cases specifically validate the new authorization scheme and the session history integration.
- The existing test suite for the /retrieve/answer endpoints is executed and MUST pass completely, proving no regressions were introduced.