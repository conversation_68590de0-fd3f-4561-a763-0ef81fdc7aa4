import hashlib
from typing import Binary<PERSON>, <PERSON>, Optional, Union, Dict
from dataclasses import dataclass, asdict
import re
from urllib.parse import urlparse
import traceback

from fastapi import APIRouter, HTTPException, File, UploadFile, Depends
from src.lib.files import QuepasaFiles
from src.lib.batch_utils import BatchState, BatchAction, BatchUtils
from quepasa.api.auth import verify_auth
from quepasa.api.models import BatchResult, BatchAccess
from src.lib.logger import QuepasaLogger

# Create router
router = APIRouter()

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize files
qp_files = QuepasaFiles()

class FileHandler:
    """Handler for file operations"""
    
    def __init__(self, files=None):
        """Initialize FileHandler
        
        Args:
            files: Optional QuepasaFiles instance. If not provided, uses global instance.
        """
        self.files = files or qp_files
        
    def _get_file_path(self, client_id: str, domain: str, filename: str) -> str:
        """Get storage path for file"""
        domain_hash = hashlib.md5(str(domain).encode()).hexdigest()
        return f"storage/crawler/api-v1/{client_id}/{domain_hash}/{filename}"

    def validate_file(self, file: BinaryIO) -> None:
        """Validate file before upload
        
        Args:
            file: File-like object to validate
            
        Raises:
            ValueError: If file is empty
        """
        # Check if file is empty
        file.seek(0, 2)  # Seek to end
        size = file.tell()
        file.seek(0)  # Reset position
        
        if size == 0:
            raise ValueError("File cannot be empty")

    def validate_urls(self, urls: List[Union[str, Dict[str, str]]]) -> None:
        """Validate URLs before processing
        
        Args:
            urls: List of URLs to validate (can be strings or dicts with url field)
            
        Raises:
            ValueError: If any URL is invalid
        """
        for url_item in urls:
            # Handle both string URLs and dictionary URLs
            url = url_item['url'] if isinstance(url_item, dict) else url_item
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc or parsed.scheme not in ['http', 'https']:
                raise ValueError("Invalid URL format")
        
    async def upload_file(
        self,
        client_id: str,
        domain: str,
        file: BinaryIO,
        filename: str,
        access: Optional[BatchAccess] = None,
        language: Optional[str] = None,
        skip_indexing: bool = False
    ) -> BatchResult:
        """Process file for crawling and indexing
        
        Supported formats:
        - Web pages (HTML, XML)
        - Documents (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, MD)
        - YouTube videos (transcripts will be extracted)
        
        Args:
            domain: Domain/namespace for the content
            file: File to process
            filename: Filename for the content
            access: Optional access level for the content
            language: Optional language code for the content
            client_id: Client identifier (from auth)
            skip_indexing: If True, skip the indexing stage after crawling
            
        Returns:
            BatchResult with batch_id and processed URLs
        """
        logger.info(f"Starting file upload for client_id={client_id}, domain={domain}, filename={filename}, access={access}, language={language}, skip_indexing={skip_indexing}")
        
        try:
            # Validate file
            logger.info("Validating file...")
            self.validate_file(file)
            
            # Upload file
            path = self._get_file_path(client_id, domain, filename)
            logger.info(f"Uploading file to path: {path}")
            
            try:
                # Reset file pointer to beginning after validation
                file.seek(0)
                await self.files.upload_stream(file, path)
                logger.info("File upload successful")
                
            except Exception as e:
                logger.error(f"File upload failed: {str(e)}")
                logger.error(f"Exception type: {type(e)}")
                logger.error(f"Exception traceback: {traceback.format_exc()}")
                raise HTTPException(
                    status_code=500,
                    detail=f"File upload failed: {str(e)}"
                )
            
            # Create batch
            logger.info("Creating batch data...")
            batch_data = {
                'client_id': client_id,
                'domain': domain,
                'action': BatchAction.UPSERT.value,
                'files': [path],
                'skip_indexing': skip_indexing
            }
            
            if access:
                batch_data['access'] = asdict(access)
            
            if language:
                batch_data['language'] = language

            # Create batch
            batch_id = BatchUtils.create_batch(client_id, BatchState.UPLOADED, batch_data, files=self.files)
            logger.info(f"Created batch with ID: {batch_id}")

            # Create batch and trigger data processor
            BatchUtils.add_task('crawler', client_id, batch_id)
            logger.info(f"Created crawler task {client_id} {batch_id}")
            
            return BatchResult(
                batch_id=batch_id,
                processed_ids=[filename],
                skip_indexing=skip_indexing
            )
            
        except HTTPException:
            raise
        
        except Exception as e:
            logger.error(f"Upload process failed: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            logger.error(f"Exception traceback: {traceback.format_exc()}")
            raise HTTPException(
                status_code=500,
                detail=f"File upload failed: {str(e)}"
            )
        
    async def upload_urls(
        self,
        client_id: str,
        domain: str,
        urls: List[Union[str, Dict[str, str]]],
        access: Optional[BatchAccess] = None,
        language: Optional[str] = None,
        skip_indexing: bool = False
    ) -> BatchResult:
        """Process URLs for crawling and indexing
        
        Supported formats:
        - Web pages (HTML, XML)
        - Documents (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, MD)
        - YouTube videos (transcripts will be extracted)
        
        Args:
            domain: Domain/namespace for the content
            urls: List of URLs to process (can be strings or dicts with url field)
            access: Optional access level for the content
            language: Optional language code for the content
            client_id: Client identifier (from auth)
            skip_indexing: If True, skip the indexing stage after crawling
            
        Returns:
            BatchResult with batch_id and processed URLs
        """
        if not urls:
            raise ValueError("No URLs provided")
        
        """Upload and process file"""
        logger.info(f"Starting file upload for client_id={client_id}, domain={domain}, urls={urls}, access={access}, language={language}, skip_indexing={skip_indexing}")
            
        # Validate URLs
        self.validate_urls(urls)
            
        # Create batch
        batch_data = {
            'client_id': client_id,
            'domain': domain,
            'action': BatchAction.UPSERT.value,
            'urls': urls,
            'skip_indexing': skip_indexing
        }

        if access:
            batch_data['access'] = asdict(access)
        
        if language:
            batch_data['language'] = language
            
        # Create batch
        batch_id = BatchUtils.create_batch(client_id, BatchState.UPLOADED, batch_data, files=self.files)
        logger.info(f"Created batch with ID: {batch_id}")

        # Create batch and trigger data processor
        BatchUtils.add_task('crawler', client_id, batch_id)
        logger.info(f"Created crawler task {client_id} {batch_id}")
        
        return BatchResult(
            batch_id=batch_id,
            processed_ids=urls,
            skip_indexing=skip_indexing
        )

# Initialize handler instance
handler = FileHandler()
