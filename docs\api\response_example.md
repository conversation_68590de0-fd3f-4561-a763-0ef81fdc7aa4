# API Response Examples

## Overview

The Quepasa API returns responses containing conversational answers, product recommendations, and optional actions.

## Streaming Support

Responses can be delivered in two modes:
- **Non-streaming**: Standard JSON response (`Content-Type: application/json`)
- **Streaming**: Server-sent events (`Content-Type: text/event-stream`)

Enable streaming via:
- Query parameter: `?stream=true`
- Accept header: `Accept: text/event-stream`

**Response ID for Streaming**: The `responseId` remains constant across all chunks in a streaming response, enabling tracing, analytics, retries, and deduplication.

## Content Format

Control response content format:
- `?format=markdown` - Formatted text with markdown (default)
- `?format=plain_text` - Plain text without formatting

## Response Structure

### Success Response Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `visitorId` | string | No | Visitor identifier (generated if not provided by client, used for long-term memory) |
| `sessionId` | string | Yes | Session identifier for conversation tracking |
| `responseId` | string | Yes | Unique identifier for this response (consistent across all chunks in streaming) |
| `timestamp` | string | Yes | ISO 8601 timestamp of the response |
| `content` | string | Yes | Response text content (format based on `?format` parameter) |
| `stream` | boolean | Yes | Whether this was a streaming response |
| `status` | string | No | Streaming status: `IN_PROGRESS`, `COMPLETE`, `ERROR` (for streaming responses) |
| `components` | array | No | Array of display components (products, references) |
| `actions` | array | No | Array of suggested actions (add-to-cart, etc.) |

### Component Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `componentType` | string | Yes | Component type enum: `PRODUCT_SET` or `REFERENCE_ITEM` |

#### PRODUCT_SET Component

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `componentType` | string | Yes | Must be `"PRODUCT_SET"` |
| `comment` | string | No | Optional comment about this set of products |
| `products` | array | Yes | Array of product objects |

#### REFERENCE_ITEM Component

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `componentType` | string | Yes | Must be `"REFERENCE_ITEM"` |
| `url` | string | Yes | Reference URL |
| `text` | string | Yes | Full text content from the source |
| `title` | string | No | Reference title |

### Product Object

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Identifier of the record as an MD5 hash |
| `url` | string | Logical URL of the record |
| `title` | string | Title of the record |
| `collection` | string | Collection the record was queried from or resides |
| `allMeta` | object | All other metadata, read fields, the record has (flexible structure) |
| `labels` | array | Labels associated with the record: `PINNED`, `BOOSTED`, `BURIED`, `SPONSORED`, `PART_NUMBER` |

### Product Metadata (allMeta)

The `allMeta` field is a flexible object that can contain any product-specific metadata fields. Common fields include:

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Product ID |
| `images` | array | Array of product images |
| `brands` | array | Array of brand names |
| `name` | string | Full product name |
| `description` | string | Product description |
| `title` | string | Display title |
| `variants` | array | Array of product variants (sizes, colors, etc.) |
| `priceInfo` | object | Price information |
| `categories` | array | Product categories |
| `tags` | array | Product tags |
| `attributes` | object | Custom product attributes |

*Note: The `allMeta` object structure is flexible and can contain additional fields not listed above, depending on the product catalog configuration.*

### Product Variant Object

Variants are complete ProductItem objects with the same structure as the main product. Common fields include:

| Field | Type | Description |
|-------|------|-------------|
| `gtin` | string | Global Trade Item Number |
| `availableTime` | string | ISO 8601 timestamp when variant became available |
| `description` | string | Variant description |
| `title` | string | Variant title |
| `uri` | string | Link to the specific variant |
| `images` | array | Variant-specific images with uri field |
| `sizes` | array | Available sizes for this variant |
| `colorInfo` | object | Color information with colorFamilies and colors arrays |
| `priceInfo` | object | Price information with price field |
| `id` | string | Variant product ID |
| `type` | string | Product type: `PRIMARY`, `VARIANT`, or `COLLECTION` |
| `primaryProductId` | string | Product ID that is primary in relation to this variant |
| `attributes` | object | Variant-specific attributes (extensive key-value pairs) |
| `availability` | string | Online availability: `IN_STOCK`, `OUT_OF_STOCK`, `PREORDER`, `BACKORDER` |
| `patterns` | array | Product patterns (e.g., "Graphic") |
| `name` | string | Variant internal name |
| `samplePrecision` | number | Precision score for the variant |

*Note: Variants contain the full product structure and can include additional fields from the catalog.*

### Action Object

| Field | Type | Description |
|-------|------|-------------|
| `type` | string | Action type (uppercase enum: `ADD_TO_CART`, `REMOVE_FROM_CART`) |
| `payload` | object | Action-specific data (SKU, quantity, etc.) |

### Reference Object

| Field | Type | Description |
|-------|------|-------------|
| `url` | string | Reference URL |
| `text` | string | Full text content from the source |
| `title` | string | Reference title |

## HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success - Request processed successfully |
| 400 | Bad Request - Invalid request format |
| 401 | Unauthorized - Invalid authentication |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Processing error |

## Success Response Examples

### Example 1: Product Recommendation

```json
{
  "visitorId": "550e8400-e29b-41d4-a716-446655440000",
  "sessionId": "session67890_from_beacontracker",
  "responseId": "resp-20250904-002",
  "timestamp": "2025-09-04T13:35:00Z",
  "content": "Great choice! Here's the Refill Round T-shirt in camel color, size M.",
  "stream": false,
  "components": [
    {
      "componentType": "PRODUCT_SET", 
      "comment": "Great selection for summer",
      "products": [
        {
          "id": "a1586a5f9d1e9c5a501dfcab396e1b1f",
          "url": "http://shoeby1products.com/1106526",
          "title": "Black Shirt",
          "collection": "products",
          "allMeta": {
            "id": "blackShirt1",
            "images": [
              {
                "uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwdf085933/products/1106526/06_1106526_99.jpg"
              }
            ],
            "brands": [
              "Shoeby"
            ],
            "name": "projects/923708338127/locations/global/catalogs/default_catalog/branches/1/products/1106526",
            "description": "The Printed Crinkle Shirt has a v-neck",
            "title": "Black V-Neck Shirt",
            "variants": [
              {
                "gtin": "196345563642",
                "availableTime": "2022-10-13T01:10:28.000Z",
                "description": "Black V-Neck Shirt in size S",
                "title": "Black V-Neck Shirt Size S",
                "uri": "http://shoeby1products.com/1106526-S",
                "images": [
                  {
                    "uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwdf085933/products/1106526/06_1106526_S_99.jpg"
                  }
                ],
                "sizes": ["S"],
                "colorInfo": {
                  "colorFamilies": ["Black"],
                  "colors": ["BLACK"]
                },
                "priceInfo": {
                  "price": 25
                },
                "id": "1106526-S-01",
                "type": "VARIANT",
                "primaryProductId": "1106526",
                "attributes": {
                  "size": ["S"],
                  "color": ["Black"]
                },
                "availability": "IN_STOCK",
                "patterns": ["Solid"],
                "name": "blackShirt1-S",
                "samplePrecision": 0.9
              }
            ]
          }
        },
        {
          "id": "b2597b6e8e2f0d6b602efdbc497f2c2g",
          "url": "http://shoeby1products.com/1106526",
          "title": "White Shirt",
          "collection": "products",
          "allMeta": {
            "id": "whiteShirt1",
            "images": [
              {
                "uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwdf085933/products/1106526/06_1106526_99.jpg"
              }
            ],
            "brands": [
              "Shoeby"
            ],
            "name": "projects/923708338127/locations/global/catalogs/default_catalog/branches/1/products/1106526",
            "description": "The Printed Crinkle Shirt has a v-neck",
            "title": "White V-Neck Shirt",
            "variants": [
              {
                "gtin": "196345563659",
                "availableTime": "2022-10-15T08:20:15.000Z",
                "description": "White V-Neck Shirt in size XS",
                "title": "White V-Neck Shirt Size XS",
                "uri": "http://shoeby1products.com/1106527-XS",
                "images": [
                  {
                    "uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwdf085933/products/1106527/06_1106527_XS_02.jpg"
                  }
                ],
                "sizes": ["XS"],
                "colorInfo": {
                  "colorFamilies": ["White"],
                  "colors": ["WHITE"]
                },
                "priceInfo": {
                  "price": 25
                },
                "id": "1106527-XS-02",
                "type": "VARIANT",
                "primaryProductId": "1106527",
                "attributes": {
                  "size": ["XS"],
                  "color": ["White"]
                },
                "availability": "IN_STOCK",
                "patterns": ["Solid"],
                "name": "whiteShirt1-XS",
                "samplePrecision": 0.8
              }
            ]
          }
        }
      ]
    }
  ]
}
```

### Example 2: Response with Actions

This example shows a response that includes suggested actions, such as adding items to cart.

```json
{
  "visitorId": "550e8400-e29b-41d4-a716-446655440001",
  "sessionId": "session67890_from_beacontracker",
  "responseId": "resp-20250904-003",
  "timestamp": "2025-09-04T13:35:00Z",
  "content": "Great choice, I've added jeans to the cart. Would you be interested in some shirts which match jeans greatly?",
  "stream": false,
  "components": [
    {
      "componentType": "PRODUCT_SET",
      "products": [
        {
          "id": "a1586a5f9d1e9c5a501dfcab396e1b1f",
          "url": "http://shoeby1products.com/1106526",
          "title": "Black Shirt",
          "collection": "products",
          "allMeta": {
            "id": "blackShirt1",
            "images": [
              {
                "uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwdf085933/products/1106526/06_1106526_99.jpg"
              }
            ],
            "brands": [
              "Shoeby"
            ],
            "name": "projects/923708338127/locations/global/catalogs/default_catalog/branches/1/products/1106526",
            "description": "The Printed Crinkle Shirt has a v-neck",
            "title": "Black V-Neck Shirt",
            "variants": [
              {
                "gtin": "196345563642",
                "availableTime": "2022-10-13T01:10:28.000Z",
                "description": "Black V-Neck Shirt in size S",
                "title": "Black V-Neck Shirt Size S",
                "uri": "http://shoeby1products.com/1106526-S",
                "images": [
                  {
                    "uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwdf085933/products/1106526/06_1106526_S_99.jpg"
                  }
                ],
                "sizes": ["S"],
                "colorInfo": {
                  "colorFamilies": ["Black"],
                  "colors": ["BLACK"]
                },
                "priceInfo": {
                  "price": 25
                },
                "id": "1106526-S-01",
                "type": "VARIANT",
                "primaryProductId": "1106526",
                "attributes": {
                  "size": ["S"],
                  "color": ["Black"]
                },
                "availability": "IN_STOCK",
                "patterns": ["Solid"],
                "name": "blackShirt1-S",
                "samplePrecision": 0.9
              }
            ]
          }
        },
        {
          "id": "b2597b6e0e2f8d6b602efdbc497f2c2g",
          "url": "http://shoeby1products.com/1106526",
          "title": "White Shirt",
          "collection": "products",
          "allMeta": {
            "id": "whiteShirt1",
            "images": [
              {
                "uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwdf085933/products/1106526/06_1106526_99.jpg"
              }
            ],
            "brands": [
              "Shoeby"
            ],
            "name": "projects/923708338127/locations/global/catalogs/default_catalog/branches/1/products/1106526",
            "description": "The Printed Crinkle Shirt has a v-neck",
            "title": "White V-Neck Shirt",
            "variants": [
              {
                "gtin": "196345563659",
                "availableTime": "2022-10-15T08:20:15.000Z",
                "description": "White V-Neck Shirt in size XS",
                "title": "White V-Neck Shirt Size XS",
                "uri": "http://shoeby1products.com/1106527-XS",
                "images": [
                  {
                    "uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwdf085933/products/1106527/06_1106527_XS_02.jpg"
                  }
                ],
                "sizes": ["XS"],
                "colorInfo": {
                  "colorFamilies": ["White"],
                  "colors": ["WHITE"]
                },
                "priceInfo": {
                  "price": 25
                },
                "id": "1106527-XS-02",
                "type": "VARIANT",
                "primaryProductId": "1106527",
                "attributes": {
                  "size": ["XS"],
                  "color": ["White"]
                },
                "availability": "IN_STOCK",
                "patterns": ["Solid"],
                "name": "whiteShirt1-XS",
                "samplePrecision": 0.8
              }
            ]
          }
        }
      ]
    }
  ],
  "actions": [
    {
      "type": "ADD_TO_CART",
      "payload": {
        "sku": "blackshirt1-small",
        "quantity": 1
      }
    }
  ]
}
```

### Example 3: Policy Search with References

This example shows a response for policy/document searches that includes source references.

```json
{
  "visitorId": "550e8400-e29b-41d4-a716-446655440002",
  "sessionId": "session67890_from_beacontracker",
  "responseId": "resp-20250904-004",
  "timestamp": "2025-09-04T13:35:00Z",
  "content": "Based on our return policy, you can return items within **30 days** of purchase. Here are the key details:\n\n- **Return window**: 30 days from purchase date\n- **Condition**: Items must be in original condition with tags\n- **Refund method**: Original payment method\n- **Shipping**: Return shipping is free for defective items\n\nWould you like me to help you initiate a return?",
  "stream": false,
  "status": "COMPLETE",
  "components": [
    {
      "componentType": "REFERENCE_ITEM",
      "url": "https://help.example.com/returns",
      "text": "Return Policy\\n\\nAll items may be returned within 30 days of purchase for a full refund when in original condition with tags attached. Items must be unworn and unwashed.\\n\\nRefund Process:\\n- Returns are processed within 3-5 business days\\n- Refunds are issued to original payment method\\n- Return shipping is free for defective items",
      "title": "Return and Refund Policy"
    },
    {
      "componentType": "REFERENCE_ITEM",
      "url": "https://help.example.com/faq/returns-shipping",
      "text": "Returns FAQ - Shipping Information\\n\\nReturn shipping is provided free of charge for defective or damaged items. For standard returns, customers are responsible for return shipping costs.\\n\\nFree return shipping applies to:\\n- Manufacturing defects\\n- Wrong item shipped\\n- Damaged during shipping",
      "title": "Returns FAQ - Shipping Information"
    }
  ]
}
```

### Example 4: Streaming Response (In Progress)

This example shows a streaming response that is still in progress.

```json
{
  "visitorId": "550e8400-e29b-41d4-a716-446655440003",
  "sessionId": "session67890_from_beacontracker",
  "responseId": "resp-************",
  "timestamp": "2025-09-04T13:35:00Z",
  "content": "I'm searching for summer dresses that would match your style preferences...",
  "stream": true,
  "status": "IN_PROGRESS",
  "components": []
}
```

## Error Response Examples

### 400 Bad Request

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Missing required field: message",
    "details": {
      "field": "message",
      "expected": "string",
      "received": "null"
    }
  },
  "timestamp": "2025-09-04T13:35:00Z",
  "requestId": "req-20250904-error-001"
}
```

### 401 Unauthorized

```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Invalid or missing authentication token",
    "details": {
      "hint": "Ensure Authorization header contains valid Bearer token"
    }
  },
  "timestamp": "2025-09-04T13:35:00Z",
  "requestId": "req-20250904-error-002"
}
```

### 429 Rate Limit Exceeded

```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many requests. Please try again later.",
    "details": {
      "retryAfter": 60,
      "limit": 100,
      "remaining": 0,
      "resetTime": "2025-09-04T14:00:00Z"
    }
  },
  "timestamp": "2025-09-04T13:35:00Z",
  "requestId": "req-20250904-error-003"
}
```

### 500 Internal Server Error

```json
{
  "error": {
    "code": "INTERNAL_ERROR",
    "message": "An internal server error occurred while processing your request",
    "details": {
      "hint": "Please try again later or contact support if the issue persists"
    }
  },
  "timestamp": "2025-09-04T13:35:00Z",
  "requestId": "req-20250904-error-004"
}
```