import pytest
import json
from unittest.mock import MagicMock, patch, call
from datetime import datetime
import requests
import os

from quepasa.crawler.tasks import (
    app, process_batch, collect_results, process_url, process_file,
    process_pdf, process_document, process_web, process_csv,
    process_pptx, process_markdown, process_txt, process_youtube,
    save_to_backlog, save_results
)

# Test data
TEST_BATCH = {
    'env': 'test',
    'client_id': 'test-client',
    'batch_id': 'test-batch-id',
    'domain': 'test-domain.com',
    'urls': [
        'https://test-domain.com/doc1.pdf',
        'https://test-domain.com/doc2.html'
    ],
    'files': [
        'test/storage/uploads/doc1.pdf',
        'test/storage/uploads/doc2.txt'
    ]
}

TEST_META = {
    'env': 'test',
    'client_id': 'test-client',
    'domain': 'test-domain.com',
    'id': 'test-domain.com:test-doc',
    'url': 'https://test-domain.com/test-doc',
    'filename': 'test-doc'
}

@pytest.fixture
def mock_qp_files():
    with patch('quepasa.crawler.tasks.qp_files') as mock:
        instance = mock
        instance.get_data.return_value = b'test content'
        instance.exists.return_value = True
        yield instance

@pytest.fixture
def mock_logger():
    with patch('quepasa.crawler.tasks.logger') as mock:
        yield mock

@pytest.fixture
def mock_requests():
    with patch('quepasa.crawler.tasks.requests') as mock:
        mock.get.return_value.content = b'test content'
        mock.get.return_value.headers = {'Content-Type': 'text/html'}
        mock.get.return_value.status_code = 200
        yield mock

@pytest.fixture
def mock_processors():
    with patch('quepasa.crawler.tasks.PDFProcessor') as pdf_mock, \
         patch('quepasa.crawler.tasks.WebProcessor') as web_mock, \
         patch('quepasa.crawler.tasks.DocumentProcessor') as doc_mock, \
         patch('quepasa.crawler.tasks.CSVProcessor') as csv_mock, \
         patch('quepasa.crawler.tasks.PPTXProcessor') as pptx_mock, \
         patch('quepasa.crawler.tasks.MarkdownProcessor') as md_mock, \
         patch('quepasa.crawler.tasks.TXTProcessor') as txt_mock, \
         patch('quepasa.crawler.tasks.YouTubeProcessor') as yt_mock:
        
        # Configure mock processors
        processor_result = {
            'title': 'Test Document',
            'chunks': [{'text': 'Test content', 'language': 'en'}]
        }
        
        for mock_proc in [pdf_mock, web_mock, doc_mock, csv_mock, 
                         pptx_mock, md_mock, txt_mock, yt_mock]:
            mock_proc.return_value.process.return_value = processor_result
        
        yield {
            'pdf': pdf_mock,
            'web': web_mock,
            'doc': doc_mock,
            'csv': csv_mock,
            'pptx': pptx_mock,
            'md': md_mock,
            'txt': txt_mock,
            'youtube': yt_mock
        }


@patch('quepasa.crawler.tasks.process_batch.run')
def test_process_batch_sync(mock_process_batch, mock_qp_files, mock_logger, mock_processors):
    """Test synchronous batch processing"""
    # Configure the mock to return a success result
    mock_process_batch.return_value = {
        'client_id': TEST_BATCH['client_id'],
        'batch_id': TEST_BATCH['batch_id'],
        'status': 'success',
        'task_id': 'mock-task-id',
        'completed_at': '2025-04-06T12:00:00Z',
        'stats': {
            'total_tasks': len(TEST_BATCH['urls']) + len(TEST_BATCH['files']),
            'url_tasks': len(TEST_BATCH['urls']),
            'file_tasks': len(TEST_BATCH['files']),
            'processed_tasks': len(TEST_BATCH['urls']) + len(TEST_BATCH['files'])
        }
    }
    
    # Process batch
    result = process_batch(TEST_BATCH, TEST_BATCH['batch_id'])
    
    # Verify result structure
    assert result['client_id'] == TEST_BATCH['client_id']
    assert result['status'] == 'success'
    assert 'task_id' in result
    assert 'completed_at' in result
    assert 'stats' in result
    
    # Verify process_batch was called with correct arguments
    mock_process_batch.assert_called_once_with(TEST_BATCH, TEST_BATCH['batch_id'])

@patch('quepasa.crawler.tasks.process_batch.run')
@patch('celery.app.base.Celery.backend')
@patch('celery.app.base.Celery.send_task')
def test_process_batch_async(mock_send_task, mock_backend, mock_process_batch, 
                            mock_qp_files, mock_logger, mock_processors):
    """Test asynchronous batch processing"""
    # Configure Celery for async operation
    app.conf.task_always_eager = False
    
    # Mock process_batch.run
    mock_process_batch.return_value = {
        'client_id': TEST_BATCH['client_id'],
        'batch_id': TEST_BATCH['batch_id'],
        'status': 'pending',
        'task_id': 'mock-task-id',
        'started_at': '2025-04-06T12:00:00Z',
        'stats': {
            'total_tasks': len(TEST_BATCH['urls']) + len(TEST_BATCH['files']),
            'url_tasks': len(TEST_BATCH['urls']),
            'file_tasks': len(TEST_BATCH['files']),
        }
    }
    
    try:
        # Process batch
        result = process_batch(TEST_BATCH, TEST_BATCH['batch_id'])
        
        # Verify result structure for async operation
        assert result['client_id'] == TEST_BATCH['client_id']
        assert result['status'] == 'pending'
        assert 'task_id' in result
        assert 'started_at' in result
        assert 'stats' in result
    finally:
        # Reset Celery config
        app.conf.task_always_eager = True

def test_collect_results(mock_logger):
    """Test results collection from multiple tasks"""
    # Mock task results
    results = [
        {'id': 'doc1', 'file': 'path/to/doc1', 'status': 'success'},
        {'id': 'doc2', 'file': 'path/to/doc2', 'status': 'success'},
        Exception('Test error'),
        {'id': 'doc4', 'file': 'path/to/doc4', 'status': 'success'}
    ]
    
    # Collect results with batch_id parameter
    batch_id = TEST_BATCH['batch_id']
    collected = collect_results(results, TEST_BATCH['client_id'], batch_id)
    
    # Verify collected results
    assert collected['client_id'] == TEST_BATCH['client_id']
    assert collected['status'] == 'success'
    assert 'completed_at' in collected
    assert collected['stats']['total'] == 4
    assert collected['stats']['success'] == 3
    assert collected['stats']['error'] == 1

@patch('quepasa.crawler.tasks.process_url.run')
def test_process_url_pdf(mock_process_url, mock_requests, mock_processors, mock_qp_files):
    """Test processing a PDF URL"""
    # Configure mock response for PDF
    mock_requests.get.return_value.headers = {'Content-Type': 'application/pdf'}
    url = 'https://test-domain.com/test.pdf'
    
    # Mock the run method of process_url
    mock_process_url.return_value = {
        'id': url,
        'status': 'success',
        'file': 'test/path/to/processed.pdf'
    }
    
    # Process URL
    result = process_url(url, TEST_BATCH)
    
    # Verify result
    assert result['status'] == 'success'
    assert 'file' in result
    assert result['id'] == url

@patch('quepasa.crawler.tasks.process_url.run')
def test_process_url_error(mock_process_url, mock_requests):
    """Test URL processing with network error"""
    # Configure mock to return an error
    url = 'https://test-domain.com/error.pdf'
    mock_process_url.return_value = {
        'id': url,
        'status': 'error',
        'error': 'Network error'
    }
    
    # Process URL
    result = process_url(url, TEST_BATCH)
    
    # Verify error handling
    assert result['status'] == 'error'
    assert 'error' in result
    assert 'Network error' in result['error']

@patch('quepasa.crawler.tasks.process_file.run')
def test_process_file_txt(mock_process_file, mock_qp_files, mock_processors):
    """Test processing a text file"""
    file_path = 'test/storage/uploads/test.txt'
    
    # Mock the process_file.run return value
    mock_process_file.return_value = {
        'id': 'test.txt',
        'status': 'success',
        'file': 'test/path/to/processed.txt'
    }
    
    # Process file
    result = process_file(file_path, TEST_BATCH)
    
    # Verify result
    assert result['status'] == 'success'
    assert 'file' in result
    assert result['id'] == "test.txt"

@patch('quepasa.crawler.tasks.process_file.run')
def test_process_file_unsupported(mock_process_file, mock_qp_files):
    """Test processing an unsupported file type"""
    file_path = 'test/storage/uploads/test.xyz'
    
    # Mock the process_file.run return value
    mock_process_file.return_value = {
        'id': 'test.xyz',
        'status': 'error',
        'error': 'Unsupported file extension: .xyz'
    }
    
    # Process file
    result = process_file(file_path, TEST_BATCH)
    
    # Verify error handling
    assert result['status'] == 'error'
    assert 'error' in result
    assert 'Unsupported file extension' in result['error']

@patch('quepasa.crawler.tasks.QuepasaConfigurationHub')
@patch('quepasa.crawler.tasks.get_language')
def test_save_to_backlog(mock_get_language, mock_config_hub, mock_qp_files):
    """Test saving processed data to backlog"""
    # Mock configuration
    config_instance = MagicMock()
    config_instance.get_fallback_language.return_value = 'en'
    config_instance.get_language_mapping.return_value = {'en': 'English'}
    mock_config_hub.from_client_code.return_value = config_instance
    
    # Test data
    client_id = 'test-client'
    batch_id = 'test-batch'
    result = {
        'status': 'success',
        'result': {
            'title': 'Test Document',
            'chunks': [
                {'text': 'Test content 1', 'language': 'en'},
                {'text': 'Test content 2', 'language': None}  # This should trigger language detection
            ],
            'keywords': 'test, document'
        }
    }
    meta = {
        'domain': 'test-domain.com',
        'id': 'test-doc-123',
        'url': 'https://test-domain.com/test-doc'
    }
    
    # Mock language detection
    mock_get_language.return_value = 'en'
    
    # Call the function
    result_path = save_to_backlog(client_id, batch_id, result, meta)
    
    # Verify the result is a path string
    assert isinstance(result_path, str)
    assert '.zlib.json' in result_path
    
    # Verify set_json_zlib was called with correct data
    call_args = mock_qp_files.set_json_zlib.call_args[0]
    path, data = call_args
    
    # Check path
    assert path == result_path
    
    # Check data structure
    assert data['id'] == meta['id']
    assert data['url'] == meta['url']
    assert data['title'] == result['result']['title']
    assert data['domain'] == meta['domain']
    assert data['keywords'] == result['result']['keywords']
    assert len(data['chunks']) == 2
    assert data['chunks'][0]['language'] == 'en'
    assert data['chunks'][1]['language'] == 'en'  # Should be set by get_language
    
    # Verify the config hub was called correctly
    mock_config_hub.from_client_code.assert_called_once_with(client_id)

@patch('quepasa.crawler.tasks.BatchUtils')
def test_save_results_success(mock_batch_utils, mock_qp_files):
    """Test saving successful batch results"""
    # Mock data
    client_id = 'test-client'
    batch_id = 'test-batch'
    domain = 'test-domain.com'
    action = 'upsert'
    
    data_result = {
        'results': [
            {'id': 'doc1', 'file': 'path/to/doc1.json', 'status': 'success'},
            {'id': 'doc2', 'file': 'path/to/doc2.json', 'status': 'success'},
        ]
    }
    
    # Mock batch filename functions
    mock_batch_utils.get_batch_filename.side_effect = lambda cid, bid, state: f"{state}/{bid}.{cid}.json"
    mock_batch_utils.add_task.return_value = 'task_id'
    
    # Call function
    result = save_results(data_result, client_id, batch_id, domain, action, skip_indexing=False)
    
    # Verify result
    assert result['status'] == 'success'
    assert result['client_id'] == client_id
    assert result['batch_id'] == batch_id
    assert result['processed_count'] == 2
    assert result['state'] == 'backlog'
    
    # Verify file operations
    mock_qp_files.exists.assert_called_once()
    mock_qp_files.delete_file.assert_called_once()
    mock_qp_files.set_json_zlib.assert_called_once()
    
    # Verify task was added
    mock_batch_utils.add_task.assert_called_once_with('data-processor', client_id, batch_id)

@patch('quepasa.crawler.tasks.BatchUtils')
def test_save_results_empty(mock_batch_utils, mock_qp_files):
    """Test saving batch results with no successful documents"""
    # Mock data
    client_id = 'test-client'
    batch_id = 'test-batch'
    domain = 'test-domain.com'
    action = 'upsert'
    
    data_result = {
        'results': [
            {'id': 'doc1', 'error': 'Processing failed', 'status': 'error'},
        ]
    }
    
    # Mock batch filename functions
    mock_batch_utils.get_batch_filename.side_effect = lambda cid, bid, state: f"{state}/{bid}.{cid}.json"
    
    # Call function
    result = save_results(data_result, client_id, batch_id, domain, action, skip_indexing=False)
    
    # Verify result
    assert result['status'] == 'success'
    assert result['client_id'] == client_id
    assert result['processed_count'] == 0
    assert result['state'] == 'failed'
    
    # Verify we saved to failed state, not backlog
    mock_batch_utils.get_batch_filename.assert_any_call(client_id, batch_id, 'failed')
    
    # Verify no task was added
    mock_batch_utils.add_task.assert_not_called()

# Add tests for content-type processors
@patch('quepasa.crawler.tasks.save_to_backlog')
def test_process_pdf(mock_save_to_backlog, mock_processors):
    """Test PDF processing"""
    client_id = 'test-client'
    batch_id = 'test-batch'
    content = b'%PDF-1.4\nTest PDF content'
    meta = {
        'client_id': client_id,
        'domain': 'test-domain.com',
        'id': 'test-pdf',
        'url': 'https://test-domain.com/test.pdf',
        'filename': 'test.pdf'
    }
    
    # Configure the processor result - direct processor output doesn't have 'status'
    processor_result = {
        'title': 'Test Document',
        'chunks': [{'text': 'Test content', 'language': 'en'}],
        'keywords': 'test, pdf'
    }
    
    # Setup the processor mock to return our result
    mock_processors['pdf'].return_value.process.return_value = processor_result
    
    mock_save_to_backlog.return_value = 'path/to/backlog/file.json'
    
    # Call function
    result = process_pdf(client_id, batch_id, content, meta)
    
    # Verify PDF processor was called
    mock_processors['pdf'].return_value.process.assert_called_once_with(content, meta)
    
    # Verify save_to_backlog was called with correct args
    mock_save_to_backlog.assert_called_once()
    
    # Get third positional argument (direct processor result, not wrapped in status dict)
    saved_result = mock_save_to_backlog.call_args[0][2]
    
    # Verify it matches our processor result
    assert saved_result == processor_result
    
    # Verify return value
    assert result == 'path/to/backlog/file.json'

@patch('quepasa.crawler.tasks.save_to_backlog')
def test_process_web(mock_save_to_backlog, mock_processors):
    """Test web content processing"""
    client_id = 'test-client'
    batch_id = 'test-batch'
    content = b'<html><body>Test content</body></html>'
    meta = {
        'client_id': client_id,
        'domain': 'test-domain.com',
        'id': 'test-html',
        'url': 'https://test-domain.com/test.html',
        'filename': 'test.html'
    }
    
    mock_save_to_backlog.return_value = 'path/to/backlog/file.json'
    
    # Call function
    result = process_web(client_id, batch_id, content, meta)
    
    # Verify web processor was called
    mock_processors['web'].return_value.process.assert_called_once_with(content, meta)
    
    # Verify return value
    assert result == 'path/to/backlog/file.json' 