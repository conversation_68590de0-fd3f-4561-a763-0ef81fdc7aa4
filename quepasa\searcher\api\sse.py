from typing import Dict, Any, <PERSON><PERSON>, Generator
from flask import Response, stream_with_context
from .base import BaseAPIHandler
from ..sources.base import STATUS_ERROR
from src.lib.logger import <PERSON>pasaLogger
from configuration.main.default import QuepasaConfigurationHub
from ..models.response import QuepasaStreamAnswer
import time
import traceback
import json
logger = QuepasaLogger().get_instance(__name__)

class SSEHandler(BaseAPIHandler):
    """Server-Sent Events (SSE) API handler"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        super().__init__(config)

    def _handle_request_internal(self, method: str, url: str, headers: Dict[str, str]) -> Response:
        """Handle SSE request internally"""
        try:
            def generate() -> Generator[str, None, None]:
                try:
                    # Initialize last_created_at to track when to send updates
                    last_created_at = None
                    last_chunk = None
                    
                    # Get streaming response
                    for chunk in self.source_factory.get_answer(stream=True):
                        chunk_data = None
                        if isinstance(chunk, QuepasaStreamAnswer):
                            chunk_data = chunk.to_dict()
                        elif isinstance(chunk, dict):
                            chunk_data = chunk
                        else:
                            logger.error(f"Invalid response data type: {type(chunk)}")
                            logger.error(f"Response data: {chunk}")
                            continue

                        # Check chunk fields
                        is_error = chunk_data.get('type') == "error" or 'error' in chunk_data
                        is_streaming = chunk_data.get('streaming', False)
                        is_loading = chunk_data.get('loading', False)
                        current_created_at = chunk_data.get('created_at')
                        
                        if (
                            is_error 
                            or not is_streaming and not is_loading
                            or current_created_at != last_created_at
                        ):
                            yield f"data: {json.dumps(chunk_data)}\n\n"
                            if current_created_at is not None:
                                last_created_at = current_created_at

                        last_chunk = chunk

                    # Add authenticated user ID to request
                    user_id = None
                    if (
                        hasattr(self.config.request, 'user_info')
                        and hasattr(self.config.request.user_info, 'id')
                    ):
                        user_id = self.config.request.user_info.id
                        
                    if last_chunk and user_id:
                        self.save_assistant_response(user_id, last_chunk)
                            
                except Exception as e:
                    logger.error(f"Error in SSE generator: {str(e)}")
                    logger.error("Traceback:")
                    for line in traceback.format_exc().splitlines():
                        logger.error(line)
                        
                    error_response = QuepasaStreamAnswer(
                        type=STATUS_ERROR,
                        text=str(e),
                        loading=False,
                        streaming=False,
                        created_at=int(time.time())
                    )
                    yield f"data: {json.dumps(error_response.to_dict())}\n\n"
                    
            sse_response = Response(
                stream_with_context(generate()),
                mimetype='text/event-stream'
            )
            
            # Set SSE headers
            sse_response.headers.update({
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no',
                'Content-Type': 'text/event-stream',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            })
            
            return sse_response
            
        except Exception as e:
            logger.error(f"Error in SSE handler: {str(e)}")
            logger.error("Traceback:")
            for line in traceback.format_exc().splitlines():
                logger.error(line)
                
            error_response = Response(
                response=f"data: {json.dumps({'error': str(e), 'status': STATUS_ERROR})}\n\n",
                status=500,
                mimetype='text/event-stream'
            )
            error_response.headers.update({
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'X-Accel-Buffering': 'no',
                'Content-Type': 'text/event-stream',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            })
            return error_response 