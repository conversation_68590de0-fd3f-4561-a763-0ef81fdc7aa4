import os
import pytest
from unittest.mock import Mock, patch
from configuration.base.auth import AuthConfig
from configuration.base.telegram_auth import TelegramAuthConfig
from configuration.base.telegram_bot import TelegramBotConfig
from configuration.base.telegram_ui import TelegramUIConfig
from configuration.base.telegram_indexer import TelegramIndexerConfig
from src.lib.constants import API_USER_RETRIEVE_ONLY
from quepasa.searcher.models.request import QuepasaRequest

@pytest.fixture
def auth_config():
    """Fixture that provides an AuthConfig instance."""
    return AuthConfig("test_client")

@pytest.fixture
def telegram_auth_config():
    """Fixture that provides a TelegramAuthConfig instance."""
    return TelegramAuthConfig("test_client")

@pytest.fixture
def telegram_bot_config():
    """Fixture that provides a TelegramBotConfig instance."""
    return TelegramBotConfig("test_client")

@pytest.fixture
def telegram_ui_config():
    """Fixture that provides a TelegramUIConfig instance."""
    return TelegramUIConfig("test_client")

@pytest.fixture
def telegram_indexer_config():
    """Fixture that provides a TelegramIndexerConfig instance."""
    return TelegramIndexerConfig("test_client")

# AuthConfig Tests
def test_auth_config_init(auth_config):
    """Test AuthConfig initialization."""
    assert auth_config.client_code == "test_client"
    assert auth_config._api_version == "1.0"

def test_get_authorized_api_users(auth_config):
    """Test getting authorized API users."""
    users = auth_config.get_authorized_api_users()
    assert isinstance(users, list)
    assert 'default' in users
    assert API_USER_RETRIEVE_ONLY in users

def test_get_auth_secret_key(auth_config):
    """Test getting auth secret key."""
    key = auth_config.get_auth_secret_key()
    assert isinstance(key, bytes)
    assert b"QUEPASA_AUTH_VERSION_1.0" in key
    assert b"test_client" in key

def test_create_auth_token(auth_config):
    """Test creating auth token."""
    user_id = "test_user"
    token = auth_config.create_auth_token(user_id)
    assert isinstance(token, str)
    # Token should be base64 encoded
    assert len(token) > 0

def test_validate_auth_token(auth_config):
    """Test validating auth token."""
    user_id = "default"
    token = auth_config.create_auth_token(user_id)
    assert auth_config.validate_auth_token(token) == True

def test_get_authenticated_user(auth_config):
    """Test getting authenticated user."""
    user_id = "default"
    token = auth_config.create_auth_token(user_id)
    assert auth_config.get_authenticated_user(token) == user_id

def test_get_third_party_auth_keys(auth_config):
    """Test getting third party auth keys."""
    keys = auth_config.get_third_party_auth_keys()
    assert isinstance(keys, list)

# TelegramAuthConfig Tests
def test_telegram_auth_init(telegram_auth_config):
    """Test TelegramAuthConfig initialization."""
    assert telegram_auth_config.client_code == "test_client"

def test_is_telegram_user_authorized(telegram_auth_config):
    """Test checking if Telegram user is authorized."""
    user_info = {"user_name": "test_user"}
    
    # Mock telegram config
    with patch.object(telegram_auth_config, '_get_telegram_config', return_value=None):
        assert telegram_auth_config.is_telegram_user_authorized(user_info) == True
        
    # Test with configured usernames
    with patch.object(telegram_auth_config, '_get_telegram_config', 
                     return_value={"user_names": ["test_user"]}):
        assert telegram_auth_config.is_telegram_user_authorized(user_info) == True
        
    # Test unauthorized user
    with patch.object(telegram_auth_config, '_get_telegram_config', 
                     return_value={"user_names": ["other_user"]}):
        assert telegram_auth_config.is_telegram_user_authorized(user_info) == False

# TelegramBotConfig Tests
def test_telegram_bot_init(telegram_bot_config):
    """Test TelegramBotConfig initialization."""
    assert telegram_bot_config.client_code == "test_client"

def test_get_telegram_access_token(telegram_bot_config):
    """Test getting Telegram access token."""
    # Test with no config
    with patch.object(telegram_bot_config, '_get_telegram_config', return_value=None):
        assert telegram_bot_config.get_telegram_access_token() is None
        
    # Test with config
    with patch.object(telegram_bot_config, '_get_telegram_config', 
                     return_value={"token": "test_token"}):
        assert telegram_bot_config.get_telegram_access_token() == "test_token"

def test_get_telegram_bot_name(telegram_bot_config):
    """Test getting Telegram bot name."""
    # Test with no config
    with patch.object(telegram_bot_config, '_get_telegram_config', return_value=None):
        assert telegram_bot_config.get_telegram_bot_name() is None
        
    # Test with config
    with patch.object(telegram_bot_config, '_get_telegram_config', 
                     return_value={"name": "test_bot"}):
        assert telegram_bot_config.get_telegram_bot_name() == "test_bot"

def test_should_stream_telegram_responses(telegram_bot_config):
    """Test checking if responses should be streamed."""
    assert telegram_bot_config.should_stream_telegram_responses() == True

def test_allows_telegram_direct_questions(telegram_bot_config):
    """Test checking if direct questions are allowed."""
    message = {}
    assert telegram_bot_config.allows_telegram_direct_questions(message) == False

def test_allows_telegram_direct_questions_in_topic(telegram_bot_config):
    """Test checking if direct questions are allowed in topics."""
    topic_name = "test_topic"
    assert telegram_bot_config.allows_telegram_direct_questions_in_topic(topic_name) == False

# TelegramUIConfig Tests
def test_telegram_ui_init(telegram_ui_config):
    """Test TelegramUIConfig initialization."""
    assert telegram_ui_config.client_code == "test_client"

def test_get_telegram_first_time_welcome_message(telegram_ui_config):
    """Test getting welcome message."""
    assert telegram_ui_config.get_telegram_first_time_welcome_message() is None
    assert telegram_ui_config.get_telegram_first_time_welcome_message('markdown') is None

def test_get_telegram_start_command_response(telegram_ui_config):
    """Test getting start command response."""
    with patch.object(telegram_ui_config, '_get_telegram_command_text', return_value="Welcome!"):
        assert telegram_ui_config.get_telegram_start_command_response() == "Welcome!"

def test_get_telegram_empty_ask_command_response(telegram_ui_config):
    """Test getting empty ask command response."""
    with patch.object(telegram_ui_config, '_get_telegram_command_text', return_value="Ask me anything!"):
        assert telegram_ui_config.get_telegram_empty_ask_command_response() == "Ask me anything!"

def test_get_telegram_available_commands(telegram_ui_config):
    """Test getting available commands."""
    commands = telegram_ui_config.get_telegram_available_commands()
    assert isinstance(commands, list)
    assert len(commands) > 0

def test_get_telegram_command_descriptions(telegram_ui_config):
    """Test getting command descriptions."""
    descriptions = telegram_ui_config.get_telegram_command_descriptions()
    assert isinstance(descriptions, dict)
    assert 'start' in descriptions
    assert 'ask' in descriptions

def test_get_telegram_empty_command_response(telegram_ui_config):
    """Test empty command response"""
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram",
        command="reset"
    )
    telegram_ui_config.set_request(request)
    response = telegram_ui_config.get_telegram_empty_command_response()
    assert isinstance(response, str)
    assert "reset" in response.lower()

# TelegramIndexerConfig Tests
def test_telegram_indexer_init(telegram_indexer_config):
    """Test TelegramIndexerConfig initialization."""
    assert telegram_indexer_config.client_code == "test_client"

def test_get_earliest_index_date(telegram_indexer_config):
    """Test getting earliest index date."""
    assert telegram_indexer_config.get_earliest_index_date() == "2014-03-01"

def test_get_min_thread_word_count(telegram_indexer_config):
    """Test getting minimum thread word count."""
    channel_id = "test_channel"
    assert telegram_indexer_config.get_min_thread_word_count(channel_id) == 30

def test_get_min_url_thread_word_count(telegram_indexer_config):
    """Test getting minimum URL thread word count."""
    channel_id = "test_channel"
    assert telegram_indexer_config.get_min_url_thread_word_count(channel_id) == 5

def test_get_authoritative_users(telegram_indexer_config):
    """Test getting authoritative users."""
    users = telegram_indexer_config.get_authoritative_users()
    assert isinstance(users, list)

def test_get_telegram_thread_requirements(telegram_indexer_config):
    """Test getting thread requirements."""
    channel_id = "test_channel"
    requirements = telegram_indexer_config.get_telegram_thread_requirements(channel_id)
    assert requirements.min_word_count == 30
    assert requirements.min_word_count_with_urls == 5
    assert isinstance(requirements.authoritative_usernames, list) 