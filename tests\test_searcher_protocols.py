import pytest
import threading
from unittest.mock import Mock, patch
from quepasa.searcher.api.http import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from quepasa.searcher.api.sse import SSEHandler
from quepasa.searcher.api.webhook import WebhookHandler
from quepasa.searcher.api.telegram import Tel<PERSON><PERSON><PERSON>andler
from quepasa.searcher.models.request import QuepasaRequest, UserInfo, WebhookConfig
from quepasa.searcher.models.response import QuepasaResponse, QuepasaAnswer, QuepasaStreamAnswer
from flask import app, Flask
from quepasa.searcher.core.auth import AuthResult

@pytest.fixture
def mock_config():
    with patch('configuration.main.default.QuepasaConfigurationHub') as mock:
        mock.return_value.get_telegram_access_token.return_value = "test_token"
        mock.return_value.is_telegram_user_authorized.return_value = True
        mock.return_value.get_language_code.return_value = "en"
        mock.return_value.request = Mock()
        mock.return_value.request.source = "gpt"
        mock.return_value.request.protocol = "http"
        mock.return_value.request.question = "test query"
        yield mock

@pytest.fixture
def mock_history_manager():
    with patch('quepasa.searcher.api.base.HistoryManager') as mock:
        yield mock

@pytest.fixture
def http_handler(mock_history_manager, mock_config):
    return HTTPHandler(mock_config())

@pytest.fixture
def mock_app():
    app = Flask(__name__)
    return app

def test_http_handler_validate_request(http_handler, mock_config):
    mock_config.return_value.request = QuepasaRequest(
        question="test query",
        protocol="http"
    )
    
    response = http_handler._handle_request_internal("POST", "/search", {})
    assert response[1] == 200  # status code

def test_http_handler_invalid_protocol(http_handler, mock_config):
    mock_config.return_value.request = QuepasaRequest(
        question="test query",
        protocol="webhook",  # Wrong protocol
        source="web",
        client="test_client"
    )
    
    # Mock the _validate_request method
    http_handler._validate_request = Mock(return_value=False)
    http_handler.error = "Invalid protocol for HTTP handler"
    
    response = http_handler._handle_request_internal("POST", "/search", {})
    assert response[1] == 400  # status code

def test_http_handler_process_request(http_handler, mock_history_manager, mock_config):
    mock_config.return_value.request = QuepasaRequest(
        question="test query",
        protocol="http"
    )
    
    mock_response = QuepasaResponse(
        status="ok",
        data={},
        error=None
    )
    
    http_handler._process_request = Mock(return_value=mock_response)
    
    response = http_handler._handle_request_internal("POST", "/search", {})
    assert response[1] == 200  # status code

def test_sse_handler(mock_history_manager, mock_config, mock_app):
    with mock_app.test_request_context('/search'):
        handler = SSEHandler(mock_config())
        mock_config.return_value.request = QuepasaRequest(
            question="test query",
            protocol="sse",
            source="web",
            client="test_client"
        )
        
        # Mock the source factory's get_answer method
        mock_stream_response = QuepasaStreamAnswer(
            text="test answer",
            loading=False,
            streaming=False
        )
        
        # Create a generator that will be consumed by the SSE handler
        def mock_generator():
            # First yield sets the final_response_data
            yield mock_stream_response
            # Second yield ensures the generator is not empty
            yield mock_stream_response
        
        handler.source_factory.get_answer = Mock(return_value=mock_generator())
        
        response = handler._handle_request_internal("POST", "/search", {})
        assert response.status_code == 200  # Check status code
        assert response.mimetype == 'text/event-stream'  # Verify correct content type
        assert 'Cache-Control' in response.headers
        assert 'Connection' in response.headers
        assert response.headers['X-Accel-Buffering'] == 'no'

def test_webhook_handler(mock_history_manager, mock_config):
    handler = WebhookHandler(mock_config())
    mock_config.return_value.request = QuepasaRequest(
        question="test query",
        protocol="webhook",
        webhook={"endpoint": "https://example.com/webhook"}
    )
    
    response = handler._handle_request_internal("POST", "/search", {})
    assert response[1] == 202  # Accepted

def test_webhook_handler_missing_url(mock_history_manager, mock_config):
    handler = WebhookHandler(mock_config())
    mock_config.return_value.request = QuepasaRequest(
        question="test query",
        protocol="webhook"  # No webhook endpoint
    )
    
    response = handler._handle_request_internal("POST", "/search", {})
    assert response[1] == 400  # Bad request

@pytest.mark.asyncio
async def test_telegram_handler(mock_history_manager, mock_config):
    handler = TelegramHandler(mock_config())
    mock_config.return_value.request = QuepasaRequest(
        question="test query",
        protocol="telegram",
        source="web",
        user_info={"id": "123", "chat_id": 123456}
    )
    
    response = handler._handle_request_internal("POST", "/search", {})
    assert response[1] == 200  # OK

def test_telegram_handler_missing_chat_id(mock_history_manager, mock_config):
    handler = TelegramHandler(mock_config())
    user_info = UserInfo(id="123")  # No chat_id
    mock_config.return_value.request = QuepasaRequest(
        question="test query",
        protocol="telegram",
        source="web",
        client="test_client",
        user_info=user_info
    )
    
    # Mock the authentication check
    mock_config.return_value.is_telegram_user_authorized.return_value = False
    
    # Mock the error response
    handler._send_error_message = Mock()
    
    # Mock the authenticate_request method to return unauthorized
    auth_result = AuthResult(is_authorized=False, error="Missing chat_id")
    handler._authenticate_request = Mock(return_value=auth_result)
    
    # Mock the handle_request method to return error response
    def mock_handle_request(*args, **kwargs):
        return {'error': 'Missing chat_id'}, 400, {}
    
    # Patch the base class's handle_request method
    with patch('quepasa.searcher.api.base.BaseAPIHandler.handle_request', side_effect=mock_handle_request):
        response = handler._handle_request_internal("POST", "/search", {})
        assert response[1] == 400  # Bad request 