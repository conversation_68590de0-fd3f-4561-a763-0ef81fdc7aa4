# Build stage
FROM python:3.9-slim AS builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    DOCKER_BUILDKIT=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    HF_HOME=/app/models \
    TRANSFORMERS_CACHE=/app/models

WORKDIR /app

# Install system dependencies including build tools and protobuf compiler
RUN apt-get update && apt-get install -y --no-install-recommends \
    g++ \
    protobuf-compiler \
    pkg-config \
    python3-dev \
    libprotobuf-dev \
    make \
    build-essential \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Upgrade pip and install build tools
RUN pip install --upgrade pip setuptools wheel

# Copy only necessary files first
COPY requirements.txt .
COPY quepasa/embedding/requirements.txt quepasa/embedding/
COPY setup.py .

# Copy requirements and install Python dependencies
RUN pip install --no-cache-dir torch --extra-index-url https://download.pytorch.org/whl/cpu && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir wheel && \
    pip install --no-cache-dir setuptools==59.8.0 && \
    pip install --no-cache-dir -r quepasa/embedding/requirements.txt

# Install PyArmor 7.x specifically for compatibility with obfuscate command
RUN pip install --no-cache-dir pyarmor==7.7.4

# Install transformers + sentencepiece + download model
RUN pip install --no-cache-dir transformers sentencepiece

# Force model download into a cache directory in the builder
RUN mkdir -p /app/models && \
    python -c "from huggingface_hub import snapshot_download; snapshot_download(repo_id='papluca/xlm-roberta-base-language-detection', local_dir='/app/models/xlm-roberta', local_dir_use_symlinks=False)" && \
    echo 'Downloaded papluca/xlm-roberta-base-language-detection to /app/models:' && \
    ls -la /app/models && \
    ls -R /app/models

# Copy application code after dependencies
COPY configuration/ configuration/
COPY quepasa/common/ quepasa/common/
COPY quepasa/embedding/ quepasa/embedding/
COPY quepasa/searcher/models/ quepasa/searcher/models/
COPY quepasa/searcher/*.py quepasa/searcher/
COPY quepasa/*.py quepasa/
COPY src/ src/

# Obfuscate the Python code
RUN mkdir -p /app/obfuscated/quepasa && \
    # Obfuscate the embedding module
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/embedding quepasa/embedding/*.py && \
    # Obfuscate other required modules
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/common quepasa/common/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher/models quepasa/searcher/models/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher quepasa/searcher/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa quepasa/*.py && \
    # Handle src directory
    mkdir -p /app/obfuscated/src && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/embedding src/lib/embedding/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/llm src/lib/llm/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/whisper src/lib/whisper/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/reranker src/lib/reranker/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib src/lib/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src src/*.py && \
    # Verify files were created (for debugging)
    echo "Checking obfuscated directories:" && \
    ls -la /app/obfuscated/quepasa && \
    ls -la /app/obfuscated/quepasa/embedding || true

# Final stage
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    busybox \
    curl \
    && mkdir -p cache \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && pip cache purge

# Copy only necessary files from builder
COPY --from=builder /usr/local/lib/python3.9/site-packages/ /usr/local/lib/python3.9/site-packages/
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# Copy obfuscated files instead of the original source
COPY --from=builder /app/obfuscated/quepasa /app/quepasa 
COPY --from=builder /app/obfuscated/src /app/src

# IMPORTANT: Copy PyArmor runtime files from each obfuscated directory
COPY --from=builder /app/obfuscated/quepasa/pytransform /app/pytransform
COPY --from=builder /app/obfuscated/quepasa/embedding/pytransform /app/quepasa/embedding/pytransform
COPY --from=builder /app/obfuscated/quepasa/common/pytransform /app/quepasa/common/pytransform
COPY --from=builder /app/obfuscated/quepasa/searcher/pytransform /app/quepasa/searcher/pytransform

COPY --from=builder /app/configuration /app/configuration

# Copy the Hugging Face cached model from the builder
COPY --from=builder /app/models /app/models

# Expose port for REST API
EXPOSE 8080

# Set environment variables
ENV PYTHONPATH=/app \
    PATH="/usr/local/bin:$PATH" \
    HF_HOME=/app/models

# Set working directory
WORKDIR /app

# Create a startup script
RUN echo '#!/bin/sh\n\
# Start the API server in the background\n\
uvicorn quepasa.embedding.app:app --host 0.0.0.0 --port 8080 & \n\
SERVER_PID=$!\n\
\n\
# Wait for the server to be available and ready\n\
echo "Waiting for embedding service to be ready..."\n\
MAX_ATTEMPTS=30\n\
ls -la /app/models\n\
ls -R /app/models\n\
ATTEMPTS=0\n\
\n\
while [ $ATTEMPTS -lt $MAX_ATTEMPTS ]; do\n\
  if curl -s http://localhost:8080/health | grep -q "healthy"; then\n\
    echo "Embedding service is ready!"\n\
    break\n\
  fi\n\
  ATTEMPTS=$((ATTEMPTS+1))\n\
  echo "Waiting for embedding service (attempt $ATTEMPTS/$MAX_ATTEMPTS)..."\n\
  sleep 2\n\
done\n\
\n\
if [ $ATTEMPTS -eq $MAX_ATTEMPTS ]; then\n\
  echo "Embedding service failed to start properly after $(($MAX_ATTEMPTS * 2)) seconds"\n\
else\n\
  # Call the startup endpoint to warm up the embedding cache\n\
  echo "Calling startup endpoint to initialize the embedding service..."\n\
  curl -v "http://localhost:8080/startup?client_id=default" || echo "Failed to call startup endpoint"\n\
  echo "Embedding service initialization completed"\n\
fi\n\
\n\
# Keep the container running with the API server\n\
wait $SERVER_PID\n\
' > /app/startup.sh && chmod +x /app/startup.sh

# Now check your /app/models folder
RUN ls -la /app/models && \
    ls -R /app/models

# Run the startup script
CMD ["/app/startup.sh"]