from __future__ import annotations

import os
from typing import List, Dict, Any, Generator
from openai import OpenAI

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .base import BaseLL<PERSON>
from .cache import LLMCacheMixin
from .providers import LL<PERSON>rovider

logger = QuepasaLogger().get_instance(__name__)

class MWSLLM(BaseLLM, LLMCacheMixin):
    """MWS LLM provider using OpenAI-compatible API."""
    
    def __init__(self):
        """Initialize MWS LLM provider."""
        super().__init__()

        # Check for API key
        self.api_key = os.getenv('MWS_API_KEY')
        if not self.api_key:
            raise ValueError("MWS_API_KEY environment variable is not set")
        
        self.client = OpenAI(
            base_url="https://api.gpt.mws.ru/v1/",
            api_key=self.api_key
        )

    @property
    def provider(self) -> LLMProvider:
        return LLMProvider.MWS

    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a completion from MWS API.
        
        Args:
            model_version: Model version
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Returns:
            Generated response text
        """
        try:
            completion = self.client.chat.completions.create(
                model=model_version,
                messages=prompt_list,
                temperature=0.0,  # Use deterministic output
                max_tokens=answer_prompt_size,
                response_format={"type": "json_object"} if json_mode else None
            )
            return completion.choices[0].message.content or ""
            
        except Exception as e:
            logger.error(f"Failed to get answer from MWS: {str(e)}")
            return ""

    def get_streaming_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> Generator[str, None, None]:
        """Get a streaming completion from MWS API.
        
        Args:
            model_version: Model version
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Yields:
            Generated response chunks
        """
        try:
            completion = self.client.chat.completions.create(
                model=model_version,
                messages=prompt_list,
                temperature=0.0,  # Use deterministic output
                max_tokens=answer_prompt_size,
                response_format={"type": "json_object"} if json_mode else None,
                stream=True
            )
            
            for chunk in completion:
                if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Failed to get streaming answer from MWS: {str(e)}")
            yield "" 