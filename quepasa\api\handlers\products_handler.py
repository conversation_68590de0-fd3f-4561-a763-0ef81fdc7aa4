from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from quepasa.api.handlers.document_handler import Document, DocumentHandler
from quepasa.api.auth import verify_auth
from quepasa.api.models import BatchResult
from src.lib.logger import QuepasaLogger
from src.lib.markdown_converter import products_to_documents
from src.lib.constants import DOCUMENT_TYPE_PRODUCT

# Maximum number of records allowed per request
MAX_RECORDS = 1000

# Logger
logger = QuepasaLogger().get_instance(__name__)

# Create router
router = APIRouter()


class ProductIngestionRequest(BaseModel):
    """Request model for product ingestion"""

    records: List[Dict[str, Any]] = Field(description="Array of product objects", max_length=MAX_RECORDS)


class ProductDeletionRequest(BaseModel):
    """Request model for batch product deletion"""

    productIds: List[str] = Field(description="Array of product IDs to delete", max_length=MAX_RECORDS)


class ProductsHandler(DocumentHandler):
    """Handles product ingestion operations"""

    def __init__(self):
        """Initialize ProductsHandler"""
        super().__init__()

    async def ingest_products(self, customer_id: str, collection: str, request: ProductIngestionRequest) -> BatchResult:
        """Ingest products and create batch for processing

        Args:
            customer_id: Customer identifier from X-Customer-Id header
            collection: Collection name for domain construction
            request: Product ingestion request containing records

        Returns:
            BatchResult with batch ID and processed product SKUs
        """
        # Validate input
        if not request.records:
            raise ValueError("No product records provided")

        if len(request.records) > MAX_RECORDS:
            raise ValueError(f"Maximum {MAX_RECORDS} product records allowed")

        # Construct domain name
        domain = f"{customer_id}_{collection}"

        # Convert products to markdown documents
        logger.info(
            f"Converting {len(request.records)} products to markdown for customer {customer_id}"
        )
        document_data_list = products_to_documents(request.records)

        if not document_data_list:
            raise ValueError("No valid products could be processed")

        # Convert dictionaries to Document objects
        markdown_documents = []
        for doc_data in document_data_list:
            document = Document(
                id=doc_data['id'],
                type=DOCUMENT_TYPE_PRODUCT,
                url=doc_data['url'],
                title=doc_data['title'],
                sku=doc_data['sku'],
                price_from=doc_data['price_from'],
                price_to=doc_data['price_to'],
                metadata=doc_data['metadata'],
                chunks=doc_data['chunks']
            )
            markdown_documents.append(document)

        return await self.upsert_documents(customer_id, domain, markdown_documents)

    async def delete_product(self, customer_id: str, collection: str, product_id: str) -> BatchResult:
        """Delete a single product from storage

        Args:
            customer_id: Customer identifier from X-Customer-Id header
            collection: Collection name for domain construction
            product_id: Product ID to delete

        Returns:
            BatchResult with batch ID and deleted product ID
        """
        # Construct domain name
        domain = f"{customer_id}_{collection}"

        return await self.remove_document(customer_id, domain, product_id)

    async def delete_products(self, customer_id: str, collection: str, request: ProductDeletionRequest) -> BatchResult:
        """Delete multiple products from storage

        Args:
            customer_id: Customer identifier from X-Customer-Id header
            collection: Collection name for domain construction
            request: Product deletion request containing product IDs

        Returns:
            BatchResult with batch ID and deleted product IDs
        """
        # Validate input
        if not request.productIds:
            raise ValueError("No product IDs provided for deletion")

        if len(request.productIds) > MAX_RECORDS:
            raise ValueError(f"Maximum {MAX_RECORDS} product IDs allowed for deletion")

        # Construct domain name
        domain = f"{customer_id}_{collection}"

        return await self.remove_documents(customer_id, domain, request.productIds)


handler = ProductsHandler()


# Initialize handler instance
@router.post("/{collection}")
async def ingest_products(
        collection: str,
        request: ProductIngestionRequest,
        customer_id: str = Depends(verify_auth)
) -> BatchResult:
    """Ingest products into storage

    Args:
        collection: Collection name for domain construction
        request: Product ingestion request
        customer_id: Validated customer ID from authentication

    Returns:
        BatchResult with batch ID and processed product SKUs
    """

    logger.info(
        f"Ingesting {len(request.records)} products for customer {customer_id}, collection {collection}"
    )

    try:
        return await handler.ingest_products(customer_id, collection, request)

    except ValueError as e:
        logger.error(f"[ingest_products] Validation error: {str(e)}")
        raise HTTPException(400, str(e))
    except Exception as e:
        logger.error(f"[ingest_products] Unexpected error: {str(e)}")
        raise HTTPException(500, str(e))


@router.delete("/{collection}/{product_id}")
async def delete_product(
        collection: str,
        product_id: str,
        customer_id: str = Depends(verify_auth)
) -> BatchResult:
    """Delete a single product from storage

    Args:
        collection: Collection name for domain construction
        product_id: Product ID to delete
        customer_id: Validated customer ID from authentication

    Returns:
        BatchResult with batch ID and deleted product ID
    """

    logger.info(
        f"Deleting product {product_id} for customer {customer_id}, collection {collection}"
    )

    try:
        return await handler.delete_product(customer_id, collection, product_id)

    except ValueError as e:
        logger.error(f"[delete_product] Validation error: {str(e)}")
        raise HTTPException(400, str(e))
    except Exception as e:
        logger.error(f"[delete_product] Unexpected error: {str(e)}")
        raise HTTPException(500, str(e))


@router.delete("/{collection}")
async def delete_products(
        collection: str,
        request: ProductDeletionRequest,  # Verify that all consumers support body for DELETE as per RFC 9110
        customer_id: str = Depends(verify_auth)
) -> BatchResult:
    """Delete multiple products from storage

    Args:
        collection: Collection name for domain construction
        request: Product deletion request with product IDs
        customer_id: Validated customer ID from authentication

    Returns:
        BatchResult with batch ID and deleted product IDs
    """

    logger.info(
        f"Deleting {len(request.productIds)} products for customer {customer_id}, collection {collection}"
    )

    try:
        return await handler.delete_products(customer_id, collection, request)

    except ValueError as e:
        logger.error(f"[delete_products] Validation error: {str(e)}")
        raise HTTPException(400, str(e))
    except Exception as e:
        logger.error(f"[delete_products] Unexpected error: {str(e)}")
        raise HTTPException(500, str(e))


@router.get("/{collection}")
async def list_products(
        collection: str,
        customer_id: str = Depends(verify_auth)
) -> Dict[str, str]:
    """List documents in storage

    Args:
        collection: Collection name for domain construction
        customer_id: Validated customer ID from authentication

    Returns:
        Dictionary mapping product IDs to product titles
    """

    logger.info(f"Listing documents for customer {customer_id} and collection {collection}")

    # Construct domain name
    domain = f"{customer_id}_{collection}"

    try:
        return await handler.list_documents_by_domain(customer_id, domain)

    except Exception as e:
        logger.error(f"[list_products] Unexpected error: {str(e)}")
        raise HTTPException(500, str(e))


@router.get("/{collection}/{product_id}")
async def get_product(
        collection: str,
        product_id: str,
        customer_id: str = Depends(verify_auth)
) -> Optional[Dict[str, Any]]:
    """Get product from storage

    Args:
        collection: Collection name for domain construction
        product_id: Product ID to retrieve
        customer_id: Validated customer ID from authentication

    Returns:
        Product document
    """

    logger.info(f"Getting product {product_id} for customer {customer_id} and collection {collection}")

    # Construct domain name
    domain = f"{customer_id}_{collection}"

    try:
        return await handler.get_document(customer_id, domain, product_id)

    except Exception as e:
        logger.error(f"[get_product] Unexpected error: {str(e)}")
        raise HTTPException(500, str(e))

