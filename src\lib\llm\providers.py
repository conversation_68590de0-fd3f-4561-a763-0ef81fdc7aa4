from enum import Enum

class LLMProvider(Enum):
    """Enum for supported LLM providers."""
    OPENAI = 'openai'
    MISTRAL = 'mistral'
    ANTHROPIC = 'anthropic'
    REPLICATE = 'replicate'
    YANDEX = 'yandex'
    OLLAMA = 'ollama'
    TOGETHER = 'together'
    NEBIUS = 'nebius'
    MWS = 'mws'
    GEMINI = 'gemini'
    
    @classmethod
    def from_str(cls, provider: str) -> 'LLMProvider':
        """Convert string to LLMProvider enum.
        
        Args:
            provider: Provider string
            
        Returns:
            Matching LLMProvider enum
            
        Raises:
            ValueError: If provider string is invalid
        """
        try:
            return next(
                p for p in cls 
                if p.value.lower() == provider.lower()
            )
        except StopIteration:
            raise ValueError(f"Unknown LLM provider: {provider}. "
                           f"Supported providers: {[p.value for p in cls]}") 