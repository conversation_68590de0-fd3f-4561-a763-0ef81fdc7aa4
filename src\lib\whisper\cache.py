import hashlib
import json
import os
import tempfile
from typing import Any, Dict, Optional, List, Tu<PERSON>
from pathlib import Path

from src.lib.files import QuepasaFiles
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)
qp_files = QuepasaFiles()

class WhisperCacheMixin:
    """Cache for Whisper transcription results"""
    
    def __init__(self):
        """
        Initialize the Whisper cache
        """

        self._cache_dir = "cache/whisper"
        self._local_cache_dir = os.path.join(os.path.dirname(__file__), "../../../cache/whisper")
        os.makedirs(self._local_cache_dir, exist_ok=True)
    
    def _get_cache_paths(self, model_version: str, audio_data: bytes, meta: Dict[str, Any]) -> Tuple[str, str]:
        """
        Generate a cache key for the given parameters
        
        Args:
            model_version: The model name
            audio_data: Audio data as bytes
            meta: Dictionary containing metadata or string language code
            
        Returns:
            Tuple of (local_path, remote_path)
        """
        # For audio, we use a smaller sample to avoid excessive hash computation
        sample_size = min(len(audio_data), 1024 * 1024)  # Use at most 1MB for hashing
        audio_sample = audio_data[:sample_size // 2] + audio_data[-sample_size // 2:]
        
        # Create a hash of all elements
        audio_md5 = hashlib.md5(audio_sample).hexdigest()

        # Remote cache path with hierarchical structure
        path_parts = []
        for i in range(3):  # Create 3 levels of directories
            if i + 1 <= len(audio_md5):
                path_parts.append(audio_md5[0:i + 1])
        path_parts.append(audio_md5)
        
        # Get language from meta (either string or dict)
        if isinstance(meta, str):
            language = meta
        else:
            language = meta.get('language', 'auto')
        
        # Local cache path
        local_path = os.path.join(self._local_cache_dir, model_version, language, *path_parts) + '.json'

        # Remote cache path
        remote_path = f"{self._cache_dir}/{model_version}/{language}/{'/'.join(path_parts)}.json"
        return local_path, remote_path
    
    def _load_local_cache(self, local_path: str) -> Optional[Dict[str, Any]]:
        """Try to load transcription from local cache.
        
        Args:
            local_path: Path to local cache file
            
        Returns:
            Cached embedding if available, None otherwise
        """
        try:
            if os.path.isfile(local_path):
                with open(local_path, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load local cache {local_path}: {str(e)}")
        return None

    def _load_remote_cache(self, remote_path: str, local_path: str) -> Optional[List[float]]:
        """Try to load embedding from remote cache.
        
        Args:
            remote_path: Path to remote cache file
            local_path: Path to save downloaded file
            
        Returns:
            Cached embedding if available, None otherwise
        """
        if qp_files.exists(remote_path):
            try:
                qp_files.download_file(remote_path, local_path)
                return self._load_local_cache(local_path)
            except Exception as e:
                logger.warning(f"Failed to load remote cache {remote_path}: {str(e)}")
        return None

    def _save_segments(self, segments: Dict[str, Any], local_path: str, remote_path: str) -> None:
        """Save transcription segments to both local and remote cache.
        
        Args:
            segments: The transcription segments to save
            local_path: Path to local cache file
            remote_path: Path to remote cache file
        """
        try:
            # Save locally
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            with open(local_path, 'w') as f:
                f.write(json.dumps(segments))

            # Save remotely
            qp_files.upload_file(local_path, remote_path)
        except Exception as e:
            logger.warning(f"Failed to save transcription cache: {str(e)}")

    def get_cached_segments(self, model_version: str, audio_data: bytes, meta: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """Get cached segments, computing if necessary.
        
        This method implements a caching strategy that:
        1. Tries to load from local cache
        2. If not found, tries to load from remote cache
        3. If not found, computes the segments
        4. If computed successfully, saves to both caches
        
        Args:
            model_version: The model version to use for embeddings
            audio_data: The audio data to get segments for
            language: Optional language code
            
        Returns:
            The embedding if available/computable, None otherwise
        """
        # Get cache paths
        local_path, remote_path = self._get_cache_paths(model_version, audio_data, meta)
        
        # Try local cache
        segments = None
        try:
            segments = self._load_local_cache(local_path)
            if segments is not None:
                return segments
        except Exception as e:
            logger.warning(f"Failed to load local cache {local_path}: {str(e)}")
            
        # Try remote cache
        try:
            segments = self._load_remote_cache(remote_path, local_path)
            if segments is not None:
                return segments
        except Exception as e:
            logger.warning(f"Failed to load remote cache {remote_path}: {str(e)}")
            
        # Compute new segments
        try:
            segments = self.get_segments(model_version, audio_data, meta)
            if segments is not None:
                self._save_segments(segments, local_path, remote_path)
        except Exception as e:
            logger.warning(f"Failed to compute segments: {str(e)}")
            
        return segments 