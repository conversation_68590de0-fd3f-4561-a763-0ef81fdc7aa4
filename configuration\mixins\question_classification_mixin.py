from typing import Union
from quepasa.searcher.models.request import QuepasaRequest
from ..base.question_classification import QuestionClassificationConfig

class QuestionClassificationMixin(QuestionClassificationConfig):
    """Mixin that implements question classification functionality."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
