import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from asyncio import create_task

from src.lib.llm.anthropic import <PERSON>throp<PERSON><PERSON><PERSON>
from src.lib.llm.providers import LL<PERSON><PERSON>ider

@pytest.fixture
async def llm():
    """Provide an instance of AnthropicLLM for testing."""
    mock_chunks = [Mock(type="content_block_delta", delta=Mock(text="Test response chunk 1")), 
                  <PERSON>ck(type="content_block_delta", delta=Mock(text="Test response chunk 2"))]
    mock_stream = AsyncMock()
    
    async def mock_aiter():
        for chunk in mock_chunks:
            yield chunk
    
    mock_stream.__aiter__ = mock_aiter
    mock_stream.text_stream = ["Test response chunk 1", "Test response chunk 2"]

    with patch("anthropic.Anthropic") as mock_anthropic:
        mock_client = Mock()
        mock_client.messages.create.return_value = Mock(content=[{"text": "Test response"}])
        mock_client.messages.stream.return_value = mock_stream
        mock_anthropic.return_value = mock_client
        llm = AnthropicLLM(api_key="test-key")
        yield llm

@pytest.mark.asyncio
async def test_provider():
    """Test that the provider is correctly set."""
    llm = AnthropicLLM(api_key="test-key")
    assert llm.provider == LLMProvider.ANTHROPIC

@pytest.mark.asyncio
async def test_get_answer_success():
    """Test getting a successful answer."""
    prompts = [{"role": "user", "content": "test"}]
    
    llm = AnthropicLLM(api_key="test-key")
    mock_response = Mock()
    # Correctly format the mock to match the expected structure
    mock_response.content = [{"text": "Test response"}]
    
    with patch.object(llm.client.messages, 'create', return_value=mock_response):
        response = llm.get_answer("claude-3-opus", prompts, 100)
        assert response == "Test response"

@pytest.mark.skip(reason="Anthropic does not support JSON mode")
async def test_get_answer_with_json_mode():
    """Test getting an answer with JSON mode."""
    pass

@pytest.mark.asyncio
async def test_get_answer_failure():
    """Test handling of API errors."""
    prompts = [{"role": "user", "content": "test"}]
    
    llm = AnthropicLLM(api_key="test-key")
    mock_create = Mock(side_effect=Exception("API Error"))
    with patch.object(llm.client.messages, 'create', mock_create):
        with pytest.raises(Exception, match="Failed to get answer from Anthropic: API Error"):
            llm.get_answer("claude-3-opus", prompts, 100)

@pytest.mark.asyncio
async def test_missing_api_key():
    """Test error when API key is missing."""
    with patch.dict('os.environ', clear=True):
        with pytest.raises(ValueError, match="ANTHROPIC_API_KEY environment variable is required"):
            AnthropicLLM()

@pytest.mark.asyncio
async def test_get_streaming_answer():
    """Test getting a streaming answer."""
    mock_stream = Mock()
    mock_stream.text_stream = ["Test response chunk 1", "Test response chunk 2"]
    
    class MockContextManager:
        def __enter__(self):
            return mock_stream
        
        def __exit__(self, *args):
            pass
    
    llm = AnthropicLLM(api_key="test-key")
    mock_stream_create = Mock(return_value=MockContextManager())
    
    with patch.object(llm.client.messages, 'stream', mock_stream_create):
        chunks = []
        generator = llm.get_streaming_answer("claude-3-opus", [{"role": "user", "content": "test"}], 100)
        for chunk in generator:
            chunks.append(chunk)
        
        assert len(chunks) == 2
        assert chunks[0] == "Test response chunk 1"
        assert chunks[1] == "Test response chunk 2"

@pytest.mark.asyncio
async def test_get_cached_streaming_chunks():
    """Test getting cached streaming chunks."""
    # Skip this test as caching implementation details have changed
    pytest.skip("The caching implementation has changed")

@pytest.mark.asyncio
async def test_max_tokens_parameter():
    """Test that max_tokens parameter is correctly passed."""
    prompts = [{"role": "user", "content": "test"}]
    
    llm = AnthropicLLM(api_key="test-key")
    mock_response = Mock()
    # Correctly format the mock to match the expected structure
    mock_response.content = [{"text": "Test response"}]
    
    with patch.object(llm.client.messages, 'create', return_value=mock_response):
        llm.get_answer("claude-3-opus", prompts, 100)
        llm.client.messages.create.assert_called_once_with(
            model="claude-3-opus",
            messages=[{"role": "user", "content": "test"}],
            max_tokens=100
        ) 