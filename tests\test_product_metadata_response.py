import json

from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.response import ProductItem, QuepasaAnswer
from quepasa.searcher.models.answer import AnswerFormatter


def _create_product_document(doc_id="SKU123", title="Product Title", url="https://example.com/product",
                             sku=None, metadata=None):
    """Helper method to create a product document"""
    return QuepasaDocument(
        root_id=f"ROOT_{doc_id}",
        id=doc_id,
        chunk_index=0,
        client="test_client",
        domain="default",
        provider="test",
        type="product",
        kind="document",
        level="chunk",
        url=url,
        language="en",
        title=title,
        keywords=["test"],
        text="Test product content",
        tokens=10,
        chunks=1,
        start_position=0,
        end_position=100,
        created_at=None,
        updated_at=None,
        embeddings=None,
        score=0.95,
        sku=sku or doc_id,
        metadata=metadata
    )


def _create_regular_document(doc_id="DOC123", title="Regular Document", url="https://example.com/doc"):
    """Helper method to create a regular (non-product) document"""
    return QuepasaDocument(
        root_id=f"ROOT_{doc_id}",
        id=doc_id,
        chunk_index=0,
        client="test_client",
        domain="default",
        provider="test",
        type="document",
        kind="document",
        level="chunk",
        url=url,
        language="en",
        title=title,
        keywords=["doc"],
        text="Regular document content",
        tokens=10,
        chunks=1,
        start_position=0,
        end_position=100,
        created_at=None,
        updated_at=None,
        embeddings=None,
        score=0.95,
        sku=None,
        metadata=None
    )


class TestProductMetadataResponse:
    """Product Metadata Response Tests"""

    def setup_method(self):
        """Setup test environment"""
        self.formatter = AnswerFormatter()
        self.mock_request = QuepasaRequest(
            question="What products do you have?",
            protocol="http",
            collection="products"
        )

    def test_malformed_json_metadata_handling(self):
        """Malformed JSON metadata should not crash product extraction
        Prevents: Production crashes from malformed product metadata
        """
        # Test malformed JSON cases
        malformed_cases = [
            '{"invalid": json}',  # Syntax error
            '',  # Empty string
            None  # Null value
        ]

        source_hash = {}
        for i, malformed_metadata in enumerate(malformed_cases):
            document = _create_product_document(
                doc_id=f"SKU{i}",
                title=f"Product {i}",
                url=f"https://example.com/product/{i}",
                metadata=malformed_metadata
            )
            source_hash[i + 1] = document

        # Should not raise exceptions
        formatted = self.formatter.format_answer(self.mock_request, "Test answer [1] [2] [3]", source_hash)
        products = formatted.products

        # All should create ProductItems with empty allMeta
        assert products is not None
        assert len(products) == 3
        for product in products:
            assert isinstance(product, ProductItem)
            assert product.allMeta == {}  # Graceful fallback to empty dict

    def test_product_metadata_preservation(self):
        """Product metadata should be preserved exactly during transformation
        Prevents: Silent data corruption in API responses
        """
        # Valid product metadata
        expected_metadata = {"name": "Running Shoe", "price": 99.99, "category": "footwear"}

        document = _create_product_document(
            doc_id="SKU123",
            title="Running Shoe",
            url="https://store.com/sku123",
            metadata=json.dumps(expected_metadata)
        )

        source_hash = {1: document}
        formatted = self.formatter.format_answer(self.mock_request, "Test answer [1]", source_hash)
        products = formatted.products

        # Verify data integrity
        assert products is not None
        assert len(products) == 1

        product = products[0]
        assert product.id == "SKU123"
        assert product.title == "Running Shoe"
        assert product.url == "https://store.com/sku123"
        assert product.collection == "products"
        assert product.allMeta == expected_metadata  # Exact preservation

        # Test optional URL field handles None gracefully
        document_no_url = _create_product_document(
            doc_id="SKU456",
            title="Product No URL",
            url=None,
            metadata=json.dumps({"name": "Test"})
        )

        source_hash_no_url = {1: document_no_url}
        formatted_no_url = self.formatter.format_answer(self.mock_request, "Test answer [1]", source_hash_no_url)
        products_no_url = formatted_no_url.products

        assert products_no_url[0].url == ""  # None handled gracefully

    def test_product_document_detection_and_filtering(self):
        """Only documents with type='product' should create ProductItem objects
        Prevents: Products not appearing in enhanced responses
        """
        # Mixed document types
        product_doc = _create_product_document(
            doc_id="SKU123",
            title="Product Title",
            url="https://store.com/product",
            metadata='{"name": "Product"}'
        )

        non_product_doc = _create_regular_document(
            doc_id="DOC456",
            title="Regular Document",
            url="https://example.com/doc"
        )

        source_hash = {1: product_doc, 2: non_product_doc}
        formatted = self.formatter.format_answer(self.mock_request, "Test answer [1] [2]", source_hash)
        products = formatted.products

        # Only product documents create ProductItem objects
        assert products is not None
        assert len(products) == 1
        assert products[0].id == "SKU123"
        assert products[0].allMeta == {"name": "Product"}

        # Verify references still work (source_hash contains both documents)
        assert len(source_hash) == 2
        assert source_hash[2].type == "document"

    def test_non_product_responses_unchanged(self):
        """Responses without products should be identical to pre-enhancement
        Prevents: Breaking existing API consumers
        """
        # Search results with NO product documents
        regular_doc = _create_regular_document(
            doc_id="DOC789",
            title="FAQ Document",
            url="https://example.com/faq"
        )

        source_hash = {1: regular_doc}
        formatted = self.formatter.format_answer(self.mock_request, "Test answer", source_hash)
        products = formatted.products

        # Products field should be None (not empty array)
        assert products is None

        # Test QuepasaAnswer backward compatibility
        answer_without_products = QuepasaAnswer(
            text="This is a regular answer",
            type="strict"
        )

        # Products field defaults to None
        assert answer_without_products.products is None

        # to_dict excludes products when None
        result_dict = answer_without_products.to_dict()
        assert "products" not in result_dict
        assert result_dict["text"] == "This is a regular answer"
        assert result_dict["type"] == "strict"

    def test_complete_product_transformation_pipeline(self):
        """Product data should survive complete transformation from document to API response
        Prevents: Products appearing in response but missing data
        """
        # Product document with valid metadata flows through complete pipeline
        original_metadata = {
            "name": "Premium Running Shoe",
            "price": 149.99,
            "category": "footwear",
            "brand": "Nike",
            "sizes": [7, 8, 9, 10],
            "in_stock": True
        }

        product_doc = _create_product_document(
            doc_id="SKU_E2E",
            title="Premium Running Shoe",
            url="https://store.com/premium-shoe",
            metadata=json.dumps(original_metadata)
        )

        source_hash = {1: product_doc}
        formatted = self.formatter.format_answer(self.mock_request, "Test answer with [1]", source_hash)
        products = formatted.products

        # Validate complete data transformation pipeline
        assert products is not None
        assert len(products) == 1

        product_item = products[0]

        # ProductItem contains all expected fields
        assert product_item.id == "SKU_E2E"
        assert product_item.title == "Premium Running Shoe"
        assert product_item.url == "https://store.com/premium-shoe"
        assert product_item.collection == "products"

        # Data matches original product document exactly
        assert product_item.allMeta == original_metadata
        assert product_item.allMeta["name"] == "Premium Running Shoe"
        assert product_item.allMeta["price"] == 149.99
        assert product_item.allMeta["sizes"] == [7, 8, 9, 10]
        assert product_item.allMeta["in_stock"] is True

        # Test complete QuepasaAnswer integration
        answer = QuepasaAnswer(
            text="Here are the products I found:",
            products=products
        )

        response_dict = answer.to_dict()

        # API response includes products array
        assert "products" in response_dict
        assert len(response_dict["products"]) == 1

        # Verify serialization preserves all data
        serialized_product = response_dict["products"][0]
        assert serialized_product["id"] == "SKU_E2E"
        assert serialized_product["title"] == "Premium Running Shoe"
        assert serialized_product["url"] == "https://store.com/premium-shoe"
        assert serialized_product["collection"] == "products"
        assert serialized_product["allMeta"] == original_metadata

        # References field still works for citations (source_hash preserved)
        assert len(source_hash) == 1
        assert source_hash[1].sku == "SKU_E2E"
