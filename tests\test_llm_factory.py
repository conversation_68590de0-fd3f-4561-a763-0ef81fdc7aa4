import pytest
import os
from unittest.mock import patch, Mock

from src.lib.llm.factory import get_llm
from src.lib.llm.providers import LLMProvider
from src.lib.llm.openai import OpenAILLM
from src.lib.llm.mistral import MistralLLM
from src.lib.llm.anthropic import Anthropic<PERSON><PERSON>
from src.lib.llm.replicate import ReplicateLL<PERSON>
from src.lib.llm.yandex import YandexLLM
from src.lib.llm.ollama import <PERSON>llamaLLM
from src.lib.llm.together import TogetherLLM
from src.lib.llm.nebius import NebiusLLM

@patch.dict(os.environ, {'OPENAI_API_KEY': 'test_key'})
@patch('openai.OpenAI')
@patch('openai.AsyncOpenAI')
def test_get_llm_openai(mock_async_client, mock_client):
    mock_client.return_value = Mock()
    mock_async_client.return_value = Mock()
    llm = get_llm(LLMProvider.OPENAI)
    assert isinstance(llm, OpenAILLM)

@patch.dict(os.environ, {'MISTRAL_API_KEY': 'test_key'})
@patch('mistralai.Mistral')
def test_get_llm_mistral(mock_client):
    mock_client.return_value = Mock()
    llm = get_llm(LLMProvider.MISTRAL)
    assert isinstance(llm, MistralLLM)

@patch.dict(os.environ, {'ANTHROPIC_API_KEY': 'test_key'})
@patch('anthropic.Anthropic')
def test_get_llm_anthropic(mock_client):
    mock_client.return_value = Mock()
    llm = get_llm(LLMProvider.ANTHROPIC)
    assert isinstance(llm, AnthropicLLM)

@patch.dict(os.environ, {'REPLICATE_API_KEY': 'test_key'})
@patch('replicate.Client')
def test_get_llm_replicate(mock_client):
    mock_client.return_value = Mock()
    llm = get_llm(LLMProvider.REPLICATE)
    assert isinstance(llm, ReplicateLLM)

@patch.dict(os.environ, {
    'YANDEX_API_KEY': 'test_key',
    'YANDEX_FOLDER_ID': 'test_folder'
})
def test_get_llm_yandex():
    llm = get_llm(LLMProvider.YANDEX)
    assert isinstance(llm, YandexLLM)

@patch('ollama.Client')
def test_get_llm_ollama(mock_client):
    mock_client.return_value = Mock()
    llm = get_llm(LLMProvider.OLLAMA)
    assert isinstance(llm, OllamaLLM)

@patch.dict(os.environ, {'TOGETHERAI_API_KEY': 'test_token'})
@patch('together.api_key', Mock())
def test_get_llm_together():
    llm = get_llm(LLMProvider.TOGETHER)
    assert isinstance(llm, TogetherLLM)

@patch.dict(os.environ, {'NEBIUS_API_KEY': 'test_key'})
def test_get_llm_nebius():
    llm = get_llm(LLMProvider.NEBIUS)
    assert isinstance(llm, NebiusLLM)

@patch.dict(os.environ, {'OPENAI_API_KEY': 'test_key'})
@patch('openai.OpenAI')
@patch('openai.AsyncOpenAI')
def test_get_llm_from_str(mock_async_client, mock_client):
    mock_client.return_value = Mock()
    mock_async_client.return_value = Mock()
    llm = get_llm("openai")
    assert isinstance(llm, OpenAILLM)

@patch.dict(os.environ, {'OPENAI_API_KEY': 'test_key'})
@patch('openai.OpenAI')
@patch('openai.AsyncOpenAI')
def test_get_llm_case_insensitive(mock_async_client, mock_client):
    mock_client.return_value = Mock()
    mock_async_client.return_value = Mock()
    llm = get_llm("OPENAI")
    assert isinstance(llm, OpenAILLM)

def test_get_llm_invalid_provider():
    with pytest.raises(ValueError) as exc_info:
        get_llm("invalid_provider")
    assert "Unknown LLM provider: invalid_provider" in str(exc_info.value) 