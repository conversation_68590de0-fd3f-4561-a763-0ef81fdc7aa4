#!/usr/bin/env python3
"""
Configuration Validation Test Runner

Runs all configuration validation tests in a single command.
This is a comprehensive test suite for YAML configuration validation.
"""

import sys
import subprocess
from pathlib import Path


def run_validation_tests():
    """Run all configuration validation tests."""
    print("🔬 Running Complete Configuration Validation Test Suite")
    print("=" * 60)
    
    tests_passed = 0
    tests_failed = 0
    
    # Test 1: Basic YAML structure validation
    print("\n📋 1. YAML Structure Validation")
    print("-" * 30)
    try:
        result = subprocess.run([
            "python", "tests/configuration/validate_yaml_values.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PASSED - YAML structure validation")
            tests_passed += 1
            # Extract test count from output
            lines = result.stdout.split('\n')
            for line in lines:
                if "Passed:" in line and "Failed:" in line:
                    print(f"   {line}")
        else:
            print("❌ FAILED - YAML structure validation")
            print(result.stdout)
            tests_failed += 1
            
    except Exception as e:
        print(f"💥 ERROR running YAML validation: {e}")
        tests_failed += 1
    
    # Test 2: Prompt template validation
    print("\n📝 2. Prompt Template Validation")
    print("-" * 30)
    try:
        result = subprocess.run([
            "python", "tests/configuration/test_yaml_prompts.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PASSED - Prompt template validation")
            tests_passed += 1
            # Extract test count from output
            lines = result.stdout.split('\n')
            for line in lines:
                if "Passed:" in line and "Failed:" in line:
                    print(f"   {line}")
        else:
            print("❌ FAILED - Prompt template validation")
            print(result.stdout)
            tests_failed += 1
            
    except Exception as e:
        print(f"💥 ERROR running prompt validation: {e}")
        tests_failed += 1
    
    # Test 3: Configuration loader validation
    print("\n⚙️ 3. Configuration Loader Validation")
    print("-" * 30)
    try:
        result = subprocess.run([
            "python", "tests/configuration/validate_config.py"
        ], capture_output=True, text=True)
        
        if "🎉 All configurations are VALID!" in result.stdout:
            print("✅ PASSED - Configuration loader validation")
            tests_passed += 1
            print("   All client configurations validated successfully")
        else:
            print("❌ FAILED - Configuration loader validation")
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            tests_failed += 1
            
    except Exception as e:
        print(f"💥 ERROR running config validation: {e}")
        tests_failed += 1
    
    # Test 4: Comprehensive validation
    print("\n🧪 4. Comprehensive Validation")
    print("-" * 30)
    try:
        result = subprocess.run([
            "python", "tests/configuration/test_yaml_comprehensive.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and "PASSED" in result.stdout:
            print("✅ PASSED - Comprehensive validation suite")
            tests_passed += 1
            # Extract test count
            lines = result.stdout.split('\n')
            for line in lines:
                if "Passed:" in line and "Failed:" in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ FAILED - Comprehensive validation suite")
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            tests_failed += 1
            
    except Exception as e:
        print(f"💥 ERROR running comprehensive validation: {e}")
        tests_failed += 1
    
    # Test 5: Configuration inheritance validation
    print("\n🔗 5. Configuration Inheritance Validation")
    print("-" * 30)
    try:
        result = subprocess.run([
            "python", "tests/configuration/test_config_inheritance.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0 and "PASSED" in result.stdout:
            print("✅ PASSED - Configuration inheritance validation")
            tests_passed += 1
            # Extract test count
            lines = result.stdout.split('\n')
            for line in lines:
                if "Passed:" in line and "Failed:" in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ FAILED - Configuration inheritance validation")
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            tests_failed += 1
            
    except Exception as e:
        print(f"💥 ERROR running inheritance validation: {e}")
        tests_failed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 CONFIGURATION VALIDATION SUMMARY")
    print("=" * 60)
    print(f"✅ Test Suites Passed: {tests_passed}")
    print(f"❌ Test Suites Failed: {tests_failed}")
    print(f"📊 Total Test Suites: {tests_passed + tests_failed}")
    
    if tests_failed == 0:
        print("\n🎉 ALL CONFIGURATION TESTS PASSED!")
        print("✨ YAML configuration system is fully validated and ready for production.")
        return 0
    else:
        print(f"\n⚠️  {tests_failed} test suite(s) failed.")
        print("🔧 Please fix the issues above before deploying.")
        return 1


def show_test_summary():
    """Show a summary of available tests."""
    print("📚 Available Configuration Tests:")
    print("=" * 40)
    print("1. validate_yaml_values.py - Direct YAML validation (41 tests)")
    print("2. test_yaml_prompts.py - Prompt template tests (29 tests)")  
    print("3. validate_config.py - Config loader validation")
    print("4. test_yaml_comprehensive.py - Comprehensive validation (31 tests)")
    print("5. test_config_inheritance.py - Configuration inheritance tests (35 tests)")
    print("6. test_config_comparison.py - Compare YAML vs original (requires deps)")
    print("\nTotal: ~130+ individual validation tests")
    print("\n🚀 Usage:")
    print("   python run_configuration_tests.py")
    print("   python run_configuration_tests.py --help")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h", "help"]:
        show_test_summary()
        sys.exit(0)
    
    result_code = run_validation_tests()
    sys.exit(result_code)