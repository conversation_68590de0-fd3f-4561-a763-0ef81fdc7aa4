import os
import pytest
from unittest.mock import Mock, patch
from src.lib.llm.replicate import ReplicateLLM
from src.lib.llm.providers import LLMProvider

@pytest.fixture
def llm(tmp_path):
    """Fixture that provides a Replicate LLM instance with mocked client."""
    with patch.dict('os.environ', {'REPLICATE_API_KEY': 'test-key'}):
        llm = ReplicateLLM()
        llm._local_cache_dir = str(tmp_path / "cache")
        os.makedirs(llm._local_cache_dir, exist_ok=True)
        return llm

def test_provider(llm):
    """Test provider property."""
    assert llm.provider == LLMProvider.REPLICATE

def test_get_answer_success(llm):
    """Test successful answer generation."""
    test_response = "Test response"
    prompts = [{"role": "user", "content": "test prompt"}]
    
    # Mock Replicate client response
    with patch('replicate.Client.run', return_value=test_response) as mock_run:
        result = llm.get_answer("meta/llama-2-70b-chat", prompts, 100)
    
    assert result == test_response
    mock_run.assert_called_once()

@pytest.mark.skip(reason="Replicate does not support JSON mode")
def test_get_answer_with_json_mode(llm):
    """Test answer generation with JSON mode enabled."""
    pass

def test_get_answer_failure(llm):
    """Test error handling in answer generation."""
    prompts = [{"role": "user", "content": "test prompt"}]
    
    # Mock Replicate client to raise an exception
    with patch('replicate.run', side_effect=Exception("API error")):
        result = llm.get_answer("meta/llama-2-70b-chat", prompts, 100)
    
    assert result == ""

def test_missing_token():
    """Test error handling for missing API token."""
    with patch.dict('os.environ', clear=True):
        with pytest.raises(ValueError) as exc_info:
            ReplicateLLM()
        assert "REPLICATE_API_KEY environment variable is not set" in str(exc_info.value)

def test_get_streaming_answer(llm):
    """Test getting streaming answer from Replicate."""
    mock_stream = Mock()
    mock_stream.return_value = iter(["Test", " response"])
    llm.client.stream = mock_stream

    prompt_list = [{"role": "user", "content": "test message"}]
    chunks = []
    for chunk in llm.get_streaming_answer("meta/llama-3-70b-instruct", prompt_list, 100):
        chunks.append(chunk)

    mock_stream.assert_called_once_with(
        "meta/llama-3-70b-instruct",
        input={
            "prompt": "test message",
            "temperature": 0.0,
            "repetition_penalty": 1,
            "min_new_tokens": 100,
        },
    )
    assert chunks == ["Test", " response"]

def test_get_streaming_answer_with_system_prompt(llm):
    """Test getting streaming answer with system prompt from Replicate."""
    mock_stream = Mock()
    mock_stream.return_value = iter(["Test", " response"])
    llm.client.stream = mock_stream

    prompt_list = [
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "user", "content": "test message"}
    ]
    chunks = []
    for chunk in llm.get_streaming_answer("meta/llama-3-70b-instruct", prompt_list, 100):
        chunks.append(chunk)

    mock_stream.assert_called_once_with(
        "meta/llama-3-70b-instruct",
        input={
            "prompt": "test message",
            "system_prompt": "You are a helpful assistant",
            "temperature": 0.0,
            "repetition_penalty": 1,
            "min_new_tokens": 100,
        },
    )
    assert chunks == ["Test", " response"]

def test_get_streaming_answer_failure(llm):
    """Test error handling in streaming answer generation."""
    prompts = [{"role": "user", "content": "test message"}]
    
    # Mock the streaming response to raise an exception
    mock_stream = Mock(side_effect=Exception("API error"))
    
    # Create a mock client
    mock_client = Mock()
    mock_client.stream = mock_stream
    
    # Replace the client instance
    with patch.object(llm, 'client', mock_client):
        chunks = []
        for chunk in llm.get_streaming_answer("meta/llama-3-70b-instruct", prompts, 100):
            chunks.append(chunk)
    
    assert chunks == [""]  # Should yield empty string on error
    mock_stream.assert_called_once()

def test_get_streaming_answer_empty_response(llm):
    """Test handling of empty streaming response."""
    test_chunks = []  # Empty response
    prompts = [{"role": "user", "content": "test message"}]
    
    # Mock the streaming response
    mock_stream = Mock()
    mock_stream.return_value = test_chunks
    
    # Create a mock client
    mock_client = Mock()
    mock_client.stream = mock_stream
    
    # Replace the client instance
    with patch.object(llm, 'client', mock_client):
        chunks = []
        for chunk in llm.get_streaming_answer("meta/llama-3-70b-instruct", prompts, 100):
            chunks.append(chunk)
    
    assert chunks == []  # Should yield no chunks
    mock_stream.assert_called_once()
