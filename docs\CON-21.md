# CON-21: Add-to-Cart Functionality

## Requirements

Request flow - `/docs/examples/add-to-cart.jpg` (do not pay attention to Merchant Proxy, for us it is just a caller of RQP API)

Two main scenarios to support:

1. **Support adding to cart from the UI**

- add new endpoints: `POST "/notify/cart/add"`, `POST "/notify/cart/remove"`, which will take visitor_id, session_id, SKU and quantity (optional).
Required headers: `X-Customer-Id: <customer_id>` and `Authorization: client-key <key>`

Body:

```json
{
  "visitor_id": "1234",
  "session_id": "5678",
  "products": [
    {
      "sku": "354666-36-M",
      "quantity": 1
    },
    {
      "sku": "354666-36-S",
      "quantity": 1
    }
  ]
}
```

- for now we skip any validation for headers (will be implemented as a separate task). 
- add a new handler in the searcher service that inherits from `BaseAPIHandler` and handle the request.
- we call `/api/v1/merchant/{merchant_id}/cart/add` on Brain Checkout (in fire and forget mode, the same we do for conversational add to cart below)
API details: `/docs/examples/brain-checkout-api.yml`. merchant_id will be retrieved from the "X-Customer-Id" request header. `variants` field won't be populated, we will use sku of any product (variant or primary) and set as `product_id` for Brain Checkout API.
we will need to add two env variables on RQP side:
  - BRAIN_CHECKOUT_API_KEY
  - BRAIN_CHECKOUT_API_URL

- we accept the UI request and return `201 Created`, `400 Bad Request` if the request body is invalid, `500 Internal Server Error` if there was an error processing the request.


2. **Supporting adding to cart from the conversation**

In conversational flow when Agent decides to add to cart based on user intent, we will:

- call /api/v1/merchant/{merchant_id}/cart/add on Brain Checkout (in fire and forget mode), the same as above.

- add to our response actions for request sender: 

```json
"actions": [
  {
    "type": "ADD_TO_CART",
    "payload": {
      "sku": "354666-36-M",
      "quantity": 1
    }
  },
  {
    "type": "REMOVE_FROM_CART",
    "payload": {
      "sku": "354666-36-S",
      "quantity": 1
    }
  }
]
```

## Implementation Details

Check if the current definitions of the agent tool calling in the `configuration/main/default.py` and agent loop in the `quepasa/searcher/sources/factory.py` need to be adjusted to support the new functionality for `add_to_cart` function calling:

- we need product SKUs for both scenarios
- call Brain Checkout API add-to-cart endpoint with `user_id`, `session_id`, `merchant_id` (customer_id from the request header) and `products` for both scenarios
- add UI `actions` to the response for conversational add to cart scenario
