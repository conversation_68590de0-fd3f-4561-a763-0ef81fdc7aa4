"""Unit tests for data model updates - Story 1.1 (Enhanced QA)"""

import json

from quepasa.searcher.models.document import QuepasaDocument


def _create_base_document(**overrides):
    """Helper to create QuepasaDocument with common defaults"""
    defaults = {
        'root_id': 'root123',
        'id': 'doc123',
        'chunk_index': 0,
        'client': 'test_client',
        'domain': 'test_domain',
        'provider': 'test_provider',
        'type': 'document',
        'kind': 'test',
        'level': '1',
        'url': 'http://example.com',
        'language': 'en',
        'title': 'Test Document',
        'keywords': ['test'],
        'text': 'Test text',
        'tokens': 10,
        'chunks': 1,
        'start_position': 0,
        'end_position': 100,
        'created_at': None,
        'updated_at': None,
        'embeddings': None,
        'score': None
    }
    defaults.update(overrides)
    return QuepasaDocument(**defaults)


class TestQuepasaDocument:
    """Behavioral tests for QuepasaDocument model"""

    def test_document_creation_with_product_metadata(self):
        """Test document creation with product-specific fields"""
        metadata_dict = {"brand": "Nike", "color": "red", "size": "M"}
        metadata_json = json.dumps(metadata_dict)

        doc = _create_base_document(
            id="product456",
            client="ecommerce",
            domain="products",
            provider="catalog",
            type="product",
            kind="apparel",
            url="http://shop.com/product/456",
            title="Nike Red T-Shirt",
            sku="NIKE-RED-M-001",
            metadata=metadata_json,
            keywords=["nike", "red", "tshirt"],
            text="Comfortable red Nike t-shirt in medium size",
            tokens=15,
            end_position=150
        )

        assert doc.sku == "NIKE-RED-M-001"
        assert doc.metadata == metadata_json
        assert doc.type == "product"

        # Verify metadata can be parsed back to dict
        parsed_metadata = json.loads(doc.metadata)
        assert parsed_metadata["brand"] == "Nike"
        assert parsed_metadata["color"] == "red"

    def test_document_to_dict_serialization_with_metadata(self):
        """Test to_dict includes metadata when present"""
        doc = _create_base_document(
            id="test456",
            type="product",
            title="Test Product",
            sku="TEST-456",
            metadata='{"category": "electronics"}',
            tokens=5,
            end_position=50
        )

        result_dict = doc.to_dict()

        # Verify new fields are included
        assert "sku" in result_dict
        assert result_dict["sku"] == "TEST-456"
        assert "metadata" in result_dict
        assert result_dict["metadata"] == '{"category": "electronics"}'

        # Verify serialization integrity
        assert result_dict["type"] == "product"
        assert result_dict["id"] == "test456"

    def test_document_pipeline_workflow(self):
        """Test complete document creation to serialization workflow"""
        # Simulate document creation from raw data (like from ingestion)
        raw_product_data = {
            "id": "integration_test_001",
            "url": "http://store.com/product/001",
            "title": "Integration Test Product",
            "sku": "INT-001",
            "metadata": json.dumps({
                "brand": "TestBrand",
                "category": "electronics",
                "price": 99.99,
                "in_stock": True
            }),
            "type": "product"
        }

        # Create QuepasaDocument (as would happen in searcher)
        doc = _create_base_document(
            root_id="integration_root",
            id=raw_product_data["id"],
            client="integration_client",
            domain="products",
            provider="catalog",
            type=raw_product_data["type"],
            kind="electronics",
            url=raw_product_data["url"],
            title=raw_product_data["title"],
            sku=raw_product_data["sku"],
            metadata=raw_product_data["metadata"],
            keywords=["integration", "test"],
            text="Integration test product description",
            tokens=20,
            end_position=200
        )

        # Test serialization (as would happen for API response)
        serialized = doc.to_dict()

        assert serialized["sku"] == "INT-001"
        assert "metadata" in serialized

        # Test metadata can be parsed back
        metadata = json.loads(serialized["metadata"])
        assert metadata["brand"] == "TestBrand"
        assert metadata["price"] == 99.99

    def test_document_get_method_works_with_new_fields(self):
        """Test that the get() method works with new fields"""
        doc = _create_base_document(
            id="get_doc",
            title="Get Test",
            sku="GET-SKU",
            metadata='{"get": "test"}'
        )

        # Test get method with new fields
        assert doc.get('sku') == "GET-SKU"
        assert doc.get('metadata') == '{"get": "test"}'
        assert doc.get('sku', 'default') == "GET-SKU"
        assert doc.get('nonexistent', 'default') == 'default'

        # Test with None values
        doc_none = _create_base_document(
            id="get_doc",
            type="document",
            title="Get Test"
        )

        assert doc_none.get('sku') is None
        assert doc_none.get('metadata') is None
        assert doc_none.get('sku', 'fallback') is None  # None is returned, not fallback
