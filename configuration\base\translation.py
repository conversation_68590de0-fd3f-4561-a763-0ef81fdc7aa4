from typing import Dict, Any, Optional, List, Union 
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from quepasa.searcher.models.request import QuepasaRequest
from src.lib.llm_utils import get_cached_llm_answer
from src.lib.utils import get_think_and_answer_from_content
from src.lib.llm.providers import LLMProvider
from .localization import LocalizationConfig

@dataclass
class TranslationHash:
    """Hash object for translated questions."""
    translations: Dict[str, Dict[str, str]]  # type -> language -> translation

class TranslationConfig(LocalizationConfig):
    """Base configuration for translation functionality."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def get_language_options(self) -> str:
        """Get formatted list of available languages."""
        languages = self.get_language_mapping()
        return "\n".join([f"— {value}, language_code = {key}" for key, value in languages.items()])
    
    def get_translation_model_name(self) -> str:
        """Get model name for translation.
        
        Returns:
            Tuple of provider and model name
        """
        return LLMProvider.NEBIUS, "google/gemma-2-9b-it-fast"
    
    def get_translation(self, text: str, source_lang: str, target_lang: str) -> str:
        """Get translated text.
        
        Previously: get_translation()
        
        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code
            data: Request data
            
        Returns:
            Translated text
        """
        # Get base translation from parent
        languages = self.get_language_mapping()
        from_lang_name = languages[source_lang] if source_lang in languages else f'"{source_lang}"'
        to_lang_name = languages[target_lang] if target_lang in languages else f'"{target_lang}"'
        
        # Add custom translation logic here
        # For example, using a translation service
        translation_provider, translation_model_name = self.get_translation_model_name()
        _, translated_text = get_think_and_answer_from_content(get_cached_llm_answer(
            translation_provider,
            translation_model_name,
            [{
                'role': 'user',
                'content': f"""
Translate text from {from_lang_name} to {to_lang_name}.
Text:
'''
{text}
'''
                """.strip()
            }],
            None
        ))
        return translated_text

    def get_translation_object(self, text: str, source_lang: str, target_lang: str) -> Dict[str, str]:
        """Get translation object with metadata.
        
        Previously: get_translation_object()
        
        Args:
            text: Text to translate
            source_lang: Source language code
            target_lang: Target language code
            data: Request data
            
        Returns:
            Dict mapping target language to translated text
        """
        translated_text = text if source_lang == target_lang else self.get_translation(text, source_lang, target_lang)
        return {target_lang: translated_text}
        
    def get_translated_question_hash(self, question: str, language_code: str, types: List[str] = None) -> TranslationHash:
        """Get hash object for translated question.
        
        Previously: get_translated_question_hash()
        
        Args:
            question: Question text to translate and hash
            language_code: Source language code
            data: Request data
            types: List of document types to translate for (default: all types)
            
        Returns:
            TranslationHash object with translations for each type and language
        """
        # Get language configuration
        document_language_config = self.get_indexed_languages()
        
        # If no types specified, use all available types
        if not types:
            types = list(document_language_config.keys())
            
        # Get unique languages across all specified types
        unique_langs = []
        for doc_type in types:
            for lang in document_language_config[doc_type]:
                if lang not in unique_langs:
                    unique_langs.append(lang)
                    
        # Translate to all required languages
        translation_hash = {}
        
        # Use parallel processing if many languages
        if len(unique_langs) >= 4:
            with ThreadPoolExecutor(max_workers=2) as executor:
                # Start translation tasks
                future_to_translation = {
                    executor.submit(
                        self.get_translation_object,
                        question,
                        language_code,
                        lang
                    ): lang for lang in unique_langs
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_translation):
                    try:
                        translation = future.result()
                        for lang, text in translation.items():
                            translation_hash[lang] = text

                    except Exception:
                        # Handle translation errors gracefully
                        lang = future_to_translation[future]
                        translation_hash[lang] = question
        else:
            # Sequential processing for fewer languages
            for lang in unique_langs:
                if lang == language_code:
                    translation_hash[lang] = question

                else:
                    translation = self.get_translation(question, language_code, lang)
                    translation_hash[lang] = translation
                    
        # Build final result structure
        result_hash = {}
        for doc_type in types:
            result_hash[doc_type] = {}
            for lang in document_language_config[doc_type]:
                result_hash[doc_type][lang] = translation_hash.get(lang, question)
                
        return result_hash