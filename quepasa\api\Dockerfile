# Build stage
FROM python:3.9-slim AS builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    DOCKER_BUILDKIT=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    protobuf-compiler \
    libprotobuf-dev \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Upgrade pip and install build tools
RUN pip install --upgrade pip setuptools wheel

# Copy only necessary files first
COPY requirements.txt .
COPY quepasa/api/requirements.txt quepasa/api/
COPY quepasa/crawler/requirements.txt quepasa/crawler/
COPY setup.py .

# Install dependencies with caching disabled
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r quepasa/api/requirements.txt && \
    pip install --no-cache-dir -r quepasa/crawler/requirements.txt

# Install PyArmor 7.x specifically for compatibility with obfuscate command
RUN pip install --no-cache-dir pyarmor==7.7.4

# Copy application code after dependencies
COPY configuration/ configuration/
COPY quepasa/auth/ quepasa/auth/
COPY quepasa/api/ quepasa/api/
COPY quepasa/crawler/ quepasa/crawler/
COPY quepasa/searcher/models/ quepasa/searcher/models/
COPY quepasa/searcher/*.py quepasa/searcher/
COPY quepasa/*.py quepasa/
COPY src/ src/

# Obfuscate the Python code
RUN mkdir -p /app/obfuscated/quepasa && \
    # Obfuscate the API module
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/api quepasa/api/*.py && \
    # Obfuscate the Crawler module
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/crawler quepasa/crawler/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/crawler/processors quepasa/crawler/processors/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/crawler/processors/telegram quepasa/crawler/processors/telegram/*.py && \
    # Obfuscate other required modules
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/auth quepasa/auth/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher/models quepasa/searcher/models/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher quepasa/searcher/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa quepasa/*.py && \
    # Handle src directory
    mkdir -p /app/obfuscated/src && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/embedding src/lib/embedding/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/llm src/lib/llm/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/whisper src/lib/whisper/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/reranker src/lib/reranker/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib src/lib/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src src/*.py && \
    # Verify files were created (for debugging)
    echo "Checking obfuscated directories:" && \
    ls -la /app/obfuscated/quepasa && \
    ls -la /app/obfuscated/quepasa/api && \
    ls -la /app/obfuscated/quepasa/crawler || true

FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install only runtime dependencies with caching
RUN apt-get update && apt-get install -y --no-install-recommends \
    libprotobuf32 \
    busybox \
    && mkdir -p cache \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && pip cache purge

# Copy only necessary files from builder
COPY --from=builder /usr/local/lib/python3.9/site-packages/ /usr/local/lib/python3.9/site-packages/
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# Copy obfuscated files instead of the original source
COPY --from=builder /app/obfuscated/quepasa /app/quepasa 
COPY --from=builder /app/obfuscated/src /app/src

# IMPORTANT: Copy PyArmor runtime files from each obfuscated directory
COPY --from=builder /app/obfuscated/quepasa/pytransform /app/pytransform
COPY --from=builder /app/obfuscated/quepasa/api/pytransform /app/quepasa/api/pytransform
COPY --from=builder /app/obfuscated/quepasa/crawler/pytransform /app/quepasa/crawler/pytransform
COPY --from=builder /app/obfuscated/quepasa/auth/pytransform /app/quepasa/auth/pytransform
COPY --from=builder /app/obfuscated/quepasa/searcher/pytransform /app/quepasa/searcher/pytransform

COPY --from=builder /app/configuration /app/configuration

# Expose port for REST API
EXPOSE 8080

# Set environment variables
ENV PYTHONPATH=/app \
    PATH="/usr/local/bin:$PATH"

# Set working directory
WORKDIR /app

# Run the application with worker settings
CMD ["uvicorn", "quepasa.api.app:app", "--host", "0.0.0.0", "--port", "8080"]