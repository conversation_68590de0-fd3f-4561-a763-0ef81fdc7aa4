import os
import json
import zlib
import time
import boto3
import base64
from botocore.client import Config
from datetime import datetime, timezone
from typing import Optional, Dict, Any, BinaryIO, Union, List, Tuple, Callable
import traceback
import asyncio

def log_timing(func):
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"Operation {func.__name__} took {end - start:.2f} seconds")
        return result
    return wrapper

default_content_settings = {
    "content_type": "plain/text",
    "content_encoding": "utf-8",
    "content_language": "en-US",
    "content_disposition": "inline",
    "content_md5": None,
    "cache_control": "max-age=0",
}

UPDATED_AT_KEY = 'updated_at'

class QuepasaFiles:
    # Default configuration
    DEFAULT_BUCKET_NAME = 'quepasa-files'
    DEFAULT_HOST = 'minio'
    DEFAULT_PORT = '9000'
    DEFAULT_ACCESS_KEY = 'minioadmin'
    DEFAULT_SECRET_KEY = 'minioadmin'
    DEFAULT_DEBUG = False

    def __init__(
        self,
        bucket_name=None,
        endpoint_url=None,
        aws_access_key_id=None,
        aws_secret_access_key=None,
        debug_flag=None
    ):
        """Initialize QuepasaFiles with MinIO configuration.
        All parameters can be overridden by environment variables:
        - MINIO_BUCKET_NAME
        - MINIO_HOST
        - MINIO_PORT
        - MINIO_ACCESS_KEY
        - MINIO_SECRET_KEY
        - MINIO_DEBUG
        """
        # Get configuration from environment or use defaults
        # Get configuration from environment or use defaults
        self.bucket_name = bucket_name if bucket_name else os.getenv('MINIO_BUCKET_NAME', self.DEFAULT_BUCKET_NAME)
        
        minio_host = os.getenv('MINIO_HOST', self.DEFAULT_HOST)
        minio_port = os.getenv('MINIO_PORT', self.DEFAULT_PORT)
        if not endpoint_url:
            endpoint_url = f"http://{minio_host}:{minio_port}"
        if not endpoint_url.startswith(('http://', 'https://')):
            endpoint_url = f"http://{endpoint_url}"
        self.endpoint_url = endpoint_url

        self.aws_access_key_id = aws_access_key_id if aws_access_key_id else os.getenv('MINIO_ACCESS_KEY', self.DEFAULT_ACCESS_KEY) 
        self.aws_secret_access_key = aws_secret_access_key if aws_secret_access_key else os.getenv('MINIO_SECRET_KEY', self.DEFAULT_SECRET_KEY)

        self.debug_flag = debug_flag if debug_flag else (os.getenv('MINIO_DEBUG', str(self.DEFAULT_DEBUG)).lower() == 'true')

        if self.debug_flag:
            print(f"[DEBUG] Initializing MinIO client with:")
            print(f"[DEBUG] - endpoint_url: {self.endpoint_url}")
            print(f"[DEBUG] - bucket_name: {self.bucket_name}")
            print(f"[DEBUG] - access_key: {self.aws_access_key_id}")
            print(f"[DEBUG] - secret_key: {'*' * len(self.aws_secret_access_key)}")

        try:
            # Initialize S3 client with retries
            config = Config(
                signature_version='s3v4',
                retries=dict(
                    max_attempts=5,
                    mode='adaptive'
                ),
                connect_timeout=5,
                read_timeout=10
            )
            
            self.s3_client = boto3.client(
                's3',
                endpoint_url=self.endpoint_url,
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
                config=config,
                region_name='us-east-1'
            )

            if self.debug_flag:
                # Test connection by listing buckets
                response = self.s3_client.list_buckets()
                print(f"[DEBUG] Successfully connected to MinIO. Found buckets: {[b['Name'] for b in response['Buckets']]}")

        except Exception as e:
            print(f"[ERROR] Failed to initialize MinIO client: {str(e)}")
            raise

    def _get_full_path(self, key: str) -> str:
        """Get full S3 path."""
        return key

    def create_local_dirs_for_path(self, file_fullname: str, mode: int = 0o766) -> None:
        """Create local directories for path."""
        pathes = []
        for path in file_fullname.split('/')[0:-1]:
            pathes.append(path)
            dir = "/".join(pathes)
            if dir != '' and not os.path.isdir(dir):
                if self.debug_flag:
                    print(f"Directory '{dir}' created on local drive.")
                os.makedirs(dir)
                os.chmod(dir, mode)

    def _get_folder_meta_path(self, folder_path: str) -> str:
        """Get the path to the folder's metadata file."""
        # Ensure the path ends with a slash
        if not folder_path.endswith('/'):
            folder_path = folder_path + '/'
        return folder_path + '.meta.json'

    def _set_folder_metadata(self, folder_path: str, metadata: Dict[str, Any]) -> None:
        """Set metadata for a folder by storing it in a .meta.json file."""
        # First check if the metadata file already exists and has the same timestamp
        try:
            existing_metadata = self._get_folder_metadata(folder_path)
            if existing_metadata.get('updated_at') == metadata.get('updated_at'):
                return  # Skip if metadata hasn't changed
        except:
            pass

        meta_path = self._get_folder_meta_path(folder_path)
        json_str = json.dumps(metadata, ensure_ascii=False)
        content = json_str.encode('utf-8')
        content_settings = {
            'content_type': 'application/json',
            'content_encoding': 'utf-8'
        }

        # Use set_data directly with create_dirs=False to avoid recursion
        full_path = self._get_full_path(meta_path)
        if self.debug_flag:
            print(f"Setting folder metadata at: {full_path}")

        extra_args = {
            'ContentType': content_settings['content_type'],
            'ContentEncoding': content_settings['content_encoding']
        }
        self.s3_client.put_object(Bucket=self.bucket_name, Key=meta_path, Body=content, **extra_args)

    def _get_folder_metadata(self, folder_path: str) -> Dict[str, Any]:
        """Get metadata for a folder from its .meta.json file."""
        meta_path = self._get_folder_meta_path(folder_path)
        if self.debug_flag:
            print(f"Looking for metadata at: {meta_path}")
        try:
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=meta_path)
            content = response['Body'].read()
            if self.debug_flag:
                print(f"Found metadata file with content: {content}")
            return json.loads(content.decode('utf-8'))
        except Exception as e:
            if self.debug_flag:
                print(f"Error reading metadata file: {e}")
            return {}

    @log_timing
    def create_remote_dirs_for_path(self, file_fullname: str) -> None:
        """Create remote directories for path."""
        dir_key = os.path.dirname(file_fullname)
        if dir_key:
            # Create all parent directories
            parts = dir_key.split('/')
            current_path = ''
            for part in parts:
                current_path = f"{current_path}/{part}" if current_path else part
                # Create directory marker with trailing slash
                dir_path = current_path + '/'
                
                # Check if directory already exists
                try:
                    self.s3_client.head_object(Bucket=self.bucket_name, Key=dir_path)
                    continue  # Skip if directory already exists
                except:
                    pass
                
                # Create directory marker
                try:
                    if self.debug_flag:
                        print(f"Creating directory marker: {dir_path}")
                    
                    # Create an empty file with a trailing slash to mark it as a directory
                    self.s3_client.put_object(Bucket=self.bucket_name, Key=dir_path, Body=b'')
                    
                    if self.debug_flag:
                        print(f"Directory '{current_path}' created in '{self.bucket_name}'.")

                except Exception as e:
                    if self.debug_flag:
                        print(f"Error creating directory marker: {e}")
                
                # Update metadata for the directory
                metadata = {'updated_at': datetime.now(timezone.utc).isoformat()}
                if self.debug_flag:
                    print(f"Setting metadata for directory: {current_path}")

                self._set_folder_metadata(current_path, metadata)

    @log_timing
    def set_data(self, file_fullname: str, file_data: Union[bytes, str, BinaryIO], content_settings: Optional[Dict[str, str]] = None, metadata: Optional[Dict[str, str]] = None, create_dirs: bool = True) -> None:
        """Write data to a file."""
        if create_dirs:
            self.create_remote_dirs_for_path(file_fullname)

        # Convert string data to bytes
        if isinstance(file_data, str):
            file_data = file_data.encode('utf-8')

        # Prepare extra arguments for put_object
        extra_args = {}
        if content_settings:
            if 'content_type' in content_settings:
                extra_args['ContentType'] = content_settings['content_type']
            if 'content_encoding' in content_settings:
                extra_args['ContentEncoding'] = content_settings['content_encoding']
            if 'content_language' in content_settings:
                extra_args['ContentLanguage'] = content_settings['content_language']
            if 'content_disposition' in content_settings:
                extra_args['ContentDisposition'] = content_settings['content_disposition']
            if 'cache_control' in content_settings:
                extra_args['CacheControl'] = content_settings['cache_control']
            if 'content_md5' in content_settings:
                # Store MD5 in both ContentMD5 and content_settings
                if not metadata:
                    metadata = {}
                metadata['content_md5'] = base64.b64encode(content_settings['content_md5']).decode()

        if metadata:
            extra_args['Metadata'] = metadata

        # Write the file
        full_path = self._get_full_path(file_fullname)
        if self.debug_flag:
            print(f"Writing file to: {full_path}")
        start = time.time()
        self.s3_client.put_object(Bucket=self.bucket_name, Key=full_path, Body=file_data, **extra_args)
        end = time.time()
        print(f"put_object operation took {end - start:.2f} seconds")

    def get_data(self, file_fullname: str) -> bytes:
        """Get file contents as bytes."""
        response = self.s3_client.get_object(Bucket=self.bucket_name, Key=file_fullname)
        return response['Body'].read()

    def exists(self, file_fullname: str) -> bool:
        """Check if a file exists."""
        try:
            self.s3_client.head_object(Bucket=self.bucket_name, Key=file_fullname)
            return True
        except Exception as e:
            if hasattr(e, 'response') and e.response['Error']['Code'] == '404':
                return False
            raise

    def delete_file(self, file_fullname: str) -> None:
        """Delete a file."""
        if self.exists(file_fullname):
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=file_fullname)
            if self.debug_flag:
                print(f"File '{file_fullname}' deleted from '{self.bucket_name}'.")

    def get_properties(self, file_fullname: str) -> Dict[str, Any]:
        """Get file or folder properties."""
        full_path = self._get_full_path(file_fullname)
        info = self.s3_client.head_object(Bucket=self.bucket_name, Key=full_path)
        
        # For folders, merge with metadata from .meta.json
        content_md5 = None
        if file_fullname.endswith('/'):
            metadata = self._get_folder_metadata(file_fullname)
            # Get last_modified from the metadata file
            meta_path = self._get_full_path(self._get_folder_meta_path(file_fullname))
            try:
                meta_info = self.s3_client.head_object(Bucket=self.bucket_name, Key=meta_path)
                last_modified = meta_info['LastModified']
            except:
                # If metadata file doesn't exist, use updated_at from metadata
                last_modified = datetime.fromisoformat(metadata['updated_at']) if 'updated_at' in metadata else None
        else:
            metadata = info.get('Metadata', {})
            last_modified = info.get('LastModified')
            if 'content_md5' in metadata:
                content_md5 = base64.b64decode(metadata['content_md5'])
        
        return {
            'path': file_fullname,
            'content_type': info.get('ContentType'),
            'content_length': info.get('ContentLength'),
            'last_modified': last_modified,
            'metadata': metadata,
            'e_tag': info.get('ETag'),
            'content_md5': content_md5
        }

    def get_properties_safe(self, file_fullname: str) -> Optional[Dict[str, Any]]:
        """Get file properties without raising exceptions."""
        try:
            return self.get_properties(file_fullname)
        except:
            return None

    def get_text(self, file_fullname: str) -> str:
        """Get file contents as text."""
        data = self.get_data(file_fullname)
        return data.decode('utf-8')

    def set_text(self, file_fullname: str, text_data: str, content_md5: str = None) -> None:
        """Write text to a file."""
        file_name = file_fullname.split('/')[-1]
        file_data = text_data.encode("utf-8")

        content_settings = {
            "content_type": "text/plain",
            "content_encoding": "utf-8",
            "content_language": "en-US",
            "content_disposition": f"attachment; filename={file_name}",
            "cache_control": "max-age=0",
        }

        if content_md5:
            content_settings['content_md5'] = content_md5

        self.set_data(file_fullname, file_data, content_settings)

    def set_json(self, file_fullname: str, json_data: Union[Dict, List], content_md5: str = None) -> None:
        """Write JSON data to a file."""
        file_name = file_fullname.split('/')[-1]
        file_data = json.dumps(json_data).encode("utf-8")

        content_settings = {
            "content_type": "application/json",
            "content_encoding": "utf-8",
            "content_language": "en-US",
            "content_disposition": f"attachment; filename={file_name}",
            "cache_control": "max-age=0",
        }

        if content_md5:
            content_settings['content_md5'] = content_md5

        self.set_data(file_fullname, file_data, content_settings)

    def get_json(self, file_fullname: str) -> Dict[str, Any]:
        """Get file contents as JSON."""
        text = self.get_text(file_fullname)
        return json.loads(text)

    def set_json_zlib(self, file_fullname: str, data: Dict[str, Any], content_md5: str = None) -> None:
        """Write compressed JSON data."""
        json_str = json.dumps(data, ensure_ascii=False)
        compressed = zlib.compress(json_str.encode('utf-8'))
        content_settings = {
            'content_type': 'application/json',
            'content_encoding': 'zlib'
        }
        
        if content_md5:
            content_settings['content_md5'] = content_md5

        self.set_data(file_fullname, compressed, content_settings)

    def get_json_zlib(self, file_fullname: str) -> Dict[str, Any]:
        """Get compressed JSON file contents."""
        data = self.get_data(file_fullname)
        decompressed = zlib.decompress(data)
        return json.loads(decompressed.decode('utf-8'))

    def get_destination_file(self, from_file: str, to_file: str) -> str:
        """Get the destination file path."""
        file_name = to_file.split('/')[-1]
        file_fullname = to_file.rstrip('/')
        if file_name == '' or '.' not in file_name:
            file_fullname += "/" + from_file.split('/')[-1]
        return file_fullname

    def download_file(self, from_file: str, to_file: str, skip_existing_flag: bool = False) -> None:
        """Download a file from S3 to local filesystem."""
        file_fullname = self.get_destination_file(from_file, to_file)
        if skip_existing_flag and os.path.exists(file_fullname):
            return

        self.create_local_dirs_for_path(file_fullname)

        if self.debug_flag:
            print(f"Downloading to: {file_fullname}")

        self.s3_client.download_file(self.bucket_name, from_file, file_fullname)

        if self.debug_flag:
            print(f"Downloaded to: {file_fullname}")

    def upload_file(self, from_file: str, to_file: str) -> None:
        """Upload a file from local filesystem to S3."""
        file_fullname = self.get_destination_file(from_file, to_file)
        self.create_remote_dirs_for_path(file_fullname)

        if self.debug_flag:
            print(f"Uploading to: {file_fullname}")

        self.s3_client.upload_file(from_file, self.bucket_name, file_fullname)

        if self.debug_flag:
            print(f"Uploaded to: {file_fullname}")

    def get_files(self, prefix: str = '', recursive: bool = True) -> List[str]:
        """List files in a directory."""
        full_path = self._get_full_path(prefix)
        if not full_path.endswith('/'):
            full_path += '/'
        
        files = []
        paginator = self.s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=self.bucket_name, Prefix=full_path):
            if 'Contents' not in page:
                continue
            
            for obj in page['Contents']:
                key = obj['Key']

                # Skip directory markers and metadata files
                if not key.endswith('/') and not key.endswith('/.meta.json'):
                    if recursive or '/' not in key[len(full_path):]:
                        files.append(key)
        
        return files

    def get_files_with_properties(self, prefix: str, 
                                filter_callback: Optional[Union[Callable, Dict[str, Callable]]] = None,
                                recursive_flag: bool = False) -> List[Dict[str, Any]]:
        """List files in a directory with their properties."""
        full_path = self._get_full_path(prefix)
        if not full_path.endswith('/'):
            full_path += '/'

        results = []
        paginator = self.s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=self.bucket_name, Prefix=full_path):
            if 'Contents' not in page:
                continue
                
            for path in page['Contents']:
                # Convert full path back to relative path
                rel_path = path['Key'].replace(f"{self.bucket_name}/", "", 1)
                
                if not recursive_flag and '/' in rel_path[len(full_path):].strip('/'):
                    continue

                is_dir = rel_path.endswith('/')
                props = self.get_properties(rel_path + '/.meta.json' if is_dir else rel_path)

                # Apply filters
                if filter_callback:
                    if callable(filter_callback):
                        if not filter_callback(props):
                            continue
                    elif isinstance(filter_callback, dict):
                        if is_dir and 'dir' in filter_callback and filter_callback['dir']:
                            if not filter_callback['dir'](props):
                                continue
                        elif not is_dir and 'file' in filter_callback and filter_callback['file']:
                            if not filter_callback['file'](props):
                                continue

                results.append(props)

        return results

    def upload_file(self, local_path: str, remote_path: str) -> None:
        """Upload a local file to S3."""
        with open(local_path, 'rb') as f:
            self.set_data(remote_path, f)

    def download_file(self, remote_path: str, local_path: str) -> None:
        """Download a file from S3 to local path."""
        data = self.get_data(remote_path)
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        with open(local_path, 'wb') as f:
            f.write(data)

    async def upload_stream(self, stream: BinaryIO, remote_path: str) -> None:
        """Upload data from a stream."""
        try:
            if self.debug_flag:
                print(f"[DEBUG] Starting stream upload to {remote_path}")
            
            # Read the entire stream into memory
            content = stream.read()
            
            if self.debug_flag:
                print(f"[DEBUG] Read {len(content)} bytes from stream")
            
            # Set default content settings
            content_settings = {
                "content_type": "application/octet-stream",
                "content_encoding": "utf-8",
                "content_language": "en-US",
                "content_disposition": f"attachment; filename={os.path.basename(remote_path)}",
                "cache_control": "max-age=0",
            }
            
            # Upload the content
            if self.debug_flag:
                print(f"[DEBUG] Uploading content to MinIO at {remote_path}")
            
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.set_data(remote_path, content, content_settings)
            )
            
            if self.debug_flag:
                print(f"[DEBUG] Stream upload completed successfully")
                
        except Exception as e:
            error_msg = f"Stream upload failed: {str(e)}"
            if self.debug_flag:
                print(f"[ERROR] {error_msg}")
                print(f"[ERROR] Exception type: {type(e)}")
                print(f"[ERROR] Exception traceback: {traceback.format_exc()}")
            raise Exception(error_msg) from e

    def get_files(self, prefix: str = '', recursive: bool = True) -> List[str]:
        """List files in a directory."""
        if not prefix.endswith('/'):
            prefix += '/'
        
        paginator = self.s3_client.get_paginator('list_objects_v2')
        files = []
        
        for page in paginator.paginate(Bucket=self.bucket_name, Prefix=prefix):
            if 'Contents' in page:
                for obj in page['Contents']:
                    key = obj['Key']
                    # Skip directory markers and metadata files
                    if not key.endswith('/') and not key.endswith('/.meta.json'):
                        if recursive or '/' not in key[len(prefix):]:
                            files.append(key)
        
        return files

    def get_files_with_properties(self, prefix: str = '') -> List[Dict[str, Any]]:
        """List files with their properties."""
        files = self.get_files(prefix)
        result = []
        
        for file in files:
            props = self.get_properties(file)
            result.append({
                'path': file,
                'size': props['content_length'],
                'last_modified': props['last_modified'],
                'metadata': props['metadata']
            })
        
        return result

    def get_public_url(self, file_fullname: str, hours: int = 24) -> str:
        """Generate a pre-signed URL for accessing a file.
        
        Args:
            file_fullname: The path to the file in the bucket
            hours: Number of hours until the URL expires (default: 24)
            
        Returns:
            A pre-signed URL that can be used to access the file for the specified duration
        """
        try:
            # Generate the pre-signed URL
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': file_fullname
                },
                ExpiresIn=hours * 3600  # Convert hours to seconds
            )
            
            if self.debug_flag:
                print(f"[DEBUG] Generated pre-signed URL for {file_fullname} expiring in {hours} hours")
                
            return url
            
        except Exception as e:
            error_msg = f"Failed to generate pre-signed URL: {str(e)}"
            if self.debug_flag:
                print(f"[ERROR] {error_msg}")
                print(f"[ERROR] Exception type: {type(e)}")
                print(f"[ERROR] Exception traceback: {traceback.format_exc()}")
            raise Exception(error_msg) from e