apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: quepasa
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      # Simplified init container for non-secure Elasticsearch
      initContainers:
      - name: wait-for-elasticsearch
        image: curlimages/curl
        command: ['/bin/sh', '-c']
        args:
          - |
            echo "Waiting for Elasticsearch to be ready..."
            until curl -s -o /dev/null -w "%{http_code}" http://elasticsearch:9200 | grep -q "200"; do
              sleep 5
              echo "Elasticsearch not ready yet, retrying..."
            done
            
            echo "Verifying Elasticsearch is ready for connections..."
            HEALTH=$(curl -s http://elasticsearch:9200/_cluster/health | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
            echo "Elasticsearch health status: $HEALTH"
            
            echo "Elasticsearch checks complete."
      containers:
      - name: kibana
        image: docker.elastic.co/kibana/kibana:8.12.2
        ports:
        - containerPort: 5601
          name: http
        env:
        - name: ELASTICSEARCH_HOSTS
          value: http://elasticsearch:9200
        # No auth for simplified setup
        - name: ELASTICSEARCH_USERNAME
          value: "kibana_system"
        - name: ELASTICSEARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: KIBANA_PASSWORD
        - name: ELASTICSEARCH_SSL_VERIFICATIONMODE
          value: none
        - name: SERVER_HOST
          value: "0.0.0.0"
        - name: SERVER_PORT
          value: "5601"
        - name: SERVER_PUBLICBASEURL
          value: "http://localhost:5601"
        - name: xpack.security.enabled
          value: "false"
        resources:
          requests:
            cpu: "100m"  # Further reduced CPU request
            memory: "512Mi"
          limits:
            cpu: "500m"  # Further reduced CPU limit
            memory: "1Gi"
        readinessProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 120
          timeoutSeconds: 10
          periodSeconds: 15
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 180
          timeoutSeconds: 10
          periodSeconds: 30
          failureThreshold: 5
        startupProbe:
          httpGet:
            path: /api/status
            port: 5601
          initialDelaySeconds: 60
          periodSeconds: 15
          failureThreshold: 20 