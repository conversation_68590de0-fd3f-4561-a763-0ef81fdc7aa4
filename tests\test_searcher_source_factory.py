import pytest
from unittest.mock import Mock, patch

from quepasa.searcher.sources.factory import SourceFactory
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.response import QuepasaResponse
from quepasa.searcher.sources.base import STATUS_SUCCESS, STATUS_ERROR
from src.lib.constants import (
    SOURCE_DOCUMENTS,
    SOURCE_WEB,
    SOURCE_WATERFALL
)

@pytest.fixture
def mock_config():
    with patch('configuration.main.default.QuepasaConfigurationHub') as mock:
        instance = mock.return_value
        instance.get_primary_search_source.return_value = SOURCE_DOCUMENTS
        instance.get_waterfall_config.return_value = [
            {"source": SOURCE_DOCUMENTS},
            {"source": SOURCE_WEB}
        ]
        instance.request = QuepasaRequest(
            question="test query",
            protocol="http",
            metadata={}
        )
        yield instance

@pytest.fixture
def source_factory(mock_config):
    return SourceFactory(mock_config)

def test_get_source(source_factory):
    # Test getting RAG source
    rag_source = source_factory.get_source(SOURCE_DOCUMENTS)
    assert rag_source is not None
    
    # Test getting web source
    web_source = source_factory.get_source(SOURCE_WEB)
    assert web_source is not None
    
    # Test getting invalid source
    invalid_source = source_factory.get_source("invalid")
    assert invalid_source is None

def test_search_with_rag_source(source_factory, mock_config):
    mock_config.request.source = SOURCE_DOCUMENTS
    
    with patch('quepasa.searcher.sources.rag.RagSearchManager') as mock_rag:
        mock_rag.return_value.search.return_value = []
        
        with patch('quepasa.searcher.sources.rag.RAGSource') as mock_source:
            mock_source.return_value.search.return_value = QuepasaResponse(
                status=STATUS_SUCCESS,
                data={"results": []}
            )
            
            response = source_factory.search()
            assert response.status == STATUS_SUCCESS

def test_search_with_web_source(source_factory, mock_config):
    mock_config.request.source = SOURCE_WEB
    
    with patch('quepasa.searcher.core.web_search.WebSearchManager') as mock_web:
        mock_web.return_value.search.return_value = [{"title": "Test Result"}]
        
        with patch('quepasa.searcher.sources.web.WebSource.search') as mock_search:
            mock_search.return_value = QuepasaResponse(
                status=STATUS_SUCCESS,
                data={"results": [{"title": "Test Result"}]}
            )
            
            response = source_factory.search()
            assert response.status == STATUS_SUCCESS
            assert response.data["results"][0]["title"] == "Test Result"

def test_search_with_invalid_source(source_factory, mock_config):
    mock_config.request.source = "invalid"
    
    response = source_factory.search()
    assert response.status == STATUS_ERROR
    assert "Unknown source type" in response.error

def test_waterfall_search_success(source_factory, mock_config):
    mock_config.request.source = SOURCE_WATERFALL
    
    # Mock RAG source to fail
    with patch('quepasa.searcher.sources.rag.RagSearchManager') as mock_rag:
        mock_rag.return_value.search.return_value = []
        
        with patch('quepasa.searcher.sources.rag.RAGSource.search') as mock_rag_search:
            mock_rag_search.return_value = QuepasaResponse(
                status=STATUS_ERROR,
                error="No results found"
            )
            
            # Mock web source to succeed
            with patch('quepasa.searcher.core.web_search.WebSearchManager') as mock_web:
                mock_web.return_value.search.return_value = [{"title": "Test Result"}]
                
                with patch('quepasa.searcher.sources.web.WebSource.search') as mock_web_search:
                    mock_web_search.return_value = QuepasaResponse(
                        status=STATUS_SUCCESS,
                        data={"results": [{"title": "Test Result"}]}
                    )
                    
                    response = source_factory.search()
                    assert response.status == STATUS_SUCCESS
                    assert response.data["results"][0]["title"] == "Test Result"

def test_waterfall_search_all_fail(source_factory, mock_config):
    mock_config.request.source = SOURCE_WATERFALL
    
    # Mock RAG source to fail
    with patch('quepasa.searcher.sources.rag.RagSearchManager') as mock_rag:
        mock_rag.return_value.search.return_value = []
        
        with patch('quepasa.searcher.sources.rag.RAGSource.search') as mock_rag_search:
            mock_rag_search.return_value = QuepasaResponse(
                status=STATUS_ERROR,
                error="No results found"
            )
            
            # Mock web source to fail
            with patch('quepasa.searcher.core.web_search.WebSearchManager') as mock_web:
                mock_web.return_value.search.return_value = []
                
                with patch('quepasa.searcher.sources.web.WebSource.search') as mock_web_search:
                    mock_web_search.return_value = QuepasaResponse(
                        status=STATUS_ERROR,
                        error="No results found"
                    )
                    
                    response = source_factory.search()
                    assert response.status == STATUS_ERROR
                    assert "No source produced a response" in response.error

def test_waterfall_search_no_config(source_factory, mock_config):
    mock_config.request.source = SOURCE_WATERFALL
    mock_config.get_waterfall_config.return_value = None
    
    response = source_factory.search()
    assert response.status == STATUS_ERROR
    assert "No waterfall configuration found" in response.error

def test_search_error_handling(source_factory, mock_config):
    mock_config.request.source = SOURCE_DOCUMENTS
    
    # Simulate search error
    with patch('quepasa.searcher.sources.rag.RagSearchManager') as mock_rag:
        mock_rag.return_value.search.side_effect = Exception("Test error")
        
        with patch('quepasa.searcher.sources.rag.RAGSource.search') as mock_source:
            mock_source.return_value = QuepasaResponse(
                status=STATUS_ERROR,
                error="Test error"
            )
            
            response = source_factory.search()
            assert response.status == STATUS_ERROR
            assert "Test error" in response.error 