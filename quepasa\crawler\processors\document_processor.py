import io
from typing import Dict, Any, List
from docx import Document as DocxDocument
from openpyxl import load_workbook
import xlrd
import mammoth
from bs4 import BeautifulSoup
from .base import BaseProcessor
from ...utils import split_text, split_paragraphs
from src.lib.extraction import html_to_pages

from src.lib.logger import QuepasaLogger

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

class DocumentProcessor(BaseProcessor):
    """Processor for Microsoft Office documents (Word, Excel)"""

    def _process_impl(self, content: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract text from document content
        
        Args:
            content: Document file content
            meta: Metadata about the document
            
        Returns:
            Dictionary containing extracted text and title
        """
        # Validate metadata
        error = self.validate_meta(meta, ['filename', 'extension'])
        if error:
            raise ValueError(error)

        extension = meta['extension'].lower()
        temp_file = io.BytesIO(content)
        
        try:
            if extension in ['.doc', '.docx']:
                try:
                    return self._process_word_markdown(temp_file, meta)
                except Exception as e:
                    logger.warning(f"Error processing word document: {e}")
                    return self._process_word_original(temp_file, meta)
            elif extension in ['.xls', '.xlsx']:
                return self._process_excel(temp_file, meta)
            else:
                raise ValueError(f"Unsupported document type: {extension}")
        finally:
            temp_file.close()

    def _process_word_markdown(self, file_obj: io.BytesIO, meta: Dict[str, Any]) -> Dict[str, Any]:
        """Process Word document by converting to markdown first"""
        try:
            # Convert DOCX to HTML using mammoth
            html_content = mammoth.convert_to_html(file_obj).value
            
            # Convert HTML to markdown pages
            pages = html_to_pages(html_content)['pages_v2']

            # Extract title from <title> tag
            soup = BeautifulSoup(html_content, 'html.parser')
            title_tag = soup.find('title')
            title = title_tag.text.strip() if title_tag else None

            if not title:
                if pages:
                    first_page = pages[0]['text']
                    lines = first_page.split('\n')
                    for line in lines:
                        if line.startswith('#'):
                            title = line.lstrip('#').strip()
                            break

                # If no title from markdown headings, try to get from document properties
                if not title:
                    # Reset file pointer to beginning of file
                    file_obj.seek(0)
                    doc = DocxDocument(file_obj)
                    try:
                        core_props = doc.core_properties
                        if core_props.title:
                            title = core_props.title.strip()
                    except:
                        pass
                    
                    # If still no title, use first paragraph if it's short
                    if not title and doc.paragraphs:
                        first_para = doc.paragraphs[0]
                        if first_para.style.name.startswith('Heading'):
                            title = first_para.text.strip()
                        elif len(doc.paragraphs) > 1:
                            # If first paragraph is short, it might be a title
                            if len(first_para.text.split()) <= 20:
                                title = first_para.text.strip()

            chunks = []            
            for page in pages:
                raw_chunks = split_paragraphs(page['text'])
                for chunk in raw_chunks:
                    chunks.append({
                        'text': chunk,
                        'position': 'main content',
                    })

            return {
                'chunks': chunks,
                'title': title or meta.get('filename', ''),
                'filename': meta.get('filename', '')
            }
            
        except Exception as e:
            # Fallback to original document processing if markdown conversion fails
            file_obj.seek(0)
            return self._process_word_original(file_obj, meta)

    def _process_word_original(self, file_obj: io.BytesIO, meta: Dict[str, Any]) -> Dict[str, Any]:
        """Original Word document processing as fallback"""
        doc = DocxDocument(file_obj)
        chunks = []
        title = None
        
        # Try to get title from document properties first
        try:
            core_props = doc.core_properties
            if core_props.title:
                title = core_props.title.strip()
        except:
            pass
        
        # If no title in properties, try first heading or short first paragraph
        if not title and doc.paragraphs:
            first_para = doc.paragraphs[0]
            if first_para.style.name.startswith('Heading'):
                title = first_para.text.strip()
            elif len(doc.paragraphs) > 1:
                # If first paragraph is short, it might be a title
                if len(first_para.text.split()) <= 20:
                    title = first_para.text.strip()
        
        # Extract headers
        header_texts = []
        for section in doc.sections:
            header_text = [p.text for p in section.header.paragraphs if p.text.strip()]
            if header_text:
                header_texts.extend(header_text)
        
        if header_texts:
            raw_chunks = split_text('\n'.join(header_texts))
            for chunk in raw_chunks:
                chunks.append({
                    'text': chunk,
                    'position': 'header'
                })
        
        # Extract text from paragraphs
        paragraph_texts = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                paragraph_texts.append(paragraph.text.strip())
        
        if paragraph_texts:
            raw_chunks = split_text('\n'.join(paragraph_texts))
            for chunk in raw_chunks:
                chunks.append({
                    'text': chunk,
                    'position': 'main content'
                })
        
        # Extract text from tables
        table_texts = []
        for table in doc.tables:
            for row in table.rows:
                row_text = '\t'.join(cell.text.strip() for cell in row.cells if cell.text.strip())
                if row_text:
                    table_texts.append(row_text)
        
        if table_texts:
            raw_chunks = split_text('\n'.join(table_texts))
            for chunk in raw_chunks:
                chunks.append({
                    'text': chunk,
                    'position': 'table'
                })
        
        # Extract text from inline shapes
        shape_texts = []
        for shape in doc.inline_shapes:
            if hasattr(shape, "text") and shape.text.strip():
                shape_texts.append(shape.text.strip())
        
        if shape_texts:
            raw_chunks = split_text('\n'.join(shape_texts))
            for chunk in raw_chunks:
                chunks.append({
                    'text': chunk,
                    'position': 'inline shape'
                })
        
        # Extract footers
        footer_texts = []
        for section in doc.sections:
            footer_text = [p.text for p in section.footer.paragraphs if p.text.strip()]
            if footer_text:
                footer_texts.extend(footer_text)
        
        if footer_texts:
            raw_chunks = split_text('\n'.join(footer_texts))
            for chunk in raw_chunks:
                chunks.append({
                    'text': chunk,
                    'position': 'footer'
                })
        
        return {
            'chunks': chunks,
            'title': title,
            'filename': meta['filename']
        }

    def _process_excel(self, file_obj: io.BytesIO, meta: Dict[str, Any]) -> Dict[str, Any]:
        """Process Excel document"""
        extension = meta['extension'].lower()
        chunks = []
        title = None
        last_error = None
        
        # Try XLSX first if extension is xlsx or if we're not sure
        if extension == '.xlsx':
            try:
                # Process XLSX using openpyxl
                workbook = load_workbook(filename=file_obj)
                
                # Use first non-empty cell in first sheet as title
                if workbook.sheetnames:
                    first_sheet = workbook[workbook.sheetnames[0]]
                    for row in first_sheet.iter_rows(min_row=1, max_row=1):
                        for cell in row:
                            if cell.value:
                                title = str(cell.value).strip()
                                break
                        if title:
                            break
                
                # Process each sheet
                for sheet_name in workbook.sheetnames:
                    sheet = workbook[sheet_name]
                    sheet_texts = []
                    
                    for row in sheet.iter_rows():
                        row_text = '\t'.join(str(cell.value).strip() for cell in row if cell.value)
                        if row_text:
                            sheet_texts.append(row_text)
                    
                    if sheet_texts:
                        # Split sheet content into chunks
                        raw_chunks = split_text('\n'.join(sheet_texts))
                        for chunk in raw_chunks:
                            chunks.append({
                                'text': chunk,
                                'position': f"sheet {sheet_name}"
                            })
                
                # If we successfully processed as XLSX, return the results
                return {
                    'chunks': chunks,
                    'title': title,
                    'filename': meta['filename']
                }
            except Exception as e:
                last_error = e
                # Reset file position for next attempt
                file_obj.seek(0)
                
        # Try XLS if extension is xls or if XLSX processing failed
        if extension == '.xls' or last_error is not None:
            try:
                # Process XLS using xlrd
                workbook = xlrd.open_workbook(file_contents=file_obj.getvalue())
                
                # Use first non-empty cell in first sheet as title
                if workbook.nsheets > 0:
                    first_sheet = workbook.sheet_by_index(0)
                    if first_sheet.nrows > 0:
                        for cell in first_sheet.row(0):
                            if cell.value:
                                title = str(cell.value).strip()
                                break
                
                # Process each sheet
                for sheet_index in range(workbook.nsheets):
                    sheet = workbook.sheet_by_index(sheet_index)
                    sheet_texts = []
                    
                    for row_idx in range(sheet.nrows):
                        row = sheet.row(row_idx)
                        row_text = '\t'.join(str(cell.value).strip() for cell in row if cell.value)
                        if row_text:
                            sheet_texts.append(row_text)
                    
                    if sheet_texts:
                        # Split sheet content into chunks
                        raw_chunks = split_text('\n'.join(sheet_texts))
                        for chunk in raw_chunks:
                            chunks.append({
                                'text': chunk,
                                'position': f"sheet {sheet.name}"
                            })
                
                # If we successfully processed as XLS, return the results
                return {
                    'chunks': chunks,
                    'title': title,
                    'filename': meta['filename']
                }
            except Exception as e:
                # If both XLSX and XLS processing failed, raise the last error
                if last_error is not None:
                    raise Exception(f"Failed to process Excel file as XLSX ({str(last_error)}) and as XLS ({str(e)})")
                raise Exception(f"Failed to process Excel file: {str(e)}")
        
        raise ValueError(f"Unsupported Excel format: {extension}")