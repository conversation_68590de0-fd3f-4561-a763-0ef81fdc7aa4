"""
Simple test script to verify Gemini integration without dependencies on other providers.
"""

import os
import sys
import asyncio

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_gemini_direct():
    """Test Gemini provider directly."""
    print("Testing Gemini provider directly...")
    
    try:
        from src.lib.llm.gemini import GeminiLLM
        from src.lib.llm.providers import LLMProvider
        
        # Test initialization (will fail without API key, but that's expected)
        try:
            llm = GeminiLLM()
            print("✓ Gemini LLM initialized successfully")
        except ValueError as e:
            if "GEMINI_API_KEY" in str(e):
                print("⚠ Gemini LLM requires GEMINI_API_KEY (expected)")
            else:
                raise
        
        # Test provider property
        if 'llm' in locals():
            assert llm.provider == LLMProvider.GEMINI
            print("✓ Provider property returns correct enum")
        
        # Test helper functions
        from src.lib.llm.gemini import _pluck_system, _to_history
        
        messages = [
            {"role": "system", "content": "You are helpful."},
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi!"}
        ]
        
        system_msg = _pluck_system(messages)
        assert system_msg == "You are helpful."
        print("✓ System message extraction works")
        
        history = _to_history(messages, drop_system=True)
        expected_roles = ["user", "model"]
        actual_roles = [msg["role"] for msg in history]
        assert actual_roles == expected_roles
        print("✓ Role mapping works (assistant -> model)")
        
        return True
        
    except Exception as e:
        print(f"✗ Direct test failed: {e}")
        return False

def test_factory_with_gemini():
    """Test factory with Gemini provider."""
    print("\nTesting factory with Gemini...")
    
    try:
        # Import only what we need
        from src.lib.llm.providers import LLMProvider
        
        # Test enum
        assert LLMProvider.GEMINI.value == "gemini"
        print("✓ GEMINI provider enum exists")
        
        # Test string conversion
        provider = LLMProvider.from_str("gemini")
        assert provider == LLMProvider.GEMINI
        print("✓ String to enum conversion works")
        
        return True
        
    except Exception as e:
        print(f"✗ Factory test failed: {e}")
        return False

def test_with_api_key():
    """Test with actual API key if available."""
    print("\nTesting with API key...")
    
    if not os.getenv("GEMINI_API_KEY"):
        print("⚠ No GEMINI_API_KEY found - skipping API tests")
        return True
    
    try:
        from src.lib.llm.gemini import GeminiLLM
        
        llm = GeminiLLM()
        
        # Test basic functionality
        prompt_list = [{"role": "user", "content": "Say 'Hello from Gemini!'"}]
        
        # Test non-streaming
        answer = llm.get_answer("gemini-2.0-flash-exp", prompt_list, 100, False)
        print(f"✓ Non-streaming answer: {answer[:50]}...")
        
        # Test streaming
        print("Testing streaming...")
        chunks = []
        async def collect_chunks():
            async for chunk in llm.get_streaming_answer("gemini-2.0-flash-exp", prompt_list, 100, False):
                chunks.append(chunk)
        
        asyncio.run(collect_chunks())
        print(f"✓ Streaming received {len(chunks)} chunks")
        
        # Test tools
        tools = [{"function_declarations": [{"name": "test_func", "description": "Test"}]}]
        result, tool_calls = llm.get_tools_answer("gemini-2.0-flash-exp", prompt_list, tools, 100)
        print(f"✓ Tools answer: {result[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ API test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Gemini Integration (Simple)")
    print("=" * 50)
    
    tests = [
        test_gemini_direct,
        test_factory_with_gemini,
        test_with_api_key,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Gemini integration is working correctly.")
    else:
        print("❌ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)