import pytest

from quepasa.common.config_models import (
    TenantConfig,
    TenantModel,
    ConversationalSearchConfigModel,
)


def make_cfg(id, tenantId, collectionId=None, areaId=None, rag=None, spd=None):
    inter = None
    if rag is not None or spd is not None:
        inter = ConversationalSearchConfigModel.Interleaving(
            ragRatio=rag, spdRatio=spd, spdIncludeOnlyPromoted=False
        )
    return ConversationalSearchConfigModel(
        id=id,
        tenantId=tenantId,
        collectionId=collectionId,
        areaId=areaId,
        interleaving=inter,
    )


def test_get_conversational_config_fallback_collection_only_when_area_missing():
    tenant = TenantModel(id=1, name="acme", enabled=True, settings={})
    c_only = make_cfg(101, tenantId=1, collectionId=1000, areaId=None, rag=0.4, spd=0.6)
    other_area = make_cfg(102, tenantId=1, collectionId=1000, areaId=999)
    tc = TenantConfig(
        tenant=tenant,
        collections=[],
        areas=[],
        attributes=[],
        conversational_search_configs=[c_only, other_area],
    )

    # Query with area that has no exact match but with same collection
    cfg = tc.get_conversational_config(area_id=555, collection_id=1000)
    assert cfg is not None
    assert cfg.id == 101  # fell back to collection-only
    inter = tc.get_interleaving(area_id=555, collection_id=1000)
    assert inter is not None
    assert inter.ragRatio == 0.4 and inter.spdRatio == 0.6


def test_get_conversational_config_prefers_exact_area_collection_match():
    tenant = TenantModel(id=1, name="acme", enabled=True, settings={})
    c_only = make_cfg(201, tenantId=1, collectionId=1000, areaId=None)
    exact = make_cfg(202, tenantId=1, collectionId=1000, areaId=123, rag=0.7, spd=0.3)
    tc = TenantConfig(
        tenant=tenant,
        conversational_search_configs=[c_only, exact],
    )

    cfg = tc.get_conversational_config(area_id=123, collection_id=1000)
    assert cfg is not None
    assert cfg.id == 202
    inter = tc.get_interleaving(area_id=123, collection_id=1000)
    assert inter is not None
    assert inter.ragRatio == 0.7 and inter.spdRatio == 0.3
