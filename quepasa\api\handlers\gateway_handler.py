from typing import List, Dict, Any
from pydantic import BaseModel, Field
from fastapi import APIRouter, HTTPException, Depends, File, UploadFile, Form
import json
import numpy as np

from quepasa.api.auth import verify_auth
from src.lib.embedding.providers import EmbeddingProvider
from src.lib.embedding_service import get_embedding_from_service
from src.lib.embedding_utils import get_cached_embedding
from src.lib.llm_utils import get_llm_answer, get_cached_llm_answer
from src.lib.whisper_utils import get_cached_segments
from src.lib.logger import QuepasaLogger

# Logger
logger = QuepasaLogger().get_instance(__name__)

router = APIRouter()

# Embeddings
class EmbeddingRequest(BaseModel):
    provider: str
    model: str
    text: str
    bypass_cache: bool = Field(default=False)

class EmbeddingResponse(BaseModel):
    embedding: List[float]
    cached: bool = Field(default=False)

# Create retryable versions of our external API calls
def get_embedding_factory(provider, model_version, text):
    """Get embedding"""
    return get_embedding_from_service(provider, model_version, text) if provider in [EmbeddingProvider.SBERT.value] else get_cached_embedding(provider, model_version, text)

@router.post("/embedding", response_model=EmbeddingResponse)
async def get_embedding(
    request: EmbeddingRequest,
    client_id: str = Depends(verify_auth)
) -> EmbeddingResponse:
    """Get embeddings for a text"""
    # If not in cache, get from provider using the direct method
    embedding = get_embedding_factory(request.provider, request.model, request.text)
    if embedding is None or len(embedding) == 0:
        logger.error(f"Failed to get embedding from provider: {request.provider} {request.model} {request.text}")
        raise HTTPException(status_code=500, detail="Failed to get embedding from provider")
    
    if isinstance(embedding, np.ndarray):
        embedding_to_cache = embedding.tolist()
    else:
        embedding_to_cache = embedding
    
    return EmbeddingResponse(
        embedding=embedding_to_cache,
        cached=False
    )

# LLM
class LLMRequest(BaseModel):
    provider: str
    model: str
    messages: List[Dict[str, str]]
    max_tokens: int = Field(default=None)
    json_mode: bool = Field(default=False)
    use_cache: bool = Field(default=True)

class LLMResponse(BaseModel):
    text: str
    cached: bool = Field(default=False)

@router.post("/llm", response_model=LLMResponse)
async def get_llm(
    request: LLMRequest,
    client_id: str = Depends(verify_auth)
) -> LLMResponse:
    """Get LLM response for the provided prompt list"""
    try:
        if request.use_cache:
            # Use cached version
            response_text = get_cached_llm_answer(
                request.provider,
                request.model,
                request.messages,
                request.max_tokens,
                request.json_mode
            )
            return LLMResponse(text=response_text, cached=True)
        
        else:
            # Direct call without caching
            response_text = get_llm_answer(
                request.provider,
                request.model,
                request.messages,
                request.max_tokens,
                request.json_mode
            )
            return LLMResponse(text=response_text, cached=False)
        
    except Exception as e:
        logger.error(f"Failed to get LLM response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get LLM response: {str(e)}")
        logger.error(f"Failed to get LLM response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get LLM response: {str(e)}")

# Whisper
class WhisperRequest(BaseModel):
    provider: str
    model: str
    meta: Dict[str, Any] = Field(default_factory=dict)

class WhisperResponse(BaseModel):
    segments: Dict[str, Any]
    cached: bool = Field(default=False)

@router.post("/whisper", response_model=WhisperResponse)
async def get_segments(
    file: UploadFile = File(...),
    provider: str = Form(...),
    model: str = Form(...),
    meta: str = Form("{}"),
    client_id: str = Depends(verify_auth)
) -> WhisperResponse:
    """Get audio transcription from the whisper service"""
    try:
        audio_data = await file.read()
        
        # Parse meta JSON string to dict
        meta_dict = json.loads(meta)
        
        # Get transcription using cached segments
        segments = get_cached_segments(
            provider,
            model,
            audio_data,
            meta_dict
        )
        
        if not segments:
            logger.error(f"Failed to get transcription: {provider} {model}")
            raise HTTPException(status_code=500, detail="Failed to get transcription")
        
        return WhisperResponse(
            segments=segments,
            cached=True
        )
        
    except Exception as e:
        logger.error(f"Failed to get whisper transcription: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get transcription: {str(e)}")