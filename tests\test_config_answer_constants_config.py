import pytest
from configuration.base.answer_constants import AnswerConstantsConfig
from quepasa.searcher.models.request import QuepasaRequest

def test_get_SMTH_WENT_WRONG_constant():
    """Test error message retrieval"""
    config = AnswerConstantsConfig("test_client")
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram",
        language="en"
    )
    config.set_request(request)
    
    message = config.get_SMTH_WENT_WRONG_constant(source="telegram", language_code="en")
    assert "something went wrong" in message.lower()
    assert isinstance(message, str)

def test_get_I_DONT_KNOW_constant():
    """Test no answer message retrieval"""
    config = AnswerConstantsConfig("test_client")
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram",
        language="en"
    )
    config.set_request(request)
    
    message = config.get_I_DONT_KNOW_constant(source="telegram", language_code="en")
    assert "don't have enough information" in message.lower()
    assert isinstance(message, str)

@pytest.mark.parametrize("source,language_code", [
    ("telegram", "en"),
    ("web", "en"), 
    ("api", "es"),
    ("", "")
])
def test_get_SMTH_WENT_WRONG_constant_various_inputs(source, language_code):
    """Test error message with various inputs"""
    config = AnswerConstantsConfig("test_client")
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source=source,
        language=language_code
    )
    config.set_request(request)
    
    message = config.get_SMTH_WENT_WRONG_constant(source=source, language_code=language_code)
    assert isinstance(message, str)
    assert len(message) > 0

@pytest.mark.parametrize("source,language_code", [
    ("telegram", "en"),
    ("web", "en"),
    ("api", "es"),
    ("", "")
])
def test_get_I_DONT_KNOW_constant_various_inputs(source, language_code):
    """Test no answer message with various inputs"""
    config = AnswerConstantsConfig("test_client")
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source=source,
        language=language_code
    )
    config.set_request(request)
    
    message = config.get_I_DONT_KNOW_constant(source=source, language_code=language_code)
    assert isinstance(message, str)
    assert len(message) > 0 