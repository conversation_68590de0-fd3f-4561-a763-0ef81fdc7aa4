import os
os.environ['MINIO_HOST'] = 'localhost'  # Mock ENV before importing config

import sys

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.lib.files import QuepasaFiles


# Test configuration
TEST_CLIENT_ID = 'test_client'
TEST_DOMAIN = 'test_domain'

qp_files = QuepasaFiles(
    bucket_name='quepasa-files',
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin',
    debug_flag=False
)

print(qp_files.get_files("external", recursive=True))