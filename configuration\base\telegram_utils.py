from typing import Optional, Dict, Any, Union
from quepasa.searcher.models.request import QuepasaRequest
from .telegram_config import TelegramConfig

class TelegramUtils(TelegramConfig):
    """Base provider for Telegram configuration functionality."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
    
    def _get_telegram_command_text(self, command: str) -> Optional[str]:
        """Get text for a specific Telegram command from config.
        
        Previously: get_telegram_command_text()
        """
        config = self._get_telegram_config()
        if (
            config and 'commands' in config 
            and isinstance(config['commands'], dict)
            and command in config['commands']
            and isinstance(config['commands'][command], dict)
            and 'message' in config['commands'][command]
        ):
            message = config['commands'][command]['message']
            if message and message.strip():
                return message.strip()
        return None