#!/usr/bin/env python3
"""
Test script for QuePasa LLM Gateway utility functions.
Tests both embedding generation and LLM response functionality.
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any

# Add the src directory to the path so we can import the library
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.lib.quepasa_llm_gateway import (
    get_embedding_from_gateway, 
    get_llm_answer_from_gateway,
    QuepasaLLMGateway
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_embedding():
    """Test the embedding generation function."""
    logger.info("Testing embedding generation...")
    
    test_text = "This is a test sentence for embedding generation."
    provider = "nebius"  # Change to your preferred provider
    model = "BAAI/bge-multilingual-gemma2"  # Change to your preferred model
    
    try:
        # Get embedding
        logger.info(f"Generating embedding for text: '{test_text}'")
        embedding = get_embedding_from_gateway(
            provider=provider,
            model=model,
            text=test_text
        )
        
        # Check if we got a valid embedding
        if embedding and isinstance(embedding, list) and len(embedding) > 0:
            logger.info(f"✓ Successfully generated embedding with {len(embedding)} dimensions")
            # Print the first 5 and last 5 elements of the embedding
            logger.info(f"First 5 elements: {embedding[:5]}")
            logger.info(f"Last 5 elements: {embedding[-5:]}")
            return True
        else:
            logger.error(f"✗ Failed to generate embedding, got: {embedding}")
            return False
            
    except Exception as e:
        logger.error(f"✗ Error testing embedding generation: {e}")
        return False

def test_llm_answer():
    """Test the LLM answer generation function."""
    logger.info("Testing LLM response generation...")
    
    # Simple conversation
    messages = [
        {"role": "system", "content": "You are a helpful assistant. Keep your answers concise."},
        {"role": "user", "content": "What is the capital of France?"}
    ]
    
    provider = "anthropic"  # Change to your preferred provider
    model = "claude-3-haiku-20240307"  # Change to your preferred model
    
    try:
        # Get LLM response
        logger.info(f"Generating LLM response for messages: {json.dumps(messages, indent=2)}")
        response = get_llm_answer_from_gateway(
            provider=provider,
            model=model,
            messages=messages,
            max_tokens=100
        )
        
        # Check if we got a valid response
        if response and isinstance(response, str) and len(response) > 0:
            logger.info(f"✓ Successfully generated LLM response")
            logger.info(f"Response: {response}")
            return True
        else:
            logger.error(f"✗ Failed to generate LLM response, got: {response}")
            return False
            
    except Exception as e:
        logger.error(f"✗ Error testing LLM response generation: {e}")
        return False

def test_custom_client():
    """Test creating a custom client with different parameters."""
    logger.info("Testing custom client creation...")
    
    try:
        # Create custom client with different parameters
        custom_client = QuepasaLLMGateway(
            max_retries=2,
            retry_delay=0.5
        )
        
        # Test the client with a simple LLM request
        messages = [{"role": "user", "content": "Hello, how are you?"}]
        response = custom_client.get_llm_answer(
            messages=messages,
            provider="anthropic",
            model="claude-3-haiku-20240307",
            max_tokens=50
        )
        
        if response and isinstance(response, str) and len(response) > 0:
            logger.info(f"✓ Custom client successfully generated LLM response")
            logger.info(f"Response: {response}")
            return True
        else:
            logger.error(f"✗ Custom client failed to generate LLM response")
            return False
            
    except Exception as e:
        logger.error(f"✗ Error testing custom client: {e}")
        return False

"""Run all tests."""
logger.info("==== Starting QuePasa LLM Gateway Tests ====")

# Check for API key
if not os.environ.get("QUEPASA_LLM_GATEWAY_API_KEY"):
    logger.error("QUEPASA_LLM_GATEWAY_API_KEY environment variable not set!")
    logger.error("Please set this variable before running the tests.")
    sys.exit(1)

# Run tests
embedding_success = test_embedding()
llm_success = test_llm_answer()
custom_client_success = test_custom_client()

# Summarize results
logger.info("==== Test Results ====")
logger.info(f"Embedding test: {'✓ PASSED' if embedding_success else '✗ FAILED'}")
logger.info(f"LLM answer test: {'✓ PASSED' if llm_success else '✗ FAILED'}")
logger.info(f"Custom client test: {'✓ PASSED' if custom_client_success else '✗ FAILED'}")

# Overall result
if embedding_success and llm_success and custom_client_success:
    logger.info("✓ All tests PASSED!")
else:
    logger.error("✗ Some tests FAILED!")
