import pytest
from src.lib.files import QuepasaFiles
import os

def test_minio_connection():
    """Test connection to MinIO and basic operations."""
    # Initialize QuepasaFiles with MinIO configuration
    storage = QuepasaFiles(
        bucket_name='quepasa-files',
        endpoint_url='http://localhost:9000',
        aws_access_key_id='minioadmin',
        aws_secret_access_key='minioadmin',
        debug_flag=True  # Enable debug logging
    )

    # Test file operations
    test_data = "Hello, MinIO!"
    test_file = "test/hello.txt"

    try:
        # Upload test
        storage.set_text(test_file, test_data)
        assert storage.exists(test_file), "File should exist after upload"

        # Download test
        retrieved_data = storage.get_text(test_file)
        assert retrieved_data == test_data, "Retrieved data should match uploaded data"

    finally:
        # Cleanup
        if storage.exists(test_file):
            storage.delete_file(test_file)
            assert not storage.exists(test_file), "File should not exist after deletion"

if __name__ == '__main__':
    test_minio_connection() 