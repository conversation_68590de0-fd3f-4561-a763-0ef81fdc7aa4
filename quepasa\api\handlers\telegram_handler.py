from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, validator
import re
import os
from typing import List, Dict, Any, Optional

from src.lib.logger import QuepasaLogger
from src.lib.files import QuepasaFiles
from src.lib.telegram.client import QuepasaTelegramClient
from quepasa.api.auth import verify_auth

# Logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize files
qp_files = QuepasaFiles()

# Create router
router = APIRouter()

class PhoneRequest(BaseModel):
    phone_number: str
    
    @validator('phone_number')
    def validate_phone(cls, v):
        # Simple validation for international phone numbers
        if not re.match(r'^\+?[1-9]\d{1,14}$', v):
            raise ValueError('Invalid phone number format')
        return v

class VerificationRequest(BaseModel):
    phone_number: str
    code: str
    phone_code_hash: str = None
    password: str = None

class TelegramHandler:
    """Handles Telegram operations"""
    
    def __init__(self, files=None):
        """Initialize TelegramHandler
        
        Args:
            files: Optional QuepasaFiles instance. If not provided, uses global instance.
        """
        self.files = files or qp_files
    
    async def request_code(self, client_id: str, phone_number: str) -> Dict[str, Any]:
        """Request authentication code from Telegram
        
        Args:
            client_id: Client identifier
            phone_number: Phone number to authenticate
            
        Returns:
            Response with status and message
        """
        try:
            client = QuepasaTelegramClient(client_id, phone_number)
            result = await client.send_code_request()
            
            if result["status"] == "error":
                raise ValueError(result["message"])
                                
            return result
        
        except Exception as e:
            logger.error(f"Error requesting code: {str(e)}", exc_info=True)
            raise
    
    async def verify_code(self, client_id: str, phone_number: str, code: str, phone_code_hash: Optional[str] = None, password: Optional[str] = None) -> Dict[str, Any]:
        """Verify the authentication code and save the session
        
        Args:
            client_id: Client identifier
            phone_number: Phone number being authenticated
            code: Verification code from Telegram
            password: Two-steps verification password
            
        Returns:
            Response with status and message
        """
        try:
            client = QuepasaTelegramClient(client_id, phone_number)
            result = await client.sign_in(code, phone_code_hash, password)
            
            if result["status"] == "error":
                raise ValueError(result["message"])
                
            return result
        
        except Exception as e:
            logger.error(f"Error verifying code: {str(e)}", exc_info=True)
            raise

# Initialize handler instance
handler = TelegramHandler()

# Router endpoints
@router.post("/request-code")
async def request_code(
    request: PhoneRequest,
    client_id: str = Depends(verify_auth)
) -> Dict[str, Any]:
    """Request authentication code from Telegram"""
    try:
        return await handler.request_code(client_id, request.phone_number)
    except ValueError as e:
        raise HTTPException(400, str(e))
    except Exception as e:
        raise HTTPException(500, str(e))

@router.post("/verify-code")
async def verify_code(
    request: VerificationRequest,
    client_id: str = Depends(verify_auth)
) -> Dict[str, Any]:
    """Verify the authentication code and save the session"""
    try:
        return await handler.verify_code(
            client_id, 
            request.phone_number,
            request.code,
            request.phone_code_hash,
            request.password
        )
    except ValueError as e:
        raise HTTPException(400, str(e))
    except Exception as e:
        raise HTTPException(500, str(e))

@router.post("/set-username-mappings")
async def set_username_mappings(
    mappings: Dict[str, Dict[str, str]],
    client_id: str = Depends(verify_auth)
) -> Dict[str, Any]:
    """Set external username mappings"""
    try:
        QuepasaTelegramClient.set_external_username_mappings(client_id, mappings)
        return {"status": "success"}
    except ValueError as e:
        raise HTTPException(400, str(e))
    except Exception as e:
        raise HTTPException(500, str(e))