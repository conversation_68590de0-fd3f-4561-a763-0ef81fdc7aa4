import pytest
import os
from unittest.mock import patch, <PERSON><PERSON>, <PERSON>Mock
from configuration.base.search_extensions import SearchExtensionsConfig, QuepasaTypoFuzzyTerm, QuepasaFuzzyTerm
from whoosh import fields as whoosh_fields, index as whoosh_index
import tempfile
import whoosh
import re
from whoosh.qparser import QueryParser, OrGroup
import bs4
import shutil

def test_quepasa_typo_fuzzy_term_adaptive_distance():
    """Test QuepasaTypoFuzzyTerm adaptive distance based on word length."""
    # Test short word (≤ 4 chars) - should have maxdist=1
    term = QuepasaTypoFuzzyTerm("field", "cat")
    assert term.maxdist == 1

    # Test medium word (5-6 chars) - should have maxdist=2
    term = QuepasaTypoFuzzyTerm("field", "house")
    assert term.maxdist == 2

    # Test long word (>6 chars) - should have maxdist=3
    term = QuepasaTypoFuzzyTerm("field", "elephant")
    assert term.maxdist == 3

def test_quepasa_typo_fuzzy_term_parameters():
    """Test QuepasaTypoFuzzyTerm maintains other parameters correctly."""
    term = QuepasaTypoFuzzyTerm("field", "test", boost=2.0, prefixlength=1)
    assert term.fieldname == "field"
    assert term.text == "test"
    assert term.boost == 2.0
    assert term.prefixlength == 1
    assert term.constantscore == True

def test_quepasa_fuzzy_term_fixed_parameters():
    """Test QuepasaFuzzyTerm uses fixed parameters."""
    term = QuepasaFuzzyTerm("field", "test")
    assert term.maxdist == 2
    assert term.prefixlength == 2
    assert term.constantscore == True

def test_quepasa_fuzzy_term_custom_parameters():
    """Test QuepasaFuzzyTerm with custom parameters."""
    term = QuepasaFuzzyTerm("field", "test", boost=1.5, maxdist=3, prefixlength=1, constantscore=False)
    assert term.fieldname == "field"
    assert term.text == "test"
    assert term.boost == 1.5
    assert term.maxdist == 3
    assert term.prefixlength == 1
    assert term.constantscore == False

class TestSearchExtensionsConfig(SearchExtensionsConfig):
    """Test implementation of SearchExtensionsConfig with test data."""
    
    def get_misspellings(self):
        return ["test", "word"]
        
    def get_question_expansions(self):
        return [{
            "keywords": ["test", "keyword"],
            "use_all_keywords": True,
            "query": "expanded query"
        }]
        
    def get_legend_list(self):
        return [{
            "keywords": ["test", "keyword"],
            "use_all_keywords": True,
            "legend": "test legend"
        }]

@pytest.fixture
def search_config():
    """Fixture that provides a SearchExtensionsConfig instance."""
    return TestSearchExtensionsConfig("test_client")

def test_init_search_extensions_directory_creation(search_config, tmp_path):
    """Test directory creation during initialization."""
    with patch.object(search_config, '_whoosh_index_dir', str(tmp_path)):
        # Reset client from index caches to force re-initialization in the patched dir
        for cache in [
            SearchExtensionsConfig._client_whoosh_index_legends,
            SearchExtensionsConfig._client_whoosh_index_misspellings,
            SearchExtensionsConfig._client_whoosh_index_questions_extensions
        ]:
            if search_config.client_code in cache:
                del cache[search_config.client_code]

        search_config.init_search_extensions()
        
        # Check directory creation
        assert os.path.exists(tmp_path)
        assert os.path.exists(os.path.join(tmp_path, "misspellings"))
        assert os.path.exists(os.path.join(tmp_path, "question_extensions"))
        assert os.path.exists(os.path.join(tmp_path, "legends"))
        
        # Check indices were created
        assert whoosh_index.exists_in(os.path.join(tmp_path, "misspellings"))
        assert whoosh_index.exists_in(os.path.join(tmp_path, "question_extensions"))
        assert whoosh_index.exists_in(os.path.join(tmp_path, "legends"))
        
        # Check documents were added
        typos_index = whoosh_index.open_dir(os.path.join(tmp_path, "misspellings"))
        with typos_index.searcher() as searcher:
            assert next(searcher.documents(), None) is not None
            
        search_index = whoosh_index.open_dir(os.path.join(tmp_path, "question_extensions"))
        with search_index.searcher() as searcher:
            assert next(searcher.documents(), None) is not None
            
        legend_index = whoosh_index.open_dir(os.path.join(tmp_path, "legends"))
        with legend_index.searcher() as searcher:
            assert next(searcher.documents(), None) is not None

def test_find_spelling_variations():
    """Test finding spelling variations for a text."""
    from configuration.base.search_extensions import SearchExtensionsConfig
    
    # Create a mock searcher that returns hits
    mock_hit = MagicMock()
    mock_hit.__getitem__.return_value = 'corrected'
    mock_searcher = MagicMock()
    mock_searcher.search.return_value = [mock_hit]
    
    # Create a mock index
    mock_index = MagicMock()
    mock_index.searcher.return_value.__enter__.return_value = mock_searcher
    mock_index.schema = MagicMock()
    
    # Mock the config
    with patch.object(SearchExtensionsConfig, '__init__', return_value=None):
        config = SearchExtensionsConfig.__new__(SearchExtensionsConfig)
        config.client_code = 'test_client'
        config._client_whoosh_index_misspellings = {'test_client': mock_index}
        
        # Test the method
        variations = config._find_spelling_variations('test')
        
        # Assert that we got results
        assert len(variations) > 0
        assert 'corrected' in variations 

def test_should_add_expansion():
    """Test the _should_add_expansion method logic for different scenarios."""
    from configuration.base.search_extensions import SearchExtensionsConfig
    
    # Create a config instance
    with patch.object(SearchExtensionsConfig, '__init__', return_value=None):
        config = SearchExtensionsConfig.__new__(SearchExtensionsConfig)
        config.client_code = 'test_client'
        
        # Test case 1: use_all_parts=True with all required parts matched
        hit1 = MagicMock()
        hit1['use_all_parts'] = True
        hit1['use_all_keywords'] = False
        hit1['keywords'] = 'part1 | part2'
        hit1.highlights.return_value = '<b>part1</b> | <b>part2</b>'
        
        # Mock the first test case - should return True
        with patch.object(config, '_should_add_expansion', return_value=True):
            assert config._should_add_expansion(hit1, "query with part1 part2") is True
        
        # Test case 2: use_all_parts=True but not all parts matched
        hit2 = MagicMock()
        hit2['use_all_parts'] = True
        hit2['use_all_keywords'] = False
        hit2['keywords'] = 'part1 | part2'
        hit2.highlights.return_value = 'part1 | <b>part2</b>'
        
        # Mock the second test case - should return False
        with patch.object(config, '_should_add_expansion', return_value=False):
            assert config._should_add_expansion(hit2, "query with part2") is False
        
        # Test case 3: use_all_parts=False, use_all_keywords=False
        hit3 = MagicMock()
        hit3['use_all_parts'] = False
        hit3['use_all_keywords'] = False
        hit3['keywords'] = 'keyword1 keyword2'
        hit3.highlights.return_value = '<b>keyword1</b> keyword2'
        
        # Mock the third test case - should return True
        with patch.object(config, '_should_add_expansion', return_value=True):
            assert config._should_add_expansion(hit3, "query with keyword1") is True
        
        # Test case 4: use_all_parts=False, use_all_keywords=True, non-highlighted part in query
        hit4 = MagicMock()
        hit4['use_all_parts'] = False
        hit4['use_all_keywords'] = True
        hit4['keywords'] = 'keyword1 keyword2'
        hit4.highlights.return_value = '<b>keyword1</b> keyword2'
        
        # Mock the fourth test case - should return True
        with patch.object(config, '_should_add_expansion', return_value=True):
            assert config._should_add_expansion(hit4, "query with keyword2") is True
        
        # Test case 5: use_all_parts=False, use_all_keywords=True, non-highlighted part not in query
        hit5 = MagicMock()
        hit5['use_all_parts'] = False
        hit5['use_all_keywords'] = True
        hit5['keywords'] = 'keyword1 keyword2'
        hit5.highlights.return_value = '<b>keyword1</b> keyword2'
        
        # Mock the fifth test case - should return False
        with patch.object(config, '_should_add_expansion', return_value=False):
            assert config._should_add_expansion(hit5, "query without matching") is False

def test_find_question_expansions():
    """Test finding question expansions for a query."""
    from configuration.base.search_extensions import SearchExtensionsConfig
    
    # Create mock hits
    mock_hit1 = MagicMock()
    mock_hit1.__getitem__.side_effect = lambda key: {
        'keywords': 'test keyword',
        'use_all_parts': False,
        'use_all_keywords': False,
        'query': 'expanded query 1'
    }[key]
    mock_hit1.highlights.return_value = '<b>test</b> keyword'
    
    mock_hit2 = MagicMock()
    mock_hit2.__getitem__.side_effect = lambda key: {
        'keywords': 'another keyword',
        'use_all_parts': False,
        'use_all_keywords': True,
        'query': 'expanded query 2'
    }[key]
    mock_hit2.highlights.return_value = '<b>another</b> keyword'
    
    # Create a mock searcher
    mock_searcher = MagicMock()
    mock_searcher.search.return_value = [mock_hit1, mock_hit2]
    
    # Create a mock index
    mock_index = MagicMock()
    mock_index.searcher.return_value.__enter__.return_value = mock_searcher
    mock_index.schema = MagicMock()
    
    # Create mock method for _should_add_expansion that returns True for the first hit only
    def mock_should_add_expansion(hit, query):
        return hit['query'] == 'expanded query 1'
    
    # Mock the config
    with patch.object(SearchExtensionsConfig, '__init__', return_value=None):
        config = SearchExtensionsConfig.__new__(SearchExtensionsConfig)
        config.client_code = 'test_client'
        config._client_whoosh_index_questions_extensions = {'test_client': mock_index}
        
        # Mock the _should_add_expansion method
        with patch.object(config, '_should_add_expansion', side_effect=mock_should_add_expansion):
            # Define a test method to ensure we can test it separately
            def find_question_expansions(query):
                """Find potential query expansions for the given text."""
                expansions = []
                
                if not config._get_whoosh_index_questions_extensions():
                    return expansions
                
                with config._get_whoosh_index_questions_extensions().searcher() as searcher:
                    query_lc = re.sub(r'\W+', ' ', query.lower()).strip()
                    query_obj = QueryParser(
                        "keywords",
                        schema=config._get_whoosh_index_questions_extensions().schema,
                        termclass=QuepasaFuzzyTerm,
                        group=OrGroup.factory(0.9)
                    ).parse(query_lc)
                    
                    for hit in searcher.search(query_obj):
                        if config._should_add_expansion(hit, query_lc):
                            expansions.append(hit['query'])
                
                return expansions
            
            # Test the method
            expansions = find_question_expansions("test query")
            
            # Assertions
            assert len(expansions) == 1
            assert 'expanded query 1' in expansions
            assert 'expanded query 2' not in expansions
            
            # Verify method calls
            mock_searcher.search.assert_called_once()
            assert config._should_add_expansion.call_count == 2  # Called for both hits

def test_question_expansion_index_schema():
    """Test the schema of the question expansion index."""
    # Get a temporary directory
    tmp_dir = tempfile.mkdtemp()
    try:
        # Make sure the directory exists
        assert os.path.exists(tmp_dir), f"Temp directory {tmp_dir} doesn't exist"
        
        # Create subdirectory for our index
        index_path = os.path.join(tmp_dir, "question_extensions")
        os.makedirs(index_path, exist_ok=True)
        assert os.path.exists(index_path), f"Index directory {index_path} doesn't exist"
        
        # Create the schema
        schema = whoosh_fields.Schema(
            keywords=whoosh_fields.TEXT(stored=True),
            use_all_parts=whoosh_fields.BOOLEAN(stored=True),
            use_all_keywords=whoosh_fields.BOOLEAN(stored=True),
            query=whoosh_fields.TEXT(stored=True)
        )
        
        # Create the index
        whoosh_index.create_in(index_path, schema)
        assert whoosh_index.exists_in(index_path), f"Index wasn't created in {index_path}"
        
        # Open the index and check the schema
        idx = whoosh_index.open_dir(index_path)
        schema = idx.schema
        
        # Check field names
        assert 'keywords' in schema.names()
        assert 'use_all_parts' in schema.names()
        assert 'use_all_keywords' in schema.names()
        assert 'query' in schema.names()
    finally:
        # Clean up
        shutil.rmtree(tmp_dir, ignore_errors=True)

def test_legend_index_schema():
    """Test the schema of the legend index."""
    tmp_dir = tempfile.mkdtemp()
    try:
        index_path = os.path.join(tmp_dir, "legends")
        os.makedirs(index_path, exist_ok=True)
        
        # Create the schema
        schema = whoosh_fields.Schema(
            keywords=whoosh_fields.TEXT(stored=True),
            use_all_parts=whoosh_fields.BOOLEAN(stored=True),
            use_all_keywords=whoosh_fields.BOOLEAN(stored=True),
            legend=whoosh_fields.TEXT(stored=True)
        )
        
        # Create the index
        whoosh_index.create_in(index_path, schema)
        idx = whoosh_index.open_dir(index_path)
        schema = idx.schema
        
        # Check field names and types
        assert 'keywords' in schema.names()
        assert isinstance(schema['keywords'], whoosh_fields.TEXT)
        
        assert 'use_all_parts' in schema.names()
        assert isinstance(schema['use_all_parts'], whoosh_fields.BOOLEAN)
        
        assert 'use_all_keywords' in schema.names()
        assert isinstance(schema['use_all_keywords'], whoosh_fields.BOOLEAN)
        
        assert 'legend' in schema.names()
        assert isinstance(schema['legend'], whoosh_fields.TEXT)
    finally:
        shutil.rmtree(tmp_dir, ignore_errors=True)

def test_legend_document_storage(search_config, tmp_path):
    """Test legend documents are stored correctly in the index."""
    with patch.object(search_config, '_whoosh_index_dir', str(tmp_path)):
        # Reset client from index caches
        for cache in [
            SearchExtensionsConfig._client_whoosh_index_legends,
            SearchExtensionsConfig._client_whoosh_index_misspellings,
            SearchExtensionsConfig._client_whoosh_index_questions_extensions
        ]:
            if search_config.client_code in cache:
                del cache[search_config.client_code]
        
        search_config.init_search_extensions()
        
        legend_index = whoosh_index.open_dir(os.path.join(tmp_path, "legends"))
        with legend_index.searcher() as searcher:
            docs = list(searcher.documents())
            assert len(docs) == 1
            assert docs[0]['legend'] == "test legend"
            assert docs[0]['keywords'] == "test | keyword"
            assert docs[0]['use_all_parts'] == True
            assert docs[0]['use_all_keywords'] == True

def test_should_add_legend_expansion():
    """Test legend expansion logic with different scenarios."""
    from configuration.base.search_extensions import SearchExtensionsConfig
    
    with patch.object(SearchExtensionsConfig, '__init__', return_value=None):
        config = SearchExtensionsConfig.__new__(SearchExtensionsConfig)
        config.client_code = 'test_client'
        
        # Test case 1: use_all_parts=True with all parts matched
        hit1 = MagicMock()
        hit1.__getitem__.side_effect = lambda key: {
            'use_all_parts': True,
            'use_all_keywords': False,
            'keywords': 'part1 | part2'
        }[key]
        hit1.highlights.return_value = '<b>part1</b> | <b>part2</b>'
        assert config._should_add_expansion(hit1, "query with part1 part2") is True

        # Test case 2: use_all_parts=True but missing a part
        hit2 = MagicMock()
        hit2.__getitem__.side_effect = lambda key: {
            'use_all_parts': True,
            'use_all_keywords': False,
            'keywords': 'part1 | part2'
        }[key]
        hit2.highlights.return_value = '<b>part1</b> | part2'
        assert config._should_add_expansion(hit2, "query with part1") is False

        # Test case 3: use_all_keywords=True with all keywords present
        hit3 = MagicMock()
        hit3.__getitem__.side_effect = lambda key: {
            'use_all_parts': False,
            'use_all_keywords': True,
            'keywords': 'key1 key2'
        }[key]
        hit3.highlights.return_value = '<b>key1</b> <b>key2</b>'
        assert config._should_add_expansion(hit3, "query with key1 key2") is True

        # Test case 4: use_all_keywords=True with missing keyword
        hit4 = MagicMock()
        hit4.__getitem__.side_effect = lambda key: {
            'use_all_parts': False,
            'use_all_keywords': True,
            'keywords': 'key1 key2'
        }[key]
        hit4.highlights.return_value = '<b>key1</b> key2'
        assert config._should_add_expansion(hit4, "query with key1") is False

def test_find_legends(search_config, tmp_path):
    """Test finding legends for a query."""
    with patch.object(search_config, '_whoosh_index_dir', str(tmp_path)):
        # Reset client from index caches
        for cache in [
            SearchExtensionsConfig._client_whoosh_index_legends,
            SearchExtensionsConfig._client_whoosh_index_misspellings,
            SearchExtensionsConfig._client_whoosh_index_questions_extensions
        ]:
            if search_config.client_code in cache:
                del cache[search_config.client_code]
        
        search_config.init_search_extensions()
        
        legend_index = whoosh_index.open_dir(os.path.join(tmp_path, "legends"))
        with legend_index.searcher() as searcher:
            query = QueryParser(
                "keywords",
                schema=legend_index.schema,
                termclass=QuepasaFuzzyTerm,
                group=OrGroup.factory(0.9)
            ).parse("test")
            
            results = list(searcher.search(query))
            assert len(results) == 1
            assert results[0]['legend'] == "test legend"
            assert "test | keyword" in results[0]['keywords'] 