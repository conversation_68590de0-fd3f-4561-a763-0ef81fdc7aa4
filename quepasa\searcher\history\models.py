from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from datetime import datetime

@dataclass
class HistoryItem:
    """Represents a single history item"""
    request: Dict[str, Any]
    ts: datetime
    role: str  # 'user', 'assistant' or 'tool'
    content: str
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    name: Optional[str] = None
    references: Optional[Dict[str, Any]] = None

@dataclass
class HistoryFilter:
    """Filter criteria for retrieving history"""
    user_id: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    roles: Optional[List[str]] = None
    max_items: Optional[int] = None 