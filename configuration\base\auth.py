import os
import hmac
import json
import base64
import hashlib
import binascii
from typing import Optional, List, Dict, Any, Union
from quepasa.searcher.models.request import QuepasaRequest
from src.lib.constants import API_USER_RETRIEVE_ONLY
from .base_config import BaseConfig

from src.lib.logger import <PERSON><PERSON><PERSON>Logger
logger = QuepasaLogger().get_instance(__name__)

class AuthConfig(BaseConfig):
    """Base configuration for API authentication functionality."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
        self._api_version = "1.0"

    def get_authorized_api_users(self) -> List[str]:
        """Get list of authorized API user IDs.
        
        Previously: get_api_users()
        """
        return [
            'default',
            API_USER_RETRIEVE_ONLY
        ] 

    def get_auth_secret_key(self) -> bytes:
        """Get secret key for token signing.
        
        Previously: get_joined_secret_key()
        """
        key_parts = [
            f"QUEPASA_AUTH_VERSION_{self._api_version}",
            self.client_code
        ]
        return bytes("@".join(key_parts), 'utf-8')

    def create_auth_token(self, user_id: str) -> str:
        """Create authentication token for API user.
        
        Previously: generate_auth_token()
        
        Args:
            user_id: API user identifier
            
        Returns:
            Base64 encoded token containing salt and signature
        """
        # Generate random salt
        salt = os.urandom(16)
        
        # Create message from user ID and salt
        message = bytes(user_id, 'utf-8') + salt
        
        # Sign message with secret key
        signature = hmac.new(
            self.get_auth_secret_key(),
            msg=message,
            digestmod=hashlib.sha256
        ).digest()
        
        # Combine salt and signature
        token_bytes = salt + signature
        
        # Encode as base64
        return base64.b64encode(token_bytes).decode('utf-8')

    def validate_auth_token(self, token: str) -> bool:
        """Validate an authentication token.
        
        Previously: verify_auth_token()
        """
        return self.get_authenticated_user(token) is not None

    def get_authenticated_user(self, token: str) -> Optional[str]:
        """Get authenticated user ID from token.
        
        Previously: get_verified_api_user()
        
        Args:
            token: Base64 encoded authentication token
            
        Returns:
            User ID if token is valid, None otherwise
        """
        try:
            # Decode token
            token_bytes = base64.b64decode(token.encode('utf-8'))
            
            # Extract salt and signature
            salt = token_bytes[:16]
            received_signature = token_bytes[16:]
            
            # Try each user's credentials
            for user_id in self.get_authorized_api_users():
                # Recreate message
                message = bytes(user_id, 'utf-8') + salt
                
                # Calculate expected signature
                expected_signature = hmac.new(
                    self.get_auth_secret_key(),
                    msg=message,
                    digestmod=hashlib.sha256
                ).digest()
                
                # Compare signatures
                if hmac.compare_digest(received_signature, expected_signature):
                    return user_id
                    
        except Exception as e:
            logger.error(f"Error getting authenticated user: {e}")
            
        return None

    def get_third_party_auth_keys(self) -> List[str]:
        """Get list of third-party authentication keys.
        
        Previously: get_external_signature_keys()
        """
        return []

    def get_external_user_id(self, token: str) -> Optional[str]:
        """Get external user ID from third-party token.
        
        Previously: get_external_uuid()
        
        Args:
            token: Base64 encoded third-party token
            
        Returns:
            External user ID if token is valid, None otherwise
        """
        try:
            # Decode token
            token_bytes = base64.b64decode(token)
            token_data = json.loads(token_bytes)
            
            # Extract fingerprint and signature
            fingerprint = base64.b64decode(token_data['fingerprint'])
            signature = base64.b64decode(token_data['signature'])
            
            # Try each auth key
            for key in self.get_third_party_auth_keys():
                # Calculate expected signature
                expected_signature = hmac.new(
                    key.encode(),
                    fingerprint,
                    hashlib.sha256
                ).digest()
                
                # Compare signatures
                if hmac.compare_digest(expected_signature, signature):
                    return binascii.hexlify(fingerprint).decode('utf-8')
                    
        except:
            pass
            
        return None

    def validate_external_auth_token(self, token: str) -> bool:
        """Validate a third-party authentication token.
        
        Previously: verify_external_user_auth_token()
        """
        return self.get_external_user_id(token) is not None 