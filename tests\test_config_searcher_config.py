import pytest
from configuration.base.searcher import SearcherConfig
from src.lib.constants import SOURCE_DOCUMENTS

class TestSearcherConfig:
    @pytest.fixture
    def config(self):
        return SearcherConfig("test_client")

    def test_get_search_reranker_model(self, config):
        # Test default behavior
        assert config.get_search_reranker_model(SOURCE_DOCUMENTS) == (None, None)

    def test_get_reranker_confidence_threshold(self, config):
        # Test default behavior
        assert config.get_reranker_confidence_threshold(SOURCE_DOCUMENTS) is None

    def test_get_reranker_confidence_threshold_document(self, config):
        # Test default behavior - should return same as get_reranker_confidence_threshold
        assert config.get_reranker_confidence_threshold_document(SOURCE_DOCUMENTS) == \
               config.get_reranker_confidence_threshold(SOURCE_DOCUMENTS)

    def test_get_reranker_confidence_threshold_chunk(self, config):
        # Test default behavior - should return same as get_reranker_confidence_threshold
        assert config.get_reranker_confidence_threshold_chunk(SOURCE_DOCUMENTS) == \
               config.get_reranker_confidence_threshold(SOURCE_DOCUMENTS) 