from abc import ABC, abstractmethod
from typing import List, Dict, Tuple, Any, Optional, Generator, AsyncGenerator

from .providers import LLMProvider

class BaseLLM(ABC):
    """Base class for LLM providers."""
    
    @property
    @abstractmethod
    def provider(self) -> LLMProvider:
        """The provider enum for this LLM implementation."""
        pass

    @abstractmethod
    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a completion from the LLM.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Returns:
            The generated response text
        """
        pass

    async def get_streaming_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> AsyncGenerator[str, None]:
        """Get a streaming completion from the LLM.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Yields:
            Generated response chunks
            
        Note:
            This is an optional method. If the provider doesn't support streaming,
            the default implementation will yield the complete response.
        """
        yield self.get_answer(model_version, prompt_list, answer_prompt_size, json_mode)

    def get_tools_answer(self, model_version: str, prompt_list: List[Dict[str, str]], tools: List[Dict[str,Any]], answer_prompt_size: int) -> Tuple[str, list]:
        """Get a completion from the LLM with function calling.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            tools: Tools used for Agentic/Function calling
            answer_prompt_size: Maximum response size
            
        Returns:
            The generated response
        """
        return self.get_answer(model_version, prompt_list, answer_prompt_size), []

    async def get_streaming_tools_answer(self, model_version: str, prompt_list: List[Dict[str, str]], tools: List[Dict[str,Any]], answer_prompt_size: int) -> AsyncGenerator[Tuple[str, list], None]:
        """Get a streaming completion from the LLM with function calling.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            tools: Tools used for Agentic/Function calling
            answer_prompt_size: Maximum response size
            
        Yields:
            Generated response chunks with tool calls
        """
        # Default implementation: get non-streaming result and yield it
        content, tool_calls = self.get_tools_answer(model_version, prompt_list, tools, answer_prompt_size)
        yield content, tool_calls