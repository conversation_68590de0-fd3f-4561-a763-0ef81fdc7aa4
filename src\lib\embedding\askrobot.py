import os
import json
import requests
from typing import Optional, List

from .base import BaseEmbedding
from .cache import EmbeddingCacheMixin
from .providers import EmbeddingProvider

class AskRobotEmbedding(BaseEmbedding, EmbeddingCacheMixin):
    def __init__(self):
        super().__init__()

        # Check for API key
        self.api_key = os.getenv('ASK_EMBEDDING_API_KEY')
        if not self.api_key:
            raise ValueError("ASK_EMBEDDING_API_KEY environment variable is not set")

    @property
    def provider(self) -> EmbeddingProvider:
        return EmbeddingProvider.ASKROBOT

    def get_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get embedding from AskRobot API."""
        json_data = {
            "input_data": {
                "columns": [0],
                "index": [0],
                "data": [[{
                    "model": model_version,
                    "text": text
                }]]
            },
            "params": {}
        }

        try:
            response = requests.post(
                'https://ask-embedding.eastus.inference.ml.azure.com/score',
                json=json_data,
                headers={
                    'Content-Type': "application/json",
                    'Authorization': f"Bearer {self.api_key}",
                }
            )
            return json.loads(response.text)
        except Exception:
            return None 