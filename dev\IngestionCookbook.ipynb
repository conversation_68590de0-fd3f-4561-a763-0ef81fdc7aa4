{"cells": [{"cell_type": "markdown", "id": "9a8dc60e", "metadata": {}, "source": ["# NOTICE"]}, {"cell_type": "markdown", "id": "23230d0a", "metadata": {}, "source": ["Please make sure that you have created and updated .env file.\n", "\n", "You are required to set up this API KEYS:\n", "- `OPENAI_API_KEY`\n", "- `NEBIUS_API_KEY`"]}, {"cell_type": "markdown", "id": "3d785018", "metadata": {}, "source": ["## Check API endpoint"]}, {"cell_type": "code", "id": "73c4ea42", "metadata": {}, "source": ["!curl http://localhost:8000"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "6db9f389", "metadata": {}, "source": ["## Check Searcher endpoint"]}, {"cell_type": "code", "id": "7c07af79", "metadata": {}, "source": ["!curl http://localhost:8080/health"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "20d506bb", "metadata": {}, "source": ["## Init"]}, {"cell_type": "code", "id": "3f87e686", "metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:04:16.845374Z", "start_time": "2025-09-08T19:04:16.731183Z"}}, "source": ["import requests\n", "\n", "API_ENDPOINT = \"http://localhost:8000\"\n", "SEARCHER_ENDPOINT = \"http://localhost:8080\"\n", "\n", "CUSTOMER_ID = \"default\"\n", "COLLECTION = \"Test\"\n", "DOMAIN = f\"{CUSTOMER_ID}_{COLLECTION}\"\n", "\n", "CLIENT_KEY = \"testKey\"\n", "YOUR_SECRET_TOKEN = \"default:ujtiDZWTGjqlZ5Aa0ZQy/W9DQ+risY57nASbBA6PZE0Jt4+1gSGBapjfGrUnSZxm\"\n", "\n", "# Expected product IDs from our JSON files\n", "EXPECTED_PRODUCT_IDS = [\"gbibaby_5717058\", \"gbibaby_5712496\"]"], "outputs": [], "execution_count": 1}, {"cell_type": "markdown", "id": "a04c4cdc", "metadata": {}, "source": "### Upload products using the new products endpoint\n\nThis section demonstrates how to ingest products using the new `/data/v1/products/` endpoint. We'll load two product JSON files and upload them with the required headers."}, {"cell_type": "code", "id": "c4a043ab", "metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:04:20.202923Z", "start_time": "2025-09-08T19:04:20.156752Z"}}, "source": "import json\n\n# Load product JSON files\nwith open(\"5717058-hw.json\", \"r\") as f:\n    product1 = json.load(f)\n\nwith open(\"5712496-hw.json\", \"r\") as f:\n    product2 = json.load(f)\n\n# Format the request for the products endpoint\nrequest_payload = {\n    \"records\": [product1, product2]\n}\n\nresponse = requests.post(\n    f\"{API_ENDPOINT}/data/v1/products/{COLLECTION}\",\n    headers={\n        \"Content-Type\": \"application/json\",\n        \"X-Customer-Id\": CUSTOMER_ID,\n        \"Authorization\": f\"client-key {CLIENT_KEY}\",\n    },\n    json=request_payload,\n)\n\nprint(f\"Status Code: {response.status_code}\")\nprint(f\"Response: {response.json()}\")", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Status Code: 200\n", "Response: {'batch_id': '1757358260.250763', 'processed_ids': ['gbibaby_5717058', 'gbibaby_5712496'], 'skip_indexing': False}\n"]}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-09-02T17:55:25.996181Z", "start_time": "2025-09-02T17:55:25.988026Z"}}, "cell_type": "markdown", "source": "### Check batch status", "id": "48da8ae06f73677a"}, {"cell_type": "code", "id": "nxturf2ocr", "source": ["batch_id = response.json()['batch_id']\n", "print(batch_id)\n", "response_batch = requests.get(\n", "    f\"{API_ENDPOINT}/data/v1/batches/{batch_id}\",\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'<PERSON><PERSON> {YOUR_SECRET_TOKEN}',\n", "    },\n", ")\n", "\n", "print( response_batch )\n", "response_batch.json()"], "metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:04:24.740744Z", "start_time": "2025-09-08T19:04:24.700794Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1757358260.250763\n", "<Response [200]>\n"]}, {"data": {"text/plain": ["{'status': 'Batch state: BatchState.DONE',\n", " 'state': 'done',\n", " 'data': {'domain': 'default_Test',\n", "  'processed_ids': ['default_Test:gbibaby_5712496',\n", "   'default_Test:gbibaby_5717058']}}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"cell_type": "markdown", "id": "b3j5nh2c8r4", "source": "### Product Ingestion Results\n\nThe response will contain:\n- `batch_id`: Unique identifier for the batch processing job\n- `processed_ids`: List of product SKUs that were successfully processed\n\nThe products are converted to markdown format and queued for processing in the data pipeline.", "metadata": {}}, {"cell_type": "markdown", "id": "b2ca76b4", "metadata": {"tags": ["List Documents"]}, "source": "### List Products"}, {"metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:04:34.386431Z", "start_time": "2025-09-08T19:04:34.364138Z"}}, "cell_type": "code", "source": "import json as _json\n\n# List all products ingested in the domain\nresponse_list = requests.get(\n  f\"{API_ENDPOINT}/data/v1/products/{COLLECTION}\",\n  headers={\n    \"Content-Type\": \"application/json\",\n    \"X-Customer-Id\": CUSTOMER_ID,\n    \"Authorization\": f\"client-key {CLIENT_KEY}\",\n  },\n)\n\nprint(f\"Status: {response_list.status_code}\")\ntry:\n  response_list.raise_for_status()\n  data = response_list.json()\n  print(_json.dumps(data, indent=2, ensure_ascii=False))\nexcept requests.exceptions.HTTPError as e:\n  print(\"HTTP error:\", e)\n  print(\"Body:\")\n  print(response_list.text)\nexcept ValueError:\n  # Response isn't JSON\n  print(\"Non-JSON response body:\")\n  print(response_list.text)", "id": "69deb3f799079440", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Status: 200\n", "{}\n"]}], "execution_count": 7}, {"cell_type": "markdown", "id": "e2cebfae", "metadata": {}, "source": "### Get Product"}, {"metadata": {"ExecuteTime": {"end_time": "2025-09-03T21:21:23.675013Z", "start_time": "2025-09-03T21:21:23.650062Z"}}, "cell_type": "code", "source": "# Fetch a single indexed document by ID\nproduct_id = EXPECTED_PRODUCT_IDS[0]\nresponse_doc = requests.get(\n    f\"{API_ENDPOINT}/data/v1/products/{COLLECTION}/{product_id}\",\n    headers={\n        \"Content-Type\": \"application/json\",\n        \"X-Customer-Id\": CUSTOMER_ID,\n        \"Authorization\": f\"client-key {CLIENT_KEY}\",\n    },\n)\n\nprint(f\"Status: {response_doc.status_code}\")\ntry:\n    response_doc.raise_for_status()\n    data = response_doc.json()\n    import json as _json\n    print(_json.dumps(data, indent=2, ensure_ascii=False))\nexcept requests.exceptions.HTTPError as e:\n    print(\"HTTP error:\", e)\n    print(\"Body:\")\n    print(response_doc.text)\nexcept ValueError:\n    # Response isn't JSON\n    print(\"Non-JSON response body:\")\n    print(response_doc.text)", "id": "9d439571da0cf9e6", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Status: 200\n", "null\n"]}], "execution_count": 35}, {"cell_type": "markdown", "id": "fa7fbc86", "metadata": {}, "source": ["### Make a RAG request"]}, {"cell_type": "code", "id": "8d4b018d", "metadata": {"scrolled": true, "ExecuteTime": {"end_time": "2025-09-03T21:20:48.047154Z", "start_time": "2025-09-03T21:20:45.159452Z"}}, "source": ["response = requests.post(\n", "    f\"{SEARCHER_ENDPOINT}/retrieve/answer\",\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'<PERSON><PERSON> {YOUR_SECRET_TOKEN}',\n", "    },\n", "    json = {\n", "        'question': \"What NFL products do you have for babies?\",\n", "        'source': \"documents\",\n", "        'domain': DOMAIN,\n", "    }\n", ")\n", "\n", "print(f\"Status: {response.status_code}\")\n", "if response.status_code == 200:\n", "    result = response.json()\n", "    print(f\"Answer: {result}\")\n", "else:\n", "    print(f\"Error: {response.text}\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Status: 200\n", "Answer: {'data': {'data': {'total_documents': 2, 'used_documents': 2}, 'markdown': \"NFL Indianapolis Colts Half Time Long Sleeve Coverall for babies, available in sizes 6\\\\-9 months and 0\\\\-3 months \\\\(out of stock\\\\)\\\\. It’s a cozy, long\\\\-sleeve coverall with the team's logo and colors, made from a cotton blend\\\\. [\\\\[🌐NFL Indianapolis Colts Half Time Long Sleeve Coverall\\\\]](https://gb-housewares.groupby.cloud/product/title/gbibaby_5717058)\", 'metadata': {'llm_params': {'frequency_penalty': 0.0, 'presence_penalty': 0.0, 'temperature': 0.0, 'top_p': 1.0}, 'model': 'Qwen/Qwen2.5-72B-Instruct-fast', 'prompt': [{'content': \"You're a bot-assistant that answers the questions, using the information from the sources below.\\n\\nPerform the following actions in the step by step way.\\n\\nStep 1.\\nAnalyze the sources below.\\n\\nStep 2.\\nSelect the sources that best fits the user's question.\\n\\nStep 3.\\nCheck if you can find an answer to the user's question in the selected sources.\\nIf there is no answer:\\n- say that you can't find the answer;\\n- ask the user to try to reformulate the question;\\n- skip Step 4 and step 5.\\n\\nStep 4.\\nCompose your answer to the question using the following rules:\\n- answer in {{LANGUAGE}} language;\\n- answer briefly in just a few sentences, strictly in accordance with the selected sources, and do not make any assumptions;\\n- reference the source if you use it in the answer, e.g. [#1] or [#2][#4].\\n\\nStep 5.\\nAnalyze the answer compiled in step 4, then retell it briefly without introductory or concluding phrases, keeping all references to sources.\\n\\nOutput the result of step 5.\\n\\nSources:\\n{{SOURCES}}\", 'role': 'system'}, {'content': '{{QUESTION}}', 'role': 'user'}], 'provider': 'nebius', 'request': {'classification': {'intent': 'search', 'language_code': 'en', 'model_version': 'Qwen/Qwen2.5-72B-Instruct-fast', 'related': 'no'}, 'client': 'default', 'domain': 'default_Test', 'engine': 'answer', 'metadata': None, 'now': '2025-09-03T21:20:45.244740', 'protocol': 'http', 'question': 'What NFL products do you have for babies?', 'source': 'documents', 'stream': False, 'ts': **********}}, 'references': {'1': {'created_at': '2025-09-03T21:20:33Z', 'end_position': 2837, 'score': 0.9695992, 'start_position': 0, 'text': \"# NFL Indianapolis Colts Half Time Long Sleeve Coverall\\n**SKU:** gbibaby_5717058\\n**Availability:** IN_STOCK\\n**brands:** NFL\\n**categories:** 32641_Gifts & More, 32641_Gifts & More > 32670_Team Shop, 32670_Team Shop > 32674_NFL\\n**priceInfo:** price: 30\\n**colorInfo:** colorFamilies: Multi, colors: TEAM COLOR\\n**patterns:** Graphic\\n\\n[View product](https://gb-housewares.groupby.cloud/product/title/gbibaby_5717058)\\n![image](https://storage.googleapis.com/groupby-demo-images/housewares/product-images/gbibaby_5717058.jpg)\\n\\n## Description\\nPerfect for bed, snuggle time and morning play, the NFL Half Time Long Sleeve Coverall will keep your little football fan cozy and comfortable. Full-snap design is crafted of cotton interlock and features your team's logo and colors.\\n\\n## Attributes\\n**s_f_Embellishment_multi:** Printed\\n**s_f_Baby_Kids_Theme_multi:** Sports\\n**s_f_Gender:** Boy\\n**FEATURE_VALUE:** Long, Printed, 1, 0-4 Feet, Not exclusive, NFL, Casual, Everyday, Indianapolis Colts, 6 - 12 Months, Coveralls (Baby), Indiana, Long Sleeves, Cotton Blend, Machine wash and dry, Up to 10 lbs, 1 piece set, 90% Cotton/10% Polyester, Knit, 0 - 6 Months, No, Sports, Lightweight (up to 6.9 lbs), Made in Philippines, Boy, Cotton, 1 piece, Snap, Customization not available, Coveralls, Machine Wash\\n**s_f_Fabric_FiberType:** 90% Cotton/10% Polyester\\n**s_f_Holiday_Occasion_multi:** Everyday\\n**SITE_ID:** gbibaby\\n**L1:** Gifts & More\\n**L2:** Team Shop\\n**s_f_Soft_Textiles_Use_and_Care_Instructions:** Machine wash and dry\\n**L3:** NFL\\n**s_f_Product_Type:** Coveralls (Baby)\\n**s_f_Soft_Textiles_Material:** Cotton\\n**BAB_PREORDER_FLAG:** 0\\n**s_f_Made_In:** Made in Philippines\\n**s_f_Closure_Type:** Snap\\n**f_binProduct_Type:** Coveralls\\n**f_Product_Type:** Coveralls (Baby)\\n**s_f_Theme:** NFL\\n\\n## Variants\\n| SKU | title | uri | images | sizes | availability |\\n|-----|-------|-----|--------|-------|--------------|\\n| gbibaby_5717058_69876510 | NFL Indianapolis Colts Size 6-9M Half Time Long Sleeve Coverall | https://gb-housewares.groupby.cloud/product/title/gbibaby_5717058_69876510 | uri: https://storage.googleapis.com/groupby-demo-images/housewares/product-images/gbibaby_5717058_69876510.jpg | 6-9 MONTHS | IN_STOCK |\\n| gbibaby_5717058_69876508 | NFL Indianapolis Colts Size 0-3M Half Time Long Sleeve Coverall | https://gb-housewares.groupby.cloud/product/title/gbibaby_5717058_69876508 | uri: https://storage.googleapis.com/groupby-demo-images/housewares/product-images/gbibaby_5717058_69876508.jpg | 0-3 MONTHS | OUT_OF_STOCK |\\n| gbibaby_5717058_69876509 | NFL Indianapolis Colts Size 3-6M Half Time Long Sleeve Coverall | https://gb-housewares.groupby.cloud/product/title/gbibaby_5717058_69876509 | uri: https://storage.googleapis.com/groupby-demo-images/housewares/product-images/gbibaby_5717058_69876509.jpg | 3-6 MONTHS | OUT_OF_STOCK |\", 'title': 'NFL Indianapolis Colts Half Time Long Sleeve Coverall', 'type': 'document', 'url': 'https://gb-housewares.groupby.cloud/product/title/gbibaby_5717058'}}, 'text': \"NFL Indianapolis Colts Half Time Long Sleeve Coverall for babies, available in sizes 6-9 months and 0-3 months (out of stock). It’s a cozy, long-sleeve coverall with the team's logo and colors, made from a cotton blend. [#1]\", 'think': None, 'type': 'strict.documents'}, 'status': 'ok'}\n"]}], "execution_count": 30}, {"cell_type": "markdown", "id": "r26h6kaz1ae", "source": ["## Product Deletion Examples\n", "\n", "This section demonstrates how to delete products using the new DELETE endpoints, including both single product deletion and batch deletion functionality."], "metadata": {}}, {"cell_type": "markdown", "id": "7djlozil8hw", "source": "### Delete Single Product\n\nFirst, let's delete one product by its ID. We'll delete the first product from our list.", "metadata": {}}, {"cell_type": "code", "id": "ey4<PERSON>ry4cj4", "source": "# Delete the first product by ID\nproduct_to_delete = EXPECTED_PRODUCT_IDS[0]\nprint(f\"Deleting product: {product_to_delete}\")\n\nresponse = requests.delete(\n    f\"{API_ENDPOINT}/data/v1/products/{COLLECTION}/{product_to_delete}\",\n    headers={\n        \"Content-Type\": \"application/json\",\n        \"X-Customer-Id\": CUSTOMER_ID,\n        \"Authorization\": f\"client-key {CLIENT_KEY}\",\n    },\n)\n\nprint(f\"Status Code: {response.status_code}\")\nprint(f\"Response: {response.json()}\")\n\nif response.status_code == 200:\n    delete_result = response.json()\n    print(f\"✅ Single product deletion successful!\")\n    print(f\"Batch ID: {delete_result['batch_id']}\")\n    print(f\"Deleted Product ID: {delete_result['processed_ids'][0]}\")\nelse:\n    print(f\"❌ Product deletion failed with status {response.status_code}\")\n    print(f\"Error: {response.text}\")", "metadata": {"ExecuteTime": {"end_time": "2025-09-03T21:20:54.882730Z", "start_time": "2025-09-03T21:20:54.828066Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Deleting product: gbibaby_5717058\n", "Status Code: 200\n", "Response: {'batch_id': '1756934454.6312', 'processed_ids': ['gbibaby_5717058'], 'skip_indexing': False}\n", "✅ Single product deletion successful!\n", "Batch ID: 1756934454.6312\n", "Deleted Product ID: gbibaby_5717058\n"]}], "execution_count": 31}, {"metadata": {"ExecuteTime": {"end_time": "2025-09-02T17:39:24.088186Z", "start_time": "2025-09-02T17:39:24.056071Z"}}, "cell_type": "markdown", "source": "### Check batch status", "id": "6a3f609a55a6bcef"}, {"metadata": {"ExecuteTime": {"end_time": "2025-09-03T21:21:08.065046Z", "start_time": "2025-09-03T21:21:08.031001Z"}}, "cell_type": "code", "source": ["batch_id = response.json()['batch_id']\n", "print(batch_id)\n", "response_batch = requests.get(\n", "    f\"{API_ENDPOINT}/data/v1/batches/{batch_id}\",\n", "    headers={\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'<PERSON><PERSON> {YOUR_SECRET_TOKEN}',\n", "    },\n", ")\n", "\n", "print(response_batch)\n", "response_batch.json()"], "id": "1534aac3ea6143d5", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1756934454.6312\n", "<Response [200]>\n"]}, {"data": {"text/plain": ["{'status': 'Batch state: BatchState.DONE',\n", " 'state': 'done',\n", " 'data': {'domain': 'default_Test', 'processed_ids': []}}"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "execution_count": 34}, {"metadata": {}, "cell_type": "markdown", "source": "### Go up and invoke List Products and Get Product to verify that the product is deleted", "id": "65b8b3e368e62f2d"}, {"cell_type": "markdown", "id": "ua78f44935k", "source": "### Delete Multiple Products (Batch Deletion)\n\nNow let's delete the remaining product using batch deletion. This endpoint allows you to delete multiple products at once.", "metadata": {}}, {"cell_type": "code", "id": "4s5fel5a9mq", "source": "# Delete the remaining product(s) using batch deletion\nprint(f\"Deleting products: {EXPECTED_PRODUCT_IDS}\")\n\nrequest_payload = {\n    \"productIds\": EXPECTED_PRODUCT_IDS\n}\n\nresponse = requests.delete(\n    f\"{API_ENDPOINT}/data/v1/products/{COLLECTION}\",\n    headers={\n        \"Content-Type\": \"application/json\", \n        \"X-Customer-Id\": CUSTOMER_ID,\n        \"Authorization\": f\"client-key {CLIENT_KEY}\",\n    },\n    json=request_payload,\n)\n\nprint(f\"Status Code: {response.status_code}\")\nprint(f\"Response: {response.json()}\")\n\nif response.status_code == 200:\n    delete_result = response.json()\n    print(f\"✅ Batch product deletion successful!\")\n    print(f\"Batch ID: {delete_result['batch_id']}\")\n    print(f\"Deleted Product IDs: {delete_result['processed_ids']}\")\n    print(f\"Total products deleted: {len(delete_result['processed_ids'])}\")\nelse:\n    print(f\"❌ Batch product deletion failed with status {response.status_code}\")\n    print(f\"Error: {response.text}\")", "metadata": {"ExecuteTime": {"end_time": "2025-09-03T21:21:45.633690Z", "start_time": "2025-09-03T21:21:45.582112Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Deleting products: ['gbibaby_5717058', 'gbibaby_5712496']\n", "Status Code: 200\n", "Response: {'batch_id': '1756934505.658812', 'processed_ids': ['gbibaby_5717058', 'gbibaby_5712496'], 'skip_indexing': False}\n", "✅ Batch product deletion successful!\n", "Batch ID: 1756934505.658812\n", "Deleted Product IDs: ['gbibaby_5717058', 'gbibaby_5712496']\n", "Total products deleted: 2\n"]}], "execution_count": 40}, {"metadata": {"ExecuteTime": {"end_time": "2025-09-02T17:41:10.466209Z", "start_time": "2025-09-02T17:41:10.458575Z"}}, "cell_type": "markdown", "source": "### Check batch status", "id": "e43ab043b7caf956"}, {"metadata": {"ExecuteTime": {"end_time": "2025-09-03T21:21:51.155666Z", "start_time": "2025-09-03T21:21:51.099885Z"}}, "cell_type": "code", "source": ["batch_id = response.json()['batch_id']\n", "print(batch_id)\n", "response_batch = requests.get(\n", "    f\"{API_ENDPOINT}/data/v1/batches/{batch_id}\",\n", "    headers={\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'<PERSON><PERSON> {YOUR_SECRET_TOKEN}',\n", "    },\n", ")\n", "\n", "print(response_batch)\n", "response_batch.json()"], "id": "886eef1f4690f64", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1756934505.658812\n", "<Response [200]>\n"]}, {"data": {"text/plain": ["{'status': 'Batch state: BatchState.DONE',\n", " 'state': 'done',\n", " 'data': {'domain': 'default_Test', 'processed_ids': []}}"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "execution_count": 42}, {"cell_type": "markdown", "id": "i1n0v3e4z6", "source": ["### Verify Deletion Results\n", "\n", "Let's verify that the products have been deleted by listing the products in the domain again."], "metadata": {}}, {"cell_type": "code", "id": "n5lw6ufkork", "source": "# Wait a moment for deletion processing to complete\nimport time\nprint(\"Waiting for deletion processing to complete...\")\ntime.sleep(3)\n\n# List products again to verify deletion\nresponse_list_after = requests.get(\n    f\"{API_ENDPOINT}/data/v1/products/{COLLECTION}\",\n    headers={\n        \"Content-Type\": \"application/json\",\n        \"X-Customer-Id\": CUSTOMER_ID,\n        \"Authorization\": f\"client-key {CLIENT_KEY}\",\n    },\n)\n\nprint(f\"Status: {response_list_after.status_code}\")\ntry:\n    response_list_after.raise_for_status()\n    data_after = response_list_after.json()\n    \n    print(f\"Products remaining in domain '{DOMAIN}':\")\n    if data_after:\n        import json as _json\n        print(_json.dumps(data_after, indent=2, ensure_ascii=False))\n        print(f\"Total products remaining: {len(data_after)}\")\n    else:\n        print(\"✅ No products found - all products have been successfully deleted!\")\n        \nexcept requests.exceptions.HTTPError as e:\n    print(\"HTTP error:\", e)\n    print(\"Body:\")\n    print(response_list_after.text)\nexcept ValueError:\n    print(\"Non-JSON response body:\")\n    print(response_list_after.text)", "metadata": {"ExecuteTime": {"end_time": "2025-09-03T21:22:26.171285Z", "start_time": "2025-09-03T21:22:23.126433Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Waiting for deletion processing to complete...\n", "Status: 200\n", "Products remaining in domain 'default_Test':\n", "{\n", "  \"gbibaby_5712496\": \"ever & ever 3-Piece Swaddle, <PERSON><PERSON>, and <PERSON> Set\"\n", "}\n", "Total products remaining: 1\n"]}], "execution_count": 45}, {"metadata": {}, "cell_type": "code", "source": "", "id": "68774165b6d005e", "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}