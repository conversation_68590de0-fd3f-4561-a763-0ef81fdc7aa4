!curl http://localhost:8000

!curl http://localhost:8080/health

import requests

API_ENDPOINT = "http://localhost:8000"
SEARCHER_ENDPOINT = "http://localhost:8080"

CUSTOMER_ID = "demos"
COLLECTION = "housewares"
DOMAIN = f"{CUSTOMER_ID}_{COLLECTION}"

CLIENT_KEY = "c4122126-ff9d-452b-ae12-ff31cc950070"
YOUR_SECRET_TOKEN = "demos:Y9e9DxaOtSZT42/ks2GoGvAAw7LFMli+mMNa1KbFN3veFNRqIXiBG1SYEY+VKLk1"

# Expected product IDs from our JSON files
EXPECTED_PRODUCT_IDS = ["gbibaby_5717058", "gbibaby_5712496"]

import json

# Load product JSON files
with open("5717058-hw.json", "r") as f:
    product1 = json.load(f)

with open("5712496-hw.json", "r") as f:
    product2 = json.load(f)

# Format the request for the products endpoint
request_payload = {
    "records": [product1, product2]
}

response = requests.post(
    f"{API_ENDPOINT}/data/v1/products/{COLLECTION}",
    headers={
        "Content-Type": "application/json",
        "X-Customer-Id": CUSTOMER_ID,
        "Authorization": f"client-key {CLIENT_KEY}",
    },
    json=request_payload,
)

print(f"Status Code: {response.status_code}")
print(f"Response: {response.json()}")

batch_id = response.json()['batch_id']
print(batch_id)
response_batch = requests.get(
    f"{API_ENDPOINT}/data/v1/batches/{batch_id}",
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {YOUR_SECRET_TOKEN}',
    },
)

print( response_batch )
response_batch.json()

import json as _json

# List all products ingested in the domain
response_list = requests.get(
  f"{API_ENDPOINT}/data/v1/products/{COLLECTION}",
  headers={
    "Content-Type": "application/json",
    "X-Customer-Id": CUSTOMER_ID,
    "Authorization": f"client-key {CLIENT_KEY}",
  },
)

print(f"Status: {response_list.status_code}")
try:
  response_list.raise_for_status()
  data = response_list.json()
  print(_json.dumps(data, indent=2, ensure_ascii=False))
except requests.exceptions.HTTPError as e:
  print("HTTP error:", e)
  print("Body:")
  print(response_list.text)
except ValueError:
  # Response isn't JSON
  print("Non-JSON response body:")
  print(response_list.text)

# Fetch a single indexed document by ID
product_id = EXPECTED_PRODUCT_IDS[0]
response_doc = requests.get(
    f"{API_ENDPOINT}/data/v1/products/{COLLECTION}/{product_id}",
    headers={
        "Content-Type": "application/json",
        "X-Customer-Id": CUSTOMER_ID,
        "Authorization": f"client-key {CLIENT_KEY}",
    },
)

print(f"Status: {response_doc.status_code}")
try:
    response_doc.raise_for_status()
    data = response_doc.json()
    import json as _json
    print(_json.dumps(data, indent=2, ensure_ascii=False))
except requests.exceptions.HTTPError as e:
    print("HTTP error:", e)
    print("Body:")
    print(response_doc.text)
except ValueError:
    # Response isn't JSON
    print("Non-JSON response body:")
    print(response_doc.text)

response = requests.post(
    f"{SEARCHER_ENDPOINT}/conversation",
    headers = {
        'Content-Type': 'application/json',
        'X-Customer-Id': CUSTOMER_ID,
        'Authorization': f'client-key {CLIENT_KEY}',
    },
    json = {
        'message': "What NFL products do you have for babies?",
        "collection": f"{COLLECTION}",
        "area": "Production",
        "sessionId": "session-123",
        "context": {
            "userInfo": {
                "name": "John Smith"
            }
        },
        "options": {
            "includeHistory": True,
            "debug": False
        }
    }
)

print(f"Status: {response.status_code}")
if response.status_code == 200:
    result = response.json()
    print(f"Answer: {result}")
else:
    print(f"Error: {response.text}")

# Delete the first product by ID
product_to_delete = EXPECTED_PRODUCT_IDS[1]
print(f"Deleting product: {product_to_delete}")

response = requests.delete(
    f"{API_ENDPOINT}/data/v1/products/{COLLECTION}/{product_to_delete}",
    headers={
        "Content-Type": "application/json",
        "X-Customer-Id": CUSTOMER_ID,
        "Authorization": f"client-key {CLIENT_KEY}",
    },
)

print(f"Status Code: {response.status_code}")
print(f"Response: {response.json()}")

if response.status_code == 200:
    delete_result = response.json()
    print(f"✅ Single product deletion successful!")
    print(f"Batch ID: {delete_result['batch_id']}")
    print(f"Deleted Product ID: {delete_result['processed_ids'][0]}")
else:
    print(f"❌ Product deletion failed with status {response.status_code}")
    print(f"Error: {response.text}")

batch_id = response.json()['batch_id']
print(batch_id)
response_batch = requests.get(
    f"{API_ENDPOINT}/data/v1/batches/{batch_id}",
    headers={
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {YOUR_SECRET_TOKEN}',
    },
)

print(response_batch)
response_batch.json()

# Delete the remaining product(s) using batch deletion
print(f"Deleting products: {EXPECTED_PRODUCT_IDS}")

request_payload = {
    "productIds": EXPECTED_PRODUCT_IDS
}

response = requests.delete(
    f"{API_ENDPOINT}/data/v1/products/{COLLECTION}",
    headers={
        "Content-Type": "application/json", 
        "X-Customer-Id": CUSTOMER_ID,
        "Authorization": f"client-key {CLIENT_KEY}",
    },
    json=request_payload,
)

print(f"Status Code: {response.status_code}")
print(f"Response: {response.json()}")

if response.status_code == 200:
    delete_result = response.json()
    print(f"✅ Batch product deletion successful!")
    print(f"Batch ID: {delete_result['batch_id']}")
    print(f"Deleted Product IDs: {delete_result['processed_ids']}")
    print(f"Total products deleted: {len(delete_result['processed_ids'])}")
else:
    print(f"❌ Batch product deletion failed with status {response.status_code}")
    print(f"Error: {response.text}")

batch_id = response.json()['batch_id']
print(batch_id)
response_batch = requests.get(
    f"{API_ENDPOINT}/data/v1/batches/{batch_id}",
    headers={
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {YOUR_SECRET_TOKEN}',
    },
)

print(response_batch)
response_batch.json()

# Wait a moment for deletion processing to complete
import time
print("Waiting for deletion processing to complete...")
time.sleep(3)

# List products again to verify deletion
response_list_after = requests.get(
    f"{API_ENDPOINT}/data/v1/products/{COLLECTION}",
    headers={
        "Content-Type": "application/json",
        "X-Customer-Id": CUSTOMER_ID,
        "Authorization": f"client-key {CLIENT_KEY}",
    },
)

print(f"Status: {response_list_after.status_code}")
try:
    response_list_after.raise_for_status()
    data_after = response_list_after.json()
    
    print(f"Products remaining in domain '{DOMAIN}':")
    if data_after:
        import json as _json
        print(_json.dumps(data_after, indent=2, ensure_ascii=False))
        print(f"Total products remaining: {len(data_after)}")
    else:
        print("✅ No products found - all products have been successfully deleted!")
        
except requests.exceptions.HTTPError as e:
    print("HTTP error:", e)
    print("Body:")
    print(response_list_after.text)
except ValueError:
    print("Non-JSON response body:")
    print(response_list_after.text)

