import sys
from typing import Dict, List, Optional

from src.lib.files import Que<PERSON>a<PERSON><PERSON>
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.lib.batch_utils import BatchState, BatchUtils

from .tasks import process_batch

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize files
qp_files = QuepasaFiles()


class CrawlerOrchestrator:
    """Main orchestrator for the crawler system"""
    
    def __init__(self):
        pass
                
    def process_client_batches(self, client_id: str, batch_files: List[Dict]):
        """Process all batches for a client"""
        logger.info(f"[{client_id}] Processing batches: {len(batch_files)}")
        
        for batch_file_obj in batch_files:
            batch_file = batch_file_obj['file']
            _, batch_id = BatchUtils.parse_batch_filename(batch_file)
            
            # Process batch using Celery
            logger.info(f"[{client_id}, {batch_id}] Batch processing started")
            process_batch.apply_async(args=[client_id, batch_id])
    
    def run(self):
        """Main execution method"""
        logger.info(f"Starting crawler orchestrator")
        try:
            # Get batches grouped by client
            batch_files_by_client = BatchUtils.get_batch_files_by_client(BatchState.UPLOADED)
            if not batch_files_by_client:
                logger.info(f"No batches to process")
                return
            
            # Process each client's batches
            for client_id, batch_files in batch_files_by_client.items():
                logger.info(f"[{client_id}] Processing batches: {len(batch_files)}")   
                self.process_client_batches(client_id, batch_files)
            
            logger.info(f"Crawler orchestrator completed successfully")
            
        except Exception as e:
            logger.error(f"Crawler orchestrator failed: {str(e)}", exc_info=True)
            sys.exit(1) 