import os
import copy
from typing import Dict, List, Union, Optional

import requests
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor

from src.lib.files import QuepasaFiles
from src.lib.utils import get_filename_id, get_basepath
from src.lib.batch_utils import BatchUtil<PERSON>, BatchStorage, BatchState

from configuration.main.default import QuepasaConfigurationHub

from datetime import datetime, timezone
import asyncio

from quepasa.config import ALLOWED_MIME_TYPES
from .processors import (
    PDFProcessor,
    DocumentProcessor,
    WebProcessor,
    CSVProcessor,
    PPTXProcessor,
    MarkdownProcessor,
    TXTProcessor,
    YouTubeProcessor,
    TelegramProcessor,
    AudioProcessor,
    VideoProcessor
)

# Import the container executor
from celery import group
from src.lib.celery_app import app

from src.lib.logger import QuepasaLogger

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize QuepasaFiles
qp_files = QuepasaFiles()

# Add import for the new get_language function
from .utils import get_language


@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler-save', 
    name='crawler-save.save_to_backlog',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def save_to_backlog(self, client_id: str, batch_id: str, result: Dict, meta: Dict) -> str:
    """Save processed data to backlog storage and return the path"""
    logger.info(f"[{client_id}, {batch_id}] Saving to backlog: {meta}")
    # logger.info(f"Saving result to backlog: {result}")

    try:
        data = {}
        if result.get('status') == 'success':
            data = result.get('result', {})

        config = QuepasaConfigurationHub.from_client_code(client_id)
        default_language = config.get_fallback_language()
        allowed_languages = list(config.get_language_mapping().keys())

        domain = meta['domain']
        id = meta['id']
        url = meta['url']

        # Generate the file id and path
        file_id = get_basepath(get_filename_id(id))
        backlog_path = f"{BatchUtils.get_storage_dir(BatchStorage.CRAWLER, client_id, domain)}/{file_id}.zlib.json"
        
        # Define a worker function to process a single chunk
        def process_chunk(chunk):
            chunk_copy = chunk.copy()
            if chunk_copy.get('text') and chunk_copy.get('language') is None:
                if len(allowed_languages) > 1:
                    chunk_copy['language'] = get_language(chunk_copy.get('text'), default_language, allowed_languages)
                else:
                    chunk_copy['language'] = default_language
            return chunk_copy

        updated_chunks = []
        chunks = data.get('chunks', [])
        if 'language' in meta and meta['language'] is not None and meta['language'] in allowed_languages:
            for chunk in chunks:
                chunk['language'] = meta['language']
                updated_chunks.append(chunk)

        elif len(chunks) > 8:
            # Process chunks in parallel using a thread pool with max 4 workers
            with ThreadPoolExecutor(max_workers=min(4, len(chunks))) as executor:
                updated_chunks = list(executor.map(process_chunk, chunks))

        else:
            updated_chunks = [process_chunk(chunk) for chunk in chunks]
        
        # Ensure the structure matches the required format
        storage_data = {
            'id': id,
            'url': url,
            'title': data.get('title', ''),
            'domain': domain,
            'access': meta.get('access'),
            'keywords': data.get('keywords', ''),
            'chunks': updated_chunks
        }
        
        qp_files.set_json_zlib(backlog_path, storage_data)
        logger.info(f"[{client_id}, {batch_id}] Saved processed data to {backlog_path}")
        return backlog_path
        
    except Exception as e:
        logger.error(f"[{client_id}, {batch_id}] Failed to save data to backlog: {str(e)}", exc_info=True)
        raise

# Task definitions
@app.task(
    bind=True, 
    max_retries=3,
    queue='crawler',
    name='crawler.process_batch',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_batch(self, client_id: str, batch_id: str) -> Dict:
    """Process a batch of URLs, files and Telegram channels"""
    batch_file = BatchUtils.get_batch_filename(client_id, batch_id, BatchState.UPLOADED)
    if not qp_files.exists(batch_file):
        logger.error(f"[{client_id}, {batch_id}] Batch file {batch_file} does not exist")
        return {
            'status': 'error',
            'message': f"Batch file {batch_file} does not exist"
        }

    try:
        batch = qp_files.get_json_zlib(batch_file)
        if 'domain' not in batch:
            raise ValueError("'domain' is required in batch")
        
        domain = batch.get('domain')
        access = batch.get('access')
        language = batch.get('language')
        action = batch.get('action')
        skip_indexing = batch.get('skip_indexing', False)
        
        logger.info(f"[{client_id}, {batch_id}] Running process_batch for domain={domain}, action={action}")

        # Process URLs and files in parallel
        url_tasks = []
        if 'urls' in batch and batch['urls']:
            logger.info(f"[{client_id}, {batch_id}] Creating URL tasks: {batch['urls']}")
            url_tasks = []
            for url_object in batch['urls']:
                url = url_object.get('url') if isinstance(url_object, dict) else url_object
                id = url_object.get('id') if isinstance(url_object, dict) and 'id' in url_object else url
                language = url_object.get('language') if isinstance(url_object, dict) and 'language' in url_object else language

                meta = {
                    'client_id': client_id,
                    'domain': domain,
                    'access': access,
                    'language': language,
                    'id': id,
                    'url': url
                }
                
                # Handle YouTube URLs separately
                if (
                    url.lower().startswith('https://youtu.be/')
                    or url.lower().startswith('https://youtube.com/')
                ):
                    url_tasks.append(process_youtube.s(client_id, batch_id, url, meta))
                
                elif url.lower().startswith('https://t.me/'):
                    url_tasks.append(process_telegram.s(client_id, batch_id, url, meta))
                
                else:
                    url_tasks.append(process_url.s(client_id, batch_id, url, batch, meta))
        
        file_tasks = []
        if 'files' in batch and batch['files']:
            logger.info(f"[{client_id}, {batch_id}] Creating file tasks: {batch['files']}")
            file_tasks = [process_file.s(client_id, batch_id, file, batch) for file in batch['files']]
        
        # Create a group of all tasks
        all_tasks = group(url_tasks + file_tasks)
        logger.info(f"[{client_id}, {batch_id}] Created task group with {len(url_tasks)} URL tasks and {len(file_tasks)} file tasks")
        
        # Update the workflow to include save_batch_results
        skip_indexing = batch.get('skip_indexing', False)
        workflow = (
            all_tasks | 
            collect_results.s(client_id, batch_id) | 
            save_results.s(client_id, batch_id, domain, action, skip_indexing)
        )
        async_result = workflow.apply_async()
        
        return {
            'client_id': client_id,
            'task_id': async_result.id,
            'status': 'pending',
            'started_at': datetime.now(timezone.utc).isoformat(),
            'stats': {
                'total_tasks': len(url_tasks) + len(file_tasks),
                'url_tasks': len(url_tasks),
                'file_tasks': len(file_tasks)
            }
        }
        
    except Exception as exc:
        logger.error(f"[{client_id}, {batch_id}] Batch processing failed: {str(exc)}", exc_info=True)
        # Add task context to retry
        retry_context = {
            'client_id': batch.get('client_id'),
            'domain': batch.get('domain'),
            'attempt': self.request.retries + 1
        }
        self.retry(exc=exc, countdown=3, kwargs={'retry_context': retry_context})

@app.task(
    bind=True,
    max_retries=3,
    queue='crawler-save',
    name='crawler-save.collect_results'
)
def collect_results(self, results, client_id: str, batch_id: str) -> Dict:
    """Collect and process results from the group of tasks"""
    logger.info(f"[{client_id}, {batch_id}] Collecting results")
    
    # Filter out None results and format errors
    processed_results = []
    success_count = 0
    error_count = 0
    
    for result in results:
        if isinstance(result, Exception):
            error_count += 1
            processed_results.append({
                'error': str(result),
                'status': 'error'
            })

        elif result is not None:
            # Check if result is a list
            if isinstance(result, list):
                # Process each item in the list
                for item in result:
                    if isinstance(item, dict):
                        if item.get('status') == 'success':
                            success_count += 1
                        else:
                            error_count += 1

                        processed_results.append(item)

                    else:
                        error_count += 1
                        processed_results.append({
                            'error': f"Invalid result item: {item}",
                            'status': 'error'
                        })
            else:
                # Process a single result dictionary
                if result.get('status') == 'success':
                    success_count += 1
                else:
                    error_count += 1
                    
                processed_results.append(result)
    
    logger.info(f"[{client_id}, {batch_id}] Collected results, success={success_count}, error={error_count}")
    return {
        'client_id': client_id,
        'results': processed_results,
        'status': 'success',
        'completed_at': datetime.now(timezone.utc).isoformat(),
        'stats': {
            'total': len(processed_results),
            'success': success_count,
            'error': error_count
        }
    }

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_url',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_url(self, client_id: str, batch_id: str, url: str, batch: Dict, meta: Dict) -> Union[Dict, List[Dict]]:
    """Process a single URL"""
    id = meta.get('id')
    language = meta.get('language')

    try:
        logger.info(f"[{client_id}, {batch_id}] Processing URL: {url}")
        meta = {
            'client_id': batch['client_id'],
            'domain': batch['domain'],
            'access': batch['access'] if 'access' in batch else None,
            'language': language,
            'id': id,
            'url': url
        }
        
        response = requests.get(url, timeout=30, allow_redirects=True)
        response.raise_for_status()
        
        content_type = response.headers.get('Content-Type', '').lower()
        filename = url.split('/')[-1] or urlparse(url).netloc
        logger.info(f"Got content type: {content_type}, filename: {filename}")
        
        # Common metadata
        meta['filename'] = filename
        
        # Determine processor based on content type
        if 'application/pdf' in content_type:
            backlog_path = process_pdf(client_id, batch_id, response.content, meta)
            
        elif 'text/html' in content_type:
            backlog_path = process_web(client_id, batch_id, response.content, meta)
            
        elif 'text/csv' in content_type:
            backlog_path = process_csv(client_id, batch_id, response.content, meta)
            
        elif 'text/plain' in content_type:
            backlog_path = process_txt(client_id, batch_id, response.content, meta)
            
        elif any(mime_type in content_type for mime_type in [
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ]):
            for mime_type, ext in ALLOWED_MIME_TYPES.items():
                if mime_type in content_type:
                    meta['extension'] = f".{ext}"
                    logger.info(f"[{client_id}, {batch_id}] Processing as document with extension: {ext}")
                    backlog_path = process_document(client_id, batch_id, response.content, meta)
                    break
                    
        elif any(mime_type in content_type for mime_type in [
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        ]):
            for mime_type, ext in ALLOWED_MIME_TYPES.items():
                if mime_type in content_type:
                    meta['extension'] = f".{ext}"
                    logger.info(f"[{client_id}, {batch_id}] Processing as presentation with extension: {ext}")
                    backlog_path = process_pptx(client_id, batch_id, response.content, meta)
                    break
                    
        elif content_type.startswith('audio/'):
            for mime_type, ext in ALLOWED_MIME_TYPES.items():
                if mime_type in content_type:
                    meta['extension'] = f".{ext}"
                    logger.info(f"[{client_id}, {batch_id}] Processing as audio with extension: {ext}")
                    backlog_path = process_audio(client_id, batch_id, response.content, meta)
                    break

        elif content_type.startswith('video/'):
            for mime_type, ext in ALLOWED_MIME_TYPES.items():
                if mime_type in content_type:
                    meta['extension'] = f".{ext}"
                    logger.info(f"[{client_id}, {batch_id}] Processing as video with extension: {ext}")
                    backlog_path = process_video(client_id, batch_id, response.content, meta)
                    break
                    
        elif 'text/markdown' in content_type:
            backlog_path = process_markdown(client_id, batch_id, response.content, meta)
            
        else:
            logger.warning(f"[{client_id}, {batch_id}] Unsupported content type: {content_type}")
            return {
                'id': id,
                'error': f"Unsupported content type: {content_type}",
                'status': 'error',
            }
            
        return {
            'id': id,
            'file': backlog_path,
            'status': 'success'
        }
            
    except Exception as exc:
        logger.error(f"[{client_id}, {batch_id}] URL processing failed: {str(exc)}", exc_info=True)
        return {
            'id': id,
            'error': str(exc),
            'status': 'error'
        }

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_file',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_file(self, client_id: str, batch_id: str, file_path: str, batch: Dict) -> Dict:
    """Process a single file"""
    filename = os.path.basename(file_path)
    id = filename  # f"{batch['domain']}:{filename}"
    try:
        logger.info(f"[{client_id}, {batch_id}] Processing file: {file_path}")

        # Common metadata
        meta = {
            'client_id': batch['client_id'],
            'domain': batch['domain'],
            'access': batch['access'] if 'access' in batch else None,
            'language': batch['language'] if 'language' in batch else None,
            'id': id,
            'url': f"file://{batch['domain']}/{filename}",
            'filename': filename
        }
        
        # Get file content
        try:
            content = qp_files.get_data(file_path)

        except Exception as e:
            logger.error(f"[{client_id}, {batch_id}] File processing failed: {str(e)}", exc_info=True)
            return {
                'id': id,
                'error': str(e),
                'status': 'error'
            }
        
        # Determine processor based on file extension
        ext = os.path.splitext(file_path)[1].lower()
        
        if ext == '.pdf':
            backlog_path = process_pdf(client_id, batch_id, content, meta)
            
        elif ext in ['.doc', '.docx', '.xls', '.xlsx']:
            meta['extension'] = ext
            backlog_path = process_document(client_id, batch_id, content, meta)
            
        elif ext in ['.ppt', '.pptx']:
            meta['extension'] = ext
            backlog_path = process_pptx(client_id, batch_id, content, meta)
            
        elif ext == '.csv':
            backlog_path = process_csv(client_id, batch_id, content, meta)
            
        elif ext in ['.htm', '.html']:
            backlog_path = process_web(client_id, batch_id, content, meta)
            
        elif ext in ['.md', '.markdown']:
            backlog_path = process_markdown(client_id, batch_id, content, meta)
            
        elif ext == '.txt':
            backlog_path = process_txt(client_id, batch_id, content, meta)
            
        elif ext in ['.mp3', '.wav', '.m4a', '.ogg', '.flac', '.aac']:
            meta['extension'] = ext
            backlog_path = process_audio(client_id, batch_id, content, meta)
            
        elif ext in ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv']:
            meta['extension'] = ext
            backlog_path = process_video(client_id, batch_id, content, meta)
            
        else:
            logger.warning(f"[{client_id}, {batch_id}] Unsupported file extension: {ext}")
            return {
                'id': id,
                'error': f"Unsupported file extension: {ext}",
                'status': 'error'
            }
            
        return {
            'id': id,
            'file': backlog_path,
            'status': 'success'
        }
            
    except Exception as exc:
        logger.error(f"[{client_id}, {batch_id}] File processing failed: {str(exc)}", exc_info=True)
        return {
            'id': id,
            'error': str(exc),
            'status': 'error'
        }

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_pdf',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_pdf(self, client_id: str, batch_id: str, content: bytes, meta: Dict) -> str:
    """Process PDF content"""
    logger.info(f"[{client_id}, {batch_id}] Processing as PDF")

    processor = PDFProcessor()
    result = processor.process(content, meta)
    return save_to_backlog(client_id, batch_id, result, meta)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_document',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_document(self, client_id: str, batch_id: str, content: bytes, meta: Dict) -> str:
    """Process Office document content"""
    logger.info(f"[{client_id}, {batch_id}] Processing as Microsoft Word or Excel")

    processor = DocumentProcessor()
    result = processor.process(content, meta)
    return save_to_backlog(client_id, batch_id, result, meta)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_web',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_web(self, client_id: str, batch_id: str, content: bytes, meta: Dict) -> str:
    """Process web content"""
    logger.info(f"[{client_id}, {batch_id}] Processing as HTML")

    processor = WebProcessor()
    result = processor.process(content, meta)
    return save_to_backlog(client_id, batch_id, result, meta)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_csv',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_csv(self, client_id: str, batch_id: str, content: bytes, meta: Dict) -> str:
    """Process CSV content"""
    logger.info(f"[{client_id}, {batch_id}] Processing as CSV")

    processor = CSVProcessor()
    result = processor.process(content, meta)
    return save_to_backlog(client_id, batch_id, result, meta)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_pptx',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_pptx(self, client_id: str, batch_id: str, content: bytes, meta: Dict) -> str:
    """Process PowerPoint content"""
    logger.info(f"[{client_id}, {batch_id}] Processing as Microsoft PowerPoint")

    processor = PPTXProcessor()
    result = processor.process(content, meta)
    return save_to_backlog(client_id, batch_id, result, meta)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_markdown',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_markdown(self, client_id: str, batch_id: str, content: bytes, meta: Dict) -> str:
    """Process Markdown content"""
    logger.info(f"[{client_id}, {batch_id}] Processing as Markdown")

    processor = MarkdownProcessor()
    result = processor.process(content, meta)
    return save_to_backlog(client_id, batch_id, result, meta)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_txt',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_txt(self, client_id: str, batch_id: str, content: bytes, meta: Dict) -> str:
    """Process plain text content"""
    logger.info(f"[{client_id}, {batch_id}] Processing as plain text")

    processor = TXTProcessor(client_id)
    result = processor.process(content, meta)
    return save_to_backlog(client_id, batch_id, result, meta)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_youtube',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_youtube(self, client_id: str, batch_id: str, url: str, meta: Dict) -> str:
    """Process YouTube video"""
    logger.info(f"[{client_id}, {batch_id}] Processing as YouTube video")

    processor = YouTubeProcessor()
    result = processor.process(url, meta)
    backlog_path = save_to_backlog(client_id, batch_id, result, meta)
    return {
        'id': url, 
        'file': backlog_path,
        'status': 'success'
    }

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_audio',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_audio(self, client_id: str, batch_id: str, content: bytes, meta: Dict) -> str:
    """Process audio file and extract transcription"""
    logger.info(f"[{client_id}, {batch_id}] Processing as audio file")

    result = AudioProcessor()._process_impl(content, meta)
    return save_to_backlog(client_id, batch_id, {'status': 'success', 'result': result}, meta)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler', 
    name='crawler.process_video',
    time_limit=3600,  # 1 hour
    soft_time_limit=3300
)
def process_video(self, client_id: str, batch_id: str, content: bytes, meta: Dict) -> str:
    """Process video file and extract transcription"""
    logger.info(f"[{client_id}, {batch_id}] Processing as video file")
    
    result = VideoProcessor()._process_impl(content, meta)
    return save_to_backlog(client_id, batch_id, {'status': 'success', 'result': result}, meta)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='crawler-telegram', 
    name='crawler-telegram.process_telegram',
    time_limit=43200,  # 12 hours    
    soft_time_limit=41400
)
def process_telegram(self, client_id: str, batch_id: str, url: str, meta: Dict) -> Union[Dict, List[Dict]]:
    """Process a single Telegram channel"""
    try:
        logger.info(f"[{client_id}, {batch_id}] Processing Telegram: {url}")
        
        # Process the channel using the TelegramProcessor
        processor = TelegramProcessor()
        
        # Run the async process method inside an event loop
        loop = asyncio.get_event_loop()
        if loop.is_closed():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        try:
            # Properly await the async process method
            result = loop.run_until_complete(processor.process(url, meta))

        finally:
            # Only close if we created a new one
            if loop != asyncio.get_event_loop():
                loop.close()
        
        # If processing was successful, save to backlog
        if result.get('status') == 'success':
            documents = result.get('results', [])

            output = []
            for doc in documents:
                doc_meta = copy.deepcopy(meta)
                doc_meta['id'] = doc.get('id')
                doc_meta['url'] = doc.get('url')
                output.append({
                    'id': doc.get('id'),
                    'file': save_to_backlog(
                        client_id,
                        batch_id,
                        {
                            'status': 'success',
                            'result': doc
                        },
                        doc_meta
                    ),
                    'status': 'success'
                })
            
            return output
        
        else:
            return {
                'id': url,
                'error': result.get('error', 'Unknown error'),
                'status': 'error'
            }
        
    except Exception as exc:
        logger.error(f"[{client_id}, {batch_id}] Telegram processing failed: {str(exc)}", exc_info=True)
        return {
            'id': url,
            'error': str(exc),
            'status': 'error'
        }

@app.task(
    bind=True,
    max_retries=3,
    queue='crawler-save',
    name='crawler-save.save_results'
)
def save_results(self, data_result: Dict, client_id: str, batch_id: str, domain: str, action: str, skip_indexing: bool) -> Dict:
    """Save the final batch results to appropriate storage location"""
    try:
        logger.info(f"[{client_id}, {batch_id}] Saving batch results for domain={domain}, action={action}, skip_indexing={skip_indexing}")
        
        new_batch_data = {
            'client_id': client_id,
            'domain': domain,
            'action': action,
            'processed_ids': [],
            'documents': [],
            'skip_indexing': skip_indexing
        }

        for item in data_result.get('results', []):
            if item.get('status') == 'success':
                new_batch_data['processed_ids'].append(f"{domain}:{item.get('id')}")
                new_batch_data['documents'].append(item.get('file'))

        logger.info(f"[{client_id}, {batch_id}] New batch data generated")

        original_batch_file = BatchUtils.get_batch_filename(client_id, batch_id, BatchState.UPLOADED)
        if qp_files.exists(original_batch_file):
            qp_files.delete_file(original_batch_file)

        if len(new_batch_data['documents']) > 0:
            backlog_batch_file = BatchUtils.get_batch_filename(client_id, batch_id, BatchState.BACKLOG)
            qp_files.set_json_zlib(backlog_batch_file, new_batch_data)
            
            # Always add data-processor task, regardless of skip_indexing
            BatchUtils.add_task('data-processor', client_id, batch_id)
            logger.info(f"[{client_id}, {batch_id}] Added to data-processor queue, skip_indexing={skip_indexing}")

        else:
            failed_batch_file = BatchUtils.get_batch_filename(client_id, batch_id, BatchState.FAILED)
            qp_files.set_json_zlib(failed_batch_file, new_batch_data)

        logger.info(f"[{client_id}, {batch_id}] Batch processed successfully, action={action}, processed_ids={len(new_batch_data['processed_ids'])}")
        return {
            'status': 'success',
            'client_id': client_id,
            'batch_id': batch_id,
            'processed_count': len(new_batch_data['documents']),
            'state': BatchState.BACKLOG if len(new_batch_data['documents']) > 0 else BatchState.FAILED,
            'skip_indexing': skip_indexing
        }

    except Exception as e:
        logger.error(f"[{client_id}, {batch_id}] Error saving batch results: {str(e)}", exc_info=True)
        raise