import os
os.environ['QUEPASA_ENV'] = 'test'  # Mock ENV before importing config

import hashlib
from pathlib import Path
from src.lib.files import QuepasaFiles
from quepasa.data_processor.tasks import process_batch_documents, app
from src.lib.batch_utils import CRAWLER_STORAGE, DATA_PROCESSOR_STORAGE, BatchAction
from src.lib.utils import get_filename_id, get_basepath

qp_files = QuepasaFiles(
    bucket_name='quepasa-files',
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin',
    debug_flag=False
)

# Client ID for testing
TEST_CLIENT_ID = 'test_data_processor_batch_integration_sandbox'
TEST_DOMAIN = 'test'

DOMAIN_STR = hashlib.md5(str(TEST_DOMAIN).encode('utf-8')).hexdigest()
DATA_OUT_DIR = f"{DATA_PROCESSOR_STORAGE}/{TEST_CLIENT_ID}/{DOMAIN_STR}"

# Configure Celery for testing
app.conf.task_always_eager = True

doc2_id = 'doc2'
file2_id = get_basepath(get_filename_id(doc2_id))
doc2_file_path = f"{CRAWLER_STORAGE}/{TEST_CLIENT_ID}/{DOMAIN_STR}/{file2_id}.zlib.json"
qp_files.set_json_zlib(doc2_file_path, {
    'id': doc2_id,
    'url': 'https://example.com/doc2',
    'title': 'Sample Document 2',
    'keywords': 'test, another, document',
    'chunks': [
        {
            'language': 'en',
            'keywords': 'introduction',
            'position': "line 1",
            'text': 'This is the first chunk of document 2. Testing different content.'
        },
        {
            'language': 'es',
            'keywords': 'contenido',
            'position': "line 2",
            'text': 'Este es el segundo fragmento del documento 2. Probando contenido en español.'
        }
    ]
})

test_batch = {
    'client_id': TEST_CLIENT_ID,
    'domain': TEST_DOMAIN,
    'action': BatchAction.UPSERT,
    'documents': [
        {
            'id': 'doc1',
            'url': 'https://example.com/doc1',
            'title': 'Sample Document 1',
            'keywords': 'test, sample, document',
            'chunks': [
                {
                    'language': 'en',
                    'keywords': 'introduction, overview',
                    'position': "line 1",
                    'text': 'This is the first chunk of document 1. It contains sample text for testing.'
                },
                {
                    'language': 'en',
                    'keywords': 'details, content',
                    'position': "line 2",
                    'text': 'This is the second chunk of document 1. It contains more sample text for testing.'
                }
            ]
        },
        doc2_file_path
    ]
}

def test_batch_processing():
    """Test batch document processing"""
    print("Test batch configuration:")
    print(f"Client ID: {test_batch['client_id']}")
    print(f"Domain: {test_batch['domain']}")
    print(f"Action: {test_batch['action']}")
    print(f"Number of documents: {len(test_batch['documents'])}")
    print("\nStarting batch processing...")
    
    # Process the batch
    result = process_batch_documents.apply(args=[test_batch]).get()
    
    # Print results
    print("\nBatch processing results:")
    print("-" * 50)
    print(f"Client ID: {result['client_id']}")
    print(f"Status: {result['status']}")
    print(f"Completed at: {result['completed_at']}")
    print(f"Total tasks: {result['stats']['total_tasks']}")
    print(f"Processed tasks: {result['stats']['processed_tasks']}")
    print(f"Processed documents: {len(result['results'])}")

    print("\nProcessed document IDs:")
    for doc_result in result['results']:
        print(f"doc_result: {doc_result}")
        doc_id = doc_result['id']
        print(f"- {doc_id} (Status: {doc_result['status']})")
        
        # Verify document was saved
        filename_id = get_filename_id(doc_id)
        document_file = f"{DATA_OUT_DIR}/{get_basepath(filename_id)}.zlib.json"
        
        if qp_files.exists(document_file):
            doc = qp_files.get_json_zlib(document_file)
            print(f"  Title: {doc['title']}")
            print(f"  Number of chunks: {len(doc['chunks'])}")
            print(f"  Languages: {doc['languages']}")
        else:
            print(f"  Warning: Document file not found at {document_file}")
        
        print("-" * 30)

    print()

    # Check if all files were removed and meta files created
    files = qp_files.get_files(DATA_OUT_DIR, True)
    print(f"Files: {files}")

def test_batch_delete():
    """Test batch document deletion"""
    # Process the batch
    result = process_batch_documents.apply(args=[test_batch]).get()

    # Print results
    print("\nBatch processing results:")
    print("-" * 50)
    print(f"Processed documents: {len(result['results'])}")
    
    # Now create a delete batch
    delete_batch = {
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': BatchAction.DELETE,
        'documents': [{'id': 'doc1'}]  # Delete first document
    }
    
    print("\nTesting document deletion:")
    print(f"Deleting document: {delete_batch['documents'][0]['id']}")
    
    # Process delete batch
    result = process_batch_documents.apply(args=[delete_batch]).get()
    
    # Verify deletion
    print("\nDeletion results:")
    print("-" * 50)
    print(f"Client ID: {result['client_id']}")
    print(f"Status: {result['status']}")
    print(f"Processed deletions: {len(result['results'])}")
    
    for doc_result in result['results']:
        doc_id = doc_result['id']
        print(f"- {doc_id} (Status: {doc_result['status']})")

        filename_id = get_filename_id(doc_id)
        document_file = f"{DATA_OUT_DIR}/{get_basepath(filename_id)}.zlib.json"
        document_meta_file = f"{DATA_OUT_DIR}/{get_basepath(filename_id)}.meta.json"
        
        print(f"\nDocument {doc_id}:")
        print(f"- Data file exists: {qp_files.exists(document_file)}")
        
        if qp_files.exists(document_meta_file):
            meta = qp_files.get_json(document_meta_file)
            print(f"- Meta status: {meta['status']}")
        else:
            print("- Meta file not found")

    print()

    # Check if all files were removed and meta files created
    files = qp_files.get_files(DATA_OUT_DIR, True)
    print(f"Files: {files}")

def test_batch_reset():
    """Test batch reset functionality"""
    # Process the batch
    result = process_batch_documents.apply(args=[test_batch]).get()

    # Print results
    print("\nBatch processing results:")
    print("-" * 50)
    print(f"Processed documents: {len(result['results'])}")
    
    # Create reset batch
    reset_batch = {
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': BatchAction.RESET
    }
    
    print("\nTesting batch reset:")
    print("Resetting all documents")
    
    # Process reset batch
    result = process_batch_documents.apply(args=[reset_batch]).get()
    
    # Verify reset
    print("\nReset results:")
    print("-" * 50)
    print(f"Client ID: {result['client_id']}")
    print(f"Status: {result['status']}")
    print(f"Processed resets: {len(result['results'])}")

    for doc_result in result['results']:
        doc_id = doc_result['id']
        print(f"- {doc_id} (Status: {doc_result['status']})")

        filename_id = get_filename_id(doc_id)
        document_file = f"{DATA_OUT_DIR}/{get_basepath(filename_id)}.zlib.json"
        document_meta_file = f"{DATA_OUT_DIR}/{get_basepath(filename_id)}.meta.json"
        
        print(f"\nDocument {doc_id}:")
        print(f"- Data file exists: {qp_files.exists(document_file)}")
        
        if qp_files.exists(document_meta_file):
            meta = qp_files.get_json(document_meta_file)
            print(f"- Meta status: {meta['status']}")
        else:
            print("- Meta file not found")

    print()
    
    # Check if all files were removed and meta files created
    files = qp_files.get_files(DATA_OUT_DIR, True)
    print(f"Files: {files}")
    
    data_files = [f for f in files if '.zlib.json' in f]
    meta_files = [f for f in files if '.meta.json' in f]
    
    print(f"\nRemaining data files: {len(data_files)}")
    print(f"Meta files: {len(meta_files)}")
    
    for meta_file in meta_files:
        meta = qp_files.get_json(meta_file)
        print(f"\nDocument {meta['id']}:")
        print(f"- Status: {meta['status']}")

if __name__ == '__main__':
    # Run tests
    print("=== Testing Batch Processing ===")
    test_batch_processing()
    print()
    print()
    print()
    
    print("\n=== Testing Batch Deletion ===")
    test_batch_delete()
    print()
    print()
    print()
    
    print("\n=== Testing Batch Reset ===")
    test_batch_reset() 