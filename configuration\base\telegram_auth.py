from typing import Dict, Any, Union
from quepasa.searcher.models.request import QuepasaRequest
from .telegram_utils import TelegramUtils

class TelegramAuthConfig(TelegramUtils):
    """Base configuration for Telegram-specific authentication."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def is_telegram_user_authorized(self, user_info: Dict[str, Any]) -> bool:
        """Check if Telegram user is authorized.
        
        Previously: is_authorized_telegram_user()
        
        Args:
            user_info: User information from Telegram
            
        Returns:
            True if user is authorized, False otherwise
        """
        # Get Telegram config
        config = self._get_telegram_config()
        
        # Check if there are authorized usernames configured
        if (config and 'user_names' in config and 
            isinstance(config['user_names'], list) and
            len(config['user_names']) > 0):
            
            # Check if user's username is in authorized list
            return (
                'user_name' in user_info and
                user_info['user_name'] and
                user_info['user_name'] in config['user_names']
            )
            
        # If no authorized usernames configured, allow all users
        return True 