from typing import Dict, Optional, Type, Union

from .base import BaseWhisper
from .providers import WhisperProvider
from .replicate import ReplicateWhisper

class WhisperFactory:
    """Factory for creating Whisper transcription provider instances"""
    
    # Cache of provider instances
    _providers: Dict[WhisperProvider, Type[BaseWhisper]] = {
        WhisperProvider.REPLICATE: ReplicateWhisper,
    }
        
    @classmethod
    def get_whisper(cls, provider: Union[str, WhisperProvider]) -> BaseWhisper:
        """Get an Whisper instance for the specified provider.
            
        Args:
            provider: The Whisper provider (string or enum)
            
        Returns:
            An instance of the specified Whisper provider
            
        Raises:
            ValueError: If the provider is not supported
        """
        if isinstance(provider, str):
            provider = WhisperProvider.from_str(provider)
            
        if provider not in cls._providers:
            raise ValueError(f"Unknown Whisper provider: {provider}. "
                           f"Supported providers: {[p.value for p in cls._providers.keys()]}")
        
        return cls._providers[provider]()

def get_whisper(provider: Union[str, WhisperProvider]) -> BaseWhisper:
    """Convenience function to get an Whisper instance for the specified provider.
    
    Args:
        provider: The Whisper provider (string or enum)
        
    Returns:
        An instance of the specified Whisper provider
        
    Raises:
        ValueError: If the provider is not supported
    """
    return WhisperFactory.get_whisper(provider) 