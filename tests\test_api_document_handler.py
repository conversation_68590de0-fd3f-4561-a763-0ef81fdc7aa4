import os
import json
import zlib
import pytest
from unittest.mock import Mock, MagicMock, patch
from src.lib.files import QuepasaFiles
from src.lib.batch_utils import BatchUtils, BatchState
from quepasa.api.handlers.document_handler import DocumentHandler
import hashlib

# Set environment variables for testing
os.environ['CELERY_BROKER_URL'] = 'redis://localhost:6379/0'
os.environ['CELERY_RESULT_BACKEND'] = 'redis://localhost:6379/0'
os.environ['MINIO_HOST'] = 'test'
os.environ['MINIO_PORT'] = '9000'
os.environ['MINIO_ACCESS_KEY'] = 'test'
os.environ['MINIO_SECRET_KEY'] = 'test'
os.environ['MINIO_BUCKET_NAME'] = 'test-bucket'

os.environ['REDIS_HOST'] = 'localhost'
os.environ['REDIS_PORT'] = '6379'

import pytest
from unittest.mock import Mock, patch, MagicMock
from quepasa.api.handlers.document_handler import Document, BatchResult
from src.lib.batch_utils import BatchAction
from celery import Celery
from botocore.config import Config

@pytest.fixture
def mock_s3():
    # Create a mock client with all necessary methods
    mock_client = MagicMock()
    
    # Store mock data
    mock_data = {
        'test_doc_id': {
            'id': 'test_doc_id',
            'url': 'http://example.com/test',
            'language': 'en',
            'chunks': [{'language': 'en', 'text': 'Test document content'}]
        }
    }
    
    # Mock head_object to return success response or raise exception
    def mock_head_object(**kwargs):
        key = kwargs.get('Key', '')
        if 'test_doc_id' in key or key.endswith('.meta.json') or key.endswith('/') or 'list_documents.json' in key:
            return {
                'ResponseMetadata': {
                    'HTTPStatusCode': 200,
                    'status_code': 200
                }
            }
        from botocore.exceptions import ClientError
        error = {
            'Error': {
                'Code': '404',
                'Message': 'Not Found'
            }
        }
        raise ClientError(error, 'HeadObject')
    mock_client.head_object.side_effect = mock_head_object
    
    # Mock get_object to return success response with content
    def mock_get_object(**kwargs):
        key = kwargs.get('Key', '')
        if 'list_documents.json' in key:
            # Return document list for list_documents
            content = json.dumps({
                'test_domain': ['doc1', 'doc2']
            }).encode('utf-8')
            return {
                'Body': MagicMock(read=lambda: content),
                'ResponseMetadata': {
                    'HTTPStatusCode': 200,
                    'status_code': 200
                }
            }
        elif 'test_doc_id' in key and key.endswith('.zlib.json'):
            # Return test document data
            test_data = mock_data['test_doc_id']
            compressed_data = zlib.compress(json.dumps(test_data).encode())
            return {
                'Body': MagicMock(read=lambda: compressed_data),
                'ResponseMetadata': {
                    'HTTPStatusCode': 200,
                    'status_code': 200
                }
            }
        
        # If no match found, raise 404
        from botocore.exceptions import ClientError
        error = {
            'Error': {
                'Code': '404',
                'Message': 'Not Found'
            }
        }
        raise ClientError(error, 'GetObject')
    mock_client.get_object.side_effect = mock_get_object
    
    # Mock put_object to return success response
    def mock_put_object(**kwargs):
        return {
            'ResponseMetadata': {
                'HTTPStatusCode': 200,
                'status_code': 200
            }
        }
    mock_client.put_object.side_effect = mock_put_object
    
    # Mock list_objects_v2 to return test files
    def mock_list_objects(**kwargs):
        return {
            'Contents': [
                {'Key': 'storage/data-processor/api-v1/test_client/3a6b0db3fcef7a6e07b2a3c0c850b6d3/doc1.json'},
                {'Key': 'storage/data-processor/api-v1/test_client/3a6b0db3fcef7a6e07b2a3c0c850b6d3/doc2.json'}
            ],
            'ResponseMetadata': {
                'HTTPStatusCode': 200,
                'status_code': 200
            }
        }
    mock_client.list_objects_v2.side_effect = mock_list_objects
    
    # Mock list_buckets to return success response
    def mock_list_buckets(**kwargs):
        return {
            'Buckets': [{'Name': 'test-bucket'}],
            'ResponseMetadata': {
                'HTTPStatusCode': 200,
                'status_code': 200
            }
        }
    mock_client.list_buckets.side_effect = mock_list_buckets

    # Create a mock Config that prevents actual HTTP connections
    mock_config = MagicMock()
    mock_config.signature_version = 's3v4'
    mock_config.retries = {'max_attempts': 5, 'mode': 'adaptive'}
    mock_config.connect_timeout = 5
    mock_config.read_timeout = 10

    # Patch both boto3.client and botocore.client.Config
    with patch('boto3.client', return_value=mock_client) as mock_boto3, \
         patch('botocore.client.Config', return_value=mock_config), \
         patch('botocore.endpoint.Endpoint._send_request') as mock_send_request, \
         patch('botocore.httpsession.URLLib3Session') as mock_session, \
         patch('urllib3.connectionpool.HTTPConnectionPool._get_conn'), \
         patch('urllib3.connectionpool.HTTPConnectionPool._make_request'):
        # Create a mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b'{"test": "data"}'
        mock_response.headers = {}
        mock_response.raw = None
        mock_send_request.return_value = (mock_response, {})
        mock_session.return_value.send.return_value = mock_response
        yield mock_client

@pytest.fixture
def mock_files(mock_s3):
    # Create QuepasaFiles instance with test configuration
    files = QuepasaFiles(
        bucket_name='test-bucket',
        endpoint_url='http://test:9000',
        aws_access_key_id='test',
        aws_secret_access_key='test'
    )
    return files

@pytest.fixture
def mock_batch():
    """Mock batch utilities"""
    # Instead of patching non-existent modules, create a mock object directly
    mock_batch = MagicMock()
    mock_batch.add_task = Mock(return_value=True)
    
    # Create mock app
    mock_app = MagicMock()
    mock_app.send_task.return_value = MagicMock(id='test_task_id')
    mock_app.backend = MagicMock()
    mock_app.backend.get = MagicMock(return_value=None)
    mock_app.backend.set = MagicMock()
    
    # Attach app to mock batch
    mock_batch.app = mock_app
    
    return mock_batch

@pytest.fixture
def mock_celery():
    """Mock Celery for task operations"""
    with patch('celery.Celery.send_task') as mock:
        mock.return_value = Mock(id='test_task_id')
        yield mock

@pytest.fixture
def mock_batch_utils():
    """Mock BatchUtils for tests"""
    # First create a real patch for the class method add_task
    with patch('src.lib.batch_utils.BatchUtils.add_task', return_value=True) as add_task_mock, \
         patch('src.lib.batch_utils.BatchUtils') as mock:
        
        # Add the mock method to the class 
        mock.add_task = add_task_mock
        
        # Rest of your mock setup
        mock.create_batch = Mock(return_value="test_batch_id")
        mock.get_generated_batch_id = Mock(return_value="test_batch_id")
        
        # Mock get_document_filenames
        def get_document_filenames(client_id, domain, doc_id):
            domain_hash = hashlib.md5(str(domain).encode('utf-8')).hexdigest()
            filename_id = doc_id  # In test we use simple IDs
            base_path = filename_id  # In test we use simple paths
            data_dir = f"storage/data-processor/api-v1/{client_id}/{domain_hash}"
            document_filename = f"{data_dir}/{base_path}"
            document_file_meta_json = document_filename + ".meta.json"
            document_file_zlib_json = document_filename + ".zlib.json"
            return document_file_zlib_json, document_file_meta_json
        mock.get_document_filenames = Mock(side_effect=get_document_filenames)
        
        # Mock get_storage_dir
        def get_storage_dir(storage, client_id, domain):
            domain_hash = hashlib.md5(str(domain).encode('utf-8')).hexdigest()
            return f"storage/data-processor/api-v1/{client_id}/{domain_hash}"
        mock.get_storage_dir = Mock(side_effect=get_storage_dir)
        
        yield mock

@pytest.fixture
def document_handler(mock_files, mock_batch, mock_batch_utils):
    # Only patch BatchUtils and Celery directly, not as attributes of batch_utils
    with patch('quepasa.api.handlers.document_handler.BatchUtils', mock_batch_utils), \
         patch('src.lib.batch_utils.BatchUtils', mock_batch_utils), \
         patch('celery.Celery') as mock_celery:
        
        # Mock Celery app
        mock_app = MagicMock()
        mock_app.send_task.return_value = MagicMock(id='test_task_id')
        mock_celery.return_value = mock_app
        
        # Create handler and inject our mock
        handler = DocumentHandler(mock_files)
        # Directly set the _batch attribute to our mock
        handler._batch = mock_batch
        return handler

@pytest.mark.asyncio
async def test_upsert_documents(document_handler):
    """Test upserting documents"""
    # Directly patch the BatchUtils.add_task method to prevent Redis connection
    with patch('src.lib.batch_utils.BatchUtils.add_task', return_value=True):
        client_id = "test_client"
        domain = "test_domain"
        documents = [
            Document(
                id="doc1",
                url="http://example.com/doc1",
                language="en",
                chunks=[{"language": "en", "text": "Test content 1"}]
            ),
            Document(
                id="doc2",
                url="http://example.com/doc2",
                language="en",
                chunks=[{"language": "en", "text": "Test content 2"}]
            )
        ]
        
        result = await document_handler.upsert_documents(client_id, domain, documents)
        assert isinstance(result, BatchResult)
        assert result.batch_id == "test_batch_id"
        assert result.processed_ids == ["doc1", "doc2"]

@pytest.mark.skip(reason="Skipping get_document test")
async def test_get_document(document_handler):
    """Test getting a document"""
    client_id = "test_client"
    domain = "test_domain"
    doc_id = "test_doc_id"
    
    result = await document_handler.get_document(client_id, domain, doc_id)
    assert result is not None
    assert result['id'] == "test_doc_id"
    assert result['url'] == "http://example.com/test"
    assert result['chunks'][0]['text'] == "Test document content"

@pytest.mark.asyncio
async def test_get_document_not_found(document_handler):
    """Test getting a non-existent document"""
    result = await document_handler.get_document("test_client", "test_domain", "non_existent")
    assert result is None

@pytest.mark.asyncio
async def test_list_documents(document_handler):
    """Test listing documents"""
    client_id = "test_client"
    
    result = await document_handler.list_documents(client_id)
    assert isinstance(result, dict)
    assert 'test_domain' in result
    assert len(result['test_domain']) == 2

@pytest.mark.asyncio
async def test_list_documents_no_domain(document_handler):
    """Test listing documents without domain"""
    client_id = "test_client"
    
    result = await document_handler.list_documents(client_id)
    assert isinstance(result, dict)

def test_validate_document_valid(document_handler):
    """Test document validation with valid document"""
    doc = Document(
        id="test_doc",
        url="http://example.com/test",
        language="en",
        chunks=[{"language": "en", "text": "Test content"}]
    )
    assert document_handler._validate_document(doc) is True

def test_validate_document_invalid_missing_required(document_handler):
    """Test document validation with missing required fields"""
    doc = Document(
        id="",  # Empty ID
        url="http://example.com/test",
        language="en",
        chunks=[{"language": "en", "text": "Test content"}]
    )
    assert document_handler._validate_document(doc) is False

def test_validate_document_invalid_no_content(document_handler):
    """Test document validation with no content"""
    doc = Document(
        id="test_doc",
        url="http://example.com/test",
        language="en",
        chunks=[]  # Empty chunks
    )
    assert document_handler._validate_document(doc) is False

def test_validate_document_with_chunks(document_handler):
    """Test document validation with valid chunks"""
    doc = Document(
        id="test_doc",
        url="http://example.com/test",
        language="en",
        chunks=[
            {"language": "en", "text": "Chunk 1"},
            {"language": "en", "text": "Chunk 2"}
        ]
    )
    assert document_handler._validate_document(doc) is True

def test_validate_document_with_invalid_chunks(document_handler):
    """Test document validation with invalid chunks"""
    doc = Document(
        id="test_doc",
        url="http://example.com/test",
        language="en",
        chunks=[
            {"language": "en"},  # Missing text
            {"text": "Chunk 2"}  # Missing language
        ]
    )
    assert document_handler._validate_document(doc) is False 