import os
import json
import requests
from typing import List, Dict, Optional, Union, Any
from src.lib.logger import QuepasaLogger

# Configure logging
logger = QuepasaLogger().get_instance(__name__)

QUEPASA_URL_PREFIX = "https://api.quepasa.ai/gateway/v1"
QUEPASA_LLM_GATEWAY_API_KEY = os.environ.get("QUEPASA_LLM_GATEWAY_API_KEY")

class QuepasaLLMGateway:
    """Client for accessing QuePasa LLM and Embedding services via API gateway."""
    
    def __init__(self, 
                 base_url: str = QUEPASA_URL_PREFIX, 
                 api_key: Optional[str] = QUEPASA_LLM_GATEWAY_API_KEY):
        """
        Initialize the QuePasa LLM Gateway client.
        
        Args:
            base_url: Base URL for the QuePasa API
            api_key: API key for authentication (defaults to QUEPASA_LLM_GATEWAY_API_KEY env var)
        """
        self.base_url = base_url
        self.api_key = api_key
        if not self.api_key:
            logger.warning("No API key provided for QuePasa LLM Gateway. Authentication will fail.")
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def get_embedding(self, 
                      provider: Union[str, Any],
                      model: Union[str, Any],
                      text: str, 
                      bypass_cache: bool = False) -> List[float]:
        """
        Get embedding for the provided text.
        
        Args:
            provider: The embedding provider to use (e.g., 'openai', 'sbert')
            model: Model name/version to use
            text: Text to get embedding for
            bypass_cache: If True, bypass any caching and get a fresh embedding
            
        Returns:
            List of embedding values if successful
            
        Raises:
            ValueError: If the request fails after max retries
        """
        endpoint = f"{self.base_url}/embedding"

        # Convert Enum types to their string values
        if hasattr(provider, 'value'):
            provider = provider.value
        if hasattr(model, 'value'):
            model = model.value

        payload = {
            "provider": provider,
            "model": model,
            "text": text,
            "bypass_cache": bypass_cache
        }
        
        try:
            response = requests.post(
                endpoint,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            data = response.json()
            return data.get("embedding", [])
            
        except requests.exceptions.Timeout:
            logger.warning(f"Timeout connecting to QuePasa API")

        except requests.exceptions.ConnectionError:
            logger.warning(f"Connection error to QuePasa API")

        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error from QuePasa API: {e}")

            # If it's a 401/403, don't retry
            if response.status_code in (401, 403):
                raise ValueError(f"Authentication failed with status code {response.status_code}")

            # If it's a 400, the request is invalid, don't retry
            if response.status_code == 400:
                raise ValueError(f"Invalid request: {response.text}")

        except Exception as e:
            logger.error(f"Unexpected error getting embedding: {e}")
            
        raise ValueError(f"Failed to get embedding")
    
    def get_llm_answer(self,
                       provider: Union[str, Any],
                       model: Union[str, Any],
                       messages: List[Dict[str, str]],
                       max_tokens: Optional[int] = None,
                       json_mode: bool = False,
                       use_cache: bool = True) -> str:
        """
        Get LLM response for the provided messages.
        
        Args:
            provider: The LLM provider to use (e.g., 'openai', 'anthropic')
            model: Model name/version to use
            messages: List of message dictionaries (format: [{"role": "user", "content": "Hello"}])
            max_tokens: Maximum number of tokens to generate
            json_mode: If True, instruct the model to return JSON
            use_cache: If True, use cached responses when available
            
        Returns:
            Generated text from the LLM
            
        Raises:
            ValueError: If the request fails after max retries
        """
        endpoint = f"{self.base_url}/llm"

        # Convert Enum types to their string values
        if hasattr(provider, 'value'):
            provider = provider.value
        if hasattr(model, 'value'):
            model = model.value
            
        payload = {
            "provider": provider,
            "model": model,
            "messages": messages,
            "json_mode": json_mode,
            "use_cache": use_cache
        }
        
        if max_tokens is not None:
            payload["max_tokens"] = max_tokens
        
        try:
            response = requests.post(
                endpoint,
                headers=self.headers,
                json=payload,
                timeout=60  # Longer timeout for LLM calls
            )
            response.raise_for_status()
            data = response.json()
            return data.get("text", "")
            
        except requests.exceptions.Timeout:
            logger.warning(f"Timeout connecting to QuePasa API")

        except requests.exceptions.ConnectionError:
            logger.warning(f"Connection error to QuePasa API")

        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error from QuePasa API: {e}")

            # If it's a 401/403, don't retry
            if response.status_code in (401, 403):
                raise ValueError(f"Authentication failed with status code {response.status_code}")
            
            # If it's a 400, the request is invalid, don't retry
            if response.status_code == 400:
                raise ValueError(f"Invalid request: {response.text}")
            
        except Exception as e:
            logger.error(f"Unexpected error getting LLM response: {e}")
        
        raise ValueError(f"Failed to get LLM response")

    def get_segments(self,
                    provider: Union[str, Any],
                    model: Union[str, Any],
                    audio_data: bytes,
                    meta: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get transcription segments for the provided audio data.
        
        Args:
            provider: The transcription provider to use (e.g., 'replicate')
            model: Model name/version to use
            audio_data: Audio data as bytes
            meta: Additional metadata for the transcription request
            
        Returns:
            Dictionary containing transcription segments
            
        Raises:
            ValueError: If the request fails after max retries
        """
        endpoint = f"{self.base_url}/whisper"

        # Convert Enum types to their string values
        if hasattr(provider, 'value'):
            provider = provider.value
        if hasattr(model, 'value'):
            model = model.value
        
        # Prepare the metadata for the request
        if meta is None:
            meta = {}
        
        # Get MIME type from meta and determine file extension
        extension = meta.get('extension', '.mp3')
        
        # Map MIME types to file extensions
        extension_to_mime_type = {
            # Audio types
            ".mp3": "audio/mpeg",
            ".wav": "audio/wav",
            ".ogg": "audio/ogg",
            ".flac": "audio/flac",
            ".aac": "audio/aac",

            # Video types
            ".mp4": "video/mp4",
            ".avi": "video/x-msvideo",
            ".mov": "video/quicktime",
            ".mkv": "video/x-matroska",
            ".webm": "video/webm",
            ".flv": "video/x-flv",
            ".wmv": "video/x-ms-wmv"
        }
        
        # Get extension, default to mp3 if mime type not found
        mime_type = extension_to_mime_type.get(extension, "audio/mpeg")
            
        # Create form data with both JSON and file parts
        files = {
            'file': (f'audio.{extension}', audio_data, mime_type)
        }
        
        # Create form data for the request parameters
        data = {
            'provider': provider,
            'model': model,
            'meta': json.dumps(meta)  # FastAPI requires JSON serializable types
        }
        
        try:
            # Using files parameter for multipart/form-data
            response = requests.post(
                endpoint,
                headers={"Authorization": f"Bearer {self.api_key}"},
                data=data,
                files=files,
                timeout=120  # Longer timeout for audio processing
            )
            response.raise_for_status()
            result = response.json()
            return result.get("segments", {})
            
        except requests.exceptions.Timeout:
            logger.warning(f"Timeout connecting to QuePasa API")

        except requests.exceptions.ConnectionError:
            logger.warning(f"Connection error to QuePasa API")

        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP error from QuePasa API: {e}")

            # If it's a 401/403, don't retry
            if response.status_code in (401, 403):
                raise ValueError(f"Authentication failed with status code {response.status_code}")
            
            # If it's a 400, the request is invalid, don't retry
            if response.status_code == 400:
                raise ValueError(f"Invalid request: {response.text}")
            
        except Exception as e:
            logger.error(f"Unexpected error getting transcription: {e}")
        
        raise ValueError(f"Failed to get transcription segments")


# Singleton client instance with default parameters
_default_client = None

def get_client() -> QuepasaLLMGateway:
    """Get or create the default QuePasa LLM Gateway client."""
    global _default_client
    if _default_client is None:
        _default_client = QuepasaLLMGateway()
    return _default_client

def get_embedding_from_gateway(provider: str, model: str, text: str, bypass_cache: bool = False) -> List[float]:
    """Get embedding using the default client."""
    return get_client().get_embedding(provider, model, text, bypass_cache)

def get_llm_answer_from_gateway(provider: str, model: str, messages: List[Dict[str, str]], max_tokens: Optional[int] = None, json_mode: bool = False, use_cache: bool = True) -> str:
    """Get LLM response using the default client."""
    return get_client().get_llm_answer(provider, model, messages, max_tokens, json_mode, use_cache)

def get_segments_from_gateway(provider: str, model: str, audio_data: bytes, meta: Dict[str, Any] = None) -> Dict[str, Any]:
    """Get transcription segments using the default client."""
    return get_client().get_segments(provider, model, audio_data, meta) 