# CON-69 Testing Epic: Product Metadata Flow Validation

## Overview

This document defines the comprehensive testing requirements for validating the CON-69 product metadata implementation. This Epic ensures that all components of the product metadata flow - from ingestion through RAG retrieval to final API response - are properly tested and validated.

**Related Documents:**
- [CON-69 PRD](/docs/archive/CON-69-prd.md)

**Epic Scope:** Implement comprehensive test coverage for structured product metadata in conversational API responses, ensuring type safety, data integrity, and backward compatibility throughout the entire product document lifecycle.

## Test Coverage Analysis

### Current State vs Required State

| Test Category | Current Coverage | Required Coverage | Gap Analysis |
|---------------|------------------|-------------------|--------------|
| **Product Ingestion** | ✅ 80% - Good SKU/field testing | ✅ 95% - Add metadata & type fields | 🟡 Medium Gap |
| **Elasticsearch Integration** | ⚠️ 60% - Basic indexing only | ✅ 90% - Product-specific mapping | 🔴 High Gap |
| **RAG Search Integration** | ⚠️ 70% - Missing metadata field | ✅ 95% - Full product document flow | 🟡 Medium Gap |
| **Meta Search Integration** | ✅ 85% - Good result mixing | ✅ 95% - Add product conversion | 🟢 Low Gap |
| **API Response Tests** | ❌ 40% - No products field | ✅ 95% - Full ProductItem | 🔴 High Gap |

## Test Modification Requirements

### 1. Product Ingestion Tests (`tests/lib/test_markdown_converter.py`)

**Existing Tests to Modify:**
- `test_minimal_product()` - Add metadata and type field validation
- `test_product_ignores_special_fields()` - Update exclusion list validation
- `test_multiple_products()` - Add metadata consistency checks

**Required Changes:**
```python
def test_minimal_product(self):
    products = [{"id": "SKU123"}]
    documents = products_to_documents(products)
    
    doc = documents[0]
    assert doc['type'] == 'product'  # NEW
    assert 'metadata' in doc  # NEW
    assert isinstance(doc['metadata'], str)  # JSON string validation
```

### 2. Elasticsearch Integration Tests (`tests/test_elasticsearch_integration.py`)

**Existing Tests to Modify:**
- `test_document_indexing()` - Add product document structure
- `test_index_creation()` - Update mapping validation

**Index Mapping Update Required:**
```python
"mappings": {
    "properties": {
        "type": {"type": "keyword"},
        "sku": {"type": "keyword"}, 
        "metadata": {"type": "text", "index": False}  # NEW
    }
}
```

### 3. RAG Search Integration Tests (`tests/test_searcher_rag.py`)

**Existing Fixtures to Update:**
- `mock_document()` - Add metadata and sku fields
- `mock_elasticsearch()` - Include product documents in mock responses

**Critical Updates Required:**
```python
@pytest.fixture
def mock_product_document():
    return QuepasaDocument(
        type="product",
        sku="SKU123",  # NEW: Missing from QuepasaDocument model
        metadata='{"title": "Test Product", "price": 29.99}',  # NEW
        # ... existing fields
    )
```

**Critical Gap:** The recent SKU implementation missed updating QuepasaDocument model and RAG search field population. Both `sku` and `metadata` fields need to be added to the model and populated during search.

### 4. Meta Search Integration Tests (`tests/test_searcher_source_meta.py`)

**Existing Tests to Enhance:**
- `test_mix_results()` - Add product document mixing validation
- `test_search_interleaves_both()` - Include product response conversion

### 5. API Response Tests (`tests/test_searcher_models.py`)

**Existing Tests to Modify:**
- `test_response_model_with_answers()` - Add products field validation
- All QuepasaAnswer creation tests - Include products parameter

## New Test Requirements

### 1. Product Ingestion Tests - New Cases

**Test: Metadata Field Creation and Filtering**
```python
def test_products_to_documents_with_metadata():
    """Test metadata field creation with proper filtering"""
    products = [{
        "id": "SKU123", 
        "title": "Test Product",
        "price": 29.99,
        "_id": "internal_id",  # Should be filtered
        "indexables": ["field1"]  # Should be filtered
    }]
    
    documents = products_to_documents(products)
    doc = documents[0]
    
    # Validate new fields
    assert doc['type'] == 'product'
    assert 'metadata' in doc
    
    # Validate JSON metadata content
    metadata = json.loads(doc['metadata'])
    assert metadata['title'] == 'Test Product'
    assert metadata['price'] == 29.99
    assert '_id' not in metadata
    assert 'indexables' not in metadata

def test_filter_product_for_metadata():
    """Test new _filter_product_for_metadata method"""
    product = {
        "id": "SKU123",
        "title": "Test Product", 
        "price": 29.99,
        "_id": "internal",
        "indexables": ["field1", "field2"],
        "samplePrecision": 1,
        "dynamicFacets": {"color": ["red", "blue"]}
    }
    
    filtered = _filter_product_for_metadata(product)
    
    # Should include business data
    assert filtered['title'] == 'Test Product'
    assert filtered['price'] == 29.99
    
    # Should exclude metadata keys
    assert '_id' not in filtered
    assert 'indexables' not in filtered
    assert 'samplePrecision' not in filtered
    assert 'dynamicFacets' not in filtered
```

### 2. Elasticsearch Integration Tests - New Cases

**Test: Product Document Indexing with Metadata**
```python
def test_product_document_indexing_with_metadata(es_client, test_index):
    """Test product document indexing with JSON metadata field"""
    product_doc = {
        "id": "SKU123",
        "type": "product",
        "sku": "SKU123",  # Already supported in mapping
        "title": "Test Product",
        "metadata": '{"title": "Test Product", "price": 29.99, "color": "red"}'  # NEW field needed
    }
    
    # Index document
    es_client.index(index=test_index, id=product_doc["id"], document=product_doc)
    es_client.indices.refresh(index=test_index)
    
    # Verify retrieval
    retrieved = es_client.get(index=test_index, id="SKU123")
    source = retrieved["_source"]
    
    assert source["type"] == "product"
    assert source["sku"] == "SKU123"
    assert source["metadata"] == product_doc["metadata"]
    
    # Verify metadata is not searchable (non-indexed)
    search_query = {
        "query": {"match": {"metadata": "Test Product"}}
    }
    results = es_client.search(index=test_index, body=search_query)
    assert len(results["hits"]["hits"]) == 0  # Should not find via metadata search

def test_metadata_field_mapping_validation(es_client, test_index):
    """Test metadata field mapping is correctly configured"""
    mappings = es_client.indices.get_mapping(index=test_index)
    properties = mappings[test_index]["mappings"]["properties"]
    
    # Verify metadata field mapping
    assert "metadata" in properties
    metadata_mapping = properties["metadata"]
    assert metadata_mapping["type"] == "text"
    assert metadata_mapping["index"] == False
```

### 3. RAG Search Integration Tests - New Cases

**Test: Product Document Retrieval with Metadata**
```python
def test_rag_search_product_documents_with_metadata(rag_manager, mock_elasticsearch):
    """Test RAG search retrieval of product documents with metadata"""
    # Mock Elasticsearch response with product document
    mock_elasticsearch.search.return_value = {
        "hits": {
            "hits": [{
                "_id": "SKU123",
                "_score": 0.8,
                "_source": {
                    "id": "SKU123",
                    "type": "product",
                    "sku": "SKU123",  # Field exists in ES but missing from QuepasaDocument
                    "title": "Test Product",
                    "metadata": '{"title": "Test Product", "price": 29.99}',  # NEW field needed
                    "text": "Test product description"
                }
            }]
        }
    }
    
    request = QuepasaRequest(client="test", question="test query")
    results = rag_manager.search(request)
    
    # Verify product document structure
    assert len(results) == 1
    product_doc = results[0]
    assert product_doc.type == "product"
    assert product_doc.sku == "SKU123"  # CRITICAL: This will fail until QuepasaDocument model is updated
    assert product_doc.metadata is not None  # CRITICAL: This will fail until model is updated
    assert '"price"' in product_doc.metadata  # JSON string validation
```

### 4. Meta Search Integration Tests - New Cases

**Test: Product Response Conversion**
```python
def test_meta_search_product_response_conversion():
    """Test conversion of mixed results to ProductItem"""
    # Mock RAG product document
    rag_product = QuepasaDocument(
        id="RAG123",
        type="product",
        sku="RAG123",
        title="RAG Product",
        url="https://example.com/RAG123",
        metadata='{"title": "RAG Product", "price": 19.99}'
    )
    
    # Mock SPD search result  
    spd_product = SPDSearchResult(
        sku="SPD123",
        title="SPD Product", 
        url="https://example.com/SPD123",
        metadata={"title": "SPD Product", "price": 39.99}
    )
    
    # Test conversion logic (implementation needed)
    rag_response = convert_document_to_product_response(rag_product)
    spd_response = convert_spd_to_product_response(spd_product)
    
    # Validate ProductItem structure
    assert isinstance(rag_response, ProductItem)
    assert isinstance(spd_response, ProductItem)
    
    assert rag_response._id == "RAG123"
    assert rag_response._t == "RAG Product"
    assert rag_response._u == "https://example.com/RAG123"
    assert isinstance(rag_response.allMeta, dict)

    assert spd_response._id == "SPD123"
    assert spd_response._t == "SPD Product"
    assert spd_response.allMeta["price"] == 39.99
```

### 5. API Response Tests - New Cases

**Test: ProductItem Model**
```python
def test_product_item_model():
    """Test ProductItem structure and validation"""
    product = ProductItem(
        _id="SKU123",
        _t="Test Product",
        _u="https://example.com/SKU123",
        collection="test_collection",
        allMeta={"price": 29.99, "color": "red", "brand": "TestBrand"}
    )

    assert product._id == "SKU123"
    assert product._t == "Test Product"
    assert product._u == "https://example.com/SKU123"
    assert product.collection == "test_collection"
    assert product.allMeta["price"] == 29.99
    assert product.allMeta["color"] == "red"

def test_quepasa_answer_with_products_field():
    """Test QuepasaAnswer with new products field"""
    products = [
        ProductItem(
            _id="SKU123",
            _t="Product 1",
            _u="https://example.com/SKU123",
            collection="test_collection",
            allMeta={"price": 29.99}
        ),
        ProductItem(
            _id="SKU456",
            _t="Product 2",
            _u="https://example.com/SKU456",
            collection="test_collection",
            allMeta={"price": 39.99}
        )
    ]
    
    answer = QuepasaAnswer(
        text="Here are some products that match your query",
        markdown="Here are some products that match your query",
        products=products,
        references={"1": Reference(title="Product Guide", url="https://guide.com")}
    )
    
    # Validate products field
    assert len(answer.products) == 2
    assert answer.products[0]._id == "SKU123"
    assert answer.products[1]._id == "SKU456"
    
    # Validate backward compatibility
    assert "1" in answer.references
    assert answer.text is not None
    assert answer.markdown is not None

def test_quepasa_answer_backward_compatibility():
    """Test backward compatibility with existing response structure"""
    # Test answer without products field
    answer_without_products = QuepasaAnswer(
        text="Regular answer",
        references={"1": Reference(title="Doc", url="https://doc.com")}
    )
    
    assert answer_without_products.products is None
    assert len(answer_without_products.references) == 1
    
    # Test serialization/deserialization
    answer_dict = answer_without_products.to_dict()
    assert "products" not in answer_dict or answer_dict["products"] is None
    
    reconstructed = QuepasaAnswer.from_dict(answer_dict)
    assert reconstructed.text == "Regular answer"
    assert reconstructed.products is None
```

## Test Data & Fixtures

### Required Fixture Updates

**1. QuepasaDocument Fixtures - Add Metadata Field**
```python
@pytest.fixture
def mock_product_document():
    return QuepasaDocument(
        id="SKU123",
        root_id="SKU123", 
        type="product",
        sku="SKU123",
        title="Test Product",
        url="https://example.com/SKU123",
        metadata='{"title": "Test Product", "price": 29.99, "color": "red"}',
        text="Test product description",
        score=0.8,
        # ... other existing fields
    )

@pytest.fixture  
def mock_product_documents_list():
    return [
        QuepasaDocument(
            id="SKU001", type="product", sku="SKU001",
            metadata='{"title": "Product 1", "price": 19.99}'
        ),
        QuepasaDocument(
            id="SKU002", type="product", sku="SKU002", 
            metadata='{"title": "Product 2", "price": 29.99}'
        )
    ]
```

**2. Elasticsearch Test Index Mapping**
```python
@pytest.fixture
def product_test_index_mapping():
    return {
        "settings": {
            "index": {"number_of_shards": 1, "number_of_replicas": 0}
        },
        "mappings": {
            "properties": {
                "id": {"type": "keyword"},
                "type": {"type": "keyword"},
                "sku": {"type": "keyword"},
                "title": {"type": "text"},
                "metadata": {"type": "text", "index": False},
                "content": {"type": "text"},
                "embedding": {
                    "type": "dense_vector",
                    "dims": 384,
                    "index": True,
                    "similarity": "cosine"
                }
            }
        }
    }
```

**3. API Test Data**
```python
@pytest.fixture
def sample_product_api_response():
    return {
        "text": "Here are some products that match your query",
        "markdown": "Here are some products that match your query", 
        "products": [
            {
                "_id": "SKU123",
                "_t": "Test Product",
                "_u": "https://example.com/SKU123",
                "collection": "test_collection",
                "allMeta": {"price": 29.99, "color": "red"}
            }
        ],
        "references": {
            "1": {"title": "Product Guide", "url": "https://guide.com"}
        }
    }
```

## Critical Implementation Gaps Identified

### Missing Components from Recent SKU Implementation
Based on analysis of commit 0c7d4a70 and related commits, the following critical gaps were identified:

**1. QuepasaDocument Model Missing Fields**
- ✅ `sku` field added to `Document` model in `document_handler.py`
- ✅ `sku` field added to Elasticsearch mapping in `cli_create_index.py`
- ❌ **MISSING**: `sku` field not added to `QuepasaDocument` model in `searcher/models/document.py`
- ❌ **MISSING**: `metadata` field not added to `QuepasaDocument` model

**2. RAG Search Field Population Missing**
- ❌ **MISSING**: RAG search doesn't populate `sku` field from Elasticsearch
- ❌ **MISSING**: RAG search doesn't populate `metadata` field from Elasticsearch

**3. Data Processor Missing Metadata Field**
- ✅ `sku`, `price_from`, `price_to` added to cleaned document object
- ❌ **MISSING**: `metadata` field not added to cleaned document object in `data_processor/tasks.py`

**4. Elasticsearch Mapping Missing Metadata**
- ✅ `sku` field mapping exists
- ❌ **MISSING**: `metadata` field mapping not added to `cli_create_index.py`

**Impact:** These gaps will cause test failures and prevent proper product metadata functionality.

## Implementation Plan

### Phase 1: Foundation Tests (Week 1)
**Priority: Critical**

1. **Update Core Model Tests**
   - Modify `QuepasaDocument` fixtures to include metadata field
   - Update `QuepasaAnswer` tests to include products field
   - Create `ProductItem` model tests

2. **Product Ingestion Tests**
   - Add metadata field creation tests
   - Add filtering method tests
   - Update existing product processing tests

**Deliverables:**
- All model tests pass with new fields
- Product ingestion tests validate metadata creation
- No breaking changes to existing functionality

### Phase 2: Integration Tests (Week 2)  
**Priority: High**

1. **Elasticsearch Integration**
   - Add product document indexing tests
   - Add metadata field mapping validation
   - Test non-indexed metadata behavior

2. **RAG Search Integration**
   - Add product document retrieval tests
   - Test metadata field population from Elasticsearch
   - Validate search result processing

**Deliverables:**
- Elasticsearch tests validate product document storage
- RAG search tests confirm metadata retrieval
- Integration between storage and search layers verified

### Phase 3: End-to-End Tests (Week 3)
**Priority: High**

1. **Meta Search Integration**
   - Add product response conversion tests
   - Test mixed RAG/SPD result processing
   - Validate products array population

2. **API Response Tests**
   - Add comprehensive response structure tests
   - Test backward compatibility
   - Add serialization/deserialization tests

**Deliverables:**
- Complete end-to-end product metadata flow tested
- API response structure fully validated
- Backward compatibility confirmed

### Phase 4: Edge Cases & Performance (Week 4)
**Priority: Medium**

1. **Error Handling Tests**
   - Malformed metadata handling
   - Missing field scenarios
   - Invalid JSON metadata

2. **Performance Tests**
   - Large metadata serialization
   - Bulk product processing
   - Search performance with metadata

**Deliverables:**
- Robust error handling validated
- Performance benchmarks established
- Edge cases covered

## Acceptance Criteria

### Test Coverage Metrics
- [ ] **80% test coverage** for all new CON-69 functionality
- [ ] **100% of existing tests** pass with new schema changes
- [ ] **Zero breaking changes** to existing API contracts

### Functional Validation
- [ ] **Product ingestion** creates documents with metadata and type fields
- [ ] **Elasticsearch indexing** stores product documents with correct mapping
- [ ] **RAG search** retrieves product documents with populated metadata
- [ ] **Meta search** converts mixed results to ProductItem objects
- [ ] **API responses** include products array alongside existing references

### Quality Assurance
- [ ] **All test categories** have comprehensive coverage per requirements
- [ ] **Test data fixtures** support both old and new schema formats
- [ ] **Error scenarios** are properly tested and handled
- [ ] **Performance impact** is measured and within acceptable limits

### Documentation & Maintainability
- [ ] **Test documentation** clearly explains new test patterns
- [ ] **Mock data** is realistic and representative
- [ ] **Test maintenance** guidelines established for future changes

## Risk Mitigation

### Potential Test Failures
1. **Schema Changes** - Update all fixtures before running tests
2. **JSON Serialization** - Validate metadata format in all test data
3. **Backward Compatibility** - Maintain parallel test paths for old/new formats
4. **Performance Impact** - Monitor test execution time and optimize as needed

