apiVersion: v1
kind: ConfigMap
metadata:
  name: shared-config
  namespace: quepasa
data:
  QUEPASA_EXECUTION_MODE: "kubernetes"
  KUBERNETES_NAMESPACE: "quepasa"
  COMPOSE_PROJECT_NAME: "quepasa-on-premises-c"
  QUEPASA_IMAGE_VERSION: "v1.0.184"
  QUEPASA_CRAWLER_IMAGE: "qpreg.azurecr.io/quepasa/crawler"
  QUEPASA_DATA_PROCESSOR_IMAGE: "qpreg.azurecr.io/quepasa/data_processor"
  QUEPASA_INDEXER_IMAGE: "qpreg.azurecr.io/quepasa/indexer"
  MINIO_HOST: "minio"
  MINIO_PORT: "9000"
  MINIO_BUCKET_NAME: "quepasa-files" 
  MINIO_DEBUG: "false"
  ELASTICSEARCH_HOST: "elasticsearch-cluster-es-http.quepasa.svc"
  ELASTICSEARCH_PORT: "9200"
  REDIS_HOST: "redis"
  REDIS_PORT: "6379"
  CELERY_BROKER_URL: "redis://redis:6379/0"
  CELERY_RESULT_BACKEND: "redis://redis:6379/0"
  SPD_API_URL: "https://search.gbiqa-lo.groupbycloud.com/api/search"
