import time
from typing import Dict, Any, List, Union
from datetime import datetime
from quepasa.searcher.models.request import QuepasaRequest
from ..base.indexer import IndexerConfig
from ..base.question import QuestionConfig

class IndexerMixin(IndexerConfig):
    """Mixin that implements generic indexing functionality."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
