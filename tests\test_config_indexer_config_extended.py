import pytest
from unittest.mock import patch, Mock
import time
from configuration.base.indexer import IndexerConfig
from src.lib.constants import (
    KIND_SUMMARY,
    KIND_TEXT,
    DOCUMENT_TYPE_DOCUMENT,
    SOURCE_LIVEWIKI
)
from src.lib.embedding.providers import EmbeddingProvider
from quepasa.searcher.models.request import QuepasaRequest

@pytest.fixture
def indexer_config():
    """Fixture that provides an IndexerConfig instance."""
    return IndexerConfig("test_client")

def test_indexer_config_init(indexer_config):
    """Test IndexerConfig initialization."""
    assert indexer_config.client_code == "test_client"
    assert indexer_config.document_languages is None
    assert indexer_config.document_languages_last_updated_at is None

def test_get_indexed_languages_cache_expiration(indexer_config):
    """Test that language cache expires after 10 minutes"""
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram"
    )
    indexer_config.set_request(request)
    
    mock_get_json = Mock(return_value={"domain1": ["en", "es"]})
    indexer_config.qp_files.get_json = mock_get_json
    
    with patch('time.time') as mock_time:
        # First call
        mock_time.return_value = 0
        result1 = indexer_config.get_indexed_languages(use_cache=True)
        
        # Second call within cache window
        mock_time.return_value = 599  # 9:59 minutes
        result2 = indexer_config.get_indexed_languages(use_cache=True)
        
        # Third call after cache expiration
        mock_time.return_value = 601  # 10:01 minutes
        result3 = indexer_config.get_indexed_languages(use_cache=True)
        
        # Verify behavior
        assert mock_get_json.call_count == 2  # Called on first request and after cache expiration
        assert result1 == result2  # Results should be same within cache window
        assert result1 == result3  # Results should still be same after cache refresh

def test_get_indexed_languages_with_livewiki(indexer_config):
    """Test language handling for LiveWiki source."""
    data = {"source": SOURCE_LIVEWIKI}
    result = indexer_config.get_indexed_languages(data)
    assert result == {DOCUMENT_TYPE_DOCUMENT: ["en"]}

def test_get_indexed_languages_with_summary(indexer_config):
    """Test language handling for summary kind."""
    data = {"kind": KIND_SUMMARY}
    result = indexer_config.get_indexed_languages(data)
    assert result == {DOCUMENT_TYPE_DOCUMENT: ["en"]}

def test_get_indexed_languages_with_text(indexer_config):
    """Test getting indexed languages with text"""
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram"
    )
    indexer_config.set_request(request)
    data = {"text": "sample text", "use_cache": False}
    result = indexer_config.get_indexed_languages(data)
    assert isinstance(result, dict)
    assert DOCUMENT_TYPE_DOCUMENT in result
    assert len(result[DOCUMENT_TYPE_DOCUMENT]) > 0

def test_get_indexed_languages_with_multiple_domains(indexer_config):
    """Test getting indexed languages with multiple domains"""
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram"
    )
    indexer_config.set_request(request)
    data = {"domain": ["domain1.com", "domain2.com"], "use_cache": False}
    result = indexer_config.get_indexed_languages(data)
    assert isinstance(result, dict)
    assert DOCUMENT_TYPE_DOCUMENT in result
    assert len(result[DOCUMENT_TYPE_DOCUMENT]) > 0

def test_get_indexed_languages_with_invalid_domain(indexer_config):
    """Test getting indexed languages with invalid domain"""
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram"
    )
    indexer_config.set_request(request)
    data = {"domain": "invalid.domain", "use_cache": False}
    result = indexer_config.get_indexed_languages(data)
    assert isinstance(result, dict)
    assert DOCUMENT_TYPE_DOCUMENT in result
    assert len(result[DOCUMENT_TYPE_DOCUMENT]) == 1
    assert result[DOCUMENT_TYPE_DOCUMENT][0] == "en"

def test_get_embedding_model_versions_by_document_type(indexer_config):
    """Test embedding model version selection by document type."""
    models = indexer_config.get_embedding_model_versions(DOCUMENT_TYPE_DOCUMENT)
    assert len(models) == 1
    provider, model_version = models[0]
    assert provider == EmbeddingProvider.SBERT
    assert model_version == "sentence-transformers/multi-qa-mpnet-base-dot-v1"

def test_get_fallback_language(indexer_config):
    """Test getting fallback language."""
    assert indexer_config.get_fallback_language() == "en" 