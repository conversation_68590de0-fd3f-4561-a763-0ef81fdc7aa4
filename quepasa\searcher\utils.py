import re
import copy
import t<PERSON><PERSON><PERSON>
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.spd_result import SPD<PERSON><PERSON>chR<PERSON>ult
from quepasa.searcher.models.web import WebSearchResult
from src.lib.constants import TELEGRAM_PROTOCOL
from src.lib.llm.providers import LLMProvider

# Constants
SOURCES_PLACEHOLDER = "{{SOURCES}}"
QUESTION_PLACEHOLDER = "{{QUESTION}}"
LANGUAGE_PLACEHOLDER = "{{LANGUAGE}}"
ENTITIES_PLACEHOLDER = "{{ENTITIES}}"


@dataclass(frozen=True)  # Make it immutable for safe hashing
class DocumentKey:
    """Key for uniquely identifying a document"""
    text: str
    url: Optional[str] = None
    title: Optional[str] = None
    
    def __post_init__(self):
        # Normalize text fields
        object.__setattr__(self, 'text', self.text.strip().lower())
        if self.url:
            object.__setattr__(self, 'url', self.url.strip().lower())
        if self.title:
            object.__setattr__(self, 'title', self.title.strip().lower())
    
    def __hash__(self):
        # Create a stable hash from normalized fields
        return hash((
            self.text,
            self.url if self.url else '',
            self.title if self.title else ''
        ))
    
    def __eq__(self, other):
        if not isinstance(other, DocumentKey):
            return False
        return (
            self.text == other.text and
            self.url == other.url and
            self.title == other.title
        )

def get_document_key(doc: Dict[str, Any]) -> DocumentKey:
    """Get unique key for document based on its content
    
    Args:
        doc: Document dictionary
        
    Returns:
        DocumentKey for uniqueness checking
    """
    # Extract and normalize text content
    text = doc.get('text', '').strip()
    if not text:  # If no text, try name + snippet combination
        name = doc.get('name', '').strip()
        snippet = doc.get('snippet', '').strip()
        if name or snippet:
            text = f"{name}\n{snippet}".strip()
    
    # Get URL and title if available
    url = doc.get('url', '').strip()
    title = doc.get('title', doc.get('name', '')).strip()
    
    return DocumentKey(
        text=text,
        url=url if url else None,
        title=title if title else None
    )

def is_unique_document(document: Union[QuepasaDocument, WebSearchResult, SPDSearchResult], seen_keys: set) -> bool:
    """Check if document is unique based on its content
    
    Args:
        doc: Document dictionary
        seen_keys: Set of already seen document keys
        
    Returns:
        True if document is unique, False otherwise
    """
    if not document.get('text'):
        return False  # Skip empty documents
        
    key = get_document_key(document)
    if key in seen_keys:
        return False
    seen_keys.add(key)
    return True

def replace_placeholders_in_prompt_messages(messages: List[Dict[str, str]], replacements: Dict[str, str]) -> List[Dict[str, str]]:
    """Replace placeholders in prompt messages with actual values
    
    Args:
        prompt_list: List of prompt message dictionaries with 'content' field
        placeholder_hash: Dictionary of placeholder -> value mappings
        
    Returns:
        New list with placeholders replaced in content fields
    """
    new_prompt_list = []
    if len(messages) > 0:
        new_prompt_list = copy.deepcopy(messages)
        for i, item in enumerate(new_prompt_list):
            content = item['content']
            for placeholder in replacements:
                content = content.replace(placeholder, replacements[placeholder])
            new_prompt_list[i]['content'] = content
    return new_prompt_list

def get_field_value_or_default(data: Dict[str, Any], key: str, default: Any) -> Any:
    """Get value from dictionary with default fallback
    
    Args:
        data: Dictionary to get value from
        key: Key to look up
        default: Default value if key not found
        
    Returns:
        Value from dictionary or default
    """
    return data.get(key, default)

def get_field_values(data: Dict[str, Any], key: str) -> List[str]:
    """Get list of values from dictionary field
    
    Args:
        data: Dictionary to get values from
        key: Key to look up
        
    Returns:
        List of values
    """
    if key not in data or data[key] is None:
        return []
        
    if isinstance(data[key], list):
        return data[key]
        
    if key in ['domain', 'subdomain']:
        return [data[key]]
        
    return re.sub(r'\s+', '', data[key]).split(',')

def get_serializable_request(request: Dict[str, Any]) -> Dict[str, Any]:
    """Get serializable version of request by removing non-serializable fields
    
    Args:
        request: Original request dictionary
        
    Returns:
        Serializable request dictionary
    """
    copy_request = request.copy()
    
    # Remove callable fields
    for key, value in request.items():
        if callable(value):
            del copy_request[key]
            
    # Remove internal fields
    # for key in ['data', 'waterfall']:
    for key in ['data']:
        if key in copy_request:
            del copy_request[key]
            
    return copy_request

def get_serializable_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """Get serializable version of response
    
    Args:
        response: Original response dictionary
        
    Returns:
        Serializable response dictionary
    """
    copy_response = response.copy()
    
    if 'request' in response:
        copy_response['request'] = get_serializable_request(response['request'])
        
    if 'parent' in response and 'request' in response['parent']:
        copy_response['parent']['request'] = get_serializable_request(response['parent']['request'])
        
    return copy_response

def remove_emoji(text: str) -> str:
    """Remove emoji characters from text
    
    Args:
        text: Input text
        
    Returns:
        Text with emoji removed
    """
    regrex_pattern = re.compile(pattern = "["
        u"\U0001F600-\U0001F64F"  # emoticons
        u"\U0001F300-\U0001F5FF"  # symbols & pictographs
        u"\U0001F680-\U0001F6FF"  # transport & map symbols
        u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
    "]+", flags = re.UNICODE)
    return regrex_pattern.sub(r'', text) 

def get_encoding(model_name: str) -> tiktoken.Encoding:
    """Get encoding for model"""
    try:
        encoding = tiktoken.encoding_for_model(model_name)
    except KeyError:
        encoding = tiktoken.get_encoding("cl100k_base")
    return encoding

def convert_relative_urls_to_absolute(text: str, base_url: str) -> str:
    """Convert relative URLs in text to absolute URLs using the base URL."""
    if not base_url:
        return text
    
    # Ensure base_url ends with a slash
    if not base_url.endswith('/'):
        base_url += '/'

    def replace_url(match):
        url = match.group(1)
        if url.startswith('http://') or url.startswith('https://'):
            return match.group(0)
        if url.startswith('/'):
            # Remove the leading slash as base_url already has one
            url = url[1:] if url.startswith('/') else url
        return f'({base_url}{url})'

    # Replace URLs in markdown links
    pattern = r'\(([^)]+)\)'
    return re.sub(pattern, replace_url, text)

def unescape_markdown_links( text ):
    unescaped_text = text

    # Define a function to unescape the link
    def unescape_link( match ):
        text = match.group(1)
        url  = match.group(2)
        return f"[{text}]({url})"

    # Define a regular expression pattern for an escaped link
    for escaped_link_pattern in [
        re.compile(r'\\\[(.*?)\\\]\\\((.*?)\\\)'),
        re.compile(r'\[(.*?)\]\((.*?)\)'),
    ]:
        # Use the sub function to replace escaped links with unescaped links
        unescaped_text = escaped_link_pattern.sub( unescape_link, unescaped_text )

    return unescaped_text

def escape_telegram_markdown(text):
    # In all other places characters '_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!' must be escaped with the preceding character '\'.
    for char in ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']:
        text = text.replace(char, "\\" + char)
    return text

def unescape_telegram_markdown(text):
    # In all other places characters '_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!' must be escaped with the preceding character '\'.
    for char in ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']:
        text = text.replace("\\" + char, char)
    return text

def escape_markdown(text):
    # In all other places characters '_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!' must be escaped with the preceding character '\'.
    for char in ['*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']:
        text = text.replace(char, "\\" + char)
    return text

def unescape_markdown(text):
    # In all other places characters '_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!' must be escaped with the preceding character '\'.
    for char in ['*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']:
        text = text.replace("\\" + char, char)
    return text

def universal_escape_markdown(protocol, text):
    if protocol == TELEGRAM_PROTOCOL:
        return escape_telegram_markdown(text)
    return escape_markdown(text)

def universal_unescape_markdown(protocol, text):
    if protocol == TELEGRAM_PROTOCOL:
        return unescape_telegram_markdown(text)
    return unescape_markdown(text)

def get_llm_model_name(llm: str) -> Tuple[str, str]:
    """Get LLM provider and model name from LLM string
    
    Args:
        llm: LLM string

    Returns:
        LLM provider and model
    """
    provider_str, model_name = llm.split(':')
    return LLMProvider.from_str(provider_str), model_name

def get_llm_prompt_template(prompt: Union[str, List[Dict[str, str]]]) -> List[Dict[str, str]]:
    """Get LLM prompt template from LLM string
    
    Args:
        prompt: Prompt string

    Returns:
        List of prompt messages
    """
    if isinstance(prompt, list):
        return prompt
    
    elif isinstance(prompt, str):
        if QUESTION_PLACEHOLDER in prompt:
            return [{'role': 'user', 'content': prompt}]
        
        return [
            {'role': 'system', 'content': prompt},
            {'role': 'user', 'content': QUESTION_PLACEHOLDER}
        ]
    
    raise ValueError(f"Invalid prompt type: {type(prompt)}")

def replace_refs_to_groups(m):
    group = m.group(1)
    group = group.replace('#', '')
    group = group.replace(' ', '')
    group = group.replace(',', '], [')
    return group

def replace_refs_period_to_groups(m):
    group = m.group(1)
    group = group[1:-1] # trim brakets []
    parts = group.split('-')
    i_from = int(parts[0].strip('#'))
    i_to   = int(parts[1].strip('#'))

    indexes = []
    for i in range(i_from, i_to + 1):
        indexes.append("[" + str(i) + "]")
    return ", ".join(indexes)

def get_fixed_markdown_with_refs(markdown, links):
    markdown = re.sub('(\[[\d\-\s,]+\])', replace_refs_to_groups, markdown)
    markdown = re.sub('(\[#?\d{1,2}\-#?\d{1,2}\])', replace_refs_period_to_groups, markdown)

    sorted_links = []
    if links:
        sorted_links = list(reversed(sorted(links.keys())))

    for index in sorted_links:
        source_pattern = '(?:(?:' + '|'.join([
            '[Ss]ource',
            '(?<=\[)[Ss]tart of(?: source)?',
            '(?<=\[)[Ee]nd of(?: source)?',
            '[Ии]сточник[аеу]',
            '[Нн]ачало',
            '[Кк]онец',
        ]) + '?):? )'

        for pattern in [
            f'{source_pattern}{index}',
            f'{source_pattern}?[#№]{index}',
            f'\[[\s\[]+{index}[\s\]]+\]',
            f'\(\[{index}\]\)',
            f'\({index}\)',
        ]:
            markdown = re.sub(pattern, f'[{index}]', markdown)

    markdown = markdown.replace('][', '], [')

    for index in sorted_links:
        markdown = re.sub(f'(?:\[{index}\], )+\[{index}\]', f'[{index}]', markdown)
    return markdown

def get_prepared_response_dict(
    request: QuepasaRequest,
    source: str,
    formatted_answer: Any,
    prompt_data: Dict[str, Any],
    think: Optional[str] = None,
    provider: str = None,
    model: str = None,
    llm_params: Dict[str, Any] = None
) -> Dict[str, Any]:
    """Prepare response dictionary.
    
    Args:
        request: Search request
        source: Source identifier
        formatted_answer: Formatted answer object
        prompt_data: Prompt data dictionary
        provider: LLM provider
        model: Model name
        llm_params: LLM parameters
        
    Returns:
        Response dictionary
    """
    # Create a serializable version of llm_params
    serializable_llm_params = None
    if llm_params:
        serializable_llm_params = {}
        for k, v in llm_params.items():
            if hasattr(v, '__dict__'):  # If it's an object
                serializable_llm_params[k] = str(v)
            else:
                serializable_llm_params[k] = v
    
    prompt = None
    used_documents = 0
    total_documents = 0
    if prompt_data != None:
        prompt = prompt_data['prompt_messages']
        used_documents = len(prompt_data['source_hash'])
        total_documents = len(prompt_data['source_hash']) + len(prompt_data['skipped_documents'])

    # Handle both enum and string types for formatted_answer.type
    type_value = formatted_answer.type.value if hasattr(formatted_answer.type, 'value') else str(formatted_answer.type)
    provider_value = None
    if provider:
        if isinstance(provider, LLMProvider):
            provider_value = provider.value
        else:
            provider_value = provider

    response_dict = {
        'type': f"{type_value}.{source}",
        'text': formatted_answer.text,
        'markdown': formatted_answer.markdown,
        'references': formatted_answer.references,
        'products': formatted_answer.products,
        'think': think,
        'data': {
            # 'skipped_documents': prompt_data['skipped_documents'],
            'total_documents': total_documents,
            'used_documents': used_documents
        },
        'metadata': {
            'request': get_serializable_request(request.to_dict()),
            'prompt': prompt,
            'provider': provider_value,
            'model': model,
            'llm_params': serializable_llm_params
        }
    }

    return response_dict

def get_think_and_answer_from_content(content: str) -> Tuple[Optional[str], Optional[int]]:
    """Get think and content from answer"""
    think = None
    answer = content

    match = re.search(r'<think>(.*?)(?:</think>|$)(.*)', content, re.DOTALL)
    if match:
        think = match.group(1).strip()
        answer = match.group(2).strip()
    return think, answer
