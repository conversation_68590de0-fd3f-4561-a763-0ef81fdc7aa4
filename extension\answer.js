// on its startup (Simple trigger) it runs the onOpen function that creates a custom menu and adds a menu item that when clicked runs the function "appendValuestoSheet"
function onOpen(){
  SpreadsheetApp.getUi()
  .createMenu("🤖 Rezolve")
  .addItem("Get Brain Results", "runBrainAPI")
  .addItem("Get Quepasa Results", "runQuepasaAPI")
  .addItem("Get LLM-as-a-judge Metrics", "runQAJudje")
  .addItem("Get Side-by-side Metrics", "runQASideBySide")
  .addItem("Get Golden-set-compare Metrics", "runQAGolden")
  .addToUi();
}

function runBrainAPI() {
  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  runAPIWithConfig( JSON.parse( sheet.getRange("A1").getValue() ), 'brain' );
}

function runQuepasaAPI() {
  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  runAPIWithConfig( JSON.parse( sheet.getRange("A1").getValue() ), 'quepasa' );
}

const endpointHash = {
  'dev': {
    'endpoint': "https://brain-api.dev.az.rezolve.com",
    'token': "d6af0326-08e9-497a-acfa-cb1ae2853b9f"
  },
  'demo': {
    'endpoint': "https://brain-api.demo.az.rezolve.com",
    'token': "9f061543-38cd-4e66-b164-833c2240cef3"
  }
}

/* 🔑  Token / host registry – fill in once and reuse everywhere */
const endpointHashQuepasa = {
  'dev': {
    'endpoint': "https://quepasa-searcher.dev.az.rezolve.com",
    'token':    "rezolve_shoeby_qwen3:0mrK9v3ZGexo/qrsQIWL+ZkRKmEPtlU4pq8wtIqyz7Ua+bKCPntjTEATRKRDISGo"
  },
};

/**
 * Collects every QUESTION in the sheet and returns an
 * object whose keys are the IDs and whose values are the questions.
 *
 * { "1": "Hello!", "2": "I want to buy an elephant.", ... }
 */
function buildParentItemHash(sheet) {
  const lastRow = sheet.getLastRow();
  const lastCol = sheet.getLastColumn();

  // ── 1. Grab the header row so we can find the column indexes dynamically ──
  // (Row 2 in your screenshot is the header; adjust if yours differs)
  const header = sheet.getRange(2, 1, 1, lastCol).getValues()[0];
  const idIdx       = header.indexOf('ID');        // 0-based within the row array
  const replyToIdx  = header.indexOf('REPLY TO');
  const questionIdx = header.indexOf('QUESTION');
  const goldenAnswerIdx = header.indexOf('GOLDEN_ANSWER');
  const savedIdealAnswerIdx = header.indexOf('SAVED_IDEAL_ANSWER');
  if (idIdx === -1 || questionIdx === -1) {
    throw new Error('Could not find "ID" or "QUESTION" column.');
  }

  // ── 2. Read all data rows in one shot (everything below the header) ──
  const data = sheet
    .getRange(3, 1, lastRow - 2, lastCol)          // start on row 3
    .getValues();

  // ── 3. Build the hash ──
  const parentItemHash = Object.create(null);  // cleaner than {}

  data.forEach(row => {
    const id        = row[idIdx];
    const replyToID = row[replyToIdx] + "";
    const question  = row[questionIdx];
    let answer = '';
    if (goldenAnswerIdx !== -1) {
      answer = row[goldenAnswerIdx];
    } else if (savedIdealAnswerIdx !== -1) {
      answer = row[savedIdealAnswerIdx];
    }

    if (id !== '' && question !== '') {            // skip blank lines
      parentItemHash[id] = {question, replyToID, answer};
    }
  });

  // Logger.log(parentQuestionHash);                  // inspect in Apps Script logs
  return parentItemHash;
}

function runAPIWithConfig( config, provider ) {
  let sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  let selectedRange = sheet.getActiveRange();

  const parentItemHash = buildParentItemHash(sheet);
  // SpreadsheetApp.getUi().alert( JSON.stringify( parentItemHash ) );

  let count = 0;
  let total = selectedRange.getNumRows() * selectedRange.getNumColumns();
  for (let i = 1; i <= selectedRange.getNumRows(); i++) {
    for (let j = 1; j <= selectedRange.getNumColumns(); j++) {
      let cell = selectedRange.getCell( i, j );
      let question = cell.getValue().toString();

      count += 1;

      const uuid = Utilities.getUuid();

      let replyToID = selectedRange.getCell( i, j ).offset(0, -1).getValue();
      let questions = [question];
      let conversation = [];
      while ( replyToID !== '' && parentItemHash[replyToID] != null ) {
        questions.push( parentItemHash[replyToID]['question'] );
        
        conversation.push({ 'role': "assistant", 'content': parentItemHash[replyToID]['answer'] });
        conversation.push({ 'role': "user", 'content': parentItemHash[replyToID]['question'] });

        replyToID = parentItemHash[replyToID]['replyToID'];
      } 

      questions.reverse();
      // SpreadsheetApp.getUi().alert( JSON.stringify( questions ) );

      conversation.reverse();
      // SpreadsheetApp.getUi().alert( JSON.stringify( conversation ) );

      let answer = 'EMPTY RESPONSE';
      if (provider == 'brain') {
        let data;
        for ( let q of questions ) { 
          SpreadsheetApp.getActive().toast( "[ " + count + " / " + total + " ]: " + q );
          data = callBrainApiStream( config, uuid, q, [], [] );
          // SpreadsheetApp.getUi().alert( JSON.stringify( data ) );
        }

        if ( data['response'] != null ) {
          answer = data['response'];
        } else if ( data['error'] != null ) {
          answer = "ERROR:\n" + data['error'];
        }
        answer = answer.replaceAll("<br>", "\n");

      } else if (provider == 'quepasa') {
        SpreadsheetApp.getActive().toast( "[ " + count + " / " + total + " ]: " + question );
        let data = callQuepasaApi( config, uuid, question, conversation );
        answer = data['text'];

      } else {
        answer = 'UNKNOWN PROVIDER';
      }

      let richValue = SpreadsheetApp.newRichTextValue().setText( answer );
      let richTextResult = richValue.build();
      selectedRange.getCell( i, j ).offset(0, 1)
        .setRichTextValue( richTextResult );
    }
  }
}
/**/

function callBrainApi( config, uuid, question, context, history ) {
  // 1️⃣  Body of the request
  const payload = {
    user_id: uuid,
    session_id: uuid,
    user_question: question,
    language: "English"
    // maximum_number_of_products_in_context: 10,
    // active_product_sku_id: "string"
  };

  const env = ( config['env'] != null ) ? config['env'] : 'dev';

  // 2️⃣  Request options
  const options = {
    method: "post",
    contentType: "application/json",
    headers: {
      accept: "application/json",
      "X-API-Key": endpointHash[ env ]['token'], // "d6af0326-08e9-497a-acfa-cb1ae2853b9f"
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true            // optional, but helpful while debugging
  };

  // 3️⃣  Endpoint
  const endpoint = endpointHash[ env ]['endpoint'] + "/merchant/v1/chat"; // "https://brain-api.dev.az.rezolve.com/merchant/v1/chat";

  // 4️⃣  Call & parse
  const response  = UrlFetchApp.fetch(endpoint, options);
  const json      = JSON.parse(response.getContentText());

  return json;
}


/**
 * Calls Rezolve Brain “chat-stream” endpoint and stitches the pieces together.
 *
 * Returns an object like:
 * {
 *   response   : "<full HTML / text>",
 *   contexts   : [ …products… ],
 *   responseId : "dd5c7202-3717-415e-9b76-e5fcc0903449",
 *   raw        : "<the raw stream text>"      // debugging-only
 * }
 */
function callBrainApiStream( config, uuid, question, context, history ) {
  /* 1️⃣  Request body ------------------------------------------------------ */
  const payload = {
    user_id        : uuid,
    session_id     : uuid,
    user_question  : question,
    language       : "English"
    // maximum_number_of_products_in_context: 10,
    // active_product_sku_id: "string"
  };

  const env = ( config['env'] != null ) ? config['env'] : 'dev';

  /* 2️⃣  Fetch options ----------------------------------------------------- */
  const options = {
    method              : "post",
    contentType         : "application/json",
    payload             : JSON.stringify(payload),
    muteHttpExceptions  : true,     // stop the script from crashing on 4xx/5xx
    headers             : {
      accept    : "application/json",
      "X-API-Key": endpointHash[ env ]['token'], // "d6af0326-08e9-497a-acfa-cb1ae2853b9f"
    }
  };

  // SpreadsheetApp.getUi().alert( JSON.stringify(payload) );

  /* 3️⃣  Hit the streaming endpoint --------------------------------------- */
  const endpoint = endpointHash[ env ]['endpoint'] + "/merchant/v1/chat-stream"; // "https://brain-api.dev.az.rezolve.com/merchant/v1/chat-stream";
  const httpResp = UrlFetchApp.fetch(endpoint, options);

  /* 4️⃣  The server pushes one JSON object per line ----------------------- */
  const rawStream = httpResp.getContentText();       // a long string with \n-separated JSON
  const lines     = rawStream.trim().split(/\r?\n/); // split on LF/CRLF

  let fullAnswer  = "";
  let contexts    = [];
  let responseId  = null;

  lines.forEach(l => {
    if (!l) return;                    // skip blank lines
    const chunk = JSON.parse(l);

    switch (chunk.stream_type) {
      case 4:
      case 2:                          // incremental answer tokens
        if (chunk.response) fullAnswer += chunk.response;
        break;

      case 1:                          // product / context blocks
        if (Array.isArray(chunk.contexts)) contexts.push(...chunk.contexts);
        break;

      case 3:                          // end-of-stream marker
        responseId = chunk.response_id || responseId;
        break;

      // stream_type 0 is not used in this API, other values ignored
    }
  });

  // SpreadsheetApp.getUi().alert( JSON.stringify({fullAnswer, responseId, rawStream}) );

  return { response: fullAnswer, contexts, responseId, raw: rawStream };
}

/**
 * Call QuePasa RAG “/retrieve/answer” API.
 *
 * @param {Object}  config    – your env config (e.g. {env: 'dev'})
 * @param {string}  uuid      – user / session ID
 * @param {string}  question  – user’s question
 * @param {Array=}  history   – optional chat history [{role:'user',content:'…'}, …]
 * @param {string=} source    – 'agentic' | 'answer' | etc.  (default: 'agentic')
 * @return {Object} Parsed JSON response from QuePasa
 */
function callQuepasaApi(config, uuid, question, history = [], source = 'agentic') {
  /* 1️⃣  Body */
  const payload = {
    question,
    history,                 // Pass an empty array if you don’t keep history yet
    source,                  // QuePasa router (‘agentic’, ‘search’, …)
    user_info: { id: "QA_TEST:" + uuid }  // Any other user metadata can live here
  };

  /* 2️⃣  Runtime env */
  const env = 'dev';

  /* 3️⃣  Request options */
  const options = {
    method: 'post',
    contentType: 'application/json',
    headers: {
      accept: 'application/json',
      Authorization: `Bearer ${endpointHashQuepasa[env].token}`
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true             // handy for debugging
  };

  /* 4️⃣  Endpoint */
  const endpoint =
    `${endpointHashQuepasa[env].endpoint}/retrieve/answer`;

  /* 5️⃣  Call & parse */
  const response = UrlFetchApp.fetch(endpoint, options);
  
  return JSON.parse(response.getContentText())['data'];
}
