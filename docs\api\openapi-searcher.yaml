openapi: 3.1.0
info:
  title: QuePasa Searcher Service
  description: Searcher service for RAG (Retrieval-Augmented Generation) search, answer generation, streaming, and conversation history.
  version: 1.0.0

servers:
  - url: https://quepasa-searcher.dev.az.rezolve.com
    description: Production server

tags:
  - name: Core
    description: Core service endpoints.
    x-displayOrder: 1
  - name: Search
    description: RAG search and answer generation endpoints.
    x-displayOrder: 2

paths:
  /health:
    get:
      summary: Health check
      description: Health check endpoint for Kubernetes probes
      operationId: healthCheck
      tags:
        - Core
      responses:
        '200':
          description: Health status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"

  /retrieve/search:
    post:
      summary: Search documents and chunks
      description: |
        Search through documents and chunks using RAG (Retrieval-Augmented Generation).
        
        This endpoint provides access to the searcher service for finding relevant content.
      operationId: searchDocuments
      tags:
        - Search
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchRequest'
      responses:
        '200':
          description: Search completed successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResponse'
        '500':
          description: Internal server error.

  /retrieve/answer:
    post:
      summary: Generate answers using RAG
      description: |
        Generate answers to questions using RAG (Retrieval-Augmented Generation).
        
        This endpoint combines search and LLM generation to provide contextual answers.
      operationId: generateAnswer
      tags:
        - Search
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchRequest'
      responses:
        '200':
          description: Answer generated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnswerResponse'
        '500':
          description: Internal server error.

  /retrieve/answer/stream:
    post:
      summary: Stream answers using RAG
      description: |
        Stream answers to questions using RAG with Server-Sent Events (SSE).
        
        This endpoint provides real-time streaming of generated answers.
      operationId: streamAnswer
      tags:
        - Search
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchRequest'
      responses:
        '200':
          description: Answer streaming started successfully.
          content:
            text/event-stream:
              schema:
                type: string
        '500':
          description: Internal server error.

  /retrieve/history:
    post:
      summary: Get conversation history
      description: |
        Retrieve conversation history for a user or session.
        
        This endpoint provides access to stored conversation data.
      operationId: getHistory
      tags:
        - Search
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HistoryRequest'
      responses:
        '200':
          description: History retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HistoryResponse'
        '500':
          description: Internal server error.

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: Opaque

  schemas:
    # Search schemas
    SearchRequest:
      type: object
      properties:
        question:
          type: string
          description: Natural language query to search for.
        client:
          type: string
          description: Client identifier.
        protocol:
          type: string
          description: Communication protocol to use.
          default: "http"
          enum: ["http", "sse", "webhook", "telegram"]
        source:
          type: string
          description: Source type for search.
          enum: ["all", "web", "documents", "dialogs", "waterfall"]
        stream:
          type: boolean
          description: Whether to stream the response.
          default: false
        classification:
          type: object
          properties:
            intent:
              type: string
              description: Intent classification.
            language_code:
              type: string
              description: Language code.
            related:
              type: array
              items:
                type: string
              description: Related terms.
            date:
              type: string
              description: Date information.
            query:
              type: string
              description: Query information.
          description: Classification information.
        metadata:
          type: object
          description: Additional metadata.
        engine:
          type: string
          description: Search engine type.
          default: "answer"
          enum: ["search", "answer", "train", "wiki", "chunks"]
        filter:
          type: array
          items:
            type: object
          description: Search filters.
        country:
          type: string
          description: Country code.
        language:
          type: string
          description: Language code.
        llm:
          type: string
          description: LLM provider and model.
        answer_llm:
          type: string
          description: LLM for answer generation.
        agent_llm:
          type: string
          description: LLM for agent operations.
        prompt:
          type: string
          description: Custom prompt template.
        kind:
          type: string
          description: Type of chunks to search.
          enum: ["text", "summary", "all"]
        domain:
          oneOf:
            - type: string
            - type: array
              items:
                type: string
          description: Document domain to search in (string or array of strings).
        bearer_token:
          type: string
          description: Bearer authentication token.
        client_token:
          type: string
          description: Client authentication token.
        external_user_token:
          type: string
          description: External user authentication token.
        history:
          type: array
          items:
            type: object
            properties:
              role:
                type: string
                description: Role of the message sender.
              content:
                type: string
                description: Content of the message.
              request:
                type: object
                description: Request metadata.
              tool_calls:
                type: array
                items:
                  type: object
                description: Tool call information.
              tool_call_id:
                type: string
                description: Tool call identifier.
              name:
                type: string
                description: Name of the tool.
              references:
                type: object
                description: Reference information.
          description: Conversation history.
        user_info:
          type: object
          properties:
            id:
              type: string
              description: Unique user identifier.
            name:
              type: string
              description: User display name.
            user_name:
              type: string
              description: Username.
            chat_id:
              type: integer
              description: Chat identifier.
            message_id:
              type: integer
              description: Message identifier.
          description: User information.
        webhook:
          type: object
          properties:
            endpoint:
              type: string
              description: Webhook endpoint URL.
            meta:
              type: object
              description: Additional metadata.
          description: Webhook configuration.
        constants:
          type: object
          properties:
            smth_went_wrong:
              type: string
              description: Error message template.
            i_dont_know:
              type: string
              description: Unknown response template.
          description: Response constants.
        command:
          type: string
          description: Command to execute.
        maximum_chunks_per_document:
          type: integer
          description: Maximum chunks per document to return.
        limit:
          type: integer
          description: Maximum total results to return.
        documents:
          type: array
          items:
            type: object
          description: Document filters.
        show_query:
          type: boolean
          description: Whether to show the query in response.
          default: false
        show_price:
          type: boolean
          description: Whether to show pricing information.
          default: false
        show_prompt:
          type: boolean
          description: Whether to show the prompt used.
          default: false
        show_all_references:
          type: boolean
          description: Whether to show all references.
          default: false
        show_text:
          type: boolean
          description: Whether to show text content.
          default: false
        show_keywords:
          type: boolean
          description: Whether to show keywords.
          default: false
        show_embedding:
          type: boolean
          description: Whether to show embedding information.
          default: false
        collection:
          type: string
          description: SNPD specific collection identifier.
        area:
          type: string
          description: SNPD specific area identifier.
        ts:
          type: integer
          description: Timestamp.
        now:
          type: string
          description: Current time string.
        waterfall:
          type: array
          items:
            type: object
          description: Waterfall configuration.
        waterfall_step_name:
          type: string
          description: Current waterfall step name.
      required:
        - question

    SearchResponse:
      type: object
      properties:
        status:
          type: string
          description: Status of the search operation.
        data:
          oneOf:
            - type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: Document identifier.
                  url:
                    type: string
                    description: Document URL.
                  text:
                    type: string
                    description: Document text content.
                  score:
                    type: number
                    description: Relevance score.
            - $ref: '#/components/schemas/QuepasaAnswer'
        error:
          type: string
          description: Error message if status indicates error.
      required:
        - status

    AnswerResponse:
      type: object
      properties:
        status:
          type: string
          description: Status of the answer generation.
        data:
          $ref: '#/components/schemas/QuepasaAnswer'
        error:
          type: string
          description: Error message if status indicates error.
      required:
        - status

    HistoryRequest:
      type: object
      properties:
        question:
          type: string
          description: Query to search history for.
        client:
          type: string
          description: Client identifier.
        user_info:
          type: object
          properties:
            id:
              type: string
              description: Unique user identifier.
          description: User information.
      required:
        - question
        - client

    HistoryResponse:
      type: object
      properties:
        status:
          type: string
          description: Status of the history retrieval.
        data:
          type: array
          items:
            type: object
            properties:
              role:
                type: string
                description: Role of the message sender.
              content:
                type: string
                description: Content of the message.
              request:
                type: object
                description: Request metadata.
              tool_calls:
                type: array
                items:
                  type: object
                description: Tool call information.
              tool_call_id:
                type: string
                description: Tool call identifier.
              name:
                type: string
                description: Name of the tool.
              references:
                type: object
                description: Reference information.
              ts:
                type: integer
                description: Timestamp.
        error:
          type: string
          description: Error message if status indicates error.
      required:
        - status

    QuepasaAnswer:
      type: object
      properties:
        type:
          type: string
          description: Type of answer.
        text:
          type: string
          description: Generated answer text.
        references:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Reference'
          description: Source references mapped by ID.
        markdown:
          type: string
          description: Answer in Markdown format.
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductItem'
          description: Product listings.
        think:
          type: string
          description: Reasoning process.
        price:
          type: number
          description: Cost information.
        data:
          type: object
          description: Additional data.
        metadata:
          type: object
          description: Response metadata.

    Reference:
      type: object
      properties:
        url:
          type: string
          description: Reference URL.
        text:
          type: string
          description: Reference text content.
        type:
          type: string
          description: Reference type.
        title:
          type: string
          description: Reference title.
        created_at:
          type: string
          description: Creation timestamp.
        start_position:
          type: string
          description: Start position in source.
        end_position:
          type: string
          description: End position in source.
        score:
          type: number
          description: Relevance score.
        sku:
          type: string
          description: Product SKU.
        metadata:
          type: string
          description: Additional metadata.
      required:
        - url
        - text

    ProductItem:
      type: object
      properties:
        id:
          type: string
          description: Product identifier.
        title:
          type: string
          description: Product title.
        url:
          type: string
          description: Product URL.
        collection:
          type: string
          description: Product collection.
        allMeta:
          type: object
          description: All product metadata.
      required:
        - id
        - title
        - url
        - collection
        - allMeta

security:
  - bearerAuth: [] 