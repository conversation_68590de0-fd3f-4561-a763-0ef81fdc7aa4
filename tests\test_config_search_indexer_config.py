import pytest
from configuration.base.searcher import SearcherConfig
from configuration.base.indexer import IndexerConfig
from quepasa.searcher.models.request import QuepasaRequest
from src.lib.constants import SOURCE_DOCUMENTS
from src.lib.embedding.providers import Embedding<PERSON>rovider

@pytest.fixture
def searcher_config():
    return SearcherConfig("test_client")

@pytest.fixture
def base_request():
    return QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram"
    )

def test_searcher_config_init(searcher_config):
    """Test SearcherConfig initialization"""
    assert searcher_config.client_code == "test_client"

def test_get_waterfall_config(searcher_config, base_request):
    """Test getting waterfall config"""
    searcher_config.set_request(base_request)
    assert searcher_config.get_waterfall_config() is None

def test_get_primary_search_source(searcher_config, base_request):
    """Test getting primary search source"""
    searcher_config.set_request(base_request)
    assert searcher_config.get_primary_search_source() == SOURCE_DOCUMENTS

def test_get_max_results_limit(searcher_config, base_request):
    """Test getting max results limit"""
    searcher_config.set_request(base_request)
    assert searcher_config.get_max_results_limit(source=base_request.source) == 50

def test_get_fuzzy_match_prefix_length(searcher_config, base_request):
    """Test getting fuzzy match prefix length"""
    searcher_config.set_request(base_request)
    assert searcher_config.get_fuzzy_match_prefix_length(source=base_request.source) == 3

def test_get_search_embedding_model(searcher_config, base_request):
    """Test getting search embedding model"""
    searcher_config.set_request(base_request)
    provider, model_version = searcher_config.get_search_embedding_model(source=base_request.source)
    assert provider == EmbeddingProvider.SBERT
    assert isinstance(model_version, str)

def test_get_excluded_search_fields(searcher_config, base_request):
    """Test getting excluded search fields"""
    searcher_config.set_request(base_request)
    fields = searcher_config.get_excluded_search_fields(source=base_request.source)
    assert isinstance(fields, list)

def test_should_filter_by_date(searcher_config, base_request):
    """Test checking if should filter by date"""
    searcher_config.set_request(base_request)
    assert searcher_config.should_filter_by_date(source=base_request.source) == False

def test_get_additional_search_filters(searcher_config, base_request):
    """Test getting additional search filters"""
    searcher_config.set_request(base_request)
    filters = searcher_config.get_additional_search_filters(source=base_request.source)
    assert isinstance(filters, list)

def test_rerank_search_results(searcher_config, base_request):
    """Test reranking search results"""
    searcher_config.set_request(base_request)
    items = [{"score": 0.8, "text": "test"}]
    reranked = searcher_config.rerank_search_results(source=base_request.source, items=items)
    assert isinstance(reranked, list)

def test_format_document(searcher_config, base_request):
    """Test document formatting"""
    searcher_config.set_request(base_request)
    document = {"title": "Test Doc"}
    item = {"text": "test text"}
    formatted = searcher_config.format_document(source=base_request.source, document=document, item=item)
    assert isinstance(formatted, dict)
    assert "text" in formatted

def test_should_deduplicate_results(searcher_config, base_request):
    """Test checking if should deduplicate results"""
    searcher_config.set_request(base_request)
    assert searcher_config.should_deduplicate_results(source=base_request.source) == True

def test_get_relevance_weights(searcher_config, base_request):
    """Test getting relevance weights"""
    searcher_config.set_request(base_request)
    weights = searcher_config.get_relevance_weights(source=base_request.source)
    assert hasattr(weights, "document")
    assert hasattr(weights, "chunk")
    assert weights.document == 0.5
    assert weights.chunk == 0.5

def test_get_document_relevance_weights(searcher_config, base_request):
    """Test getting document relevance weights"""
    searcher_config.set_request(base_request)
    weights = searcher_config.get_document_relevance_weights(source=base_request.source)
    assert hasattr(weights, "text")
    assert hasattr(weights, "semantic")
    assert weights.text == 0.2
    assert weights.semantic == 0.8

def test_get_chunk_relevance_weights(searcher_config, base_request):
    """Test getting chunk relevance weights"""
    searcher_config.set_request(base_request)
    weights = searcher_config.get_chunk_relevance_weights(source=base_request.source)
    assert hasattr(weights, "text")
    assert hasattr(weights, "semantic")
    assert weights.text == 0.2
    assert weights.semantic == 0.8

def test_should_use_missed_links(searcher_config, base_request):
    """Test checking if should use missed links"""
    searcher_config.set_request(base_request)
    assert searcher_config.should_use_missed_links(source=base_request.source) == False

def test_get_reranker_prompt(searcher_config, base_request):
    """Test getting reranker prompt"""
    searcher_config.set_request(base_request)
    assert searcher_config.get_reranker_prompt(source=base_request.source) is None

def test_get_document_reranker_prompt(searcher_config, base_request):
    """Test getting document reranker prompt"""
    searcher_config.set_request(base_request)
    assert searcher_config.get_document_reranker_prompt(source=base_request.source) is None

def test_get_chunk_reranker_prompt(searcher_config, base_request):
    """Test getting chunk reranker prompt"""
    searcher_config.set_request(base_request)
    assert searcher_config.get_chunk_reranker_prompt(source=base_request.source) is None 