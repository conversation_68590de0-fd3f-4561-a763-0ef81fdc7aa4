from typing import Union
from quepasa.searcher.models.request import QuepasaR<PERSON>quest
from .base_config import BaseConfig

CONSTANT_KEY = 'constant'
SMTH_WENT_WRONG_KEY = 'SMTH_WENT_WRONG'
I_DONT_KNOW_KEY = 'I_DONT_KNOW'

class AnswerConstantsConfig(BaseConfig):
    """Base configuration for answer-related constants."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]): 
        super().__init__(request_or_client_code)

    def _get_constant(self, key: str) -> str:
        """Get constant from request or client config"""
        # Check request constants
        if (
            CONSTANT_KEY in self._request
            and key in self._request[CONSTANT_KEY] 
            and self._request[CONSTANT_KEY][key].strip() != ""
        ):                
            return self._request[CONSTANT_KEY][key].strip()
            
        return None
    
    def get_SMTH_WENT_WRONG_constant(self, source: str, language_code: str) -> str:
        """Get error message for when something goes wrong.
        
        Previously: get_SMTH_WENT_WRONG_constant()
        
        Args:
            source: Source type
            language_code: Language code
            
        Returns:
            Error message string
        """
        # Check request constants
        value = self._get_constant(SMTH_WENT_WRONG_KEY)
        if value:
            return value
        
        return "I apologize, but something went wrong while processing your request. Please try again later."
        
    def get_I_DONT_KNOW_constant(self, source: str, language_code: str) -> str:
        """Get message for when no answer is found.
        
        Previously: get_I_DONT_KNOW_constant()
        
        Args:
            source: Source type
            language_code: Language code
        Returns:
            No answer message string
        """
        value = self._get_constant(I_DONT_KNOW_KEY)
        if value:
            return value
        
        return "I apologize, but I don't have enough information to answer your question accurately." 
