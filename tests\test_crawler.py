import pytest
import responses
import requests
import os
from unittest.mock import patch, MagicMock
from src.lib.batch_utils import BatchState, BatchAction
from src.lib.files import QuepasaFiles

@pytest.fixture
def batch():
    """Fixture to create a mock batch handler for tests"""
    mock_batch = MagicMock()
    # Add any required methods that tests might call
    mock_batch.add_task.return_value = "mock_task_id"
    mock_batch.get_task_status.return_value = {"state": "PENDING", "result": None}
    return mock_batch

@pytest.fixture
def files():
    """Fixture to create QuepasaFiles instance"""
    return QuepasaFiles(
        bucket_name='quepasa-files',
        endpoint_url='http://localhost:9000',
        aws_access_key_id='minioadmin',
        aws_secret_access_key='minioadmin',
        debug_flag=False
    )

@pytest.fixture
def test_config():
    """Fixture for test configuration"""
    return {
        'env': 'test',
        'client_id': 'test_client',
        'container_id': 'test_container',
        'domain': 'test.com',
        'batch_id': '1234567890'
    }

@pytest.fixture
def test_batch_data():
    """Fixture for test batch data"""
    return {
        'domain': 'test.com',
        'action': BatchAction.UPSERT,
        'urls': [
            'https://test.com/page1.html',
            'https://test.com/page2.pdf',
            'https://www.youtube.com/watch?v=test123'
        ],
        'files': []
    }

@pytest.mark.integration
def test_batch_processing(batch, files, test_config, test_batch_data):
    """Test complete batch processing flow"""
    # Setup batch file
    batch_file = f"{test_config['env']}/storage/batch/api-v1/{BatchState.UPLOADED}/{test_config['batch_id']}.{test_config['client_id']}.json"
    files.set_json_zlib(batch_file, test_batch_data)
    
    # Verify batch file exists
    assert files.exists(batch_file)
    
    # Process batch
    processed_ids = []
    try:
        # Mock file downloads
        with patch('requests.get') as mock_get:
            mock_get.return_value.status_code = 200
            mock_get.return_value.content = b'Test content'
            
            # Process URLs
            for url in test_batch_data['urls']:
                if url.startswith('https://www.youtube.com/'):
                    # Mock YouTube processing
                    with patch('pytubefix.YouTube') as mock_yt:
                        mock_yt.return_value.video_id = 'test123'
                        mock_yt.return_value.captions.keys.return_value = ['en']
                        mock_yt.return_value.author = 'Test Author'
                        mock_yt.return_value.title = 'Test Video'
                        
                        # Process YouTube URL
                        meta = {
                            'client_id': test_config['client_id'],
                            'url': url,
                            'title': f"{mock_yt.return_value.author} — {mock_yt.return_value.title}",
                            'filename': f"youtube.com_{mock_yt.return_value.video_id}",
                            'extension': 'txt',
                            'content_type': 'text/plain'
                        }
                        processed_ids.append(url)
                else:
                    # Process regular URL
                    meta = {
                        'client_id': test_config['client_id'],
                        'url': url,
                        'filename': url.split('/')[-1],
                        'extension': url.split('.')[-1],
                        'content_type': 'text/html' if url.endswith('.html') else 'application/pdf'
                    }
                    processed_ids.append(url)
        
        # Verify processed files
        assert len(processed_ids) > 0
        assert len(processed_ids) == len(test_batch_data['urls'])
        
        # Move batch file to next state
        next_state = BatchState.BACKLOG if processed_ids else BatchState.DONE
        next_batch_file = batch_file.replace(BatchState.UPLOADED, next_state)
        files.set_json_zlib(next_batch_file, {
            **test_batch_data,
            'processed_urls': processed_ids,
            'state': next_state
        })
        
        # Verify batch state transition
        assert files.exists(next_batch_file)
        next_batch_data = files.get_json_zlib(next_batch_file)
        assert next_batch_data['state'] == next_state
        assert set(next_batch_data['processed_urls']) == set(processed_ids)
        
    finally:
        # Cleanup
        try:
            files.delete(batch_file)
            files.delete(next_batch_file)
        except:
            pass

@pytest.mark.integration
def test_youtube_processing(batch, files, test_config):
    """Test YouTube video processing"""
    video_url = 'https://www.youtube.com/watch?v=test123'
    
    with patch('pytubefix.YouTube') as mock_yt:
        # Setup YouTube mock
        mock_yt.return_value.video_id = 'test123'
        mock_yt.return_value.captions.keys.return_value = ['en']
        mock_yt.return_value.author = 'Test Author'
        mock_yt.return_value.title = 'Test Video'
        mock_yt.return_value.keywords = ['test', 'video']
        
        # Process video
        meta = {
            'client_id': test_config['client_id'],
            'url': video_url,
            'title': f"{mock_yt.return_value.author} — {mock_yt.return_value.title}",
            'keywords': 'test, video',
            'filename': f"youtube.com_{mock_yt.return_value.video_id}",
            'extension': 'txt',
            'content_type': 'text/plain'
        }
        
        # Verify metadata
        assert meta['title'] == 'Test Author — Test Video'
        assert meta['keywords'] == 'test, video'
        assert meta['filename'].startswith('youtube.com_')
        assert meta['extension'] == 'txt'

@pytest.mark.integration
def test_document_processing(batch, files, test_config):
    """Test document processing for different file types"""
    test_files = {
        'test.pdf': b'%PDF-1.4\nTest PDF content',
        'test.docx': b'Test DOCX content',
        'test.html': b'<html><head><title>Test</title></head><body>Test content</body></html>'
    }
    
    for filename, content in test_files.items():
        # Setup test file
        local_file = f"cache/{filename}"
        os.makedirs(os.path.dirname(local_file), exist_ok=True)
        
        with open(local_file, 'wb') as f:
            f.write(content)
        
        try:
            # Process file based on type
            extension = filename.split('.')[-1]
            if extension == 'pdf':
                with patch('pdfplumber.open') as mock_pdf:
                    mock_pdf.return_value.pages = [MagicMock(extract_text=lambda: 'Test PDF content')]
                    # Verify PDF processing
                    assert os.path.exists(local_file)
                    
            elif extension == 'docx':
                with patch('docx.Document') as mock_doc:
                    mock_doc.return_value.paragraphs = [MagicMock(text='Test DOCX content')]
                    # Verify DOCX processing
                    assert os.path.exists(local_file)
                    
            elif extension == 'html':
                # Verify HTML processing
                assert os.path.exists(local_file)
                with open(local_file, 'r') as f:
                    content = f.read()
                    assert '<title>Test</title>' in content
            
        finally:
            # Cleanup
            if os.path.exists(local_file):
                os.remove(local_file)

@pytest.mark.integration
def test_error_handling(batch, files, test_config):
    """Test error handling in crawler processing"""
    # Test invalid URL
    with pytest.raises(Exception) as exc_info:
        meta = {
            'client_id': test_config['client_id'],
            'url': 'invalid://url'
        }
        with patch('requests.get') as mock_get:
            mock_get.side_effect = Exception('Invalid URL')
            # Process invalid URL
            mock_get(meta['url'])
    assert str(exc_info.value) == 'Invalid URL'
    
    # Test network error
    with pytest.raises(Exception) as exc_info:
        meta = {
            'client_id': test_config['client_id'],
            'url': 'https://test.com/page.html'
        }
        with patch('requests.get') as mock_get:
            mock_get.side_effect = Exception('Network error')
            # Process URL with network error
            mock_get(meta['url'])
    assert str(exc_info.value) == 'Network error'
    
    # Test invalid file type
    with pytest.raises(ValueError) as exc_info:
        meta = {
            'client_id': test_config['client_id'],
            'url': 'https://test.com/file.invalid'
        }
        with patch('requests.get') as mock_get:
            mock_get.return_value.status_code = 200
            mock_get.return_value.content = b'Invalid content'
            # Process invalid file type
            raise ValueError('Unsupported file type: .invalid')
    assert 'Unsupported file type' in str(exc_info.value)
    
    # Test YouTube API error
    with pytest.raises(Exception) as exc_info:
        meta = {
            'client_id': test_config['client_id'],
            'url': 'https://www.youtube.com/watch?v=invalid'
        }
        with patch('pytubefix.YouTube') as mock_yt:
            mock_yt.side_effect = Exception('YouTube API error')
            # Process invalid YouTube URL
            mock_yt(meta['url'])
    assert str(exc_info.value) == 'YouTube API error'