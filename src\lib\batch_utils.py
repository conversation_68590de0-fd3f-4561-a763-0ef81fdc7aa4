import os
import hashlib
from datetime import datetime
from enum import Enum
from typing import Dict, <PERSON><PERSON>, Tu<PERSON>, List

from src.lib.files import QuepasaFiles
from src.lib.utils import get_basepath, get_filename_id

from src.lib.celery_app import app
from src.lib.logger import QuepasaLogger

class BatchState(str, Enum):
    """Enum for batch processing states"""
    UPLOADED = 'uploaded'
    BACKLOG = 'backlog'
    IN_PROGRESS = 'in_progress'
    DONE_WITHOUT_INDEXING = 'done_without_indexing'
    DONE = 'done'
    FAILED = 'failed'

class BatchAction(str, Enum):
    """Enum for batch actions"""
    UPSERT = 'upsert'
    REPLACE = 'replace'
    DELETE = 'delete'
    RESET = 'reset'

class BatchStorage(str, Enum):
    """Enum for storage directories"""
    CRAWLER = 'crawler'
    DATA_PROCESSOR = 'data-processor'
    INDEXER = 'indexer'

class DataProcessorAction(str, Enum):
    """Enum for data processor actions"""
    UPSERT = 'upsert'
    DELETE = 'delete'

# Environment
ENV = os.getenv('QUEPASA_ENV', 'prod')

# API Configuration
QUEPASA_IMAGE_VERSION = os.getenv('QUEPASA_IMAGE_VERSION', 'latest')

API_V1_CRAWLER_POOL_ID = "crawler-v1"
API_V1_CRAWLER_CONTAINER_IMAGE = os.getenv('QUEPASA_CRAWLER_IMAGE', f'quepasa-on-premises-c-crawler') + ':' + QUEPASA_IMAGE_VERSION

API_V1_DATA_PROCESSOR_POOL_ID = "data-processor-v1"
API_V1_DATA_PROCESSOR_CONTAINER_IMAGE = os.getenv('QUEPASA_DATA_PROCESSOR_IMAGE', f'quepasa-on-premises-c-data-processor') + ':' + QUEPASA_IMAGE_VERSION

API_V1_INDEXER_POOL_ID = "indexer-v1"
API_V1_INDEXER_CONTAINER_IMAGE = os.getenv('QUEPASA_INDEXER_IMAGE', f'quepasa-on-premises-c-indexer') + ':' + QUEPASA_IMAGE_VERSION

# Storage Paths
API_V1_VERSION = "api-v1"
STORAGE_ROOT = f"{ENV}/storage"
STORAGE_BATCH_ROOT = f"{STORAGE_ROOT}/batch"
STORAGE_BATCH_API_V1_ROOT = f"{STORAGE_BATCH_ROOT}/{API_V1_VERSION}"
BATCH_STORAGE_DIR = {
    BatchState.UPLOADED: f"{STORAGE_BATCH_API_V1_ROOT}/{BatchState.UPLOADED}",
    BatchState.BACKLOG: f"{STORAGE_BATCH_API_V1_ROOT}/{BatchState.BACKLOG}",
    BatchState.IN_PROGRESS: f"{STORAGE_BATCH_API_V1_ROOT}/{BatchState.IN_PROGRESS}",
    BatchState.DONE_WITHOUT_INDEXING: f"{STORAGE_BATCH_API_V1_ROOT}/{BatchState.DONE_WITHOUT_INDEXING}",
    BatchState.DONE: f"{STORAGE_BATCH_API_V1_ROOT}/{BatchState.DONE}",
    BatchState.FAILED: f"{STORAGE_BATCH_API_V1_ROOT}/{BatchState.FAILED}"
}

YOUTUBE_STORAGE = f"{STORAGE_ROOT}/crawler/youtube"
CRAWLER_STORAGE = f"{STORAGE_ROOT}/crawler/{API_V1_VERSION}"
DATA_PROCESSOR_STORAGE = f"{STORAGE_ROOT}/data-processor/{API_V1_VERSION}"
INDEXER_STORAGE = f"{STORAGE_ROOT}/indexer/{API_V1_VERSION}"

STORAGE_DIR = {
    BatchStorage.CRAWLER: CRAWLER_STORAGE,
    BatchStorage.DATA_PROCESSOR: DATA_PROCESSOR_STORAGE,
    BatchStorage.INDEXER: INDEXER_STORAGE
}

# Initialize QuepasaFiles with default values
qp_files = QuepasaFiles()

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)


class BatchUtils:
    """Utility class for handling batch operations"""

    @staticmethod
    def add_task(task_type: str, client_id: str, batch_id: Optional[str] = None) -> str:
        """Add a single task to the queue"""
        if task_type not in ['crawler', 'data-processor', 'indexer']:
            raise ValueError(f"Invalid task type: {task_type}")
            
        if not client_id:
            raise ValueError("Missing required client_id")
            
        try:
            # Send task to appropriate queue
            logger.info(f"[{client_id}, {batch_id}] Sending task to queue {task_type}")
            result = app.send_task(
                f"{task_type}.process_batch",
                args=[client_id, batch_id],
                queue=task_type,
                retry=True,
                retry_policy={
                    'max_retries': 3,
                    'interval_start': 0,
                    'interval_step': 0.2,
                    'interval_max': 0.2,
                }
            )
            logger.info(f"[{client_id}, {batch_id}] Task sent to queue {task_type} result: {result.id}")
            return result.id

        except Exception as e:
            logger.error(f"[{client_id}, {batch_id}] ERROR: Failed to add task: {str(e)}")
            raise
    
    @staticmethod
    def get_batch_filename(client_id: str, batch_id: str, state: BatchState) -> str:
        """Generate batch filename based on parameters."""
        base_path = get_basepath(batch_id, offset=3)
        return f"{STORAGE_BATCH_API_V1_ROOT}/{state}/{base_path}.{client_id}.json"
    
    @staticmethod
    def parse_batch_filename(batch_file: str) -> Tuple[str, str]:
        """Extract batch ID and client ID from batch filename.
        
        Args:
            batch_file: Full path to batch file (e.g. '/path/to/1234567890.123.client_id.json')
            
        Returns:
            Tuple containing:
                - batch_id (str): Combined timestamp ID (e.g. '1234567890.123')
                - client_id (str): Client identifier
        """
        batch_id = '.'.join(batch_file.split('/')[-1].split('.')[0:2])
        client_id = batch_file.split('.')[-2]
        return client_id, batch_id
    
    @staticmethod
    def get_storage_dir(storage: BatchStorage, client_id: str, domain: str) -> str:
        """Get storage directory for a batch."""
        domain_dir = hashlib.md5(str(domain).encode('utf-8')).hexdigest()
        return f"{STORAGE_DIR[storage]}/{client_id}/{domain_dir}"
    
    @staticmethod
    def get_batch_files_by_client(state: BatchState, target_client_id: Optional[str] = None, target_batch_id: Optional[str] = None) -> Dict[str, List[Dict]]:
        """Get all batches grouped by client"""
        batch_files_by_client = {}
        
        # Read all batch files from backlog
        for file in qp_files.get_files(BATCH_STORAGE_DIR[state], True):
            client_id, batch_id = BatchUtils.parse_batch_filename(file)

            # Filter by target client_id or batch_id if specified
            if (
                target_client_id and client_id != target_client_id
                or target_batch_id and batch_id != target_batch_id
            ):
                continue

            if not batch_id.replace('.', '').isdigit():
                continue
            
            ts = float(batch_id)
                            
            # Group by client
            if client_id not in batch_files_by_client:
                batch_files_by_client[client_id] = []
                
            batch_files_by_client[client_id].append({
                'ts': ts,
                'file': file
            })
        
        # Sort batches by timestamp
        for client_id in batch_files_by_client:
            batch_files_by_client[client_id].sort(key=lambda x: x['ts'])
            
        # Sort clients by earliest batch timestamp
        return dict(sorted(
            batch_files_by_client.items(),
            key=lambda item: item[1][0]['ts'] if item[1] else float('inf')
        ))

    @staticmethod
    def get_storage_filename(client_id: str, domain: str, filename_id: str) -> Tuple[str, str]:
        """Get filenames for a document"""
        data_dir = BatchUtils.get_storage_dir(BatchStorage.CRAWLER, client_id, domain)
        return f"{data_dir}/{filename_id}"

    @staticmethod
    def get_document_filenames(client_id: str, domain: str, doc_id: str) -> Tuple[str, str]:
        """Get filenames for a document"""
        filename_id = get_filename_id(doc_id)
        data_dir = BatchUtils.get_storage_dir(BatchStorage.DATA_PROCESSOR, client_id, domain)
        document_filename = f"{data_dir}/{get_basepath(filename_id)}"
        document_file_meta_json = document_filename + ".meta.json"
        document_file_zlib_json = document_filename + ".zlib.json"
        return document_file_zlib_json, document_file_meta_json

    @staticmethod
    def get_generated_batch_id() -> str:
        """Generate a new batch ID based on current timestamp."""
        timestamp = datetime.utcnow().timestamp()
        return str(timestamp)

    @staticmethod
    def create_batch(client_id: str, state: BatchState, batch_data: Dict, files=None) -> str:
        """Create a new batch with given data.
        
        Args:
            client_id: Client identifier
            state: Batch state
            batch_data: Batch data to store
            files: Optional QuepasaFiles instance. If not provided, uses global instance.
        """
        files = files or qp_files
        batch_id = BatchUtils.get_generated_batch_id()
        batch_filename = BatchUtils.get_batch_filename(client_id, batch_id, state)
        batch_data['batch_id'] = batch_id
        files.set_json_zlib(batch_filename, batch_data)
        return batch_id

    @staticmethod
    def get_batch_status(client_id: str, batch_id: str, files=None) -> Tuple[Optional[BatchState], Optional[Dict]]:
        """Get status of a batch.
        
        Args:
            client_id: Client identifier
            batch_id: Batch identifier
            files: Optional QuepasaFiles instance. If not provided, uses global instance.
        """
        files = files or qp_files
        for state in BatchState:
            batch_file = BatchUtils.get_batch_filename(client_id, batch_id, state)
            if files.exists(batch_file):
                if state in [BatchState.DONE, BatchState.DONE_WITHOUT_INDEXING, BatchState.IN_PROGRESS]:
                    try:
                        data = files.get_json_zlib(batch_file)
                        return state, {
                            'domain': data.get('domain'),
                            'processed_ids': data.get('processed_ids', [])
                        }

                    except:
                        pass

                return state, None
                
        return None, None
