import os
import json
import requests
from typing import List, Dict, Any, AsyncGenerator

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .base import BaseLLM
from .cache import LLMCacheMixin
from .providers import LLMProvider

logger = QuepasaLogger().get_instance(__name__)

class YandexLLM(BaseLLM, LLMCacheMixin):
    """Yandex LLM provider.
    
    This class provides access to Yandex's language models through their API.
    """
    
    def __init__(self):
        """Initialize Yandex LLM provider."""
        super().__init__()
        self.api_key = os.getenv('YANDEX_API_KEY')
        self.folder_id = os.getenv('YANDEX_FOLDER_ID')
        if not self.api_key or not self.folder_id:
            raise ValueError("YANDEX_API_KEY and YANDEX_FOLDER_ID environment variables must be set")

    @property
    def provider(self) -> LLMProvider:
        return LLMProvider.YANDEX

    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a completion from Yandex API.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Returns:
            Generated response text
        """
        try:
            url = "https://llm.api.cloud.yandex.net/foundationModels/v1/completion"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Api-Key {self.api_key}",
                "x-folder-id": self.folder_id
            }
            
            # Convert prompts to Yandex format
            messages = []
            for msg in prompt_list:
                role = "user" if msg["role"] == "user" else "assistant"
                messages.append({
                    "role": role,
                    "text": msg["content"]
                })
            
            data = {
                "modelUri": f"gpt://{self.folder_id}/{model_version}",
                "completionOptions": {
                    "stream": False,
                    "temperature": 0.0,
                    "maxTokens": answer_prompt_size
                },
                "messages": messages
            }
            
            if json_mode:
                data["completionOptions"]["format"] = "json"
            
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            if "result" in result and "alternatives" in result["result"]:
                return result["result"]["alternatives"][0]["message"]["text"].strip()
            else:
                raise ValueError("Unexpected response format from Yandex API")
                
        except Exception as e:
            logger.error(f"Failed to get answer from Yandex: {str(e)}")
            raise
