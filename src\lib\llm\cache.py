import os
import json
import hashlib
from abc import ABC
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Generator, AsyncGenerator, List, Dict

from src.lib.logger import <PERSON>pasa<PERSON>ogger
from src.lib.files import QuepasaFiles

logger = QuepasaLogger().get_instance(__name__)
qp_files = QuepasaFiles()

class LLMCacheMixin:
    """Mixin class that provides caching functionality for LLM responses."""

    def __init__(self):
        """Initialize the cache mixin.
        
        Note: This should be called via super().__init__() in the implementing class.
        """
        self._cache_dir = "cache/llm"
        self._local_cache_dir = os.path.join(os.path.dirname(__file__), "../../../cache/llm")
        os.makedirs(self._local_cache_dir, exist_ok=True)

    def _get_cache_paths(self, model_version: str, prompt_list: list, answer_prompt_size: Optional[int] = None, json_mode: bool = False) -> Tuple[str, str]:
        """Get the local and remote cache paths for a given request."""
        cache_key = hashlib.sha256(
            json.dumps({
                "model_version": model_version,
                "prompt_list": prompt_list,
                "answer_prompt_size": answer_prompt_size,
                "json_mode": json_mode
            }).encode()
        ).hexdigest()
        
        path_parts = []
        for i in range(3):  # Create 3 levels of directories
            if i + 1 <= len(cache_key):
                path_parts.append(cache_key[0:i + 1])
        path_parts.append(cache_key)
        
        # Local cache path
        local_path = os.path.join(self._local_cache_dir, model_version, *path_parts) + '.txt'

        # Remote cache path
        remote_path = f"{self._cache_dir}/{model_version}/{'/'.join(path_parts)}.txt"
        return local_path, remote_path

    def _load_local_cache(self, local_path) -> Optional[str]:
        """Load data from local cache."""
        try:
            if os.path.exists(local_path):
                with open(local_path, 'r') as f:
                    return f.read()
        except Exception as e:
            logger.warning(f"Failed to load local cache {local_path}: {e}")
        return None

    def _load_remote_cache(self, remote_path, local_path) -> Optional[str]:
        """Load data from remote cache."""
        try:
            if qp_files.exists(remote_path):
                qp_files.download_file(remote_path, local_path)
                return self._load_local_cache(local_path)
        except Exception as e:
            logger.warning(f"Failed to load remote cache {remote_path}: {e}")
        return None
    
    def _save_llm_answer(self, answer: str, local_path: str, remote_path: str) -> None:
        """Save answer to both local and remote cache.
        
        Args:
            embedding: The embedding to save
            local_path: Path to local cache file
            remote_path: Path to remote cache file
        """
        try:
            # Save locally
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            with open(local_path, 'w') as f:
                f.write(answer)
            
            # Save remotely
            qp_files.upload_file(local_path, remote_path)

        except Exception as e:
            logger.warning(f"Failed to save embedding cache: {str(e)}")

    def get_cached_answer(self, model_version: str, prompt_list: list, answer_prompt_size: Optional[int] = None, json_mode: bool = False):
        """Get a cached answer for the given request."""
        local_path, remote_path = self._get_cache_paths(model_version, prompt_list, answer_prompt_size, json_mode)
        
        # Try local cache first
        cached_answer = self._load_local_cache(local_path)
        if cached_answer:
            return cached_answer
        
        # Try remote cache
        cached_answer = self._load_remote_cache(remote_path, local_path)
        if cached_answer:
            return cached_answer
        
        # Get new response
        answer = self.get_answer(model_version, prompt_list, answer_prompt_size, json_mode)
        self._save_llm_answer(answer, local_path, remote_path)
        return answer

    async def get_cached_streaming_chunks(self, model_version: str, prompt_list: list, answer_prompt_size: Optional[int] = None, json_mode: bool = False):
        """Get cached streaming chunks for the given request."""
        local_path, remote_path = self._get_cache_paths(model_version, prompt_list, answer_prompt_size, json_mode)
        
        # Try local cache first
        try:
            cached_answer = self._load_local_cache(local_path)
            if cached_answer:
                yield cached_answer
                return
            
        except Exception as e:
            logger.warning(f"Failed to load local cache {local_path}: {e}")
        
        # Try remote cache
        try:
            cached_answer = self._load_remote_cache(remote_path, local_path)
            if cached_answer:
                yield cached_answer
                return
            
        except Exception as e:
            logger.warning(f"Failed to load remote cache {remote_path}: {e}")
        
        # Get new response
        collected_answer = []
        try:        
            # Get the generator from get_streaming_answer
            generator = self.get_streaming_answer(model_version, prompt_list, answer_prompt_size, json_mode)
            
            # Check if it's an async generator or a regular generator
            if hasattr(generator, '__aiter__'):
                # For async generators, use async for
                async for chunk in generator:
                    yield chunk
                    collected_answer.append(chunk)

            else:
                # For regular generators, use regular for
                for chunk in generator:
                    yield chunk
                    collected_answer.append(chunk)
                
            if collected_answer:
                self._save_llm_answer(''.join(collected_answer), local_path, remote_path)

        except Exception as e:
            logger.warning(f"Failed to get streaming chunks: {e}")
            raise e
