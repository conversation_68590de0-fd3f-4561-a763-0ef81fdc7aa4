apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: standard
  namespace: quepasa
provisioner: kubernetes.io/azure-disk
parameters:
  storageaccounttype: Standard_LRS
  kind: Managed
---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: premium
  namespace: quepasa
provisioner: kubernetes.io/azure-disk
parameters:
  storageaccounttype: Premium_LRS
  kind: Managed
  cachingmode: ReadWrite 