import io
import csv
from typing import Dict, Any, List
from .base import BaseProcessor
from ...utils import split_text, get_title_by_filename

class CSVProcessor(BaseProcessor):
    """Processor for CSV files"""

    def _process_impl(self, content: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract text from CSV content
        
        Args:
            content: CSV file content
            meta: Metadata about the CSV
            
        Returns:
            Dictionary containing extracted text and title
        """
        # Validate metadata
        error = self.validate_meta(meta, ['filename'])
        if error:
            raise ValueError(error)

        # Try different encodings
        encodings = ['utf-8', 'latin1', 'cp1252']
        text_content = None
        
        for encoding in encodings:
            try:
                text_content = content.decode(encoding)
                break
            except UnicodeDecodeError:
                continue
                
        if text_content is None:
            raise ValueError("Could not decode CSV content with any supported encoding")

        chunks = []
        title = None
        full_text = []
        line_numbers = []
        current_line = 1
        
        try:
            # Use StringIO to create a file-like object
            csv_file = io.StringIO(text_content)
            
            # Try to detect dialect with a larger sample
            sample_size = min(32768, len(text_content))  # Use up to 32KB for dialect detection
            try:
                dialect = csv.Sniffer().sniff(text_content[:sample_size])
            except:
                # If sniffing fails, use Excel dialect as fallback
                dialect = csv.excel
            
            # Create CSV reader with the detected or default dialect
            reader = csv.reader(csv_file, dialect)
            
            # Read header first
            try:
                header = next(reader)
                if header:
                    full_text.append('\t'.join(header))
                    line_numbers.append(current_line)
                current_line += 1
            except StopIteration:
                pass
            
            # Process all rows as content
            for row in reader:
                # Join non-empty values with tabs
                row_text = '\t'.join(cell.strip() for cell in row if cell.strip())
                if row_text:
                    full_text.append(row_text)
                    line_numbers.append(current_line)
                current_line += 1
            
            # Join all text and split into chunks
            raw_chunks = split_text('\n'.join(full_text))
            
            # Map line numbers to chunks
            current_pos = 0
            for chunk in raw_chunks:
                # Find start and end positions of chunk in full text
                chunk_start_pos = current_pos
                chunk_end_pos = current_pos + len(chunk)
                
                # Count newlines before start and end to get line numbers
                start_line = full_text[:chunk_start_pos].count('\n') + 1
                end_line = full_text[:chunk_end_pos].count('\n') + 1
                
                chunks.append({
                    'text': chunk,
                    'position': f"lines {start_line}-{end_line}"
                })
                current_pos = chunk_end_pos + 1  # +1 to skip newline
            
            return {
                'chunks': chunks,
                'title': get_title_by_filename(meta['filename']),
                'filename': meta['filename']
            }
            
        except Exception as e:
            self.logger.error(f"Failed to process CSV: {str(e)}", exc_info=True)
            raise
        finally:
            if 'csv_file' in locals():
                csv_file.close()