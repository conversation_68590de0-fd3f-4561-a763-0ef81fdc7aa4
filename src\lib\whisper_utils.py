from typing import Dict, Any, Optional
import time
import random
from functools import wraps

from src.lib.whisper.factory import WhisperFactory

def retry_with_exponential_backoff(max_retries: int = 10):
    """Decorator that implements exponential backoff retry logic
    
    Args:
        max_retries: Maximum number of retries before giving up
        
    Returns:
        Decorated function with retry logic
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retry = 0
            while retry < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if retry == max_retries - 1:  # Last retry
                        raise e
                    
                    # Calculate sleep time with exponential backoff and jitter
                    sleep_time = (2 ** retry) + random.uniform(0, 1)
                    time.sleep(sleep_time)
                    retry += 1
            return None
        return wrapper
    return decorator

@retry_with_exponential_backoff()
def get_segments(provider: str, model: str, audio_data: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
    """Get transcription segments from the specified provider.
    
    Args:
        provider: The transcription provider to use (e.g., 'replicate')
        model: Model to use (e.g., 'default', 'diarization')
        audio_data: Audio data as bytes
        meta: Dictionary containing metadata
        
    Returns:
        Dictionary containing transcription results
    """
    whisper = WhisperFactory.get_whisper(provider)
    return whisper.get_segments(model, audio_data, meta)

@retry_with_exponential_backoff()
def get_cached_segments(provider: str, model: str, audio_data: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
    """Get cached transcription segments from the specified provider.
    
    Args:
        provider: The transcription provider to use (e.g., 'replicate')
        model: Model to use (e.g., 'default', 'diarization')
        audio_data: Audio data as bytes
        meta: Dictionary containing metadata
        
    Returns:
        Dictionary containing transcription results from cache or freshly computed
    """
    whisper = WhisperFactory.get_whisper(provider)
    return whisper.get_cached_segments(model, audio_data, meta) 