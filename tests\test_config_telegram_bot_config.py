import pytest
from unittest.mock import patch, MagicMock
from configuration.base.telegram_bot import TelegramBotConfig
import configuration.base.telegram_config as telegram_config

def test_telegram_bot_config_initialization():
    config = TelegramBotConfig("test_client")
    assert config.client_code == "test_client"

@pytest.mark.slow
def test_get_telegram_access_token_default():
    config = TelegramBotConfig("test_client")
    token = config.get_telegram_access_token()
    assert token is None  # Default implementation returns None

@pytest.mark.slow
def test_get_telegram_bot_name_default():
    config = TelegramBotConfig("test_client")
    name = config.get_telegram_bot_name()
    assert name is None  # Default implementation returns None

@patch('src.lib.files.QuepasaFiles')
@pytest.mark.slow
def test_get_telegram_access_token_whitespace(mock_files):
    # Mock the config file with whitespace token
    mock_files.return_value.exists.return_value = True
    mock_files.return_value.get_json.return_value = {"token": "   "}
    
    config = TelegramBotConfig("test_client")
    token = config.get_telegram_access_token()
    assert token is None

@patch('src.lib.files.QuepasaFiles')
@pytest.mark.slow
def test_get_telegram_bot_name_empty_string(mock_files):
    # Mock the config file with empty name
    mock_files.return_value.exists.return_value = True
    mock_files.return_value.get_json.return_value = {"name": ""}
    
    config = TelegramBotConfig("test_client")
    name = config.get_telegram_bot_name()
    assert name is None

@patch('src.lib.files.QuepasaFiles')
@pytest.mark.slow
def test_get_telegram_config_file_not_found(mock_files):
    # Mock file not found
    mock_files.return_value.exists.return_value = False
    
    config = TelegramBotConfig("test_client")
    assert config._get_telegram_config() is None

@patch('src.lib.files.QuepasaFiles')
@pytest.mark.slow
def test_get_telegram_config_invalid_json(mock_files):
    # Mock invalid JSON
    mock_files.return_value.exists.return_value = True
    mock_files.return_value.get_json.side_effect = ValueError()
    
    config = TelegramBotConfig("test_client")
    assert config._get_telegram_config() is None

def test_get_telegram_config_caching():
    # Create a mock instance
    mock_instance = MagicMock()
    mock_instance.exists.return_value = True
    mock_instance.get_json.return_value = {"token": "test_token"}
    
    # Save original qp_files
    original_qp_files = telegram_config.qp_files
    
    try:
        # Replace global qp_files with mock
        telegram_config.qp_files = mock_instance
        
        config = TelegramBotConfig("test_client")
        
        # First call should read from file
        config1 = config._get_telegram_config()
        assert mock_instance.get_json.call_count == 1
        
        # Second call should use cache
        config2 = config._get_telegram_config()
        assert mock_instance.get_json.call_count == 1
        assert config1 == config2
    finally:
        # Restore original qp_files
        telegram_config.qp_files = original_qp_files

def test_should_stream_telegram_responses():
    config = TelegramBotConfig("test_client")
    assert config.should_stream_telegram_responses() is True

def test_allows_telegram_direct_questions():
    config = TelegramBotConfig("test_client")
    message = {"chat": {"type": "private"}}
    assert config.allows_telegram_direct_questions(message) is False

def test_allows_telegram_direct_questions_in_topic():
    config = TelegramBotConfig("test_client")
    assert config.allows_telegram_direct_questions_in_topic("General") is False

@pytest.mark.parametrize("message,expected", [
    ({"chat": {"type": "private"}}, False),
    ({"chat": {"type": "group"}}, False),
    ({"chat": {"type": "supergroup"}}, False),
    ({}, False),  # Edge case with empty message
    ({"chat": {}}, False),  # Edge case with empty chat
    (None, False),  # Edge case with None message
])
def test_allows_telegram_direct_questions_various_types(message, expected):
    config = TelegramBotConfig("test_client")
    assert config.allows_telegram_direct_questions(message) is expected

@pytest.mark.parametrize("topic_name,expected", [
    ("General", False),
    ("Questions", False),
    ("", False),  # Edge case with empty topic name
    (None, False),  # Edge case with None topic name
    ("   ", False),  # Edge case with whitespace topic name
    ("🤖 Bot Chat", False),  # Edge case with emoji and spaces
])
def test_allows_telegram_direct_questions_in_topic_various_names(topic_name, expected):
    config = TelegramBotConfig("test_client")
    assert config.allows_telegram_direct_questions_in_topic(topic_name) is expected 