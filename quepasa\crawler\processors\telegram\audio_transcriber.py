#!/usr/bin/env python3
import os
from pydub import AudioSegment
import tempfile
from src.lib.files import QuepasaFiles
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>ogger

from src.lib.whisper.providers import Whisper<PERSON>rovider
from src.lib.whisper.base import WhisperModelType
from src.lib.whisper_utils import get_cached_segments
from src.lib.gateway import get_segments_from_gateway

# Set up logger
logger = QuepasaLogger().get_instance(__name__)

qp_files = QuepasaFiles()

# Get API key for gateway
QUEPASA_LLM_GATEWAY_API_KEY = os.environ.get("QUEPASA_LLM_GATEWAY_API_KEY")

class AudioTranscriber:
    CHUNK_LENGTH = 5 * 60 * 1000  # 5 minutes in milliseconds
    OVERLAP = 5 * 1000  # 5 seconds overlap in milliseconds
    
    def __init__(self):
        pass

    def split_audio(self, audio_path: str) -> list:
        """Split audio file into chunks of 5 minutes with 5 seconds overlap"""
        try:
            # Load audio file
            audio = AudioSegment.from_file(audio_path)
            
            # If audio is shorter than chunk length, return original
            if len(audio) <= self.CHUNK_LENGTH:
                return [audio_path]
                
            # Create temporary directory for chunks
            temp_dir = tempfile.mkdtemp()
            chunk_paths = []
            
            # Split audio into chunks
            for i, start in enumerate(range(0, len(audio), self.CHUNK_LENGTH - self.OVERLAP)):
                # Calculate end time with overlap
                end = start + self.CHUNK_LENGTH
                
                # Extract chunk
                chunk = audio[start:end]
                
                # Save chunk to temporary file
                chunk_path = os.path.join(temp_dir, f"chunk_{i}.ogg")
                chunk.export(chunk_path, format="ogg")
                chunk_paths.append(chunk_path)
                
            return chunk_paths
            
        except Exception as e:
            logger.error(f"Error splitting audio file {audio_path}: {str(e)}")
            return [audio_path]  # Return original file if splitting fails

    def transcribe(self, file_path: str, output_dir: str) -> str:
        """Transcribe audio file and save transcription"""
        try:
            logger.info(f"Transcribing file: {file_path}")

            # Generate output path
            filename = os.path.basename(file_path)
            base_name = os.path.splitext(filename)[0]
            output_path = os.path.join(output_dir, f"{base_name}_transcription.txt")
            logger.info(f"Output path: {output_path}")

            if os.path.exists(output_path):
                return output_path

            # Convert paths to absolute
            file_path = os.path.abspath(file_path)
            output_dir = os.path.abspath(output_dir)
            os.makedirs(output_dir, exist_ok=True)

            # Verify file exists
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return None

            # If MP4, extract audio to WAV
            audio_path = file_path
            if file_path.lower().endswith('.mp4'):
                wav_path = os.path.splitext(file_path)[0] + '.wav'
                if not os.path.exists(wav_path):
                    video = AudioSegment.from_file(file_path, format="mp4")
                    video.export(wav_path, format="wav")
                audio_path = wav_path
            
            # Generate output path
            filename = os.path.basename(audio_path)
            base_name = os.path.splitext(filename)[0]
            output_path = os.path.join(output_dir, f"{base_name}_transcription.txt")
            logger.info(f"Output path: {output_path}")
            
            if os.path.exists(output_path):
                return output_path
            
            # Split audio into chunks
            chunk_paths = self.split_audio(audio_path)
            if not chunk_paths:
                logger.error(f"Failed to split audio file {audio_path}")
                return None
                
            all_transcriptions = []
            
            # Transcribe each chunk
            for chunk_path in chunk_paths:
                try:
                    # Verify chunk exists
                    if not os.path.exists(chunk_path):
                        logger.error(f"Chunk file not found: {chunk_path}")
                        continue

                    # Open audio file
                    with open(chunk_path, "rb") as f:
                        # Read the audio data as bytes
                        audio_data = f.read()
                        
                        # Determine MIME type based on file extension
                        extension = os.path.splitext(chunk_path)[1].lower()                        
                        meta = {'extension': extension}
                        
                        # Check if we should use gateway
                        if QUEPASA_LLM_GATEWAY_API_KEY:
                            logger.info("Using gateway service for transcription")
                            output = get_segments_from_gateway(WhisperProvider.REPLICATE, WhisperModelType.DIARIZATION, audio_data, meta)
                        else:
                            logger.info("Using local cached service for transcription")
                            # Run transcription using whisper_utils
                            output = get_cached_segments(WhisperProvider.REPLICATE, WhisperModelType.DIARIZATION, audio_data, meta)
                    
                    # Extract transcription text
                    transcription = output.get("text", "").strip()
                    all_transcriptions.append(transcription)
                    
                except Exception as e:
                    logger.error(f"Error transcribing chunk {chunk_path}: {str(e)}")
                    continue
                    
                finally:
                    # Clean up temporary chunk file if it's not the original
                    if chunk_path != audio_path:
                        try:
                            os.remove(chunk_path)
                        except Exception as e:
                            logger.error(f"Failed to remove temporary chunk {chunk_path}: {str(e)}")
            
            # Check if we got any transcriptions
            if not all_transcriptions:
                logger.error(f"No successful transcriptions for {audio_path}")
                return None
            
            # Combine all transcriptions
            final_transcription = " ".join(all_transcriptions)
            
            # Save the combined transcription
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(final_transcription.strip())
            except Exception as e:
                logger.error(f"Failed to save transcription to {output_path}: {str(e)}")
                return None
                
            # Clean up temporary directory if it exists
            if chunk_paths[0] != audio_path:
                try:
                    os.rmdir(os.path.dirname(chunk_paths[0]))
                except Exception as e:
                    logger.error(f"Failed to remove temporary directory: {str(e)}")
                
            return output_path
            
        except Exception as e:
            logger.error(f"Error transcribing audio file {audio_path}: {str(e)}")
            return None 