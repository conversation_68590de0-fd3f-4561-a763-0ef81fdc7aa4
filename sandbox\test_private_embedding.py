import os
import pytest
from src.lib.embedding.providers import EmbeddingProvider
from src.lib.embedding_utils import get_embedding, get_cached_embedding

def test_private_embedding():
    """Test private embedding provider."""
    # Test text
    text = "This is a test text for private embedding service."
    
    # Get embedding without cache
    embedding = get_embedding(
        provider=EmbeddingProvider.PRIVATE.value,
        model_version="Alibaba-NLP/gte-Qwen2-7B-instruct",
        text=text
    )
    
    # Verify embedding
    assert embedding is not None
    assert isinstance(embedding, list)
    assert len(embedding) > 0
    assert all(isinstance(x, float) for x in embedding)
    
    # Get embedding with cache
    cached_embedding = get_cached_embedding(
        provider=EmbeddingProvider.PRIVATE.value,
        model_version="Alibaba-NLP/gte-Qwen2-7B-instruct",
        text=text
    )
    
    # Verify cached embedding
    assert cached_embedding is not None
    assert isinstance(cached_embedding, list)
    assert len(cached_embedding) > 0
    assert all(isinstance(x, float) for x in cached_embedding)
    
    # Verify that both embeddings are the same
    assert embedding == cached_embedding

def test_private_embedding_long_text():
    """Test private embedding provider with long text."""
    # Create a long text
    long_text = "This is a test text. " * 1000
    
    # Get embedding
    embedding = get_embedding(
        provider=EmbeddingProvider.PRIVATE.value,
        model_version="Alibaba-NLP/gte-Qwen2-7B-instruct",
        text=long_text
    )
    
    # Verify embedding
    assert embedding is not None
    assert isinstance(embedding, list)
    assert len(embedding) > 0
    assert all(isinstance(x, float) for x in embedding)

def test_private_embedding_missing_env():
    """Test private embedding provider with missing environment variables."""
    # Save original environment variables
    original_endpoint = os.environ.get('PRIVATE_EMBEDDING_ENDPOINT')
    original_auth = os.environ.get('PRIVATE_EMBEDDING_AUTH_TOKEN')
    
    try:
        # Remove environment variables
        if 'PRIVATE_EMBEDDING_ENDPOINT' in os.environ:
            del os.environ['PRIVATE_EMBEDDING_ENDPOINT']
        if 'PRIVATE_EMBEDDING_AUTH_TOKEN' in os.environ:
            del os.environ['PRIVATE_EMBEDDING_AUTH_TOKEN']
        
        # Test that it raises ValueError
        with pytest.raises(ValueError):
            get_embedding(
                provider=EmbeddingProvider.PRIVATE.value,
                model_version="Alibaba-NLP/gte-Qwen2-7B-instruct",
                text="Test text"
            )
            
    finally:
        # Restore environment variables
        if original_endpoint:
            os.environ['PRIVATE_EMBEDDING_ENDPOINT'] = original_endpoint
        if original_auth:
            os.environ['PRIVATE_EMBEDDING_AUTH_TOKEN'] = original_auth

if __name__ == "__main__":
    # Set environment variables for testing
    os.environ['PRIVATE_EMBEDDING_ENDPOINT'] = "http://98.66.219.211/embedding/v1"
    os.environ['PRIVATE_EMBEDDING_AUTH_TOKEN'] = "quepasa:juF6neeD0Ki0lOp5"

    print(get_embedding(
        provider=EmbeddingProvider.PRIVATE.value,
        model_version="Alibaba-NLP/gte-Qwen2-7B-instruct",
        text="Test text"
    ))

    """
    # Run tests
    test_private_embedding()
    test_private_embedding_long_text()
    test_private_embedding_missing_env()
    
    print("All tests passed!") 
    """    
