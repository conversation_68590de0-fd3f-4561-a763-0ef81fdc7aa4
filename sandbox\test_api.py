import requests
import time
import io
from typing import Dict, Any

BASE_URL = "http://localhost:8000"
CLIENT_ID = "default"  # Using default client ID from .env.example
TEST_DOMAIN = "test"

def get_auth_header() -> Dict[str, str]:
    """Return authorization header for API requests"""
    return {
        "Authorization": f"Bearer {CLIENT_ID}"
    }

def test_file_upload() -> str:
    """Test file upload functionality"""
    print("\nTesting file upload...")
    
    # Create a simple text file in memory
    test_content = "This is a test document for QuePasa API verification."
    file_obj = io.BytesIO(test_content.encode('utf-8'))
    
    files = {
        'file': ('test.txt', file_obj, 'text/plain')
    }
    
    response = requests.post(
        f"{BASE_URL}/files/{TEST_DOMAIN}",
        headers=get_auth_header(),
        files=files
    )
    
    if response.status_code != 200:
        raise Exception(f"File upload failed with status {response.status_code}: {response.text}")
    
    result = response.json()
    print(f"Upload successful. Batch ID: {result['data']['batch_id']}")
    return result['data']['batch_id']

def wait_for_batch_completion(batch_id: str, timeout: int = 30) -> None:
    """Wait for batch processing to complete"""
    print("\nWaiting for batch processing...")
    start_time = time.time()
    
    while True:
        if time.time() - start_time > timeout:
            raise Exception("Batch processing timeout")
            
        response = requests.get(
            f"{BASE_URL}/batches/{batch_id}",
            headers=get_auth_header()
        )
        
        if response.status_code != 200:
            raise Exception(f"Batch status check failed: {response.text}")
            
        result = response.json()
        state = result['data']['state']
        
        if state == "COMPLETED":
            print("Batch processing completed")
            break
        elif state == "FAILED":
            raise Exception("Batch processing failed")
            
        print(f"Batch state: {state}")
        time.sleep(2)

def list_documents() -> None:
    """List documents in the test domain"""
    print("\nListing documents...")
    
    response = requests.get(
        f"{BASE_URL}/documents",
        params={"domain": TEST_DOMAIN},
        headers=get_auth_header()
    )
    
    if response.status_code != 200:
        raise Exception(f"Document listing failed: {response.text}")
        
    result = response.json()
    documents = result['data'].get(TEST_DOMAIN, [])
    print(f"Found {len(documents)} documents in domain '{TEST_DOMAIN}'")
    for doc_id in documents:
        print(f"- {doc_id}")

def main():
    try:
        # Step 1: Upload test file
        batch_id = test_file_upload()
        
        # Step 2: Wait for processing
        wait_for_batch_completion(batch_id)
        
        # Step 3: List documents
        list_documents()
        
        print("\nAPI verification completed successfully!")
        
    except Exception as e:
        print(f"\nError during API verification: {str(e)}")
        exit(1)

if __name__ == "__main__":
    main() 