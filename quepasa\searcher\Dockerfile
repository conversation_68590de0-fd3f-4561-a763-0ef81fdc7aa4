# Build stage
FROM python:3.9-slim AS builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    DOCKER_BUILDKIT=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1

WORKDIR /app

# Install system dependencies including build tools and protobuf compiler
R<PERSON> apt-get update && apt-get install -y --no-install-recommends \
    gnupg2 \
    build-essential \
    pkg-config \
    python3-dev \
    libprotobuf-dev \
    protobuf-compiler \
    git \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Upgrade pip and install build tools
RUN pip install --upgrade pip setuptools wheel

# Copy only necessary files first
COPY requirements.txt .
COPY quepasa/searcher/requirements.txt quepasa/searcher/
COPY setup.py .

# Install dependencies with caching disabled
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r quepasa/searcher/requirements.txt

# Install PyArmor 7.x specifically for compatibility with obfuscate command
RUN pip install --no-cache-dir pyarmor==7.7.4

# Copy application code after dependencies
COPY configuration/ configuration/
COPY quepasa/common/ quepasa/common/
COPY quepasa/searcher/api/ quepasa/searcher/api/
COPY quepasa/searcher/core/ quepasa/searcher/core/
COPY quepasa/searcher/history/ quepasa/searcher/history/
COPY quepasa/searcher/models/ quepasa/searcher/models/
COPY quepasa/searcher/sources/ quepasa/searcher/sources/
COPY quepasa/searcher/ quepasa/searcher/
COPY quepasa/*.py quepasa/
COPY src/ src/

# Obfuscate the Python code
RUN mkdir -p /app/obfuscated/quepasa && \
    # Obfuscate the API module
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/common quepasa/common/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher/api quepasa/searcher/api/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher/core quepasa/searcher/core/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher/history quepasa/searcher/history/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher/models quepasa/searcher/models/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher/sources quepasa/searcher/sources/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher quepasa/searcher/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa quepasa/*.py && \
    # Handle src directory
    mkdir -p /app/obfuscated/src && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/embedding src/lib/embedding/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/llm src/lib/llm/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/whisper src/lib/whisper/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/reranker src/lib/reranker/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib src/lib/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src src/*.py && \
    # Verify files were created (for debugging)
    echo "Checking obfuscated directories:" && \
    ls -la /app/obfuscated/quepasa && \
    ls -la /app/obfuscated/quepasa/searcher || true

# Final stage
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gnupg2 \
    && rm -rf /var/lib/apt/lists/* \
    libgbm1 \
    libasound2 \
    libpango-1.0-0 \
    busybox \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && pip cache purge

# Create and set permissions for cache directory
RUN mkdir -p /cache && chmod 777 /cache
RUN mkdir -p /cache/localdb && chmod 777 /cache/localdb

# Copy only necessary files from builder
COPY --from=builder /usr/local/lib/python3.9/site-packages/ /usr/local/lib/python3.9/site-packages/
COPY --from=builder /usr/local/ /usr/local/

# Copy obfuscated files instead of the original source
COPY --from=builder /app/obfuscated/quepasa /app/quepasa 
COPY --from=builder /app/obfuscated/src /app/src

# IMPORTANT: Copy PyArmor runtime files from each obfuscated directory
COPY --from=builder /app/obfuscated/quepasa/pytransform /app/pytransform
COPY --from=builder /app/obfuscated/quepasa/common/pytransform /app/quepasa/common/pytransform
COPY --from=builder /app/obfuscated/quepasa/searcher/pytransform /app/quepasa/searcher/pytransform

COPY --from=builder /app/configuration /app/configuration

# Expose port
EXPOSE 8080

# Set environment variables
ENV PYTHONPATH=/app \
    TRANSFORMERS_CACHE=/cache \
    HF_HOME=/cache \
    PATH="/usr/local/bin:$PATH"

# Set working directory
WORKDIR /app

# Run the application
CMD ["python", "/app/quepasa/searcher/main.py"]