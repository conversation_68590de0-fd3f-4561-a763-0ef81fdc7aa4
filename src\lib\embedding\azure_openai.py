import os
import tiktoken
from typing import Optional, List
from openai import AzureOpenAI

from .base import BaseEmbedding
from .cache import EmbeddingCacheMixin
from .providers import EmbeddingProvider
from src.lib.utils import get_tokenizer
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)

class AzureOpenAIEmbedding(BaseEmbedding, EmbeddingCacheMixin):
    """Azure OpenAI embedding provider.
    
    This class provides access to Azure OpenAI's embedding models.
    It supports various models like text-embedding-ada-002 and text-embedding-3-small.
    """
    
    def __init__(self):
        """Initialize Azure OpenAI embedding provider."""
        super().__init__()
        self.azure_client = AzureOpenAI(
            azure_endpoint=os.getenv('AZURE_OPENAI_ENDPOINT'),
            api_version=os.getenv('AZURE_OPENAI_API_VERSION'),
            api_key=os.getenv('AZURE_OPENAI_ACCESS_TOKEN')
        )
        self.max_tokens = 8192

    @property
    def provider(self) -> EmbeddingProvider:
        return EmbeddingProvider.AZURE_OPENAI

    def _truncate_text(self, text: str) -> str:
        """Truncate text to fit within token limit."""
        if len(get_tokenizer().encode(text)) > self.max_tokens:
            new_text = ""
            for line in text.split("\n"):
                line_nl = "\n" + line
                if len(get_tokenizer().encode((new_text + line_nl).strip())) > self.max_tokens:
                    break
                new_text += line_nl
            return new_text.strip()
        return text

    def get_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get embedding from Azure OpenAI API.
        
        Args:
            model_version: The model version to use for embeddings
            text: The text to get embedding for
            
        Returns:
            List of floats representing the embedding, or None if the request fails
        """
        text = self._truncate_text(text)
        try:
            return self.azure_client.embeddings.create(model=model_version, input=text).data[0].embedding
        
        except Exception as e:
            logger.error(f"Failed to get embedding from Azure OpenAI: {str(e)}")
            return None 