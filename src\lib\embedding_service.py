from typing import Optional, List, Union, Any
import requests
import time
import logging
import enum
import json

logger = logging.getLogger(__name__)

class EnumEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles Enum serialization."""
    def default(self, obj):
        if isinstance(obj, enum.Enum):
            return obj.value
        return super().default(obj)

def get_embedding_from_service(provider: Union[str, Any], model_version: Union[str, Any], text: str, 
                               service_url: str = None, max_retries: int = 3, 
                               retry_delay: float = 1.0) -> List[float]:
    """Get embedding from the embedding service.
    
    Args:
        provider: The embedding provider to use (e.g., 'openai', 'askrobot')
                  Can be string or Enum
        model_version: Model version to use (e.g., 'gpt-4-turbo-preview')
                       Can be string or Enum
        text: Text to get embedding for
        service_url: URL of the embedding service, defaults to Kubernetes service
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds
        
    Returns:
        List of embedding values if successful, empty list on failure
    """
    if not service_url:
        service_url = "http://embedding:8080"
    
    # Convert Enum types to their string values
    if hasattr(provider, 'value'):
        provider = provider.value
    if hasattr(model_version, 'value'):
        model_version = model_version.value
    
    retry_count = 0
    while retry_count < max_retries:
        try:
            payload = {
                "provider": provider,
                "model_version": model_version,
                "text": text
            }
            
            # Use custom JSON encoder for the request
            response = requests.post(
                f"{service_url}/api/embedding",
                data=json.dumps(payload, cls=EnumEncoder),
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            response.raise_for_status()
            embedding = response.json().get("embedding")
            
            # Verify we got a valid embedding
            if embedding is None:
                logger.error(f"Embedding service returned null embedding for text: {text[:100]}...")
                retry_count += 1
                time.sleep(retry_delay)
                continue
                
            return embedding
        except requests.exceptions.Timeout:
            logger.warning(f"Timeout connecting to embedding service (attempt {retry_count+1}/{max_retries})")
        except requests.exceptions.ConnectionError:
            logger.warning(f"Connection error to embedding service (attempt {retry_count+1}/{max_retries})")
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error from embedding service: {e}")
        except Exception as e:
            logger.error(f"Unexpected error getting embedding: {e}: {type(e).__name__}")
            logger.error(f"Payload attempted: {payload}")
        
        retry_count += 1
        if retry_count < max_retries:
            time.sleep(retry_delay)
    
    # If we get here, all retries failed
    logger.error(f"Failed to get embedding after {max_retries} attempts. Provider: {provider}, Model: {model_version}")
    return []  # Return empty list instead of None to avoid 'NoneType is not iterable' errors 