# Quepasa Configuration Management System

## Overview

This document outlines the architecture and implementation plan for the Quepasa Configuration Management System. The system enables dynamic configuration of RAG (Retrieval-Augmented Generation) components without code changes, supporting multi-tenant conversational commerce applications.

**Target Audience:** Technical consultants and developers familiar with the Quepasa codebase.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Pub-Sub Configuration Updates](#pub-sub-configuration-updates) 
3. [Configuration API Endpoints](#configuration-api-endpoints)
4. [Configurable Fields Reference](#configurable-fields-reference)
5. [Implementation Examples](#implementation-examples)
6. [Migration Strategy](#migration-strategy)

---

## Architecture Overview

### Current State

The current configuration system is hardcoded in Python classes:
- **Single Configuration File:** `configuration/main/default.py` (743+ lines)
- **Client-Specific Classes:** Multiple inheritance chains (FinanceBench, Shoeby)
- **Embedded Settings:** Prompts, model names, search weights directly in code
- **Manual Updates:** Requires code deployment for configuration changes

### Target Architecture

```mermaid
graph TB
    subgraph "Brain Studio System"
        BS_API[Brain Studio API]
        BS_DB[(PostgreSQL Config Store)]
        BS_PubSub[Brain Studio Pub/Sub]
        Admin[Admin Interface]
        Consul[Technical Consultants]
    end
    
    subgraph "Quepasa System"
        subgraph "Configuration Layer"
            CM[Configuration Manager]
            Cache[(Local Config Cache)]
            PS_Sub[Pub/Sub Subscriber]
        end
        
        subgraph "Application Services"
            AS[API Service :8000]
            SS[Searcher Service :8080] 
            ES[Embedding Service :8001]
        end
        
        subgraph "Fallback (Optional)"
            YAML[Default YAML Files]
        end
    end
    
    Admin --> BS_API
    Consul --> BS_API
    BS_API --> BS_DB
    BS_API --> BS_PubSub
    
    BS_PubSub --> PS_Sub
    PS_Sub --> CM
    CM --> BS_API
    CM --> Cache
    CM --> YAML
    
    AS --> CM
    SS --> CM
    ES --> CM
```

### Key Components

**Brain Studio System:**
1. **Brain Studio API:** External API providing configuration management endpoints
2. **PostgreSQL Config Store:** Brain Studio's PostgreSQL database for storing client configurations
3. **Brain Studio Pub/Sub:** Publishes configuration change events (likely using PostgreSQL LISTEN/NOTIFY or message queue)
4. **Admin Interface:** Brain Studio's UI for technical consultants

**Quepasa System:**
1. **Configuration Manager:** Fetches and caches configurations from Brain Studio API
2. **Local Config Cache:** In-memory cache for performance with TTL and invalidation
3. **Pub/Sub Subscriber:** Listens for configuration change events from Brain Studio
4. **Default YAML Files:** Optional fallback configurations stored in repository

---

## Pub-Sub Configuration Updates

### Architecture Pattern

The system implements a **Publisher-Subscriber pattern** where Brain Studio publishes configuration changes and Quepasa services subscribe to receive real-time updates.

### Message Flow

```mermaid
sequenceDiagram
    participant Admin as Technical Consultant
    participant BS_API as Brain Studio API
    participant BS_PubSub as Brain Studio Pub/Sub
    participant QP_Sub as Quepasa Subscriber
    participant QP_CM as Quepasa Config Manager
    participant QP_Service as Quepasa Service
    
    Admin->>BS_API: Update Configuration
    BS_API->>BS_API: Store New Config
    BS_API->>BS_PubSub: Publish Change Event
    BS_PubSub->>QP_Sub: Configuration Update Event
    QP_Sub->>QP_CM: Trigger Config Reload
    QP_CM->>BS_API: Fetch Updated Configuration
    BS_API->>QP_CM: Return New Configuration
    QP_CM->>QP_CM: Update Local Cache
    QP_CM->>QP_Service: Notify Config Changed
    QP_Service->>Admin: ACK Configuration Applied
```


## Safe Brain Studio Configuration Options (Phase 1)

**Important:** For the initial Brain Studio integration, we're limiting configurable options to **safe, low-risk fields** that enhance retrieval without compromising system quality or stability.

### Scope of Configuration Control

**✅ ALLOWED:** Fields that improve user experience without affecting core retrieval quality  
**❌ NOT ALLOWED:** LLM models, embedding models, core prompts, search weights, or system architecture

---

### 1. Query Enhancement Configuration

**Config Type:** `query_enhancements`  
**Risk Level:** 🟢 **LOW** - Currently implemented functionality

| Field | Type | Description | Impact |
|-------|------|-------------|---------|
| `query_expansions` | array | Expand user queries with synonyms/aliases | Enhances search results |

**Example:**
```yaml
query_enhancements:
  query_expansions:
    - keywords: "trainers"
      query: "sneakers"
    - keywords: "azure" 
      query: "light blue cyan"
```


---

## Configurable Fields Reference

### 1. Language and Localization Configuration

**Config Type:** `language`

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `fallback_language` | string | Default language when detection fails | `"en"` |
| `language_mapping` | object | Language code to display name mapping | `{"en": "English", "nl": "Dutch"}` |
| `supported_languages` | array | List of supported language codes | `["en", "nl"]` |
| `indexed_languages` | object | Languages supported per document type | `{"document": ["en", "nl"]}` |

**Example Configuration:**
```yaml
language:
  fallback_language: "en"
  language_mapping:
    en: "English"
    nl: "Dutch"
    fr: "French"
  supported_languages: ["en", "nl", "fr"]
  indexed_languages:
    document: ["en", "nl"]
    faq: ["en", "nl", "fr"]
```
