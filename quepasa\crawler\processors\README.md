# QuePasa Content Processors

This directory contains processors for different content types in the QuePasa system.

## Processors Overview

The processors handle different content types and extract text/information from them:

- `PDFProcessor`: PDF documents
- `DocumentProcessor`: Microsoft Office documents (Word, Excel)
- `PPTXProcessor`: PowerPoint presentations
- `WebProcessor`: HTML content
- `CSVProcessor`: CSV files
- `MarkdownProcessor`: Markdown files
- `TXTProcessor`: Plain text files
- `YouTubeProcessor`: YouTube videos
- `TelegramProcessor`: Telegram channels
- `AudioProcessor`: Audio files
- `VideoProcessor`: Video files

## Audio and Video Transcription

The `AudioProcessor` and `VideoProcessor` classes have been added to provide transcription functionality for audio and video files.

### Features

- Speech-to-text transcription using Replicate API
- Speaker diarization (identification of different speakers)
- Automatic handling of long files by splitting into chunks
- Support for various audio and video formats

### Supported Formats

**Audio Formats**:
- MP3 (.mp3)
- WAV (.wav)
- M4A (.m4a)
- OGG (.ogg)
- FLAC (.flac)
- AAC (.aac)

**Video Formats**:
- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)
- WebM (.webm)
- FLV (.flv)
- WMV (.wmv)

### Requirements

- A Replicate API key (set as environment variable `REPLICATE_API_KEY`)
- FFmpeg installed on the system for audio/video processing
- Python libraries: replicate, pydub

### Usage

The processors are automatically used when processing files with the supported extensions. No additional configuration is needed beyond setting the `REPLICATE_API_KEY` environment variable.

### Model Used

For transcription with speaker identification:
- `thomasmol/whisper-diarization:1495a9cddc83b2203b0d8d3516e38b80fd1572ebc4bc5700ac1da56a9b3ed886`