apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch
  namespace: quepasa
spec:
  serviceName: elasticsearch
  replicas: 1
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      initContainers:
      - name: fix-permissions
        image: busybox
        command: ["sh", "-c", "echo 'Setting permissions...' && chown -R 1000:1000 /usr/share/elasticsearch/data && echo 'Setting vm.max_map_count...' && sysctl -w vm.max_map_count=262144 && echo 'Init container completed successfully'"]
        securityContext:
          privileged: true
        volumeMounts:
        - name: es-data
          mountPath: /usr/share/elasticsearch/data
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch:8.12.2
        env:
        - name: node.name
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: discovery.type
          value: single-node
        - name: network.host
          value: "0.0.0.0"
        - name: http.host
          value: "0.0.0.0"
        - name: transport.host
          value: "0.0.0.0"
        - name: xpack.security.enabled
          value: "false"
        - name: xpack.security.enrollment.enabled
          value: "false"
        - name: xpack.security.http.ssl.enabled
          value: "false"
        - name: xpack.security.transport.ssl.enabled
          value: "false"
        - name: bootstrap.memory_lock
          value: "true"
        - name: node.store.allow_mmap
          value: "true"
        - name: CLUSTER_NAME
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: ELASTICSEARCH_HOST
        - name: HTTP_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: ELASTICSEARCH_PORT
        - name: ELASTIC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ELASTICSEARCH_PASSWORD
        - name: ES_JAVA_OPTS
          value: "-Xms2g -Xmx2g -Dlog4j2.formatMsgNoLookups=true -XX:+UseG1GC"
        - name: cluster.routing.allocation.disk.watermark.low
          value: "85%"
        - name: cluster.routing.allocation.disk.watermark.high
          value: "90%"
        - name: cluster.routing.allocation.disk.watermark.flood_stage
          value: "95%"
        - name: indices.fielddata.cache.size
          value: "10%"
        - name: indices.memory.index_buffer_size
          value: "10%"
        securityContext:
          runAsUser: 1000
          runAsGroup: 1000
          privileged: false
          capabilities:
            drop:
              - ALL
            add:
              - IPC_LOCK
        resources:
          requests:
            cpu: "1000m"
            memory: "2Gi"
          limits:
            cpu: "2"
            memory: "4Gi"
        ports:
        - containerPort: 9200
          name: http
          protocol: TCP
        - containerPort: 9300
          name: transport
          protocol: TCP
        volumeMounts:
        - name: es-data
          mountPath: /usr/share/elasticsearch/data
        readinessProbe:
          httpGet:
            path: /_cluster/health
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          timeoutSeconds: 5
          periodSeconds: 10
          failureThreshold: 6
        livenessProbe:
          httpGet:
            path: /
            port: http
            scheme: HTTP
          initialDelaySeconds: 120
          timeoutSeconds: 5
          periodSeconds: 10
          failureThreshold: 6
      volumes:
      - name: es-data
        persistentVolumeClaim:
          claimName: es-data-elasticsearch-0
  volumeClaimTemplates:
  - metadata:
      name: es-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: premium
      resources:
        requests:
          storage: 100Gi 