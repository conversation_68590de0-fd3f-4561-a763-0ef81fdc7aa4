import os
import sys

SCRIPT_DIR = os.path.realpath(os.path.dirname(__file__))
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, "..", "..", ".."))
sys.path.insert(1, PROJECT_ROOT)

from src.lib.extraction import markdown_to_pages_recursive
from .base import BaseProcessor
from ...utils import split_paragraphs
from ..exceptions import ProcessorError

class MarkdownProcessor(BaseProcessor):
    def _process_impl(self, content: bytes, metadata: dict) -> dict:
        """Process markdown content and extract text."""
        try:
            markdown_content = content.decode('utf-8')
            
            # Convert markdown to pages
            pages = markdown_to_pages_recursive(markdown_content)
            chunks = []
            
            # Extract title from first heading
            title = None
            if pages:
                first_page = pages[0]['text']
                lines = first_page.split('\n')
                for line in lines:
                    if line.startswith('#'):
                        title = line.lstrip('#').strip()
                        break
            
            # Process text content and track line numbers
            current_line = 1
            for page in pages:
                page_text = page['text']
                raw_chunks = split_paragraphs(page_text)
                
                for chunk in raw_chunks:
                    # Find start and end positions of chunk in page text
                    chunk_start_pos = page_text.find(chunk)
                    chunk_end_pos = chunk_start_pos + len(chunk)
                    
                    # Count newlines before start and end to get line numbers
                    start_line = current_line + page_text[:chunk_start_pos].count('\n')
                    end_line = start_line + chunk.count('\n')
                    
                    chunks.append({
                        'text': chunk,
                        'position': f"lines {start_line}-{end_line}"
                    })
                
                current_line += page_text.count('\n') + 1  # +1 for the implicit newline between pages
            
            return {
                'chunks': chunks,
                'title': title or metadata.get('filename', ''),
                'filename': metadata.get('filename', '')
            }
            
        except Exception as e:
            raise ProcessorError(f"Error processing markdown content: {str(e)}")