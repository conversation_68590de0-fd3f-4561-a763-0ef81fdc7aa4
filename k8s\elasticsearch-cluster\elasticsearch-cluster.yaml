apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: elasticsearch-cluster
  namespace: quepasa
spec:
  version: 8.17.4
  http:
    service:
      spec:
        type: LoadBalancer
        ports:
          - name: http
            port: 9200
    tls:
      selfSignedCertificate:
        disabled: true

  nodeSets:
    # 1️⃣ Dedicated masters (metadata only) ─ 3 GiB total
    - name: master
      count: 3
      config:
        node.roles: ["master"]
        action.destructive_requires_name: true
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              env:
                - name: ES_JAVA_OPTS            # 512 MiB heap
                  value: "-Xms512m -Xmx512m"
              resources:
                requests:
                  cpu: 250m
                  memory: 1Gi
                limits:
                  cpu: 500m
                  memory: 1Gi

    # 2️⃣ Hot tier ─ high‑write nodes, no ingest ─ 8 GiB total
    - name: hot
      count: 2
      config:
        node.roles: ["data_hot", "ingest"]
        action.destructive_requires_name: true
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              env:
                - name: ES_JAVA_OPTS            # 2 GiB heap
                  value: "-Xms2g -Xmx2g"
              resources:
                requests:
                  cpu: "1"
                  memory: 4Gi
                limits:
                  cpu: "2"
                  memory: 4Gi
          initContainers:
            - name: sysctl
              securityContext:
                privileged: true
                runAsUser: 0
              command: ["sh", "-c", "sysctl -w vm.max_map_count=262144"]
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data
          spec:
            accessModes: ["ReadWriteOnce"]
            storageClassName: managed-csi-premium
            resources:
              requests:
                storage: 120Gi

    # 3️⃣ Content/search tier ─ read‑heavy ─ 12 GiB total
    - name: content
      count: 2
      config:
        node.roles: ["data_content"]
        action.destructive_requires_name: true
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              env:
                - name: ES_JAVA_OPTS            # 3 GiB heap
                  value: "-Xms3g -Xmx3g"
              resources:
                requests:
                  cpu: "1"
                  memory: 6Gi
                limits:
                  cpu: "2"
                  memory: 6Gi
          initContainers:
            - name: sysctl
              securityContext:
                privileged: true
                runAsUser: 0
              command: ["sh", "-c", "sysctl -w vm.max_map_count=262144"]
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data
          spec:
            accessModes: ["ReadWriteOnce"]
            storageClassName: managed-csi-premium
            resources:
              requests:
                storage: 500Gi   # longer‑retention PVC
---
apiVersion: kibana.k8s.elastic.co/v1
kind: Kibana
metadata:
  name: kibana-cluster
  namespace: quepasa
spec:
  version: 8.17.4
  count: 1
  elasticsearchRef:
    name: elasticsearch-cluster
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  podTemplate:
    spec:
      containers:
        - name: kibana
          resources:
            requests:
              cpu: 100m
              memory: 512Mi
            limits:
              cpu: 500m
              memory: 1Gi
