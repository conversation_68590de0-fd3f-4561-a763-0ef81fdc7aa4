import os
import json
import pytest
from typing import List, Dict, Any
from unittest.mock import patch, MagicMock
from src.lib.reranker.base import BaseReranker
from src.lib.reranker.cache import RerankerCacheMixin

class MockCachedReranker(RerankerCacheMixin, BaseReranker):
    """Mock reranker with caching for testing"""
    def __init__(self):
        super().__init__()
        self.get_results_called = 0
    
    def get_results(self, model_version: str, query: str, documents: List[Dict[str, Any]], top_n: int = None) -> List[Dict[str, Any]]:
        self.get_results_called += 1
        return documents[:top_n] if top_n else documents

@pytest.fixture
def reranker():
    return MockCachedReranker()

@pytest.fixture
def test_data():
    return {
        "model_version": "test-model",
        "query": "test query",
        "documents": [{"id": 1}, {"id": 2}, {"id": 3}],
        "top_n": 2
    }

def test_cache_paths(reranker, test_data):
    """Test cache path generation"""
    local_path, remote_path = reranker._get_cache_paths(
        test_data["model_version"],
        test_data["query"], 
        test_data["documents"],
        test_data["top_n"]
    )
    
    assert local_path.endswith(".txt")
    assert remote_path.endswith(".txt")
    assert "test-model" in local_path
    assert "test-model" in remote_path

@patch("os.path.isfile")
def test_load_local_cache_missing(mock_isfile, reranker):
    """Test behavior when local cache is missing"""
    mock_isfile.return_value = False
    result = reranker._load_local_cache("fake/path")
    assert result is None

@patch("os.path.isfile")
def test_load_local_cache_exists(mock_isfile, reranker, test_data):
    """Test loading from local cache"""
    mock_isfile.return_value = True
    mock_open = MagicMock()
    mock_open.return_value.__enter__.return_value.read.return_value = json.dumps({
        "response": test_data["documents"][:2]
    })
    
    with patch("builtins.open", mock_open):
        result = reranker._load_local_cache("fake/path")
        assert result == test_data["documents"][:2]

@patch("src.lib.reranker.cache.qp_files")
def test_load_remote_cache_missing(mock_files, reranker):
    """Test behavior when remote cache is missing"""
    mock_files.exists.return_value = False
    result = reranker._load_remote_cache("fake/remote", "fake/local")
    assert result is None

@patch("src.lib.reranker.cache.qp_files")
@patch("builtins.open", new_callable=MagicMock)
def test_load_remote_cache_exists(mock_open, mock_files, reranker, test_data):
    """Test loading from remote cache"""
    mock_files.exists.return_value = True
    
    # Create a mock file content
    mock_content = json.dumps({
        "response": test_data["documents"][:2]
    })
    
    # Set up the mock file
    mock_file = MagicMock()
    mock_file.read.return_value = mock_content
    mock_open.return_value.__enter__.return_value = mock_file
    
    # Run the test
    result = reranker._load_remote_cache("fake/remote", "fake/local")
    
    # Verify the result and interactions
    assert result == test_data["documents"][:2]
    mock_files.download_file.assert_called_once_with("fake/remote", "fake/local")
    mock_open.assert_called_once_with("fake/local", "r")
    mock_file.read.assert_called_once()

def test_get_cached_results_no_cache(reranker, test_data):
    """Test getting results when no cache exists"""
    with patch.object(reranker, "_load_local_cache", return_value=None), \
         patch.object(reranker, "_load_remote_cache", return_value=None), \
         patch.object(reranker, "_save_response"):
        
        result = reranker.get_cached_results(
            test_data["model_version"],
            test_data["query"],
            test_data["documents"],
            test_data["top_n"]
        )
        
        assert result == test_data["documents"][:2]
        assert reranker.get_results_called == 1

def test_get_cached_results_with_local_cache(reranker, test_data):
    """Test getting results from local cache"""
    cached_result = test_data["documents"][:2]
    
    with patch.object(reranker, "_load_local_cache", return_value=cached_result):
        result = reranker.get_cached_results(
            test_data["model_version"],
            test_data["query"],
            test_data["documents"],
            test_data["top_n"]
        )
        
        assert result == cached_result
        assert reranker.get_results_called == 0  # Should not call get_results

def test_get_cached_results_with_remote_cache(reranker, test_data):
    """Test getting results from remote cache"""
    cached_result = test_data["documents"][:2]
    
    with patch.object(reranker, "_load_local_cache", return_value=None), \
         patch.object(reranker, "_load_remote_cache", return_value=cached_result):
        
        result = reranker.get_cached_results(
            test_data["model_version"],
            test_data["query"],
            test_data["documents"],
            test_data["top_n"]
        )
        
        assert result == cached_result
        assert reranker.get_results_called == 0  # Should not call get_results 