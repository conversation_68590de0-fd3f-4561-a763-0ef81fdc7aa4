from __future__ import annotations

from typing import Dict, Type, Union

from .base import BaseLL<PERSON>
from .openai import OpenA<PERSON><PERSON>
from .mistral import MistralLL<PERSON>
from .anthropic import AnthropicLL<PERSON>
from .replicate import ReplicateLLM
from .yandex import <PERSON>dexLL<PERSON>
from .ollama import <PERSON>lla<PERSON>LL<PERSON>
from .providers import LLMProvider
from .together import TogetherLL<PERSON>
from .nebius import NebiusLL<PERSON>
from .mws import MW<PERSON><PERSON>
from .gemini import GeminiLL<PERSON>
class LLMFactory:
    """Factory class for creating LLM providers."""
    
    _providers: Dict[LLMProvider, Type[BaseLLM]] = {
        LLMProvider.OPENAI: OpenAILLM,
        LLMProvider.MISTRAL: MistralLLM,
        LLMProvider.ANTHROPIC: AnthropicLLM,
        LLMProvider.REPLICATE: ReplicateLLM,
        LLMProvider.YANDEX: YandexLLM,
        LLMProvider.OLLAMA: OllamaLLM,
        LLMProvider.TOGETHER: TogetherLLM,
        LLMProvider.NEBIUS: NebiusLLM,
        LLMProvider.MWS: MWSLLM,
        LLMProvider.GEMINI: GeminiLLM,
    }

    @classmethod
    def get_llm(cls, provider: Union[str, LLMProvider]) -> BaseLLM:
        """Get an LLM instance for the specified provider.
        
        Args:
            provider: The LLM provider (string or enum)
            
        Returns:
            An instance of the specified LLM provider
            
        Raises:
            ValueError: If the provider is not supported
        """
        if isinstance(provider, str):
            provider = LLMProvider.from_str(provider)
            
        if provider not in cls._providers:
            raise ValueError(f"Unknown LLM provider: {provider}. "
                           f"Supported providers: {[p.value for p in cls._providers.keys()]}")
        
        return cls._providers[provider]()

def get_llm(provider: Union[str, LLMProvider]) -> BaseLLM:
    """Convenience function to get an LLM instance for the specified provider.
    
    Args:
        provider: The LLM provider (string or enum)
        
    Returns:
        An instance of the specified LLM provider
        
    Raises:
        ValueError: If the provider is not supported
    """
    return LLMFactory.get_llm(provider) 