from abc import ABC, abstractmethod
from typing import List, Dict, Any

class BaseReranker(ABC):
    """Base class for rerankers"""
    
    @abstractmethod
    def get_results(self, model_version: str, query: str, documents: List[Dict[str, Any]], top_n: int = None) -> List[Dict[str, Any]]:
        """
        Get reranked documents based on query
        
        Args:
            model_version: Model version to use
            query: Search query
            documents: List of documents to rerank
            top_n: Number of top documents to return. If None, returns all documents
            
        Returns:
            List of reranked documents
        """
        pass 

    def get_cached_results(self, model_version: str, query: str, documents: List[Dict[str, Any]], top_n: int = None) -> List[Dict[str, Any]]:
        """Get cached reranked documents based on query"""
        return self.get_results(model_version, query, documents, top_n)