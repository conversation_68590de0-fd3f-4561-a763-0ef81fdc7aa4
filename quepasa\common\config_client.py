import os
from collections import defaultdict
from typing import Optional, Dict, Any, List, Iterable, DefaultDict

import requests
from requests.adapters import HTTPAdapter
from urllib3.util import Retry
import threading

from src.lib.logger import Que<PERSON>aLogger
from quepasa.common.config_models import (
    TenantConfig,
    TenantModel,
    ProjectConfiguration,
    AreaModel,
    Attribute as AttributeModel,
    AttributeType,
    ConversationalSearchConfigModel,
    ConversationalPromptConfigModel,
    ConversationalFilterableAttributeModel,
)

# Background refresh thread controls
_REFRESH_THREAD: Optional[threading.Thread] = None
_STOP_EVENT: Optional[threading.Event] = None
_THREAD_LOCK = threading.Lock()

logger = QuepasaLogger().get_instance(__name__)

# Configure retry strategy and HTTP session for external API calls
_RETRY = Retry(
    total=3,
    connect=3,
    read=3,
    backoff_factor=0.5,
    status_forcelist=[500, 502, 503, 504],
    allowed_methods={"GET", "POST"},
    respect_retry_after_header=True,
)

_HTTP = requests.Session()
_HTTP.mount("https://", HTTPAdapter(max_retries=_RETRY))
_HTTP.mount("http://", HTTPAdapter(max_retries=_RETRY))

# In-memory configuration store (thread-safe)
_STORE_LOCK = threading.RLock()
# Structure: { tenant_name: TenantConfig }
_TENANT_STORE: Dict[str, TenantConfig] = {}


# In-memory helpers
class _MerchantInMemoryStore:
    def __init__(self):
        self.lock = _STORE_LOCK
        self.tenants = _TENANT_STORE  # alias

    def set_tenant_view(self, tenant_name: str, data: TenantConfig) -> None:
        with self.lock:
            self.tenants[tenant_name] = data

    def get_tenant_view(self, tenant_name: str) -> Optional[TenantConfig]:
        with self.lock:
            return self.tenants.get(tenant_name)

    def all_tenant_names(self) -> set[str]:
        with self.lock:
            return set(self.tenants.keys())

    def clear(self) -> None:
        with self.lock:
            self.tenants.clear()

    def replace_all(self, new_views_by_name: Dict[str, TenantConfig]) -> None:
        """Atomically replace the entire tenant map (no empty or half-built window)."""
        with self.lock:
            self.tenants.clear()
            self.tenants.update(new_views_by_name)

_MERCHANT_STORE = _MerchantInMemoryStore()

def login_get_bearer(timeout: float = 10.0) -> str:
    """Login to external API and return access token.

    Email and password are read from env if not provided.
    """
    miss_env_vars = []
    email = os.getenv("BRAIN_STUDIO_SUPERADMIN_EMAIL")
    if not email:
        miss_env_vars.append("BRAIN_STUDIO_SUPERADMIN_EMAIL")
    password = os.getenv("BRAIN_STUDIO_SUPERADMIN_PASSWORD")
    if not password:
        miss_env_vars.append("BRAIN_STUDIO_SUPERADMIN_PASSWORD")
    login_url = os.getenv("BRAIN_STUDIO_LOGIN_URL")
    if not login_url:
        miss_env_vars.append("BRAIN_STUDIO_LOGIN_URL")

    if len(miss_env_vars) > 0:
        raise ValueError(f"Missing environment variables for fetching BrainStudio access token. Missing variables are: {', '.join(miss_env_vars)}")

    resp = _HTTP.post(login_url, json={"email": email, "password": password}, timeout=timeout)
    if resp.status_code != 200:
        raise RuntimeError(f"Login failed with status {resp.status_code}: {resp.text}")
    data = resp.json()
    token = data.get("access_token")
    if not token:
        raise RuntimeError("Login response missing access token field")
    return token


def fetch_dynamic_config(bearer_token: str, timeout: float = 15.0) -> Dict[str, Any]:
    """Fetch dynamic configuration from BrainStudio Global Configuration API and store it in-memory."""
    headers = {
        "Authorization": f"Bearer {bearer_token}"
    }
    base_dynamic_config_url = os.getenv("BRAIN_STUDIO_CONFIGURATION_URL")
    suffix = os.getenv("BRAIN_STUDIO_CONFIGURATION_URL_SUFFIX")
    miss_env_vars = []
    if not base_dynamic_config_url:
        miss_env_vars.append("BRAIN_STUDIO_CONFIGURATION_URL")
    if not suffix:
        miss_env_vars.append("BRAIN_STUDIO_CONFIGURATION_URL_SUFFIX")
    if len(miss_env_vars) > 0:
        raise ValueError(f"Missing environment variables for fetching BrainStudio Global Configuration. Missing variables are: {', '.join(miss_env_vars)}")

    url = f"{base_dynamic_config_url}{suffix}"
    response = _HTTP.get(url, headers=headers, timeout=timeout)
    if response.status_code != 200:
        raise RuntimeError(f"Dynamic config fetch failed {response.status_code}: {response.text}")
    return response.json()


def get_dynamic_config() -> Optional[Dict[str, Any]]:
    """
    Full refresh version (simple & safe):
      - Always fetch ALL categories using a fixed, configurable URL suffix.
      - Build a brand-new per-tenant snapshot off to the side.
      - Atomically swap it into the in-memory store.
      - Return the fetched payload with a small _summary.
    """
    try:
        bearer_token = login_get_bearer()

        # Always perform a full fetch
        fetched_config = fetch_dynamic_config(bearer_token)

        tenant_dicts: List[dict] = (fetched_config or {}).get("tenants") or []
        project_dicts: List[dict] = (fetched_config or {}).get("projectConfigurations") or []
        area_dicts: List[dict] = (fetched_config or {}).get("areas") or []
        attribute_dicts: List[dict] = (fetched_config or {}).get("attributes") or []
        conversational_dicts: List[dict] = (fetched_config or {}).get("conversationalSearchConfigs") or []
        prompt_dicts: List[dict] = (fetched_config or {}).get("conversationalPromptConfigs") or []

        # Index helpers
        projects_by_tenant_id = _group_by_key(project_dicts, "tenantId")
        conversations_by_tenant_id = _group_by_key(conversational_dicts, "tenantId")
        prompts_by_tenant_id = _group_by_key(prompt_dicts, "tenantId")
        attributes_by_collection_id = _group_by_key(attribute_dicts, "collectionId")

        new_views_by_name: Dict[str, TenantConfig] = {}
        processed_tenant_names: set[str] = set()

        for tenant in tenant_dicts:
            tenant_name = tenant.get("name")
            tenant_id = tenant.get("id")
            if not tenant_name:
                continue  # must have a stable cache key

            # Projects for this tenant
            tenant_projects = projects_by_tenant_id.get(tenant_id, [])

            # Areas: prefer tenantId match; otherwise match by the tenant's project collection names
            tenant_areas: List[dict] = []
            if area_dicts:
                project_collection_names = {
                    project.get("collection")
                    for project in tenant_projects
                    if isinstance(project, dict) and project.get("collection")
                }
                for area in area_dicts:
                    if not isinstance(area, dict):
                        continue
                    if area.get("tenantId") == tenant_id:
                        tenant_areas.append(area)
                    elif area.get("collectionName") and area.get("collectionName") in project_collection_names:
                        tenant_areas.append(area)

            # Attributes for this tenant's collections (from areas and any project-derived collectionId, defensively)
            collection_ids_from_areas = {
                area.get("collectionId")
                for area in tenant_areas
                if isinstance(area, dict)
            }
            collection_ids_from_projects = {
                project.get("collectionId")
                for project in tenant_projects
                if isinstance(project, dict) and project.get("collectionId") is not None
            }
            all_collection_ids = {
                collection_id
                for collection_id in (*collection_ids_from_areas, *collection_ids_from_projects)
                if collection_id is not None
            }

            tenant_attributes: List[dict] = []
            for collection_id in all_collection_ids:
                tenant_attributes.extend(attributes_by_collection_id.get(collection_id, []))

            # Conversational configs for this tenant
            tenant_conversational_configs = conversations_by_tenant_id.get(tenant_id, [])
            tenant_prompt_configs = prompts_by_tenant_id.get(tenant_id, [])

            tenant_dto = TenantModel.model_validate(tenant)
            collections_dto: List[ProjectConfiguration] = [
                ProjectConfiguration.model_validate(project) for project in tenant_projects
            ]
            areas_dto: List[AreaModel] = [AreaModel.model_validate(area) for area in tenant_areas]
            attributes_dto: List[AttributeModel] = [
                AttributeModel.model_validate(attribute) for attribute in tenant_attributes
            ]
            conversational_dto: List[ConversationalSearchConfigModel] = [
                ConversationalSearchConfigModel.model_validate(conversational_config)
                for conversational_config in tenant_conversational_configs
            ]
            prompt_dto: List[ConversationalPromptConfigModel] = [
                ConversationalPromptConfigModel.model_validate(prompt_config)
                for prompt_config in tenant_prompt_configs
            ]

            # Populate conversational_filterable_attributes with external facet API refinements
            conversational_filterable_attributes: List[ConversationalFilterableAttributeModel] = []
            facet_attributes = [
                attribute for attribute in attributes_dto if
                attribute.conversationalFilterable is True
                and attribute.key is not None
                and attribute.type == AttributeType.TEXTUAL
            ]
            # Facet API configuration
            facet_api_url = os.getenv("GROUPBY_FACET_API_URL")
            if not facet_api_url:
                logger.warning("Facet API URL not configured; skipping facet API refinement population")

            if facet_attributes and facet_api_url:
                # Build lookup maps
                first_area_by_collection_id: Dict[Optional[int], Optional[int]] = {}
                first_area_name_by_collection_id: Dict[Optional[int], Optional[str]] = {}
                for area in areas_dto or []:
                    collection_id = area.collectionId
                    if collection_id not in first_area_by_collection_id:
                        first_area_by_collection_id[collection_id] = area.id
                    if collection_id not in first_area_name_by_collection_id:
                        first_area_name_by_collection_id[collection_id] = area.name

                collection_name_by_id: Dict[Optional[int], Optional[str]] = {}
                for project_configuration in collections_dto or []:
                    try:
                        collection_id = project_configuration.id
                        collection_name = project_configuration.collection
                        if collection_id is not None and collection_name and collection_id not in collection_name_by_id:
                            collection_name_by_id[collection_id] = collection_name
                    except Exception:
                        continue

                client_key = tenant_dto.activeKey_1 or tenant_dto.activeKey_2
                customer_id = tenant_dto.name

                for attribute_for_facet_request in facet_attributes:
                    try:
                        collection_id = attribute_for_facet_request.collectionId
                        area_id = first_area_by_collection_id.get(collection_id)
                        area_name = first_area_name_by_collection_id.get(collection_id) or 'Production'
                        collection_name = collection_name_by_id.get(collection_id)
                        attribute_key = attribute_for_facet_request.key
                        refinement_values = _fetch_refinements(facet_api_url, collection_name, area_name, attribute_key, client_key, customer_id)
                        conversational_filterable_attributes.append(
                            ConversationalFilterableAttributeModel(
                                tenantId=tenant_dto.id,
                                collectionId=collection_id,
                                areaId=area_id,
                                key=attribute_key,
                                refinement_values=refinement_values,
                            )
                        )
                    except Exception:
                        continue

            tenant_view = TenantConfig(
                tenant=tenant_dto,
                collections=collections_dto,
                areas=areas_dto,
                attributes=attributes_dto,
                conversational_search_configs=conversational_dto,
                conversational_prompt_configs=prompt_dto,
                conversational_filterable_attributes=conversational_filterable_attributes,
            )

            new_views_by_name[tenant_name] = tenant_view
            processed_tenant_names.add(tenant_name)

        # Optional safety: avoid wiping cache if API returns an empty tenant list unexpectedly.
        if not tenant_dicts and _MERCHANT_STORE.all_tenant_names():
            logger.warning("Full refresh returned 0 tenants; skipping commit to avoid accidental wipe")
            return {
                "warning": "Full refresh returned 0 tenants; skipping commit to avoid accidental wipe"
            }

        # Atomic swap of the entire snapshot
        _MERCHANT_STORE.replace_all(new_views_by_name)
        logger.info("Full refresh committed: %d tenant view(s) replaced atomically", len(new_views_by_name))

        # Observability summary attached to returned payload
        try:
            fetched_config = fetched_config or {}
            query = os.getenv("BRAIN_STUDIO_CONFIGURATION_URL_SUFFIX")
            query_sections = ["all"]
            if query:
                query = query.lstrip("?")
                query_sections=  [part for part in query.split("&") if part]
            fetched_config["_summary"] = {
                "updated_sections": query_sections,
                "tenants_processed": sorted(list(processed_tenant_names)),
                "atomic_swap": True,
            }
        except Exception:
            pass

        return fetched_config

    except Exception as error:
        logger.error(f"Failed to refresh dynamic config: {error}")
        return None

def _group_by_key(records: Iterable[dict], key_name: str) -> DefaultDict[Any, List[dict]]:
    grouped_map: DefaultDict[Any, List[dict]] = defaultdict(list)
    for record in records or []:
        if isinstance(record, dict):
            grouped_map[record.get(key_name)].append(record)
    return grouped_map

def _fetch_refinements(facet_url: str, facet_collection_name: Optional[str], area_name: Optional[str], navigation_name: str, client_key: Optional[str], customer_id: Optional[str]) -> List[str]:
    if not navigation_name or not client_key or not customer_id or not facet_collection_name:
        return []
    headers = {
        "Authorization": f"client-key {client_key}",
        "X-Groupby-Customer-Id": f"{customer_id}",
        "Content-Type": "application/json",
        "X-Groupby-Skip-Cache": "true"
    }
    body = {
        "originalRequest": {
            "collection": facet_collection_name,
            "area": area_name or "Production",
            "includedNavigations": [navigation_name],
        },
        "facet": {
            "navigationName": navigation_name,
            "type": "Value",
        }
    }
    try:
        facet_response = _HTTP.post(facet_url, headers=headers, json=body, timeout=5.0)
        if facet_response.status_code != 200:
            logger.debug("Facet API returned non-200 response code for %s/%s/%s: %s", facet_collection_name, area_name, navigation_name, facet_response.status_code)
            return []
        data = facet_response.json() if facet_response.content else {}
        available_navigation = (data or {}).get("availableNavigation") or {}
        refinements = available_navigation.get("refinements") or []
        refinement_values: List[str] = []
        for refinement in refinements:
            if isinstance(refinement, dict):
                refinement_value = refinement.get("value") or None
                if refinement_value and refinement_value.strip():
                    refinement_values.append(refinement_value)
        return refinement_values
    except Exception as e:
        logger.debug("Facet API error for %s/%s/%s: %s", facet_collection_name, area_name, navigation_name, e)
        return []


def get_tenant_config(tenant_name: str) -> Optional[TenantConfig]:
    """Fetch per-tenant config view from in-memory store. Returns TenantConfig if enabled, or error dict if disabled/missing."""
    tenant_configuration = _MERCHANT_STORE.get_tenant_view(tenant_name)
    if tenant_configuration is None or not tenant_configuration.tenant:
        raise ValueError(f"Tenant configuration for tenant '{tenant_name}' not found")
    if not tenant_configuration.tenant.enabled:
        raise ValueError(f"Tenant '{tenant_name}' is disabled. Please contact support.")
    return tenant_configuration


def _refresh_loop(interval_sec: int, stop_event: threading.Event) -> None:
    """Background loop to refresh dynamic configuration periodically (always full refresh)."""
    logger.info(f"Starting dynamic config refresh loop with interval {interval_sec}s")

    # The loop will now wait for the first interval BEFORE the first refresh.
    while not stop_event.wait(interval_sec):
        try:
            get_dynamic_config()
        except Exception as periodic_error:
            logger.error(f"Periodic dynamic config fetch failed: {periodic_error}")


def start_background_refresh(interval_in_seconds: int, initial_sync: bool = False) -> bool:
    """Start a singleton background thread to refresh dynamic config periodically (always full refresh).

    If initial_sync is True, perform a blocking config fetch before starting the thread.
    Returns True if a new thread was started, False if one was already running.
    """
    global _REFRESH_THREAD, _STOP_EVENT
    if initial_sync:
        try:
            logger.info("Performing blocking initial dynamic config fetch before starting background refresh thread.")
            get_dynamic_config()
        except Exception as e:
            logger.error(f"Initial blocking dynamic config fetch failed: {e}")
    with _THREAD_LOCK:
        if _REFRESH_THREAD and _REFRESH_THREAD.is_alive():
            logger.info("Dynamic config background refresh already running; skipping start.")
            return False
        _STOP_EVENT = threading.Event()
        refresh_thread = threading.Thread(
            target=_refresh_loop, args=(interval_in_seconds, _STOP_EVENT), daemon=True, name="DynamicConfigRefreshThread"
        )
        _REFRESH_THREAD = refresh_thread
        refresh_thread.start()
        logger.info("Dynamic config background refresh thread started.")
        return True


def stop_background_refresh(timeout: float = 3.0) -> None:
    """Signal the background thread to stop and optionally join for a short time."""
    global _REFRESH_THREAD, _STOP_EVENT
    with _THREAD_LOCK:
        stop_event = _STOP_EVENT
        background_thread = _REFRESH_THREAD
        _STOP_EVENT = None
        _REFRESH_THREAD = None
    if stop_event:
        stop_event.set()
    if background_thread:
        try:
            background_thread.join(timeout=timeout)
        except Exception:
            pass
