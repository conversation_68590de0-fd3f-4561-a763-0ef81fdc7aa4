from typing import Dict, Any, Union
from quepasa.searcher.models.request import QuepasaRequest
from ..base.source_reference import SourceReferenceConfig
from src.lib.constants import SOURCE_DOCUMENTS

class SourceReferenceMixin(SourceReferenceConfig):
    """Mixin that implements source reference formatting functionality."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
    
    def get_source_reference(self, source: str, item: Dict[str, Any]) -> str:
        """Get formatted reference string for display.
        
        Previously: get_source_reference()
        
        Args:
            source: Source type (e.g. SOURCE_ALL, DOCUMENT_TYPE_DIALOG)
            item: Source item data
                
        Returns:
            Formatted reference string
        """
        if (
            source == SOURCE_DOCUMENTS
            and 'title' in item
            and item['title'] != None
            and item['title'].strip() != ""
        ):
            return f"🌐{item['title']}"
            
        return super().get_source_reference(source, item)
