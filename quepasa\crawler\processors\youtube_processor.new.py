import os
import random
import logging
from datetime import datetime, timed<PERSON>ta
from urllib.parse import parse_qs, urlparse
from pytubefix import YouTube
from youtube_transcript_api import YouTube<PERSON>rans<PERSON><PERSON><PERSON>
from .base import BaseProcessor
from src.lib.files import QuepasaFiles
from ...config import VS_CHUNK_SIZE
from ..exceptions import ProcessorError
from src.lib.batch_utils import YOUTUBE_STORAGE

# Initialize QuepasaFiles
qp_files = QuepasaFiles()

def generate_random_headers():
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:102.0) Gecko/20100101 Firefox/102.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
    ]

    accept_languages = [
        "en-US,en;q=0.9",
        "en-GB,en;q=0.8",
        "en-US,en;q=0.7,es;q=0.3",
        "en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    ]

    headers = {
        "User-Agent": random.choice(user_agents),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Accept-Language": random.choice(accept_languages),
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
    }
    return headers

class YouTubeProcessor(BaseProcessor):
    def __init__(self, proxy_list=None):
        """Initialize YouTubeProcessor with optional proxy list."""
        super().__init__()
        self.proxy_list = proxy_list or []

    def _get_proxy(self):
        """Get a random proxy from the proxy list."""
        if not self.proxy_list:
            return None
        proxy = random.choice(self.proxy_list)
        return {'http': proxy, 'https': proxy}

    def _process_impl(self, url: str, metadata: dict) -> dict:
        """Process YouTube video URL and extract transcript."""
        try:
            # If url is bytes, get it from metadata instead
            if isinstance(url, bytes):
                if 'url' not in metadata:
                    raise ProcessorError("No URL provided in metadata")
                url = metadata['url']
            
            video_id = self._extract_video_id(url)
            
            # Get random proxy and headers
            proxies = self._get_proxy()
            headers = generate_random_headers()
            
            # Get video metadata with proxy if available
            if proxies:
                yt = YouTube(url, proxies=proxies)
            else:
                yt = YouTube(url)
            title = f"{yt.author} — {yt.title}"
            
            # Extract caption code and language
            caption_list = list(yt.captions.keys())
            if not caption_list:
                raise ProcessorError("No captions available for this video")
            
            caption_code = caption_list[0].code.split('.')[-1].split('-')[0]
            
            # Check cache first
            cache_filename = f"{video_id}.json"
            cache_file = f"{YOUTUBE_STORAGE}/{cache_filename}"
            
            if qp_files.exists(cache_file):
                transcript = qp_files.get_json(cache_file)

            else:
                # Get transcript with proxy if available
                if proxies:
                    transcript_list = YouTubeTranscriptApi.list_transcripts(video_id, proxies=proxies)
                else:
                    transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
                
                try:
                    # Try to get transcript in the detected language first
                    transcript = transcript_list.find_transcript([caption_code]).fetch()
                except:
                    try:
                        # Fallback to English if available
                        transcript = transcript_list.find_transcript(['en']).fetch()
                        caption_code = 'en'
                    except:
                        # Get first available transcript as last resort
                        transcript = transcript_list.find_transcript([]).fetch()
                
                # Cache the transcript
                qp_files.set_json(cache_file, transcript)
            
            # Get transcript in WebVTT format with timestamps
            chunks = self._get_webvtt(transcript)
            
            return {
                'chunks': chunks,
                'title': title or metadata.get('filename', ''),
                'filename': metadata.get('filename', ''),
                'language': caption_code
            }
        except Exception as e:
            raise ProcessorError(f"Error processing YouTube video: {str(e)}")

    def _extract_video_id(self, url: str) -> str:
        """Extract video ID from YouTube URL."""
        try:
            if 'youtu.be' in url:
                return url.split('/')[-1]
            else:
                query = parse_qs(urlparse(url).query)
                return query['v'][0]
        except Exception as e:
            raise ProcessorError(f"Invalid YouTube URL: {str(e)}")

    def _get_webvtt(self, transcript: list) -> str:
        """Convert transcript to WebVTT format, grouping entries into 60 second chunks."""
        chunks = []
        text = ""
        start = 0.0
        duration = 0.0
        last_start = None
        last_duration = None

        for entry in transcript:
            if text == "":
                start = entry['start']
                duration = 0.0

            duration += (
                entry['duration'] +
                (entry['start'] - last_start if last_start is not None else 0.0) -
                (last_duration if last_duration is not None else 0.0)
            )
            text += entry['text'] + " "

            if len(text) > VS_CHUNK_SIZE:
                chunks.append({
                    'text': text.strip(),
                    'position': f"timestamp {self._format_timestamp(start)} - {self._format_timestamp(entry['start'] + entry['duration'])}"
                })
                text = ""

            last_start = entry['start']
            last_duration = entry['duration']

        if text != "":
            chunks.append({
                'text': text.strip(),
                'position': f"timestamp {self._format_timestamp(start)} - {self._format_timestamp(last_start + last_duration)}"
            })

        return chunks

    def _format_timestamp(self, seconds: float) -> str:
        """Format timestamp in HH:MM:SS.mmm format."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"

    def format_for_quepasa(self, video_data, language='en'):
        """Format YouTube video data for the QuePasa API format."""
        try:
            # Extract relevant fields
            video_id = video_data.get('video_id', '')
            url = video_data.get('url', '')
            title = video_data.get('title', '')
            formatted_transcript = video_data.get('formatted_transcript', [])
            
            if not formatted_transcript or not video_id:
                logging.warning(f"Missing required data for video: {video_id}")
                return None
                        
            # Group transcriptions into pages (e.g., 2 minutes per page)
            page_duration = 120  # 2 minutes in seconds
            pages = []
            
            current_page = []
            current_duration = 0
            
            for t in formatted_transcript:
                # Skip invalid transcript entries
                if 'start_time' not in t or 'end_time' not in t or 'text' not in t or t['text'] is None:
                    continue
                    
                segment_duration = t['end_time'] - t['start_time']
                if current_duration + segment_duration > page_duration and current_page:
                    # Join current page transcriptions with double newlines
                    page_text = ""
                    try:
                        page_text = '\n\n'.join(
                            f"[{timedelta(seconds=int(item['start_time']))} - {timedelta(seconds=int(item['end_time']))}]\n{item['text']}" 
                            for item in current_page if item['text'] is not None
                        ).strip()
                    except Exception as e:
                        logging.error(f"Error formatting page text: {str(e)}")
                        # Create an emergency fallback version
                        page_text = '\n\n'.join(
                            f"[{timedelta(seconds=int(item.get('start_time', 0)))} - {timedelta(seconds=int(item.get('end_time', 0)))}]\n{item.get('text', '')}" 
                            for item in current_page
                        ).strip()
                    
                    pages.append({
                        'language': language,
                        'text': page_text
                    })
                    current_page = []
                    current_duration = 0
                
                current_page.append(t)
                current_duration += segment_duration
            
            # Add remaining transcriptions as the last page
            if current_page:
                page_text = ""
                try:
                    page_text = '\n\n'.join(
                        f"[{timedelta(seconds=int(item['start_time']))} - {timedelta(seconds=int(item['end_time']))}]\n{item['text']}" 
                        for item in current_page if item['text'] is not None
                    ).strip()
                except Exception as e:
                    logging.error(f"Error formatting last page text: {str(e)}")
                    # Create an emergency fallback version
                    page_text = '\n\n'.join(
                        f"[{timedelta(seconds=int(item.get('start_time', 0)))} - {timedelta(seconds=int(item.get('end_time', 0)))}]\n{item.get('text', '')}" 
                        for item in current_page
                    ).strip()
                
                pages.append({
                    'language': language,
                    'text': page_text
                })
            
            # Create dates from the video_data
            created_at = video_data.get('date_posted', datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')).split('.')[0] + "Z"
            
            # Create the API document format
            document = {
                # Required fields
                'id': url,
                'url': url,
                'title': title,  # Using title directly from the input data
                'chunks': [
                    {
                        'text': page['text'],
                        'language': page['language'],
                        'position': f"page_{idx+1}"
                    }
                    for idx, page in enumerate(pages)
                ],
                
                # Optional fields
                'created_at': created_at,
                'updated_at': datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            }        
            return document
            
        except Exception as e:
            logging.error(f"Error formatting API output: {str(e)}")
            return None

    def process_brightdata_payload(self, payload, batch_meta):
        """Process videos from BrightData webhook payload."""
        results = []
        for video_data in payload:
            try:
                # Format the video data for QuePasa
                document = self.format_for_quepasa(video_data, language=batch_meta.get('language', 'en'))
                
                if document:
                    # Prepare metadata for saving
                    meta = {
                        'client_id': batch_meta['client_id'],
                        'domain': batch_meta['domain'],
                        'id': document['id'],
                        'url': document['url'],
                        'filename': document.get('title', ''),
                        'language': batch_meta.get('language', 'en')
                    }
                    
                    # Import save_to_backlog here to avoid circular imports
                    from quepasa.crawler.tasks import save_to_backlog
                    
                    # Save to backlog
                    backlog_path = save_to_backlog(
                        {
                            'status': 'success',
                            'result': document
                        },
                        meta
                    )
                    
                    results.append({
                        'id': document['id'],
                        'file': backlog_path,
                        'status': 'success'
                    })
                else:
                    logging.warning(f"Skipping video due to missing data or formatting error")
            except Exception as e:
                logging.error(f"Error processing video from BrightData: {str(e)}")
                results.append({
                    'id': video_data.get('video_id', 'unknown'),
                    'error': str(e),
                    'status': 'error'
                })
                
        return results