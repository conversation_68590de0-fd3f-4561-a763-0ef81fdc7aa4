"""
Test script to verify Gemini integration with existing LLM utils.
Run this script to test the Gemini provider integration.
"""

import os
import sys
import asyncio
from typing import List, Dict

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.lib.llm_utils import get_llm_answer, get_streaming_chunks, get_cached_llm_answer, get_llm_tools_answer
from src.lib.llm.factory import LLMFactory
from src.lib.llm.providers import LLMProvider

def test_factory_integration():
    """Test that Gemini can be created through the factory."""
    print("Testing factory integration...")
    
    try:
        # Test string provider
        llm = LLMFactory.get_llm("gemini")
        print(f"✓ Created Gemini LLM via string: {type(llm).__name__}")
        
        # Test enum provider
        llm = LLMFactory.get_llm(LLMProvider.GEMINI)
        print(f"✓ Created Gemini LLM via enum: {type(llm).__name__}")
        
        # Test provider property
        assert llm.provider == LLMProvider.GEMINI
        print("✓ Provider property returns correct enum")
        
    except Exception as e:
        print(f"✗ Factory integration failed: {e}")
        return False
    
    return True

def test_llm_utils_integration():
    """Test integration with LLM utils functions."""
    print("\nTesting LLM utils integration...")
    
    if not os.getenv("GEMINI_API_KEY"):
        print("⚠ Skipping LLM utils tests - GEMINI_API_KEY not set")
        return True
    
    try:
        # Test basic answer
        prompt_list = [{"role": "user", "content": "Say 'Hello from Gemini!'"}]
        answer = get_llm_answer("gemini", "gemini-2.0-flash-exp", prompt_list, 100, False)
        print(f"✓ Basic answer: {answer[:50]}...")
        
        # Test cached answer
        cached_answer = get_cached_llm_answer("gemini", "gemini-2.0-flash-exp", prompt_list, 100, False)
        print(f"✓ Cached answer: {cached_answer[:50]}...")
        
        # Test tools answer
        tools = [{"function_declarations": [{"name": "test_func", "description": "Test function"}]}]
        tools_result = get_llm_tools_answer("gemini", "gemini-2.0-flash-exp", prompt_list, tools, 100)
        print(f"✓ Tools answer: {tools_result[0][:50]}...")
        
    except Exception as e:
        print(f"✗ LLM utils integration failed: {e}")
        return False
    
    return True

def test_streaming_integration():
    """Test streaming integration."""
    print("\nTesting streaming integration...")
    
    if not os.getenv("GEMINI_API_KEY"):
        print("⚠ Skipping streaming tests - GEMINI_API_KEY not set")
        return True
    
    try:
        prompt_list = [{"role": "user", "content": "Count from 1 to 3, one number per line."}]
        
        print("Streaming chunks:")
        chunks = []
        for chunk in get_streaming_chunks("gemini", "gemini-2.0-flash-exp", prompt_list, 100, False):
            print(f"  Chunk: {repr(chunk)}")
            chunks.append(chunk)
        
        print(f"✓ Received {len(chunks)} chunks")
        
    except Exception as e:
        print(f"✗ Streaming integration failed: {e}")
        return False
    
    return True

def test_role_mapping():
    """Test role mapping functionality."""
    print("\nTesting role mapping...")
    
    try:
        from src.lib.llm.gemini import _pluck_system, _to_history
        
        # Test system message extraction
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"}
        ]
        
        system_msg = _pluck_system(messages)
        assert system_msg == "You are a helpful assistant."
        print("✓ System message extraction works")
        
        # Test role mapping
        history = _to_history(messages, drop_system=True)
        expected_roles = ["user", "model"]
        actual_roles = [msg["role"] for msg in history]
        assert actual_roles == expected_roles
        print("✓ Role mapping works (assistant -> model)")
        
    except Exception as e:
        print(f"✗ Role mapping failed: {e}")
        return False
    
    return True

def main():
    """Run all integration tests."""
    print("🧪 Testing Gemini Integration")
    print("=" * 50)
    
    tests = [
        test_factory_integration,
        test_role_mapping,
        test_llm_utils_integration,
        test_streaming_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Gemini integration is working correctly.")
    else:
        print("❌ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)