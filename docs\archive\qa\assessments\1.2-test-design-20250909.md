# Test Design: Story 1.2 - Enhance Ingestion Pipeline (Minimum Essential Tests)

Date: 2025-09-09
Designer: <PERSON> (Test Architect)
Approach: **Minimum Absolutely Necessary Tests Only**

## Test Strategy Overview

- Total test scenarios: **5** (focused on P0 critical path only)
- Unit tests: 3 (60%) - Core logic verification
- Integration tests: 2 (40%) - Data flow validation  
- E2E tests: 0 (0%) - Not necessary for internal pipeline changes
- Priority distribution: P0: 5, P1: 0, P2: 0

**Rationale**: This story affects internal data processing pipeline only. Focus on data integrity and essential functionality verification.

## Test Scenarios by Acceptance Criteria

### AC1: Filtering method _filter_product_for_metadata implementation

#### Essential Test
| ID           | Level | Priority | Test                                    | Justification                               |
| ------------ | ----- | -------- | --------------------------------------- | ------------------------------------------- |
| 1.2-UNIT-001 | Unit  | P0       | Verify exclusion of METADATA_EXCLUDE_KEYS | Critical - prevents data corruption in metadata |

**Test Details:**
- Input: Product with all possible fields including excluded keys
- Expected: Only non-excluded keys remain in output
- Edge cases: Empty product, missing fields, null values

### AC2: products_to_documents function update

#### Essential Tests
| ID           | Level | Priority | Test                                | Justification                          |
| ------------ | ----- | -------- | ----------------------------------- | -------------------------------------- |
| 1.2-UNIT-002 | Unit  | P0       | Verify document type set to "product" | Critical - enables downstream processing |
| 1.2-UNIT-003 | Unit  | P0       | Verify metadata JSON serialization   | Critical - data integrity requirement   |

**Test Details:**
- 1.2-UNIT-002: Ensure `type: "product"` is set in document output
- 1.2-UNIT-003: Ensure `metadata` contains valid JSON string of filtered data

### AC3: Data processor task update

#### Essential Test  
| ID           | Level       | Priority | Test                                  | Justification                      |
| ------------ | ----------- | -------- | ------------------------------------- | ---------------------------------- |
| 1.2-INT-001  | Integration | P0       | Verify metadata preserved in clean_doc | Critical - data loss prevention    |

**Test Details:**
- Mock product document with metadata field
- Verify metadata survives the cleaning process
- Confirm field appears in final output

### AC4 & AC5: Testing and Integration validation

#### Essential Test
| ID           | Level       | Priority | Test                               | Justification                       |
| ------------ | ----------- | -------- | ---------------------------------- | ----------------------------------- |
| 1.2-INT-002  | Integration | P0       | End-to-end ingestion verification | Critical - validates complete flow  |

**Test Details:**
- Input: Sample product with various field types
- Verify: MinIO file contains correct type and metadata JSON
- Validates: Complete pipeline from ingestion through storage

## Risk Coverage

**Critical Risk Mitigated**: Data corruption or loss during pipeline processing
- All tests focus on data integrity preservation
- JSON serialization correctness prevents downstream parsing failures
- Type field validation ensures proper document classification

## Recommended Execution Order

1. **1.2-UNIT-001** - Filtering logic (fail fast on core functionality)
2. **1.2-UNIT-002** - Document type setting  
3. **1.2-UNIT-003** - JSON serialization
4. **1.2-INT-001** - Data processor preservation
5. **1.2-INT-002** - End-to-end validation

## Tests Deliberately Excluded (Minimum Approach)

**Not Testing (Rationale):**
- Performance testing (not critical for internal pipeline)
- Edge case handling beyond data corruption prevention
- Error scenarios beyond basic null/empty checks
- UI/API impact (no direct user-facing changes)
- Load testing (existing infrastructure)

**Coverage Assessment:**
- ✅ All ACs have essential test coverage
- ✅ Data integrity protected at critical points
- ✅ Core functionality verified
- ✅ Pipeline flow validated end-to-end
- ✅ No duplicate coverage across levels

## Implementation Notes

**Existing Test Files to Update:**
- `tests/lib/test_markdown_converter.py` - Add units 001, 002, 003
- `tests/test_data_processor.py` - Add integration 001
- `tests/integration/test_product_pipeline.py` - Create new for integration 002

**Test Data Requirements:**
- Sample product with all field types
- Products with excluded keys present
- Empty/null scenarios for edge cases