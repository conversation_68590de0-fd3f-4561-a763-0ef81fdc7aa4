# Story 1.3: SSE Streaming Response

## Status
Draft

## Story
**As a** developer,  
**I want to** add Server-Sent Events (SSE) streaming capabilities to the /conversation endpoint,  
**so that** clients can receive real-time, progressive updates for an improved user experience.

## Acceptance Criteria
1. The endpoint correctly initiates an SSE stream when ?stream=true is in the URL or the Accept header is text/event-stream.
2. The implementation reuses as much of the existing underlying streaming logic as possible.
3. A unique requestId is generated at the start of the request and used as the constant responseId for all events in that stream.
4. All events sent on the stream conform to the ChatResponse shape.
5. Interim events have a status of IN_PROGRESS.
6. The final event in the stream has a status of COMPLETE.
7. The SSE stream closes correctly after the COMPLETE event is sent.
8. The non-streaming functionality remains the default behavior.

## Tasks / Subtasks

- [ ] Task 1: Refactor handler selection logic in main.py (AC: 1)
  - [ ] Modify get_endpoint_from_path or create new logic to detect streaming requests for /conversation
  - [ ] Update handler selection to support conversation endpoint with both streaming and non-streaming modes
  - [ ] Ensure existing endpoint routing remains unaffected
  - [ ] Add logic to parse ?stream=true query parameter and Accept: text/event-stream header

- [ ] Task 2: Create conversation streaming handler (AC: 2, 3, 4, 5, 6, 7)
  - [ ] Extend HTTPHandler to detect streaming conversation requests and delegate to streaming logic
  - [ ] Create streaming conversation method that reuses existing SSE infrastructure
  - [ ] Implement ChatResponse-shaped SSE event generation (not QuepasaStreamAnswer format)
  - [ ] Ensure responseId remains constant throughout the stream using requestId
  - [ ] Add status field management: IN_PROGRESS for interim events, COMPLETE for final event
  - [ ] Implement proper SSE stream closure after COMPLETE event

- [ ] Task 3: Integrate response transformation for streaming (AC: 2, 4)
  - [ ] Adapt existing response transformation logic to work in streaming context
  - [ ] Ensure each streamed chunk conforms to ChatResponse schema
  - [ ] Handle format parameter (markdown/plain_text) in streaming context
  - [ ] Integrate with existing source factory streaming via get_answer(stream=True)

- [ ] Task 4: Update conversation request routing (AC: 1, 8)
  - [ ] Modify _handle_conversation method to detect streaming requests
  - [ ] Route streaming requests to new streaming logic
  - [ ] Ensure non-streaming remains default behavior
  - [ ] Test both streaming and non-streaming conversation flows

- [ ] Task 5: Add essential unit tests for streaming functionality (AC: 1, 2, 3, 4, 5, 6, 7, 8)
  - [ ] Test streaming request detection via query parameter and Accept header
  - [ ] Test ChatResponse schema compliance for streaming events
  - [ ] Test responseId consistency throughout stream
  - [ ] Test status progression (IN_PROGRESS → COMPLETE)
  - [ ] Test proper SSE stream closure
  - [ ] Test non-streaming default behavior preservation

## Dev Notes

### Previous Story Insights
**[Source: Story 1.2 Dev Agent Record]**
- ✅ **Conversation Endpoint Foundation**: HTTPHandler._handle_conversation method implemented (lines 103-177)
- ✅ **Source Factory Integration**: Working integration with get_answer(stream=False) and source: "agentic"
- ✅ **Response Transformation**: ChatResponse format transformation working for non-streaming requests
- ✅ **Authentication**: BearerAuth working with dual-header validation
- ✅ **Request Processing**: ChatRequest model parsing and user context mapping established
- ⚠️ **Critical Gap**: No streaming support - current implementation only handles non-streaming requests

**Key Technical Foundation Available:**
- HTTPHandler._handle_conversation method ready for streaming enhancement
- ChatResponse transformation logic ready for streaming adaptation  
- Source factory streaming available via get_answer(stream=True)
- Request/response model infrastructure complete

### Architecture Context and Current Handler Selection Issue

**[Source: quepasa/searcher/main.py lines 133-158]**

**Current Handler Selection Logic Problem:**
```python
# Current problematic logic
quepasa_request.protocol = request_data.get('protocol', SSE_PROTOCOL if endpoint == ANSWER_STREAM_ENDPOINT else HTTP_PROTOCOL)

if config.request.protocol == SSE_PROTOCOL:
    handler = SSEHandler(config)
else:  
    handler = HTTPHandler(config)
```

**Critical Issue**: The current logic hardcodes SSE_PROTOCOL only for ANSWER_STREAM_ENDPOINT (`/stream`). The `/conversation` endpoint needs dynamic protocol selection based on:
- Query parameter: `?stream=true`  
- Accept header: `text/event-stream`

**[Source: docs/CON-76-architecture.md#3.2.1]**

**Required Handler Selection Enhancement:**
- Conversation endpoint must support BOTH streaming and non-streaming in single endpoint
- Cannot rely on hardcoded endpoint-to-protocol mapping
- Must parse request parameters/headers to determine protocol dynamically
- Existing endpoints must remain unaffected

### SSE Infrastructure Reuse Analysis

**[Source: quepasa/searcher/api/sse.py lines 19-98]**

**Existing SSE Handler Capabilities:**
- ✅ **SSE Response Generation**: Working SSE headers and Response creation (lines 83-96)
- ✅ **Source Factory Streaming**: Integration with source_factory.get_answer(stream=True) (line 29)
- ✅ **Error Handling**: Comprehensive error handling with proper SSE error responses (lines 68-81)
- ✅ **Generator Pattern**: Working stream_with_context generator pattern (line 84)
- ❌ **Wrong Response Format**: Current format is QuepasaStreamAnswer, need ChatResponse format

**QuepasaStreamAnswer vs ChatResponse Format Mismatch:**
- Current SSE handler yields QuepasaStreamAnswer.to_dict() format
- Story 1.3 requires ChatResponse format with status: IN_PROGRESS/COMPLETE fields
- Need new conversation streaming method that transforms to ChatResponse format

### Response Format Requirements

**[Source: docs/api/conversational_shopping_assistant_v1.yaml]**

**ChatResponse Schema for Streaming:**
```yaml
ChatResponse:
  required: [sessionId, responseId, timestamp, content, stream]
  properties:
    sessionId: string (from request)
    responseId: string (constant requestId throughout stream) 
    timestamp: string (ISO 8601 with Z suffix)
    content: string (partial content in streaming)
    stream: boolean (true for streaming responses)
    status: string (IN_PROGRESS | COMPLETE) # Key streaming field
    visitorId: string (if provided/generated)
    components: array (ProductSet/ReferenceItem objects)
    actions: array (ActionItem objects)
```

**Streaming Status Field Requirements:**
- **Interim Events**: `status: "IN_PROGRESS"` for all partial updates
- **Final Event**: `status: "COMPLETE"` for stream completion signal
- **ResponseId Consistency**: Same responseId (from requestId) across all events

### Handler Selection Solution Architecture

**[Source: Analysis of main.py + Architecture Requirements]**

**Required Enhancement Pattern:**
1. **Parse Streaming Indicators**: Check `?stream=true` query param and `Accept: text/event-stream` header
2. **Dynamic Protocol Assignment**: Set protocol based on request analysis, not endpoint hardcoding
3. **Handler Selection**: Route to HTTPHandler for conversation endpoint but with streaming capability flag
4. **Internal Routing**: HTTPHandler detects streaming and delegates to streaming logic when needed

**Implementation Approach:**
```python
# New logic needed in main.py
def determine_protocol_for_conversation(request_data, headers, path):
    if 'conversation' in path:
        # Check for streaming indicators
        stream_query = request_data.get('stream', '').lower() == 'true' 
        stream_header = headers.get('Accept', '') == 'text/event-stream'
        return SSE_PROTOCOL if (stream_query or stream_header) else HTTP_PROTOCOL
    # Existing logic for other endpoints...
```

### Integration Points and File Locations

**[Source: Story 1.2 File List + main.py Analysis]**

**Files Requiring Modification:**
- **Main Logic**: `/quepasa/searcher/main.py` (handler selection logic lines 133-158)
- **Conversation Handler**: `/quepasa/searcher/api/http.py` (_handle_conversation method enhancement)
- **Response Models**: `/quepasa/searcher/models/response.py` (potential ChatResponse streaming extensions)
- **SSE Integration**: May need conversation-specific SSE logic or reuse existing patterns

**New Files Potentially Needed:**
- **Conversation Streaming Module**: If SSE handler reuse becomes complex, may need dedicated conversation streaming handler

### Testing Requirements

**[Source: Story 1.1/1.2 Testing Patterns + Architecture]**

**Streaming-Specific Test Scenarios:**
- **Request Detection**: Test ?stream=true and Accept header parsing
- **Response Format**: Validate each SSE event matches ChatResponse schema
- **Status Progression**: Verify IN_PROGRESS → COMPLETE status flow  
- **ResponseId Consistency**: Ensure same responseId across all stream events
- **Stream Closure**: Verify proper SSE connection closure after COMPLETE
- **Default Behavior**: Confirm non-streaming remains default when no streaming indicators

**Test File Location**: `/tests/test_searcher_conversation.py`
- Extend existing conversation test foundation with streaming test cases
- Add SSE-specific test utilities and fixtures
- Mock source_factory.get_answer(stream=True) responses

### Technical Constraints

**[Source: docs/CON-76-prd.md NFR5 + Architecture Analysis]**

**ResponseId/RequestId Requirements:**
- RequestId must remain UUID v4 format (generated once per request)
- Same requestId used as responseId throughout entire stream
- Must be consistent across all SSE events in single stream

**Backward Compatibility:**
- Existing /retrieve/answer/stream endpoint must remain unaffected
- Non-streaming /conversation behavior must remain default
- SSE handler reuse must not break existing streaming endpoints

**Format Parameter Integration:**
- ?format=markdown/plain_text must work with streaming
- Content formatting applied to each streaming chunk as appropriate

## Testing

### Test File Location
- `/tests/test_searcher_conversation.py`

### Test Standards  
- Extend existing conversation test foundation (17 tests) with streaming scenarios
- Use pytest framework with async support for SSE testing
- Test both streaming and non-streaming conversation flows
- Mock source_factory.get_answer(stream=True) responses appropriately
- Validate ChatResponse schema compliance for streaming events

### Testing Frameworks and Patterns
- pytest with pytest-asyncio for async test support
- SSE testing utilities for stream event validation
- Mock streaming responses from source factory
- Test request parameter/header parsing for streaming detection
- Validate responseId consistency across streaming events

## Change Log

| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- |
| 2025-09-12 | 1.0 | Initial story creation focusing on handler selection refactoring and SSE streaming | Bob (SM) |

## Dev Agent Record

### Agent Model Used
*[To be filled by development agent]*

### Debug Log References
*[To be filled by development agent]*

### Completion Notes List
*[To be filled by development agent]*

### File List
*[To be filled by development agent]*

## QA Results
*[To be filled by QA agent]*