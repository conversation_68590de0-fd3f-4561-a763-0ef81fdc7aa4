import pytest
from configuration.base.telegram_ui import TelegramUIConfig
from quepasa.searcher.models.request import QuepasaRequest

@pytest.fixture
def telegram_ui_config():
    return TelegramUIConfig("test_client")

@pytest.fixture
def base_request():
    return QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram",
        command="ask"
    )

def test_get_telegram_first_time_welcome_message(telegram_ui_config):
    """Test welcome message retrieval"""
    result = telegram_ui_config.get_telegram_first_time_welcome_message()
    assert result is None

@pytest.mark.slow
def test_get_telegram_start_command_response(telegram_ui_config):
    """Test start command response retrieval"""
    result = telegram_ui_config.get_telegram_start_command_response()
    assert result is None

@pytest.mark.slow
def test_get_telegram_empty_ask_command_response(telegram_ui_config):
    """Test empty ask command response retrieval"""
    result = telegram_ui_config.get_telegram_empty_ask_command_response()
    assert result is None

def test_get_telegram_available_commands(telegram_ui_config):
    """Test available commands retrieval"""
    commands = telegram_ui_config.get_telegram_available_commands()
    assert isinstance(commands, list)
    assert len(commands) > 0
    assert "start" in commands
    assert "ask" in commands

def test_get_telegram_command_descriptions(telegram_ui_config):
    """Test command descriptions retrieval"""
    descriptions = telegram_ui_config.get_telegram_command_descriptions()
    assert isinstance(descriptions, dict)
    assert "start" in descriptions
    assert "ask" in descriptions

def test_get_telegram_empty_command_response(telegram_ui_config):
    """Test empty command response"""
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram",
        command="reset"
    )
    telegram_ui_config.set_request(request)
    response = telegram_ui_config.get_telegram_empty_command_response()
    assert isinstance(response, str)
    assert "reset" in response.lower()

def test_get_telegram_positive_feedback_response(telegram_ui_config):
    """Test positive feedback response retrieval"""
    result = telegram_ui_config.get_telegram_positive_feedback_response("en")
    assert result is None

def test_get_telegram_negative_feedback_response(telegram_ui_config):
    """Test negative feedback response retrieval"""
    result = telegram_ui_config.get_telegram_negative_feedback_response("en")
    assert result is None

def test_get_telegram_response_buttons(telegram_ui_config):
    """Test response buttons"""
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram"
    )
    telegram_ui_config.set_request(request)
    response = "Test response"
    buttons = [{"text": "👍", "callback_data": "feedback_positive"}]
    result = telegram_ui_config.get_telegram_response_buttons(
        response=response,
        buttons=buttons,
        source=request.source
    )
    assert isinstance(result, list)
    assert len(result) > 0
    assert isinstance(result[0], dict)
    assert "text" in result[0]
    assert "callback_data" in result[0]

def test_get_no_results_message(telegram_ui_config, base_request):
    """Test no results message retrieval"""
    telegram_ui_config.set_request(base_request)
    message, buttons = telegram_ui_config.get_no_results_message("en")
    assert isinstance(message, str)
    assert isinstance(buttons, list)
    assert "couldn't find an answer" in message.lower()

def test_should_show_preview_links(telegram_ui_config):
    """Test preview links display check"""
    assert telegram_ui_config.should_show_preview_links() == False

def test_should_show_webapp_preview(telegram_ui_config):
    """Test webapp preview display check"""
    assert telegram_ui_config.should_show_webapp_preview() == True 