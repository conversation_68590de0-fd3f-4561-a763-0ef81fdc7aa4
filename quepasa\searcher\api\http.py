import json
import uuid
from datetime import datetime
from typing import Dict, <PERSON>, <PERSON><PERSON>, <PERSON>, Generator

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>og<PERSON>
from .base import BaseAPIHandler
from src.lib.constants import (
    SEARCH_ENDPOINT, ANSWER_ENDPOINT, HISTORY_ENDPOINT, CONVERSATION_ENDPOINT, SOURCE_AGENTIC,
    ERROR_CODE_INVALID_REQUEST, ERROR_CODE_INTERNAL_SERVER_ERROR, ERROR_CODE_UNAUTHORIZED,
    ERROR_CODE_NOT_FOUND, FORMAT_MARKDOWN, FORMAT_PLAIN_TEXT, HTTP_PROTOCOL
)
from configuration.main.default import QuepasaConfigurationHub
from ..models.response import QuepasaResponse, QuepasaStreamAnswer, ChatRequest, ChatResponse, ResponseStatus

logger = QuepasaLogger().get_instance(__name__)

class HTTPHandler(BaseAPIHandler):
    """HTTP API handler for search requests"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        super().__init__(config)
        self.default_cors_headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST',
            'Access-Control-Allow-Headers': '*',
            'Access-Control-Max-Age': '3600'
        }

    def _handle_request_internal(self, method: str, url: str, headers: Dict[str, str]) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Internal request handler implementation"""
        
        # Store current URL for query parameter parsing
        self._current_url = url
        
        # Handle OPTIONS request
        if method == 'OPTIONS':
            return {}, 204, self.default_cors_headers

        if method != 'POST':
            return self._error_response('Method not allowed', 405)

        # Validate protocol
        if not self._validate_request():
            return self._error_response(self.error, 400)
            
        # Route request to appropriate handler
        try:
            endpoint = self._get_endpoint(url)
            if endpoint == SEARCH_ENDPOINT:
                return self._handle_search()
            elif endpoint == ANSWER_ENDPOINT:
                return self._handle_answer()
            elif endpoint == HISTORY_ENDPOINT:
                return self._handle_history()
            elif endpoint == CONVERSATION_ENDPOINT:
                return self._handle_conversation()
            else:
                return self._error_response('Unknown endpoint', 404)
                
        except Exception as e:
            logger.error(f"Error handling request: {str(e)}")
            return self._error_response('Internal server error', 500)

    def _handle_search(self) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Handle search request"""
        try:
            return self._success_response(self.source_factory.search())

        except Exception as e:
            logger.error(f"Error handling search request: {str(e)}")
            return self._error_response(str(e), 500)

    def _handle_answer(self) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Handle answer request"""
        try:
            return self._success_response(self.source_factory.get_answer(stream=False))

        except Exception as e:
            logger.error(f"Error handling answer request: {str(e)}")
            return self._error_response(str(e), 500)

    def _handle_history(self) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Handle history request"""
        history_list = []
        if (
            hasattr(self.config.request, 'user_info')
            and hasattr(self.config.request.user_info, 'id')
        ):
            user_id = self.config.request.user_info.id
            if user_id:
                try:
                    history_list = self.history_manager.get_conversation_history(user_id)
                    
                except Exception as e:
                    logger.error(f"Error loading history: {str(e)}")

        return self._success_response({
            'data': history_list,
            'status': 'OK'
        })

    def _handle_conversation(self) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Handle conversation request"""
        try:
            # Parse ChatRequest from request body
            if not hasattr(self.config.request, 'body') or not self.config.request.body:
                return self._conversation_error_response('Missing request body', ERROR_CODE_INVALID_REQUEST, 400)

            try:
                chat_request_data = json.loads(self.config.request.body)
            except json.JSONDecodeError:
                return self._conversation_error_response('Invalid JSON in request body', ERROR_CODE_INVALID_REQUEST, 400)
            
            # Validate required fields before parsing ChatRequest
            required_fields = ['message', 'sessionId', 'collection', 'area']
            missing_fields = []
            for field in required_fields:
                if field not in chat_request_data or not chat_request_data[field]:
                    missing_fields.append(field)
            
            if missing_fields:
                error_msg = f"Missing required parameters: {', '.join(missing_fields)}"
                return self._conversation_error_response(error_msg, ERROR_CODE_INVALID_REQUEST, 400)
            
            # Parse and validate ChatRequest
            try:
                chat_request = ChatRequest.from_dict(chat_request_data)
            except (KeyError, TypeError) as e:
                return self._conversation_error_response(f'Invalid chat request format: {str(e)}', ERROR_CODE_INVALID_REQUEST, 400)
            
            # Parse query parameters for format switching
            format_param = self._get_query_param('format', FORMAT_MARKDOWN)
            
            # Set user context for conversation (generates visitor ID if needed)
            self._set_conversation_request_context()
            
            # Set request configuration for source factory
            self.config.request.source = SOURCE_AGENTIC
            self.config.request.question = chat_request.message
            
            # Set additional request fields
            if hasattr(self.config.request, 'collection'):
                self.config.request.collection = chat_request.collection
                # Set domain from client + collection if client is available
                if hasattr(self.config.request, 'client') and self.config.request.client:
                    self.config.request.domain = self.config.request.client + "_" + chat_request.collection
            if hasattr(self.config.request, 'area'):
                self.config.request.area = chat_request.area
            
            # Get answer from the source factory using the agentic source
            try:
                response = self.source_factory.get_answer(stream=False)
                logger.info(f"Source factory response status: {response.status}")
                
                if response.status == 'error':
                    return self._conversation_error_response(
                        response.error or 'Source factory error', 
                        ERROR_CODE_INTERNAL_SERVER_ERROR,
                        500
                    )
                
            except Exception as e:
                logger.error(f"Source factory error: {str(e)}")
                return self._conversation_error_response(
                    'Failed to process conversation request', 
                    ERROR_CODE_INTERNAL_SERVER_ERROR,
                    500
                )
            
            # Transform response to ChatResponse format
            chat_response = self._transform_to_chat_response(
                response, chat_request.sessionId, format_param
            )
            
            return self._success_response(chat_response.to_dict())

        except Exception as e:
            logger.error(f"Error handling conversation request: {str(e)}")
            return self._conversation_error_response(str(e), ERROR_CODE_INTERNAL_SERVER_ERROR, 500)

    def _success_response(self, data: Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None], Dict[str, Any]]) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Create success response"""
        headers = {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store'
        }
        headers.update(self.default_cors_headers)
        return data, 200, headers
    
    def _conversation_error_response(self, message: str, error_code: str, status_code: int) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Create conversation-specific error response matching OpenAPI ErrorResponse schema"""
        response_data = {
            'error': {
                'code': error_code,
                'message': message
            },
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'requestId': getattr(self.config.request, 'request_id', str(uuid.uuid4()))
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store'
        }
        headers.update(self.default_cors_headers)
        return response_data, status_code, headers
    
    def _get_query_param(self, param_name: str, default: str = None) -> str:
        """Extract query parameter from request URL"""
        try:
            # Get URL from the current request (stored during handler processing)
            if hasattr(self, '_current_url') and self._current_url:
                from urllib.parse import urlparse, parse_qs
                parsed_url = urlparse(self._current_url)
                query_params = parse_qs(parsed_url.query)
                
                if param_name in query_params and query_params[param_name]:
                    return query_params[param_name][0]  # Return first value
                    
        except Exception as e:
            logger.warning(f"Error parsing query parameter {param_name}: {str(e)}")
            
        return default
    
    def _transform_to_chat_response(self, quepasa_response: QuepasaResponse, session_id: str, format_param: str) -> ChatResponse:
        """Transform QuepasaResponse to ChatResponse format
        
        NOTE: This is a helper method that could be moved to a separate component
        if the transformation logic becomes complex enough to warrant separation.
        """
        from ..models.response import ProductSet, ReferenceItem, ActionItem
        
        # Get visitor ID from user_info (includes generated ID if not provided)
        visitor_id = None
        if (hasattr(self.config.request, 'user_info') 
            and hasattr(self.config.request.user_info, 'visitor_id')):
            visitor_id = self.config.request.user_info.visitor_id
        
        # Get request ID for response ID
        response_id = getattr(self.config.request, 'request_id', str(uuid.uuid4()))
        
        # Extract content based on format parameter
        content = ""
        if quepasa_response.data:
            # Handle different data formats (object with attributes vs dictionary)
            if hasattr(quepasa_response.data, 'text'):
                # Standard QuepasaAnswer format
                if format_param == FORMAT_PLAIN_TEXT:
                    content = quepasa_response.data.text
                else:  # default to markdown
                    content = quepasa_response.data.markdown or quepasa_response.data.text
            elif isinstance(quepasa_response.data, dict):
                # Dictionary format (e.g., from agentic source)
                if format_param == FORMAT_PLAIN_TEXT:
                    content = quepasa_response.data.get('text', '')
                else:  # default to markdown
                    content = quepasa_response.data.get('markdown') or quepasa_response.data.get('text', '')
            elif isinstance(quepasa_response.data, str):
                # Simple string format
                content = quepasa_response.data
        
        # Extract components (products and references)
        components = []
        products = []
        
        # Extract products from different data formats
        if quepasa_response.data:
            if hasattr(quepasa_response.data, 'products'):
                products = quepasa_response.data.products
            elif isinstance(quepasa_response.data, dict) and 'products' in quepasa_response.data:
                products = quepasa_response.data.get('products', [])
        
        if products:
            # Create ProductSet component
            product_set = ProductSet(
                products=products,
                comment=None  # Could be extracted from response if available
            )
            components.append(product_set)
        
        # Extract references from different data formats
        references = None
        if quepasa_response.data:
            if hasattr(quepasa_response.data, 'references'):
                references = quepasa_response.data.references
            elif isinstance(quepasa_response.data, dict) and 'references' in quepasa_response.data:
                references = quepasa_response.data.get('references')
        
        if references:
            # Create ReferenceItem components
            for ref_key, ref in references.items():
                reference_item = ReferenceItem(
                    url=ref.url if hasattr(ref, 'url') else ref.get('url'),
                    text=ref.text if hasattr(ref, 'text') else ref.get('text'),
                    title=ref.title if hasattr(ref, 'title') else ref.get('title')
                )
                components.append(reference_item)
        
        # Extract actions (will be implemented later in the scope of https://rezolvetech.atlassian.net/browse/CON-21)
        actions = []
        
        # Create ChatResponse
        chat_response = ChatResponse(
            sessionId=session_id,
            responseId=response_id,
            timestamp=datetime.utcnow().isoformat() + 'Z',
            content=content,
            stream=False,
            visitorId=visitor_id,
            status=ResponseStatus.COMPLETE,
            components=components if components else None,
            actions=actions if actions else None
        )
        
        return chat_response

    def _error_response(self, message: str, status_code: int) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Create error response"""
        headers = {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store'
        }
        headers.update(self.default_cors_headers)
        
        # Create ErrorResponse schema compliant response
        error_response = {
            'error': {
                'code': self._get_error_code(status_code),
                'message': message
            },
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'requestId': getattr(self.config.request, 'request_id', 'unknown')
        }
        
        return error_response, status_code, headers
    
    def _get_error_code(self, status_code: int) -> str:
        """Map HTTP status codes to error codes"""
        error_codes = {
            400: ERROR_CODE_INVALID_REQUEST,
            401: ERROR_CODE_UNAUTHORIZED,
            404: ERROR_CODE_NOT_FOUND,
            500: ERROR_CODE_INTERNAL_SERVER_ERROR
        }
        return error_codes.get(status_code, 'UNKNOWN_ERROR') 

    def _validate_request(self) -> bool:
        """Validate request protocol"""
        if self.config.request.protocol != HTTP_PROTOCOL:
            self.error = "Invalid protocol for HTTP handler"
            return False
        return True
    
    def _set_conversation_request_context(self) -> None:
        """Extract parameters from the conversation request and set into request context with full visitor ID logic"""
        try:
            from ..models.request import UserInfo
            
            # Parse request body to extract user information
            if hasattr(self.config.request, 'body') and self.config.request.body:
                try:
                    chat_request = json.loads(self.config.request.body)
                    session_id = chat_request.get('sessionId')
                    if session_id:
                        # Extract user name from context.userInfo if available
                        user_name = None
                        context = chat_request.get('context', {})
                        if isinstance(context, dict):
                            context_user_info = context.get('userInfo', {})
                            if isinstance(context_user_info, dict):
                                user_name = context_user_info.get('name')
                        
                        # Generate visitorId if not provided
                        visitor_id = chat_request.get('visitorId')
                        if not visitor_id:
                            visitor_id = str(uuid.uuid4())
                            logger.info(f"Generated visitorId: {visitor_id}")
                        
                        # Map conversation request fields to UserInfo fields
                        user_info = UserInfo(
                            id=session_id,  # Use sessionId as the primary ID for history
                            session_id=session_id,
                            visitor_id=visitor_id,
                            name=user_name,  # Extract from context.userInfo.name
                            user_name=user_name,  # Extract from context.userInfo.name
                            chat_id=session_id  # Use sessionId as chat_id for conversation
                        )
                        self.config.request.user_info = user_info
                        logger.info(f"Set user_info with sessionId: {session_id}, visitorId: {visitor_id}")
                except json.JSONDecodeError:
                    logger.warning("Failed to parse conversation request body for user info")
                except Exception as e:
                    logger.warning(f"Error extracting user info from conversation request: {str(e)}")
        except Exception as e:
            logger.error(f"Error in _set_conversation_request_context: {str(e)}") 