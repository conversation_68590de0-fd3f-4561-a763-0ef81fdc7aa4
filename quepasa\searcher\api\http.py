from typing import Dict, <PERSON>, <PERSON><PERSON>, <PERSON>, Generator, Optional
from flask import Response, jsonify
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>og<PERSON>

from .base import BaseAPIHandler
from src.lib.constants import SEARCH_ENDPOINT, ANSWER_ENDPOINT, HISTORY_ENDPOINT
from configuration.main.default import QuepasaConfigurationHub
from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse, QuepasaStreamAnswer

logger = QuepasaLogger().get_instance(__name__)

class HTTPHandler(BaseAPIHandler):
    """HTTP API handler for search requests"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        super().__init__(config)
        self.default_cors_headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST',
            'Access-Control-Allow-Headers': '*',
            'Access-Control-Max-Age': '3600'
        }

    def _handle_request_internal(self, method: str, url: str, headers: Dict[str, str]) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Internal request handler implementation"""
        
        # Handle OPTIONS request
        if method == 'OPTIONS':
            return {}, 204, self.default_cors_headers

        if method != 'POST':
            return self._error_response('Method not allowed', 405)

        # Validate protocol
        if not self._validate_request():
            return self._error_response(self.error, 400)
            
        # Route request to appropriate handler
        try:
            endpoint = self._get_endpoint(url)
            if endpoint == SEARCH_ENDPOINT:
                return self._handle_search()
            elif endpoint == ANSWER_ENDPOINT:
                return self._handle_answer()
            elif endpoint == HISTORY_ENDPOINT:
                return self._handle_history()
            else:
                return self._error_response('Unknown endpoint', 404)
                
        except Exception as e:
            logger.error(f"Error handling request: {str(e)}")
            return self._error_response('Internal server error', 500)

    def _handle_search(self) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Handle search request"""
        try:
            return self._success_response(self.source_factory.search())

        except Exception as e:
            logger.error(f"Error handling search request: {str(e)}")
            return self._error_response(str(e), 500)

    def _handle_answer(self) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Handle answer request"""
        try:
            return self._success_response(self.source_factory.get_answer(stream=False))

        except Exception as e:
            logger.error(f"Error handling answer request: {str(e)}")
            return self._error_response(str(e), 500)

    def _handle_history(self) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Handle history request"""
        history_list = []
        if (
            hasattr(self.config.request, 'user_info')
            and hasattr(self.config.request.user_info, 'id')
        ):
            user_id = self.config.request.user_info.id
            if user_id:
                try:
                    history_list = self.history_manager.get_conversation_history(user_id)
                    
                except Exception as e:
                    logger.error(f"Error loading history: {str(e)}")

        return self._success_response({
            'data': history_list,
            'status': 'OK'
        })

    def _success_response(self, data: Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Create success response"""
        headers = {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store'
        }
        headers.update(self.default_cors_headers)
        return data, 200, headers

    def _error_response(self, message: str, status_code: int) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Create error response"""
        headers = {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store'
        }
        headers.update(self.default_cors_headers)
        return {'error': message}, status_code, headers 

    def _validate_request(self) -> bool:
        """Validate request protocol"""
        if self.config.request.protocol != "http":
            self.error = "Invalid protocol for HTTP handler"
            return False
        return True 