#!/usr/bin/env python3
"""
Configuration Inheritance Tests

Test the hierarchical configuration system with base + variant configs.
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from configuration.yaml_loader import YAMLConfigurationLoader, ConfigurationValidationError, load_client_config


def test_inheritance_system():
    """Test the configuration inheritance system."""
    print("🔗 Testing Configuration Inheritance System")
    print("=" * 50)
    
    tests_passed = 0
    tests_failed = 0
    
    def test_result(test_name: str, passed: bool, details=""):
        nonlocal tests_passed, tests_failed
        if passed:
            print(f"✅ {test_name}")
            tests_passed += 1
        else:
            print(f"❌ {test_name}")
            if details:
                print(f"   {details}")
            tests_failed += 1
    
    # Test 1: Base configuration exists
    try:
        base_config = load_client_config("shoeby_base")
        test_result("Base configuration loads", True)
    except Exception as e:
        test_result("Base configuration loads", False, str(e))
        return False
    
    # Test 2: Base configuration has all required fields (simplified structure)
    required_fields = [
        'client_code', 'query_expansions', 'language',
        'search', 'user_history', 'llm_models', 'store_info'
    ]
    
    missing_fields = [field for field in required_fields if field not in base_config]
    test_result("Base config has all required fields", len(missing_fields) == 0,
               f"Missing: {missing_fields}" if missing_fields else "")
    
    # Test 3: Variants inherit from base
    variants = ['rezolve_shoeby_openai', 'rezolve_shoeby_bge', 'rezolve_shoeby_bge_icl', 'rezolve_shoeby_qwen3']
    
    for variant in variants:
        try:
            variant_config = load_client_config(variant)
            
            # Test inheritance of base fields
            test_result(f"{variant} - Store info inherited", 
                       variant_config.get('store_info', {}).get('name') == base_config.get('store_info', {}).get('name'))
            test_result(f"{variant} - Prompts inherited", True, "Prompts now handled in Python code")
            
            # Test variant-specific overrides
            test_result(f"{variant} - Client code overridden", variant_config.get('client_code') == variant)
            test_result(f"{variant} - Has embedding config", 'embedding' in variant_config)
            
            if 'embedding' in variant_config:
                embedding = variant_config['embedding']
                test_result(f"{variant} - Has index suffix", 'index_suffix' in embedding)
                test_result(f"{variant} - Has search model", 'search_model' in embedding)
                
        except Exception as e:
            test_result(f"{variant} - Loads successfully", False, str(e))
    
    # Test 4: Variant differentiation
    openai_config = load_client_config('rezolve_shoeby_openai')
    bge_config = load_client_config('rezolve_shoeby_bge')
    
    test_result("OpenAI vs BGE - Different index suffixes",
               openai_config['embedding']['index_suffix'] != bge_config['embedding']['index_suffix'])
    test_result("OpenAI vs BGE - Different providers",
               openai_config['embedding']['search_model']['provider'] != bge_config['embedding']['search_model']['provider'])
    test_result("OpenAI vs BGE - Same base content",
               openai_config['store_info']['name'] == bge_config['store_info']['name'])
    
    # Test 5: Deep merge functionality
    test_result("Deep merge - Base fields preserved in variants",
               len(openai_config['query_expansions']) == len(base_config['query_expansions']))
    test_result("Deep merge - Variant fields added",
               'embedding' in openai_config and 'embedding' not in base_config)
    
    print("\n" + "=" * 50)
    print(f"📊 Inheritance Test Results:")
    print(f"   ✅ Passed: {tests_passed}")
    print(f"   ❌ Failed: {tests_failed}")
    print(f"   📈 Total: {tests_passed + tests_failed}")
    
    if tests_failed == 0:
        print("\n🎉 ALL INHERITANCE TESTS PASSED!")
        print("✨ Hierarchical configuration system is working correctly!")
        return True
    else:
        print(f"\n⚠️  {tests_failed} tests failed.")
        return False


def test_configuration_variants():
    """Test all configuration variants comprehensively."""
    print("\n🧪 Testing All Configuration Variants")
    print("=" * 50)
    
    variants = {
        'rezolve_shoeby_openai': 'openai',
        'rezolve_shoeby_bge': 'bge', 
        'rezolve_shoeby_bge_icl': 'bge-icl',
        'rezolve_shoeby_qwen3': 'qwen3'
    }
    
    for variant_name, expected_suffix in variants.items():
        try:
            config = load_client_config(variant_name)
            
            print(f"\n📋 {variant_name}:")
            print(f"   Client Code: {config['client_code']}")
            print(f"   Index Suffix: {config['embedding']['index_suffix']}")
            print(f"   Search Provider: {config['embedding']['search_model']['provider']}")
            print(f"   Search Model: {config['embedding']['search_model']['model']}")
            print(f"   Store Name: {config['store_info']['name']}")
            print(f"   Has Prompts: False")
            print(f"   Note: Prompts now managed in Python configuration")
            
            assert config['client_code'] == variant_name
            assert config['embedding']['index_suffix'] == expected_suffix
            assert config['store_info']['name'] == 'Shoeby'
            # Prompts are now handled in Python code, not YAML
            
            print("   ✅ All assertions passed")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return False
    
    print(f"\n🎉 All {len(variants)} configuration variants validated successfully!")
    return True


if __name__ == "__main__":
    success1 = test_inheritance_system()
    success2 = test_configuration_variants()
    
    sys.exit(0 if (success1 and success2) else 1)