import os
import io
import gc
from typing import Dict, Any, List
import pdfplumber
from langchain_community.document_loaders import PyMuPDFLoader
import fitz  # PyMuPDF
from ...utils import split_text
from .base import BaseProcessor

BAD_TITLES = [
    "Use these links to rapidly review the document",
]

def length_of_chunks(chunks: List[Dict[str, Any]]) -> int:
    return sum([len(chunk['text']) for chunk in chunks])

class PDFProcessor(BaseProcessor):
    """Processor for PDF documents"""

    def _process_impl(self, content: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract text from PDF content
        
        Args:
            content: PDF file content
            meta: Metadata about the PDF
            
        Returns:
            Dictionary containing extracted text and title
        """
        # Validate metadata
        error = self.validate_meta(meta, ['filename'])
        if error:
            raise ValueError(error)

        # Save content to temporary file for processing
        temp_file = io.BytesIO(content)
        title = None
        
        try:
            # Try to extract title from metadata first
            try:
                # Try PyMuPDF first as it has better metadata support
                pdf_doc = fitz.open(stream=content, filetype="pdf")
                metadata = pdf_doc.metadata
                if metadata.get('title') and metadata['title'].strip():
                    title = metadata['title'].strip()
                pdf_doc.close()
                del pdf_doc
                gc.collect()

            except Exception as e:
                self.logger.warning(f"Failed to extract metadata from {meta['filename']} using fitz: {str(e)}", exc_info=True)
                
            # Try several different methods to extract text from the PDF
            # 1. pdfplumber
            chunks_pdfplumber = []
            try:
                with pdfplumber.open(temp_file) as pdf:
                    # If no title in metadata, try pdfplumber metadata
                    if (
                        (not title or title in BAD_TITLES)
                        and hasattr(pdf, 'metadata') and pdf.metadata
                        and pdf.metadata.get('Title') and pdf.metadata['Title'].strip()
                    ):
                        title = pdf.metadata['Title'].strip()
                
                    # If still no title, try first line of first page
                    if (
                        (not title or title in BAD_TITLES)
                        and hasattr(pdf, 'pages') and pdf.pages
                        and len(pdf.pages) > 0
                    ):
                        first_page = pdf.pages[0]
                        first_text = first_page.extract_text()
                        if first_text and first_text.strip():
                            # Use first line as title if it's not too long
                            lines = first_text.strip().split('\n')
                            if lines and len(lines[0].split()) <= 20:
                                title = lines[0].strip()

                    # Extract chunks
                    for page_num, page in enumerate(pdf.pages, 0):
                        text = page.extract_text()
                        if text and text.strip():
                            raw_chunks = split_text(text.strip())
                            for chunk in raw_chunks:
                                chunks_pdfplumber.append({
                                    'text': chunk,
                                    'position': f"page {page_num}"
                                })
                    del pdf
                    gc.collect()

            except Exception as e:
                self.logger.warning(f"Failed to extract text from {meta['filename']} using pdfplumber: {str(e)}", exc_info=True)

            # 2. PyMuPDF
            chunks_pymupdf = []
            if len(content) < 16 * 1024 * 1024:  # 16MB
                pdf_reader = None
                try:
                    # Save to temporary file since PyMuPDFLoader requires a file path
                    temp_path = '/tmp/temp.pdf'
                    with open(temp_path, 'wb') as f:
                        f.write(content)
                    
                    pdf_reader = PyMuPDFLoader(temp_path)
                    for doc in pdf_reader.load():
                        try:
                            if (
                                doc 
                                and hasattr(doc, 'page_content') and doc.page_content and doc.page_content.strip()
                                and hasattr(doc, 'metadata') and doc.metadata
                            ):
                                raw_chunks = split_text(doc.page_content.strip())
                                for chunk in raw_chunks:
                                    chunks_pymupdf.append({
                                        'text': chunk,
                                        'position': f"page {doc.metadata.get('page', 1)}"
                                    })

                        except Exception as e:
                            self.logger.warning(f"Failed to process page {doc.metadata.get('page', 1)}: {str(e)}", exc_info=True)

                except Exception as e:
                    self.logger.warning(f"Failed to extract text from {meta['filename']} using pymupdf: {str(e)}", exc_info=True)

                finally:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)

                    del pdf_reader
                    gc.collect()

            chunks_pymupdf_length = length_of_chunks(chunks_pymupdf)
            chunks_pdfplumber_length = length_of_chunks(chunks_pdfplumber)
            self.logger.info(f"Chunks pymupdf length: {chunks_pymupdf_length}")
            self.logger.info(f"Chunks pdfplumber length: {chunks_pdfplumber_length}")

            return {
                'chunks': chunks_pymupdf if chunks_pymupdf_length >= chunks_pdfplumber_length else chunks_pdfplumber,
                'title': title,
                'filename': meta['filename']
            }
            
        except Exception as e:
            self.logger.error(f"Failed to process PDF: {str(e)}", exc_info=True)
            raise
        finally:
            temp_file.close()