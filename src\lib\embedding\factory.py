from __future__ import annotations

from typing import Dict, Type, Union, Any

from .base import BaseEmbedding
from .providers import EmbeddingProvider

class EmbeddingFactory:
    """Factory class for creating embedding providers."""
    
    _providers: Dict[EmbeddingProvider, Type[BaseEmbedding]] = {}

    @classmethod
    def get_embedding(cls, provider: Union[str, EmbeddingProvider], **kwargs: Any) -> BaseEmbedding:
        """Get an embedding instance for the specified provider.
        
        Args:
            provider: The embedding provider (string or enum)
            **kwargs: Additional arguments to pass to the embedding constructor
            
        Returns:
            An instance of the specified embedding provider
            
        Raises:
            ValueError: If the provider is not supported
        """
        if isinstance(provider, str):
            provider = EmbeddingProvider.from_str(provider)

        if provider not in cls._providers:
            if provider == EmbeddingProvider.OPENAI:
                from .openai import OpenAIEmbedding
                cls._providers[provider] = OpenAIEmbedding
                
            elif provider == EmbeddingProvider.NEBIUS:
                from .nebius import NebiusEmbedding
                cls._providers[provider] = NebiusEmbedding

            elif provider == EmbeddingProvider.MWS:
                from .mws import MWSEmbedding
                cls._providers[provider] = MWSEmbedding

            elif provider == EmbeddingProvider.ASKROBOT:
                from .askrobot import AskRobotEmbedding
                cls._providers[provider] = AskRobotEmbedding

            elif provider == EmbeddingProvider.SBERT:
                from .sbert import SBERTEmbedding
                cls._providers[provider] = SBERTEmbedding

            elif provider == EmbeddingProvider.HUGGINGFACE:
                from .huggingface import HuggingfaceEmbedding
                cls._providers[provider] = HuggingfaceEmbedding

            elif provider == EmbeddingProvider.REPLICATE:
                from .replicate import ReplicateEmbedding
                cls._providers[provider] = ReplicateEmbedding

            elif provider == EmbeddingProvider.PRIVATE:
                from .private import PrivateEmbedding
                cls._providers[provider] = PrivateEmbedding

            else:
                raise ValueError(f"Unknown embedding provider: {provider}.")
        
        return cls._providers[provider](**kwargs) 