apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
  namespace: quepasa
spec:
  replicas: 2
  selector:
    matchLabels:
      app: api
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: api
    spec:
      containers:
      - name: api
        image: qpreg.azurecr.io/quepasa/api:v1.0.184
        imagePullPolicy: Always
        command: ["python", "-m", "uvicorn", "quepasa.api.app:app", "--host", "0.0.0.0", "--port", "8080"]
        env:
        - name: QUEPASA_EXECUTION_MODE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_EXECUTION_MODE
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: KUBERNETES_NAMESPACE
        - name: QUEPASA_IMAGE_VERSION
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_IMAGE_VERSION
        - name: MINIO_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_HOST
        - name: MINIO_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_PORT
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_BUCKET_NAME
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_BUCKET_NAME
        - name: MINIO_DEBUG
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_DEBUG
        - name: ELASTICSEARCH_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: ELASTICSEARCH_HOST
        - name: ELASTICSEARCH_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: ELASTICSEARCH_PORT
        - name: ELASTICSEARCH_USERNAME
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ELASTICSEARCH_USERNAME
        - name: ELASTICSEARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ELASTICSEARCH_PASSWORD
        - name: CELERY_BROKER_URL
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: CELERY_BROKER_URL
        - name: CELERY_RESULT_BACKEND
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: CELERY_RESULT_BACKEND
        - name: QUEPASA_CRAWLER_IMAGE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_CRAWLER_IMAGE
        - name: QUEPASA_DATA_PROCESSOR_IMAGE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_DATA_PROCESSOR_IMAGE
        - name: QUEPASA_INDEXER_IMAGE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_INDEXER_IMAGE
        - name: REPLICATE_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: REPLICATE_API_KEY
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: OPENAI_API_KEY
        - name: NEBIUS_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: NEBIUS_API_KEY
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ANTHROPIC_API_KEY
        - name: HUGGINGFACE_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: HUGGINGFACE_API_KEY
        - name: PRIVATE_EMBEDDING_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: PRIVATE_EMBEDDING_ENDPOINT
        - name: PRIVATE_EMBEDDING_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: PRIVATE_EMBEDDING_AUTH_TOKEN
        - name: PYTHONPATH
          value: /app
        envFrom:
        - configMapRef:
            name: shared-config
        - secretRef:
            name: shared-secrets
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: api-cache
          mountPath: /app/cache
        - name: config-volume
          mountPath: /app/configuration/main
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
      volumes:
      - name: api-cache
        emptyDir:
          sizeLimit: 25Gi
      - name: config-volume
        configMap:
          name: quepasa-config 