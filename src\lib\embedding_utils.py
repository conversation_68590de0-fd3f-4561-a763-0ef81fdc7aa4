from typing import Optional
import time
from functools import wraps
import random
from src.lib.embedding.factory import EmbeddingFactory

def retry_with_exponential_backoff(max_retries: int = 10):
    """Decorator that implements exponential backoff retry logic
    
    Args:
        max_retries: Maximum number of retries before giving up
        
    Returns:
        Decorated function with retry logic
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retry = 0
            while retry < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if retry == max_retries - 1:  # Last retry
                        raise e
                    
                    # Calculate sleep time with exponential backoff and jitter
                    sleep_time = (2 ** retry) + random.uniform(0, 1)
                    time.sleep(sleep_time)
                    retry += 1
            return None
        return wrapper
    return decorator

@retry_with_exponential_backoff()
def get_embedding(provider: str, model_version: str, text: str) -> Optional[list]:
    """Get an embedding from the specified provider.
    
    Args:
        provider: The embedding provider to use (e.g., 'openai', 'askrobot')
        model_version: Model version to use (e.g., 'gpt-4-turbo-preview')
        text: Text to get embedding for
        
    Returns:
        List of embedding values if successful, None otherwise
    """
    embedding = EmbeddingFactory.get_embedding(provider)
    return embedding.get_embedding(model_version, text)

@retry_with_exponential_backoff()
def get_cached_embedding(provider: str, model_version: str, text: str) -> Optional[list]:
    """Get a cached embedding from the specified provider.
    
    Args:
        provider: The embedding provider to use (e.g., 'openai', 'askrobot')
        model_version: Model version to use (e.g., 'gpt-4-turbo-preview')
        text: Text to get embedding for
        
    Returns:
        List of embedding values from cache or computed fresh
    """
    embedding = EmbeddingFactory.get_embedding(provider)
    return embedding.get_cached_embedding(model_version, text) 