import json
import time
from typing import Dict, Any, Optional, Type, Union, Generator

from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse, QuepasaStreamAnswer, Reference, QuepasaAnswer
from ..models.answer import AnswerFormatter
from ..history.manager import HistoryManager
from .base import BaseSource, STATUS_SUCCESS, STATUS_ERROR
from .rag import RAGSource
from .web import WebSource
from .gpt import GPTSource
from .meta import MetaSource
from .spd import SPDSource
from configuration.main.default import QuepasaConfigurationHub
from src.lib.logger import QuepasaLogger
from src.lib.constants import (
    SOURCE_DOCUMENTS,
    SOURCE_DIALOGS,
    SOURCE_WEB,
    SOURCE_GPT,
    SOURCE_WATERFALL,
    SOURCE_AGENTIC,
    ANSWER_TYPE_STRICT,
    SOURCE_META_SEARCH,
    SOURCE_SPD_SEARCH
)
from src.lib.utils import ALLOWED_LANGUAGES
from src.lib.llm_utils import get_llm_tools_answer, get_streaming_tools_chunks
from ..utils import (
    LANGUAGE_PLACEHOLDER,
    QUESTION_PLACEHOLDER,
    replace_placeholders_in_prompt_messages,
    get_llm_prompt_template,
    get_llm_model_name,
    get_prepared_response_dict,
    get_think_and_answer_from_content
)

logger = QuepasaLogger().get_instance(__name__)

class SourceFactory:
    """Factory for managing different search sources"""

    def __init__(self, config: QuepasaConfigurationHub):
        self.config = config
        self._sources: Dict[str, Type[BaseSource]] = {
            SOURCE_DOCUMENTS: RAGSource,
            SOURCE_META_SEARCH: MetaSource,
            SOURCE_SPD_SEARCH: SPDSource,
            SOURCE_DIALOGS: RAGSource,
            SOURCE_WEB: WebSource,
            SOURCE_GPT: GPTSource,
        }

    def get_source(self, source_type: str) -> Optional[BaseSource]:
        """Get source instance by type"""
        source_class = self._sources.get(source_type)
        if source_class:
            return source_class()
        return None
    
    def _get_source_type_from_config(self, source_config: Any) -> Optional[str]:
        """Extract source type from config"""
        if isinstance(source_config, str):
            return source_config
        elif isinstance(source_config, dict):
            return source_config.get("source")
        return None
    
    def _create_source_request(self, source_config: Any) -> QuepasaRequest:
        """Create new request with source config"""
        request_dict = self.config.request.to_dict()
        
        if isinstance(source_config, dict):
            request_dict.update(source_config)
            
        return QuepasaRequest.from_dict(request_dict) 

    def search(self) -> QuepasaResponse:
        """
        Execute search using appropriate source
        
        Returns:
            QuepasaResponse: Search response
        """
        try:
            source_type = self.config.request.source or self.config.get_primary_search_source()
            if source_type == SOURCE_WATERFALL:
                return self._handle_waterfall_search()
                
            source = self.get_source(source_type)
            if not source:
                logger.error(f"Unknown source type: {source_type}")
                return QuepasaResponse(
                    status=STATUS_ERROR,
                    error=f"Unknown source type: {source_type}"
                )
                
            return source.search(self.config.request)
            
        except Exception as e:
            logger.error(f"Search error: {str(e)}")
            return QuepasaResponse(
                status=STATUS_ERROR,
                error=str(e)
            )

    def _handle_waterfall_search(self) -> QuepasaResponse:
        """Handle waterfall search across multiple sources"""
        waterfall_config = self.config.get_waterfall_config()
        if not waterfall_config:
            return QuepasaResponse(
                status=STATUS_ERROR,
                error="No waterfall configuration found"
            )

        results = []
        for source_config in waterfall_config:
            source_type = self._get_source_type_from_config(source_config)
            if not source_type:
                continue

            source = self.get_source(source_type)
            if not source:
                continue

            # Create new request with source config
            source_request = self._create_source_request(source_config)
            source_request.waterfall = results.copy()
            
            response = source.search(source_request)
            if response.status == STATUS_SUCCESS:
                return response

        return QuepasaResponse(
            status=STATUS_ERROR,
            error="No source produced a response"
        )

    def get_answer(self, stream: bool = False) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """
        Get answer using appropriate source
        
        Parameters:
            stream (bool): Whether to stream the answer
            
        Returns:
            Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]: Answer response or generator of QuepasaStreamAnswer objects
        """
        logger.info("Getting answer")
        try:
            source_type = self.config.request.source or self.config.get_primary_search_source()
            logger.info(f"Source type: {source_type}")
            if source_type == SOURCE_WATERFALL:
                return self._handle_waterfall_answer(stream)
            
            if source_type == SOURCE_AGENTIC:
                return self._handle_agentic_answer(stream)
                
            source = self.get_source(source_type)
            if not source:
                logger.error(f"Unknown source type: {source_type}")
                return QuepasaResponse(
                    status=STATUS_ERROR, 
                    error=f"Unknown source type: {source_type}"
                )
                
            return source.get_answer(self.config.request, stream)
            
        except Exception as e:
            logger.error(f"Answer error: {str(e)}")
            return QuepasaResponse(
                status=STATUS_ERROR,
                error=str(e)
            )

    def _handle_waterfall_answer(self, stream: bool = False) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """
        Handle waterfall answer generation across multiple sources
        
        Parameters:
            stream (bool): Whether to stream the answer
            
        Returns:
            Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]: Answer response or generator of QuepasaStreamAnswer objects   
        """
        logger.info("Handling waterfall answer")
        waterfall_config = self.config.get_waterfall_config()
        if not waterfall_config:
            return QuepasaResponse(
                status=STATUS_ERROR,
                error="No waterfall configuration found"
            )

        # Handle streaming responses
        if stream:            
            # Create a wrapper generator to collect final response while streaming
            def stream_wrapper():
                last_chunk = None  # Initialize last_chunk variable
                waterfall_steps = []
                
                for source_config in waterfall_config:
                    logger.info(f"Source config: {source_config}")

                    source_type = self._get_source_type_from_config(source_config)
                    if not source_type:
                        continue

                    source = self.get_source(source_type)
                    if not source:
                        continue

                    # Create new request with source config
                    source_request = self._create_source_request(source_config)
                    source_request.waterfall = waterfall_steps.copy()
                                
                    # Add non-streaming response to results
                    response_generator = source.get_answer(source_request, stream=True)
                    
                    # Stream all chunks to the client
                    for chunk in response_generator:
                        last_chunk = chunk
                        yield chunk

                    logger.info(f"Last chunk: {last_chunk}")
                        
                    # After streaming completes, evaluate if we should continue waterfall
                    if last_chunk:
                        waterfall_steps.append(self.config.get_waterfall_step_metadata(source_request, last_chunk))
                        
                        # Check if this is a satisfactory answer
                        answer_type = getattr(last_chunk, 'type', None)
                        logger.info(f"Answer type: {answer_type}")

                        if answer_type and answer_type.startswith(f"{ANSWER_TYPE_STRICT}."):
                            # This source gave a strict answer, stop waterfall
                            return

            # Return the wrapper generator
            return stream_wrapper()

        last_response = None  # Initialize last_response variable
        waterfall_steps = []

        for source_config in waterfall_config:
            logger.info(f"Source config: {source_config}")

            source_type = self._get_source_type_from_config(source_config)
            if not source_type:
                continue

            source = self.get_source(source_type)
            if not source:
                continue

            # Create new request with source config
            source_request = self._create_source_request(source_config)
            source_request.waterfall = waterfall_steps.copy()
                        
            # Add non-streaming response to results
            response = source.get_answer(source_request, stream=False)
            last_response = response  # Update last_response
            
            if response.status == STATUS_SUCCESS and hasattr(response, 'data'):
                waterfall_steps.append(self.config.get_waterfall_step_metadata(source_request, response.data))

                # Check if this is a satisfactory answer
                answer_type = response.data.get('type', None)
                logger.info(f"Answer type: {answer_type}")

                if answer_type and answer_type.startswith(f"{ANSWER_TYPE_STRICT}."):
                    # This source gave a strict answer, stop waterfall
                    return response

        # Return last response if we got here
        return last_response if last_response else QuepasaResponse(
            status=STATUS_ERROR,
            error="No source produced a response"
        )
    
    def _get_agentic_response_dict(self, content: str, source_hash: dict) -> Dict:
        config = self.config
        request = config.request
        source = request.source

        formatter = AnswerFormatter()

        provider, model = get_llm_model_name(request.agent_llm) if request.agent_llm else config.get_llm_model_name(source)
        llm_params = config.get_llm_parameters(source)
        
        think, answer = get_think_and_answer_from_content(content)
        answer, source_hash = config.process_answer(source, False, answer, source_hash)

        # Format answer
        formatted_answer = formatter.format_answer(request, answer, source_hash)
        
        # Prepare response
        response_dict = get_prepared_response_dict(
            request=request,
            source=source,
            formatted_answer=formatted_answer,
            prompt_data=None,
            think=think,
            provider=provider,
            model=model,
            llm_params=llm_params
        )

        # Process and return
        processed_response = config.process_response(response_dict)
        response_dict = processed_response.to_dict() if hasattr(processed_response, 'to_dict') else processed_response
        return response_dict
    
    def _get_agentic_function_answer(self, tool_call, max_source_index: int, source_hash: dict):
        config = self.config

        # Handle both dictionary and object formats
        function_name = tool_call['function']['name']
        function_arguments = tool_call['function']['arguments']

        # Get answer
        function_answer = ""
        function_references = {}

        try:
            arguments = json.loads(function_arguments)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse tool arguments: {e}")
            return function_name, function_answer, function_references, max_source_index, source_hash

        try:
            function_config = config.get_agentic_function(function_name, arguments)

        except Exception as e:
            logger.error(f"Failed process agentic function callback: {e}")
            return function_name, function_answer, function_references, max_source_index, source_hash

        if isinstance(function_config, str):
            # This is the direct answer
            function_answer = function_config

        else:
            # This is QuePasa request
            source_type = self._get_source_type_from_config(function_config)
            if not source_type:
                return function_name, function_answer, function_references, max_source_index, source_hash

            source = self.get_source(source_type)
            if not source:
                return function_name, function_answer, function_references, max_source_index, source_hash

            # Create new request with source config
            function_request = self._create_source_request(function_config)
            function_request.history = []
                        
            # Add non-streaming response to results
            response = source.get_answer(function_request, stream=False)
            logger.info(f"RAG function response date: {response.data}")

            # Reorder reference indexes
            function_answer = response.data['text']

            index_replacement_hash = {}
            for index in response.data['references']:
                index_int = int(index)  # Always convert to int
                max_source_index += 1
                index_replacement_hash[index_int] = max_source_index

            logger.info(f"Index reference replacement {index_replacement_hash}")

            # Convert all keys to integers for proper sorting
            ordered_indexes = sorted(list(index_replacement_hash.keys()))
            logger.info(f"Ordered reference indexes {ordered_indexes}")

            # Reverse order to prevent override of indexes
            for index in sorted(ordered_indexes, reverse=True):
                new_index = index_replacement_hash[index]
                if index != new_index:
                    for replace_ref in [f"[{index}]", f"[#{index}]"]:
                        function_answer = function_answer.replace(replace_ref, f"[#{new_index}]")

            sources = ""
            for index in ordered_indexes:
                source_index = index_replacement_hash[index]
                reference = response.data['references'][index]

                sources += config.format_context_chunk(source, source_index, reference) + "\n\n"
                source_hash[source_index] = reference
                function_references[source_index] = reference.to_dict()

            if sources:
                function_answer += f"\n\nSources:\n{sources}"

        return function_name, function_answer, function_references, max_source_index, source_hash
    
    def _handle_agentic_answer(self, stream: bool = False) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """
        Handle agentic answer generation across multiple tools
        
        Parameters:
            stream (bool): Whether to stream the answer
            
        Returns:
            Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]: Answer response or generator of QuepasaStreamAnswer objects   
        """
        logger.info("Handling agentic answer")

        config = self.config
        request = config.request
        source = request.source

        provider, model = get_llm_model_name(request.agent_llm) if request.agent_llm else config.get_llm_model_name(source)
        logger.info(f"Retrieved Agentic LLM configuration: provider: {provider}, model: {model}, request.answer_llm: {request.answer_llm}")

        llm_params = config.get_llm_parameters(source)
        logger.info(f"Agentic LLM params {llm_params = }")

        llm_prompt_template = config.get_llm_prompt_template(source)
        logger.info(f"Agentic prompt template {llm_prompt_template = }")

        tools = config.get_agentic_tools()
        logger.info(f"Agentic tools {tools = }")

        if not llm_prompt_template or not tools:
            return QuepasaResponse(
                status=STATUS_ERROR,
                error="No agentic configuration found"
            )

        history_manager = HistoryManager(config)

        # Add authenticated user ID to request
        user_id = None
        if (
            hasattr(request, 'user_info')
            and hasattr(request.user_info, 'id')
        ):
            user_id = request.user_info.id


        language_name = ALLOWED_LANGUAGES[config.get_fallback_language()]
        if (
            request.classification
            and hasattr(request.classification, 'language_code')
            and request.classification.language_code in ALLOWED_LANGUAGES
        ):
            language_name = ALLOWED_LANGUAGES[request.classification.language_code]

        messages = replace_placeholders_in_prompt_messages(
            get_llm_prompt_template(llm_prompt_template),
            {
                LANGUAGE_PLACEHOLDER: language_name,
                QUESTION_PLACEHOLDER: request.question
            }
        )

        source_hash = {}
        max_source_index = 0

        # Add history
        if request.history:
            # Append befor last message
            messages[-1:-1] = [
                {
                    'role': history_item.role,
                    'content': history_item.content,
                    'tool_calls': history_item.tool_calls,
                    'tool_call_id': history_item.tool_call_id,
                    'name': history_item.name,
                }
                for history_item in request.history
            ]

            for history_item in request.history:
                if (
                    history_item.role == "tool"
                    and history_item.references
                    and len(history_item.references) > 0
                ):
                    for index_str, reference_dict in history_item.references.items():
                        index = int(index_str)  # Always convert to int
                        source_hash[index] = Reference(**reference_dict)
                        if index > max_source_index:
                            max_source_index = index
                        

        logger.info(f"Agentic source_hash: {source_hash}")
        logger.info(f"Agentic max_source_index: {max_source_index}")

        # Handle streaming responses
        if stream:
            def stream_wrapper():
                nonlocal source_hash, max_source_index

                for _ in range(5):
                    logger.info(f"Agentic prompt step: {messages}")
                    
                    # Use streaming tools answer
                    last_content = ""
                    last_answer = ""
                    last_tool_calls = []
                    
                    for content, tool_calls in get_streaming_tools_chunks(provider, model, messages, tools):
                        # Stream content chunks as they come
                        if content and content != last_content:
                            # Process the content
                            response_dict = self._get_agentic_response_dict(content, source_hash)

                            # Stream the processed content chunk
                            yield QuepasaStreamAnswer(
                                loading=True,
                                streaming=True,
                                created_at=int(time.time()),
                                **response_dict
                            )
                            last_content = content
                        
                        # Update tool calls
                        last_tool_calls = tool_calls

                    logger.info(f"Agentic content: {last_content}")
                    logger.info(f"Agentic tool_calls: {last_tool_calls}")

                    # No tool calls, process as final answer
                    if last_content:
                        _, last_answer = get_think_and_answer_from_content(last_content)
                        if last_answer:
                            messages.append({
                                'role': "assistant",
                                'content': last_answer
                            })

                        if not last_tool_calls:
                            # Process answer and exit
                            response_dict = self._get_agentic_response_dict(last_content, source_hash)

                            # Stream the final answer
                            yield QuepasaStreamAnswer(
                                loading=False,
                                streaming=False,
                                created_at=int(time.time()),
                                **response_dict
                            )
                            return
                    
                    # After streaming is complete, check if we have tool calls
                    if last_tool_calls:
                        if last_answer:
                            history_manager.save_message(
                                user_id=user_id,
                                role="assistant",
                                content=last_answer
                            )

                        if len(last_tool_calls) > 0:
                            assistant_tools_content = "\n".join([str(tool_call['function']) for tool_call in last_tool_calls])
                            messages.append({
                                "role": "assistant",
                                "content": assistant_tools_content,
                                "tool_calls": last_tool_calls
                            })

                            history_manager.save_message(
                                user_id=user_id,
                                role="assistant",
                                content=assistant_tools_content,
                                tool_calls=last_tool_calls
                            )

                        for tool_call in last_tool_calls:
                            try:
                                # Handle both dictionary and object formats
                                function_name = tool_call['function']['name']
                                function_arguments = {}
                                try:
                                    function_arguments = json.loads(tool_call['function']['arguments'])

                                except json.JSONDecodeError as e:
                                    pass
                                                        
                                function_thinking = config.get_agentic_function_thinking(function_name, function_arguments)
                                logger.info(f"Agentic function thinking: {function_thinking}")

                                if function_thinking:
                                    yield QuepasaStreamAnswer(
                                        think=function_thinking,
                                        loading=True,
                                        streaming=True,
                                        created_at=int(time.time())
                                    )

                                function_name, function_answer, function_references, max_source_index, source_hash = self._get_agentic_function_answer(tool_call, max_source_index, source_hash)

                                messages.append({
                                    "role": "tool",
                                    "content": function_answer,
                                    "tool_call_id": tool_call['id'],
                                    "name": function_name,
                                })

                                history_manager.save_message(
                                    user_id=user_id,
                                    role="tool",
                                    content=function_answer,
                                    name=function_name,
                                    tool_call_id=tool_call['id'],
                                    references=function_references
                                )
                            
                            except Exception as e:
                                logger.error(f"Failed to process tool {tool_call}: {e}")                        

                # If we reach here, we've exceeded the maximum iterations
                yield QuepasaStreamAnswer(
                    type=STATUS_ERROR,
                    text="Only 5 steps for agentic loop are allowed",
                    loading=False,
                    streaming=False,
                    created_at=int(time.time())
                )

            # Return the wrapper generator
            return stream_wrapper()

        for _ in range(5):
            logger.info(f"Agentic prompt step: {messages}")
            content, tool_calls = get_llm_tools_answer(provider, model, messages, tools)
            logger.info(f"Agentic content: {content}")
            logger.info(f"Agentic tool_calls: {tool_calls}")

            answer = ""
            if content:
                _, answer = get_think_and_answer_from_content(content)
                if answer:
                    messages.append({
                        'role': "assistant",
                        'content': answer
                    })

                if not tool_calls:
                    # Process answer and exit
                    response_dict = self._get_agentic_response_dict(content, source_hash)
                    return QuepasaResponse(
                        status=STATUS_SUCCESS,
                        data=response_dict
                    )
                
            if tool_calls:
                if answer:
                    history_manager.save_message(
                        user_id=user_id,
                        role="assistant",
                        content=answer
                    )

                if len(tool_calls) > 0:
                    assistant_tools_content = "\n".join([str(tool_call['function']) for tool_call in tool_calls])
                    messages.append({
                        "role": "assistant",
                        "content": assistant_tools_content,
                        "tool_calls": tool_calls
                    })

                    history_manager.save_message(
                        user_id=user_id,
                        role="assistant",
                        content=assistant_tools_content,
                        tool_calls=tool_calls
                    )

                for tool_call in tool_calls:
                    try:
                        function_name, function_answer, function_references, max_source_index, source_hash = self._get_agentic_function_answer(tool_call, max_source_index, source_hash)

                        messages.append({
                            "role": "tool",
                            "content": function_answer,
                            "tool_call_id": tool_call['id'],
                            "name": function_name,
                        })

                        history_manager.save_message(
                            user_id=user_id,
                            role="tool",
                            content=function_answer,
                            name=function_name,
                            tool_call_id=tool_call['id'],
                            references=function_references
                        )
                    
                    except Exception as e:
                        logger.error(f"Failed to process tool {tool_call}: {e}")

        return QuepasaResponse(
            status=STATUS_ERROR, 
            error=f"Only file steps for agentic loop are allowed"
        )
