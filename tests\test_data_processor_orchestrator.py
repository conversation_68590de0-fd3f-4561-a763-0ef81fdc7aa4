import os
os.environ['REDIS_HOST'] = 'localhost'
os.environ['REDIS_PORT'] = '6379'

os.environ['CELERY_BROKER_URL'] = 'redis://localhost:6379/0'
os.environ['CELERY_RESULT_BACKEND'] = 'redis://localhost:6379/0'

os.environ['MINIO_BUCKET_NAME'] = 'quepasa-files'
os.environ['MINIO_HOST'] = 'localhost'
os.environ['MINIO_PORT'] = '9000'
os.environ['MINIO_ACCESS_KEY'] = 'minioadmin'
os.environ['MINIO_SECRET_KEY'] = 'minioadmin'

import pytest
from unittest.mock import MagicMock, patch, call

from quepasa.data_processor.orchestrator import DataProcessorOrchestrator
from src.lib.batch_utils import BatchAction, BatchState, BatchUtils

# Mock constants for testing
MOCK_STORAGE = {
    BatchState.BACKLOG: 'test/storage/batch/backlog',
    BatchState.IN_PROGRESS: 'test/storage/batch/in_progress',
    BatchState.DONE: 'test/storage/batch/done'
}

@pytest.fixture
def mock_qp_files():
    with patch('quepasa.data_processor.orchestrator.qp_files') as mock:
        mock.exists.return_value = True
        mock.get_files.return_value = []
        mock.get_json_zlib.return_value = {}
        mock.delete_file = MagicMock()
        mock.move = MagicMock()
        mock.set_json_zlib = MagicMock()
        mock.upload_file = MagicMock()
        mock.download_file = MagicMock()
        mock.set_data = MagicMock()
        yield mock

@pytest.fixture
def mock_logger():
    with patch('quepasa.data_processor.orchestrator.logger') as mock:
        yield mock

@pytest.fixture
def mock_process_batch():
    with patch('quepasa.data_processor.orchestrator.process_batch') as mock:
        mock.apply_async.return_value = MagicMock(
            id='test-task-id'
        )
        yield mock

@pytest.fixture
def mock_time():
    with patch('quepasa.data_processor.orchestrator.time') as mock:
        mock.time.return_value = 1234567890.123
        yield mock

@pytest.fixture
def mock_batch_utils():
    with patch('quepasa.data_processor.orchestrator.BatchUtils') as mock:
        # Return batch ID based on the input filename
        def parse_batch_filename(filename):
            # Extract 'batch1' from 'test/storage/batch/backlog/1234567890.batch1.json'
            base = os.path.basename(filename)
            parts = base.split('.')
            if len(parts) > 2:
                return None, parts[1]  # Return the batch1 part
            else:
                return None, parts[0]
        mock.parse_batch_filename.side_effect = parse_batch_filename
        
        # Mock the get_batch_files_by_client method
        mock.get_batch_files_by_client.return_value = {}
        
        yield mock

@pytest.fixture
def orchestrator(mock_qp_files, mock_logger, mock_time, mock_batch_utils):
    return DataProcessorOrchestrator()

def test_process_client_batches_success(orchestrator, mock_process_batch, mock_logger):
    # Setup
    client_id = 'test-client'
    batch_files = [
        {'ts': 1234567890, 'file': 'test/storage/batch/backlog/1234567890.batch1.json'}
    ]
    
    # Execute
    orchestrator.process_client_batches(client_id, batch_files)
    
    # Assert
    mock_process_batch.apply_async.assert_called_once_with(args=['test-client', 'batch1'])
    mock_logger.info.assert_any_call(f"[{client_id}, batch1] Batch processing started")

def test_run_with_batches(orchestrator, mock_batch_utils, mock_process_batch):
    """Test running the orchestrator with batches"""
    # Configure mock to return batch files
    mock_batch_utils.get_batch_files_by_client.return_value = {
        'test-client': [
            {'file': 'batch1.json'},
            {'file': 'batch2.json'}
        ]
    }
    
    # Call the run method
    orchestrator.run()
    
    # Verify that BatchUtils.get_batch_files_by_client was called with BACKLOG state
    mock_batch_utils.get_batch_files_by_client.assert_called_once_with(BatchState.BACKLOG)
    
    # Verify that process_batch.apply_async was called twice
    assert mock_process_batch.apply_async.call_count == 2
    mock_process_batch.apply_async.assert_any_call(args=['test-client', 'batch1'])
    mock_process_batch.apply_async.assert_any_call(args=['test-client', 'batch2'])

def test_run_no_batches(orchestrator, mock_batch_utils, mock_process_batch):
    """Test running the orchestrator with no batches"""
    # Configure mock to return empty dict
    mock_batch_utils.get_batch_files_by_client.return_value = {}
    
    # Call the run method
    orchestrator.run()
    
    # Verify that BatchUtils.get_batch_files_by_client was called with BACKLOG state
    mock_batch_utils.get_batch_files_by_client.assert_called_once_with(BatchState.BACKLOG)
    
    # Verify that process_batch.apply_async was not called
    mock_process_batch.apply_async.assert_not_called()

def test_run_handles_exceptions(orchestrator, mock_batch_utils, mock_process_batch):
    """Test that the orchestrator handles exceptions properly"""
    # Configure mock to raise an exception
    mock_batch_utils.get_batch_files_by_client.side_effect = Exception("Test error")
    
    # Call the run method and expect it to exit
    with pytest.raises(SystemExit):
        orchestrator.run() 