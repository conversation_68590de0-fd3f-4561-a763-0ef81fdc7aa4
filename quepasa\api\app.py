from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from fastapi import FastAPI, HTTPException, Depends, File, UploadFile, Request, Form, Body, Query
from fastapi.security import HTT<PERSON><PERSON>earer
from fastapi.responses import Response
from fastapi.middleware.cors import CORSMiddleware
import json
import numpy as np
import os
import re
from datetime import datetime

from quepasa.api.models import <PERSON>ch<PERSON><PERSON><PERSON>, BatchResult
from quepasa.api.handlers import document_handler, file_handler, batch_handler, telegram_handler, products_handler
from quepasa.api.auth import verify_auth

from src.lib.embedding.providers import EmbeddingProvider
from src.lib.embedding_service import get_embedding_from_service
from src.lib.embedding_utils import get_cached_embedding
from src.lib.llm_utils import get_llm_answer, get_cached_llm_answer
from src.lib.whisper_utils import get_cached_segments
from src.lib.llm.providers import LLMProvider
from src.lib.files import QuepasaFiles

from src.lib.logger import QuepasaLogger

# Logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize files client
qp_files = QuepasaFiles()


app = FastAPI(
    title="QuePasa API",
    description="API for QuePasa document processing system",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include routers
app.include_router(document_handler.router, prefix="/data/v1/documents", tags=["documents"])
app.include_router(batch_handler.router, prefix="/data/v1/batches", tags=["batches"])
app.include_router(telegram_handler.router, prefix="/data/v1/telegram", tags=["telegram"])
app.include_router(products_handler.router, prefix="/data/v1/products", tags=["products"])

security = HTTPBearer()

@app.get("/", include_in_schema=True)
async def root():
    """Public endpoint - Welcome message"""
    return {"message": "Welcome to QuePasa API"}

@app.get("/health", include_in_schema=True)
async def health_check():
    """Public endpoint - Health check"""
    return {"status": "healthy"}

@app.get("/docs", include_in_schema=True)
async def get_docs():
    """Public endpoint - API documentation"""
    return app.openapi()

# Router endpoints
@app.post("/data/v1/files/{domain}", response_model=BatchResult)
async def upload_file(
    domain: str,
    file: UploadFile = File(...),
    access: Optional[str] = Form(default=None),
    language: Optional[str] = Form(default=None),
    skip_indexing: bool = Query(default=False),
    client_id: str = Depends(verify_auth)
) -> BatchResult:
    """Upload file to storage"""
    logger.info(f"Uploading file to storage for client_id={client_id}, domain={domain}, filename={file.filename}, language={language}, skip_indexing={skip_indexing}")
    try:
        # If access is provided as a dictionary, convert it to BatchAccess
        if access:
            try:
                access = BatchAccess(**json.loads(access))

            except Exception as e:
                logger.error(f"Failed to parse access: {str(e)}")
                raise HTTPException(500, str(e))
            
        return await file_handler.handler.upload_file(
            client_id,
            domain,
            file.file,
            file.filename,
            access,
            language,
            skip_indexing
        )
    except ValueError as e:
        raise HTTPException(400, str(e))
    except Exception as e:
        raise HTTPException(500, str(e))

@app.post("/data/v1/urls/{domain}", response_model=BatchResult)
async def upload_urls(
    domain: str,
    urls: Union[List[str], List[Dict[str, str]]],
    access: Optional[Dict[str, List[str]]] = Body(default=None),
    language: Optional[str] = Body(default=None),
    skip_indexing: bool = Query(default=False),
    client_id: str = Depends(verify_auth)
) -> BatchResult:
    """Process URLs
    
    Supported formats:
    - txt, md, html, pdf, doc, docx, xls, xlsx, ppt, pptx
    - Youtube transcripts (from Youtube videos)
    - Telegram channels (with authentication)
    """
    logger.info(f"Uploading URLs to storage for client_id={client_id}, domain={domain}, urls={urls}, language={language}, skip_indexing={skip_indexing}")
    try:
        # If access is provided as a dictionary, convert it to BatchAccess
        if access:
            access = BatchAccess(**access)
            
        return await file_handler.handler.upload_urls(
            client_id,
            domain,
            urls,
            access,
            language,
            skip_indexing
        )
    except ValueError as e:
        raise HTTPException(400, str(e))
    except Exception as e:
        raise HTTPException(500, str(e))

# Public URL upload endpoint
class PublicUrlResponse(BaseModel):
    public_url: str
    expires_in_hours: int
    file_size: int
    content_type: str

@app.post("/data/v1/upload", response_model=PublicUrlResponse)
async def upload_file_public_url(
    request: Request,
    file: UploadFile = File(...),
    expires_in_hours: int = Query(default=24, description="Number of hours until the URL expires"),
    client_id: str = Depends(verify_auth)
) -> PublicUrlResponse:
    """Upload file and get a public URL for accessing it
    
    Args:
        request: FastAPI request object to get base URL
        file: The file to upload
        expires_in_hours: Number of hours until the URL expires (default: 24)
        client_id: Client identifier (from auth)
        
    Returns:
        PublicUrlResponse with public URL, expiration time, file size, and content type
    """
    logger.info(f"Uploading file for public URL access for client_id={client_id}, filename={file.filename}, expires_in_hours={expires_in_hours}")
    
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(400, "Filename is required")
        
        # Read file content
        file_content = await file.read()
        if len(file_content) == 0:
            raise HTTPException(400, "File cannot be empty")
        
        # Generate unique file path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_extension = os.path.splitext(file.filename)[1] if '.' in file.filename else ''
        safe_filename = re.sub(r'[^a-zA-Z0-9._-]', '_', file.filename)
        unique_filename = f"{timestamp}_{safe_filename}"
        
        # Create storage path
        file_path = f"storage/public-uploads/{client_id}/{unique_filename}"
        
        # Set content settings based on file type
        content_settings = {
            "content_type": file.content_type or "application/octet-stream",
            "content_encoding": "utf-8",
            "content_language": "en-US",
            "content_disposition": f"attachment; filename={file.filename}",
            "cache_control": "max-age=0",
        }
        
        # Upload file to storage
        qp_files.set_data(file_path, file_content, content_settings)
        
        # Generate public URL with current API host
        base_url = str(request.base_url).rstrip('/')
        public_url = f"{base_url}/data/v1/download/{client_id}/{unique_filename}"
        
        logger.info(f"Successfully uploaded file and generated public URL: {file_path}")
        
        return PublicUrlResponse(
            public_url=public_url,
            expires_in_hours=expires_in_hours,
            file_size=len(file_content),
            content_type=file.content_type or "application/octet-stream"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to upload file for public URL: {str(e)}")
        raise HTTPException(500, f"Failed to upload file: {str(e)}")

# Alternative: Get file info as JSON (for API clients)
@app.get("/data/v1/download/{client_id}/{filename}/info")
async def get_file_info_public(
    request: Request,
    client_id: str,
    filename: str,
    expires_in_hours: int = Query(default=24, description="Number of hours until the URL expires")
):
    """Get file information and public URL as JSON without authentication
    
    Args:
        request: FastAPI request object to get base URL
        client_id: Client identifier
        filename: The unique filename (e.g., 20250804_122433_TTTMWeb.pdf)
        expires_in_hours: Number of hours until the URL expires (default: 24)
        
    Returns:
        JSON response with file information and public URL
    """
    logger.info(f"Public file info request for filename: {filename}, client_id={client_id}, expires_in_hours={expires_in_hours}")
    
    try:
        # Validate filename format
        if not filename or len(filename) < 10:
            raise HTTPException(400, "Invalid filename format")
        
        # Search for the file in the specific client directory
        found_file_path = None
        
        try:
            all_files = qp_files.get_files(f"storage/public-uploads/{client_id}")
            
            # Find the file with matching filename
            for file_path in all_files:
                if file_path.endswith(f"/{filename}"):
                    found_file_path = file_path
                    break
                    
        except Exception as e:
            logger.error(f"Error searching for file {filename}: {str(e)}")
            raise HTTPException(500, "Error searching for file")
        
        if not found_file_path:
            logger.warning(f"File not found: {filename}")
            raise HTTPException(404, "File not found")
        
        # Get file properties
        file_props = qp_files.get_properties(found_file_path)
        
        # Generate public URL with current API host
        base_url = str(request.base_url).rstrip('/')
        public_url = f"{base_url}/data/v1/download/{client_id}/{filename}"
        
        logger.info(f"Successfully retrieved file info for: {found_file_path}")
        
        return {
            "filename": filename,
            "public_url": public_url,
            "expires_in_hours": expires_in_hours,
            "file_size": file_props.get('content_length', 0),
            "content_type": file_props.get('content_type', 'application/octet-stream'),
            "last_modified": file_props.get('last_modified'),
            "exists": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get file info for {filename}: {str(e)}")
        raise HTTPException(500, f"Failed to get file info: {str(e)}")

# Alternative: Stream file content directly
@app.get("/data/v1/download/{client_id}/{filename}")
async def download_file_content_public(
    client_id: str,
    filename: str
):
    """Download file content directly without authentication
    
    Args:
        filename: The unique filename (e.g., 20250804_122433_TTTMWeb.pdf)
        
    Returns:
        File content as streaming response
    """
    logger.info(f"Public file content request for filename: {filename}")
    
    try:
        # Validate filename format
        if not filename or len(filename) < 10:
            raise HTTPException(400, "Invalid filename format")
        
        # Search for the file in all client directories
        found_file_path = None
        
        try:
            all_files = qp_files.get_files(f"storage/public-uploads/{client_id}")
            
            # Find the file with matching filename
            for file_path in all_files:
                if file_path.endswith(f"/{filename}"):
                    found_file_path = file_path
                    break
                    
        except Exception as e:
            logger.error(f"Error searching for file {filename}: {str(e)}")
            raise HTTPException(500, "Error searching for file")
        
        if not found_file_path:
            logger.warning(f"File not found: {filename}")
            raise HTTPException(404, "File not found")
        
        # Get file properties for content type
        file_props = qp_files.get_properties(found_file_path)
        content_type = file_props.get('content_type', 'application/octet-stream')
        
        # Get file content
        file_content = qp_files.get_data(found_file_path)
        
        logger.info(f"Successfully streaming file content for: {found_file_path}")
        
        return Response(
            content=file_content,
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Length": str(len(file_content))
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stream file content for {filename}: {str(e)}")
        raise HTTPException(500, f"Failed to stream file content: {str(e)}")


# Embeddings
class EmbeddingRequest(BaseModel):
    provider: str
    model: str
    text: str
    bypass_cache: bool = Field(default=False)

class EmbeddingResponse(BaseModel):
    embedding: List[float]
    cached: bool = Field(default=False)

# Create retryable versions of our external API calls
def get_embedding_factory(provider, model_version, text):
    """Get embedding"""
    return get_embedding_from_service(provider, model_version, text) if provider in [EmbeddingProvider.SBERT.value] else get_cached_embedding(provider, model_version, text)

@app.post("/gateway/v1/embedding", response_model=EmbeddingResponse)
async def get_embedding(
    request: EmbeddingRequest,
    client_id: str = Depends(verify_auth)
) -> EmbeddingResponse:
    """Get embeddings for a text"""
    # If not in cache, get from provider using the direct method
    embedding = get_embedding_factory(request.provider, request.model, request.text)
    if embedding is None or len(embedding) == 0:
        logger.error(f"Failed to get embedding from provider: {request.provider} {request.model} {request.text}")
        raise HTTPException(status_code=500, detail="Failed to get embedding from provider")
    
    if isinstance(embedding, np.ndarray):
        embedding_to_cache = embedding.tolist()
    else:
        embedding_to_cache = embedding
    
    return EmbeddingResponse(
        embedding=embedding_to_cache,
        cached=False
    )


# LLM
class LLMRequest(BaseModel):
    provider: str
    model: str
    messages: List[Dict[str, str]]
    max_tokens: int = Field(default=None)
    json_mode: bool = Field(default=False)
    use_cache: bool = Field(default=True)

class LLMResponse(BaseModel):
    text: str
    cached: bool = Field(default=False)

@app.post("/gateway/v1/llm", response_model=LLMResponse)
async def get_llm(
    request: LLMRequest,
    client_id: str = Depends(verify_auth)
) -> LLMResponse:
    """Get LLM response for the provided prompt list"""
    try:
        if request.use_cache:
            # Use cached version
            response_text = get_cached_llm_answer(
                request.provider,
                request.model,
                request.messages,
                request.max_tokens,
                request.json_mode
            )
            return LLMResponse(text=response_text, cached=True)
        
        else:
            # Direct call without caching
            response_text = get_llm_answer(
                request.provider,
                request.model,
                request.messages,
                request.max_tokens,
                request.json_mode
            )
            return LLMResponse(text=response_text, cached=False)
        
    except Exception as e:
        logger.error(f"Failed to get LLM response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get LLM response: {str(e)}")


# Whisper
class WhisperRequest(BaseModel):
    provider: str
    model: str
    meta: Dict[str, Any] = Field(default_factory=dict)

class WhisperResponse(BaseModel):
    segments: Dict[str, Any]
    cached: bool = Field(default=False)

@app.post("/gateway/v1/whisper", response_model=WhisperResponse)
async def get_segments(
    file: UploadFile = File(...),
    provider: str = Form(...),
    model: str = Form(...),
    meta: str = Form("{}"),
    client_id: str = Depends(verify_auth)
) -> WhisperResponse:
    """Get audio transcription from the whisper service"""
    try:
        audio_data = await file.read()
        
        # Parse meta JSON string to dict
        meta_dict = json.loads(meta)
        
        # Get transcription using cached segments
        segments = get_cached_segments(
            provider,
            model,
            audio_data,
            meta_dict
        )
        
        if not segments:
            logger.error(f"Failed to get transcription: {provider} {model}")
            raise HTTPException(status_code=500, detail="Failed to get transcription")
        
        return WhisperResponse(
            segments=segments,
            cached=True
        )
        
    except Exception as e:
        logger.error(f"Failed to get whisper transcription: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get transcription: {str(e)}")
    

#
# LLM-as-a-Judge
#
class JudgementBaseRequest(BaseModel):
    llm: str = Field(default="openai:gpt-4o")
    prompt: str = Field(default=None)
    max_tokens: int = Field(default=None)

class JudgementScoresRequest(JudgementBaseRequest):
    messages: List[Dict[str,str]]

class JudgementSideBySideRequest(JudgementBaseRequest):
    messages: List[Dict[str,str]]
    golden_messages: List[Dict[str,str]]

class JudgementGoldenMatchRequest(JudgementBaseRequest):
    messages: List[Dict[str,str]]
    golden_messages: List[Dict[str,str]]

# todo: remove this class and endpoint
class JudgementRequest(JudgementBaseRequest):
    messages: List[Dict[str,str]]
    golden_messages: List[Dict[str,str]] = Field(default_factory=list)

class JudgementResponse(BaseModel):
    status: str
    response: Any # Todo: fix to when we will figure out the final format

JUDGEMENT_PROMPT_SCORE = """
You are an exceptional e-commerce LLM chatbot performance evaluator.
The e-commerce store being evaluated specialises in **clothing, footwear, and accessories for adults and children**.
Evaluate the AI assistant's responses using the methodology and scoring guide below.

Important rule: If a metric is **not applicable** to the given conversation (e.g., the customer never requests or hints at products, so product recommendations are irrelevant), assign "N/A" for that metric and state "Not applicable" in the justification.

Return your response **strictly** as a valid JSON object, with:
- a numeric score (0–5) or "N/A" for each metric, plus a short justification
- clear recommendations for improvement
Respond only in valid JSON format. Do NOT include explanations, formatting, or markdown.

---

Here's the dialog between the customer and the AI assistant:
{{MESSAGES}}

---

📏 Review Methodology (5-Part Assessment)
1. **Intent Understanding (0–5)**
- Does the AI accurately understand and track user goals?
- Does it manage ambiguous or shifting intents effectively?
- Does it respond to tone/emotion implicitly?
2. **Role Adherence (0–5)**
- Does it maintain its salesperson persona?
- Is the tone natural, empathetic, persuasive, and brand-aligned?
- Does it drive toward product discovery or purchase?
- Does it try and close the sale at the earliest reasonable stage?
3. **Product Recommendation Performance (0–5)**
- Are recommendations relevant and appropriate?
- Are they diverse and personalized?
- Are descriptions clear and aligned with industry norms?
4. **Fallback Handling & Conversational Repair (0–5)**
- Does it handle errors/misunderstandings gracefully?
- Are fallbacks natural and non-repetitive?
- Is coherence maintained during recovery?
5. **Knowledge Retention & Context Evaluation (0–5)**
- Does it recall and utilize earlier context accurately?
- Does it maintain and leverage conversation history?
- Is context applied smoothly across turns?

---

📊 Scoring Guide (0–5 Scale)
- 0 = Absolute failure
- 1 = Poor: significant gaps
- 2 = Fair: partial but insufficient
- 3 = Good: meets expectations, minor fixes
- 4 = Very Good: strong, minor flaws
- 5 = Excellent: outstanding, no issues
- "N/A" = Not applicable to the given conversation

---

:return: JSON format example
{{
  "Intent Understanding":{{
    "score":5,
    "justification":"Accurately understood the user's greeting and responded appropriately."}},
  "Role Adherence":{{
    "score":4,
    "justification":"Maintained salesperson persona with minor tone issues."}},
  "Product Recommendation Performance":{{
    "score":"N/A",
    "justification":"Not applicable — no product request detected."}},
  "Fallback Handling & Conversational Repair":{{
    "score":5,
    "justification":"Excellent fallback handling."}},
  "Knowledge Retention & Context Evaluation":{{
    "score":4,
    "justification":"Good use of context, minor lapses."}},
  "Recommendations":"Maintain tone consistency. No product suggestions needed for this interaction."}}
}}
"""

JUDGEMENT_PROMPT_SIDE_BY_SIDE = """
You are an expert evaluator of e-commerce AI assistants.
The e-commerce store being evaluated specialises in **clothing, footwear, and accessories for adults and children**.
Compare the **two dialogs** below (Dialog A and Dialog B) across 5 key performance metrics.

Important rule: If a metric is not applicable to either dialog (e.g., no product recommendations were expected or needed), assign "N/A" to the **better** field and write "Not applicable" in the **justification**. This metric should be excluded from the majority vote when determining the overall winner.

Return your answer strictly as a valid JSON object with:
- For each metric: which dialog performed better ("A", "B", "equal", or "N/A") and a brief justification
- A final overall winner ("A", "B", or "equal") based on majority vote excluding any "N/A" metrics
- Optional concise recommendations for improvement for the weaker dialog (only if there is a clear winner)
Do NOT include any explanations or markdown, just raw JSON.

---

Dialog A:
{{MESSAGES}}

---

Dialog B:
{{GOLDEN_MESSAGES}}

---

📏 Evaluation Criteria (Compare Dialog A vs. Dialog B)
1. **Intent Understanding**
- Which dialog better demonstrates accurate understanding and tracking of the user's goals?
- Which handles ambiguous or shifting intents more effectively?
- Which responds more appropriately to the user's tone/emotional implicitly?
2. **Role Adherence**
- Which dialog better maintains the assistant’s persona as a helpful e-commerce salesperson?
- Which uses a more natural, empathetic, persuasive, and brand-aligned tone?
- Which drives more effectively toward product discovery or purchase?
- Which attempts to close the sale at the earliest reasonable opportunity?
3. **Product Recommendation Performance**
- Which dialog offers more relevant and appropriate product suggestions?
- Which provides more diverse and personalized recommendations?
- Which includes clearer product descriptions that align better with industry norms?
4. **Fallback Handling & Repair**
- Which dialog handles user confusion/misunderstandings more gracefully?
- Which offers fallbacks that are more natural, non-repetitive, and informative?
- Which maintains better coherence and flow during recovery?
5. **Knowledge Retention & Context**
- Which dialog more accurately recalls and uses previous conversation context?
- Which maintains and uses conversation history more effectively?
- Which applies context more smoothly across dialog turns?

---

:return: JSON format example
{{
  "Intent Understanding": {{
    "better": "equal",
    "justification": "Both dialogs xxx xxxxx."
  }},
  "Role Adherence": {{
    "better": "B",
    "justification": "Dialog B xxx xxxxxx xxxx."
  }},
  "Product Recommendation Performance": {{
    "better": "A",
    "justification": "Dialog A xxx xx."
  }},
  "Fallback Handling & Repair": {{
    "better": "N/A",
    "justification": "Not applicable xxxxx xxx xx."
  }},
  "Knowledge Retention & Context": {{
    "better": "equal",
    "justification": "Both dialogs xxx xxx xxxx x."
  }},
  "Overall Winner": "equal",
  "Improvement Suggestions": "Xxxx xxx xx xxx xxxx."
}}
"""

JUDGEMENT_PROMPT_GOLDEN_MATCH = """
You are an expert evaluator of e-commerce AI assistants.
The e-commerce store being evaluated specialises in **clothing, footwear, and accessories for adults and children**.  
Compare the **current dialog** below to a **gold-standard reference dialog**, judging how well the current version matches or improves upon the golden one across 5 key performance metrics.

Your task is to compare the **current dialog** to a **gold-standard reference dialog** and assess whether the assistant’s decisions were appropriate across five key binary criteria.

Return your answer strictly as a valid JSON object with:
- For each criterion:
  - "ok": true or false — Was the assistant’s behaviour appropriate in this regard?
  - "justification": a brief explanation
- Final field "Overall Evaluation":
  - "good" if **all** "ok" values are true
  - "bad" if **any** "ok" is false
- If "Overall Evaluation" is "bad", include a field "Improvement Suggestions" with specific, concise advice
- Do NOT include any explanations or markdown — just raw JSON

---

Current Dialog:
{{MESSAGES}}

---

Golden Dialog:
{{GOLDEN_MESSAGES}}

---

📏 Evaluation Criteria (binary)
1. **Show Product List**  
Was it appropriate to show or not show a list of products at this point?

2. **Product Relevance**  
Were the shown products relevant and appropriate?

3. **Ask Clarification Before Showing**  
Was it appropriate to ask a clarification question *before* showing anything?

4. **Ask Clarification in General**  
Was the use of clarification questions overall appropriate and timely?

5. **Close the Sale**  
Was it appropriate to attempt to close the sale at this point?

---

:return: JSON format example
{{
  "Show Product List": {{
    "ok": false,
    "justification": "Xxx xxx xx."
  }},
  "Product Relevance": {{
    "ok": true,
    "justification": "X xxx xx."
  }},
  "Ask Clarification Before Showing": {{
    "ok": false,
    "justification": "Xxx x xxxx."
  }},
  "Ask Clarification in General": {{
    "ok": true,
    "justification": "Xxxxxx xx."
  }},
  "Close the Sale": {{
    "ok": false,
    "justification": "Xxx xxx."
  }},
  "Overall Evaluation": "bad",
  "Improvement Suggestions": "Xxx xx."
}}
"""

def get_formatted_dialog(messages):
    formatted_dialog = ""
    for turn in messages:
        role = turn['role'].capitalize()
        content = turn['content'].strip()
        formatted_dialog += f"{role}:\n{content}\n\n"
    return formatted_dialog

@app.post("/judgement/v1/scores", response_model=JudgementResponse)
async def get_judgement_scores(
    request: JudgementScoresRequest,
    client_id: str = Depends(verify_auth)
) -> LLMResponse:
    """Get Judgement response for the provided Judgement request"""
    try:
        # Detect llm provider
        provider_str, model_name = request.llm.split(':')
        provider = LLMProvider.from_str(provider_str)

        # Choose prompt
        eval_prompt = request.prompt if request.prompt != None else JUDGEMENT_PROMPT_SCORE

        # Replace conversations
        eval_prompt = eval_prompt.replace("{{MESSAGES}}", get_formatted_dialog(request.messages))

        response_text = get_cached_llm_answer(
            provider,
            model_name,
            [{"role": "user", "content": eval_prompt.strip()}],
            request.max_tokens,
            True
        )

        response_json = json.loads(response_text)
        try:
            scores = []
            for _, value in response_json.items():
                if (
                    'score' in value 
                    and value['score']
                    and (
                        isinstance(value['score'], int)
                        or isinstance(value['score'], str) and value['score'].isdigit()
                    )
                    and int(value['score']) in [0, 1, 2, 3, 4, 5]
                ):
                    scores.append(int(value['score']))
            
            response_json['Average Score'] = sum(scores) / len(scores)
            response_json['Total Score'] = sum(scores)

        except Exception as e:
            logger.info(f"Response JSON: {response_json}")
            logger.error(f"Failed to calculate average and total score: {str(e)}")

        return {
            'status': "OK",
            'response': response_json
        }
        
    except Exception as e:
        logger.error(f"Failed to get Judgement Scores response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Judgement Scores response: {str(e)}")

@app.post("/judgement/v1/side-by-side", response_model=JudgementResponse)
async def get_judgement_side_by_side(
    request: JudgementSideBySideRequest,
    client_id: str = Depends(verify_auth)
) -> LLMResponse:
    """Get Judgement response for the provided Judgement request"""
    try:
        # Detect llm provider
        provider_str, model_name = request.llm.split(':')
        provider = LLMProvider.from_str(provider_str)

        # Choose prompt
        eval_prompt = request.prompt if request.prompt != None else JUDGEMENT_PROMPT_SIDE_BY_SIDE

        # Replace conversations
        eval_prompt = eval_prompt.replace("{{MESSAGES}}", get_formatted_dialog(request.messages))
        eval_prompt = eval_prompt.replace("{{GOLDEN_MESSAGES}}", get_formatted_dialog(request.golden_messages))

        response_text = get_cached_llm_answer(
            provider,
            model_name,
            [{"role": "user", "content": eval_prompt.strip()}],
            request.max_tokens,
            True
        )

        response_json = json.loads(response_text)
        return {
            'status': "OK",
            'response': response_json
        }
        
    except Exception as e:
        logger.error(f"Failed to get Judgement Side by Side response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Judgement Side by Side response: {str(e)}")

@app.post("/judgement/v1/golden-match", response_model=JudgementResponse)
async def get_judgement_golden_match(
    request: JudgementGoldenMatchRequest,
    client_id: str = Depends(verify_auth)
) -> LLMResponse:
    """Get Judgement response for the provided Judgement request"""
    try:
        # Detect llm provider
        provider_str, model_name = request.llm.split(':')
        provider = LLMProvider.from_str(provider_str)

        # Choose prompt
        eval_prompt = request.prompt if request.prompt != None else JUDGEMENT_PROMPT_GOLDEN_MATCH

        # Replace conversations
        eval_prompt = eval_prompt.replace("{{MESSAGES}}", get_formatted_dialog(request.messages))
        eval_prompt = eval_prompt.replace("{{GOLDEN_MESSAGES}}", get_formatted_dialog(request.golden_messages))

        response_text = get_cached_llm_answer(
            provider,
            model_name,
            [{"role": "user", "content": eval_prompt.strip()}],
            request.max_tokens,
            True
        )

        response_json = json.loads(response_text)
        return {
            'status': "OK",
            'response': response_json
        }
        
    except Exception as e:
        logger.error(f"Failed to get Judgement Golden Match response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Judgement Golden Match response: {str(e)}")

# todo: remove this endpoint
@app.post("/judgement/v1/answer", response_model=JudgementResponse)
async def get_judgement(
    request: JudgementRequest,
    client_id: str = Depends(verify_auth)
) -> LLMResponse:
    """Get Judgement response for the provided Judgement request"""
    try:
        # Detect llm provider
        provider_str, model_name = request.llm.split(':')
        provider = LLMProvider.from_str(provider_str)

        # Choose prompt
        eval_prompt = JUDGEMENT_PROMPT_SIDE_BY_SIDE if len(request.golden_messages) > 0 else JUDGEMENT_PROMPT_SCORE
        if request.prompt != None:
            eval_prompt = request.prompt

        # Replace conversations
        eval_prompt = eval_prompt.replace("{{MESSAGES}}", get_formatted_dialog(request.messages))
        if len(request.golden_messages) > 0:
            eval_prompt = eval_prompt.replace("{{GOLDEN_MESSAGES}}", get_formatted_dialog(request.golden_messages))

        response_text = get_cached_llm_answer(
            provider,
            model_name,
            [{"role": "user", "content": eval_prompt.strip()}],
            request.max_tokens,
            True
        )

        response_json = json.loads(response_text)
        if not request.golden_messages or len(request.golden_messages) == 0:
            try:
                scores = []
                for _, value in response_json.items():
                    if (
                        'score' in value 
                        and value['score']
                        and (
                            isinstance(value['score'], int)
                            or isinstance(value['score'], str) and value['score'].isdigit()
                        )
                        and int(value['score']) in [0, 1, 2, 3, 4, 5]
                    ):
                        scores.append(int(value['score']))
                
                response_json['Average Score'] = sum(scores) / len(scores)
                response_json['Total Score'] = sum(scores)

            except Exception as e:
                logger.info(f"Response JSON: {response_json}")
                logger.error(f"Failed to calculate average and total score: {str(e)}")

        return {
            'status': "OK",
            'response': response_json
        }
        
    except Exception as e:
        logger.error(f"Failed to get Judgement response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Judgement response: {str(e)}")