import asyncio
from contextlib import asynccontextmanager
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel
from fastapi import FastAPI, HTTPException, Depends, File, UploadFile, Request, Form, Body, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>
from fastapi.responses import Response
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import json

from quepasa.api.models import <PERSON>ch<PERSON><PERSON><PERSON>, BatchResult, RefreshConfigResponse
from quepasa.api.handlers import (
    document_handler, file_handler, batch_handler,
    telegram_handler, products_handler, gateway_handler,
    judgement_handler
)
from quepasa.api.auth import verify_auth
from quepasa.common.config_client import get_dynamic_config, _MERCHANT_STORE
from quepasa.common.config_models import TenantConfig
from src.lib.files import QuepasaFiles
from dotenv import load_dotenv
from src.lib.logger import QuepasaLogger

# Logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize files client
qp_files = QuepasaFiles()

async def refresh_config(refresh_interval: int = 1800):
    while True:
        logger.info(f"Invoking get_dynamic_config to refresh configuration")
        refresh_response = get_dynamic_config()
        refresh_summary = refresh_response.get("_summary") if isinstance(refresh_response, dict) else None
        logger.info(f"Refresh status: {refresh_summary}")
        logger.info(f"Next invocation of get_dynamic_config is in {refresh_interval} seconds.")
        await asyncio.sleep(refresh_interval)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    task = asyncio.create_task(refresh_config())
    yield
    # Shutdown
    task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        pass


app = FastAPI(
    title="RQP API",
    description="API for RQP document processing system",
    version="1.0.0",
    lifespan=lifespan,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

load_dotenv()

# Include routers
app.include_router(document_handler.router, prefix="/data/v1/documents", tags=["documents"])
app.include_router(batch_handler.router, prefix="/data/v1/batches", tags=["batches"])
app.include_router(telegram_handler.router, prefix="/data/v1/telegram", tags=["telegram"])
app.include_router(products_handler.router, prefix="/data/v1/products", tags=["products"])
app.include_router(gateway_handler.router, prefix="/gateway/v1", tags=["gateway"])
app.include_router(judgement_handler.router, prefix="/judgement/v1", tags=["judgement"])

security = HTTPBearer()

@app.get("/", include_in_schema=True)
async def root():
    """Public endpoint - Welcome message"""
    return {"message": "Welcome to RQP API"}

@app.get("/health", include_in_schema=True)
async def health_check():
    """Public endpoint - Health check"""
    return {"status": "healthy"}

@app.get("/docs", include_in_schema=True)
async def get_docs():
    """Public endpoint - API documentation"""
    return app.openapi()

@app.get("/configured-brain-admin-tenants", include_in_schema=True)
def get_config():
    return _MERCHANT_STORE.all_tenant_names()

@app.get("/get-tenant-config", include_in_schema=True)
def get_config(
        tenant_name: str = Query(..., description="Tenant name"),
        collection_id: Optional[int] = Query(None, description="Collection Id"),
        area_id: Optional[int] = Query(None, description="Area Id"),
):
    tenant_data: TenantConfig = _MERCHANT_STORE.get_tenant_view(tenant_name)
    if tenant_data:
        tenant_data.attributes = [] # removing because of the size of the response
        conversational_config = tenant_data.get_conversational_config(
            area_id=area_id,
            collection_id=collection_id,
        )
        interleaving = tenant_data.get_interleaving(
            area_id=area_id,
            collection_id=collection_id,
        )
        offerings = tenant_data.get_offerings(
            area_id=area_id,
            collection_id=collection_id,
        )
        filtering_attributes = tenant_data.get_filtering_attributes(
            area_id=area_id,
            collection_id=collection_id,
        )
        response_data = {
            "tenant_data": tenant_data,
            "get_conversational_config_response": conversational_config,
            "get_interleaving_response": interleaving,
            "get_offerings_response": offerings,
            "get_filtering_attributes_response": filtering_attributes,
        }
        return response_data
    else:
        raise HTTPException(status_code=404, detail="Tenant configuration not found")

@app.post(
    "/refresh-config",
    response_model=RefreshConfigResponse,
    include_in_schema=True,
)
async def refresh_dynamic_configuration():
    """Refresh dynamic configuration for the API service."""
    try:
        refreshed_configuration = get_dynamic_config()
        if refreshed_configuration is None:
            # Keep 500 for failure case; response model documents only 200 success
            return Response(content=json.dumps({"error": "Failed to refresh dynamic config"}), media_type="application/json", status_code=500)


        summary = (refreshed_configuration or {}).get("_summary") if isinstance(refreshed_configuration, dict) else None
        response_data = {
            "status": "ok",
            "message": "Dynamic config refreshed",
            "updated": "all",
        }
        if summary is not None:
            response_data["summary"] = summary

        response_model = RefreshConfigResponse(**response_data)

        return response_model
    except Exception as e:
        logger.error(f"Error in POST refresh-config endpoint: {str(e)}")
        raise HTTPException(500, "Internal server error")


# Router endpoints
@app.post("/data/v1/files/{domain}", response_model=BatchResult)
async def upload_file(
    domain: str,
    file: UploadFile = File(...),
    access: Optional[str] = Form(default=None),
    language: Optional[str] = Form(default=None),
    skip_indexing: bool = Query(default=False),
    client_id: str = Depends(verify_auth)
) -> BatchResult:
    """Upload file to storage"""
    logger.info(f"Uploading file to storage for client_id={client_id}, domain={domain}, filename={file.filename}, language={language}, skip_indexing={skip_indexing}")
    try:
        # If access is provided as a dictionary, convert it to BatchAccess
        if access:
            try:
                access = BatchAccess(**json.loads(access))

            except Exception as e:
                logger.error(f"Failed to parse access: {str(e)}")
                raise HTTPException(500, str(e))
            
        return await file_handler.handler.upload_file(
            client_id,
            domain,
            file.file,
            file.filename,
            access,
            language,
            skip_indexing
        )
    except ValueError as e:
        raise HTTPException(400, str(e))
    except Exception as e:
        raise HTTPException(500, str(e))

@app.post("/data/v1/urls/{domain}", response_model=BatchResult)
async def upload_urls(
    domain: str,
    urls: Union[List[str], List[Dict[str, str]]],
    access: Optional[Dict[str, List[str]]] = Body(default=None),
    language: Optional[str] = Body(default=None),
    skip_indexing: bool = Query(default=False),
    client_id: str = Depends(verify_auth)
) -> BatchResult:
    """Process URLs
    
    Supported formats:
    - txt, md, html, pdf, doc, docx, xls, xlsx, ppt, pptx
    - Youtube transcripts (from Youtube videos)
    - Telegram channels (with authentication)
    """
    logger.info(f"Uploading URLs to storage for client_id={client_id}, domain={domain}, urls={urls}, language={language}, skip_indexing={skip_indexing}")
    try:
        # If access is provided as a dictionary, convert it to BatchAccess
        if access:
            access = BatchAccess(**access)
            
        return await file_handler.handler.upload_urls(
            client_id,
            domain,
            urls,
            access,
            language,
            skip_indexing
        )
    except ValueError as e:
        raise HTTPException(400, str(e))
    except Exception as e:
        raise HTTPException(500, str(e))

# Public URL upload endpoint
class PublicUrlResponse(BaseModel):
    public_url: str
    expires_in_hours: int
    file_size: int
    content_type: str

@app.post("/data/v1/upload", response_model=PublicUrlResponse)
async def upload_file_public_url(
    request: Request,
    file: UploadFile = File(...),
    expires_in_hours: int = Query(default=24, description="Number of hours until the URL expires"),
    client_id: str = Depends(verify_auth)
) -> PublicUrlResponse:
    """Upload file and get a public URL for accessing it
    
    Args:
        request: FastAPI request object to get base URL
        file: The file to upload
        expires_in_hours: Number of hours until the URL expires (default: 24)
        client_id: Client identifier (from auth)
        
    Returns:
        PublicUrlResponse with public URL, expiration time, file size, and content type
    """
    logger.info(f"Uploading file for public URL access for client_id={client_id}, filename={file.filename}, expires_in_hours={expires_in_hours}")
    
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(400, "Filename is required")
        
        # Read file content
        file_content = await file.read()
        if len(file_content) == 0:
            raise HTTPException(400, "File cannot be empty")
        
        # Generate unique file path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_extension = os.path.splitext(file.filename)[1] if '.' in file.filename else ''
        safe_filename = re.sub(r'[^a-zA-Z0-9._-]', '_', file.filename)
        unique_filename = f"{timestamp}_{safe_filename}"
        
        # Create storage path
        file_path = f"storage/public-uploads/{client_id}/{unique_filename}"
        
        # Set content settings based on file type
        content_settings = {
            "content_type": file.content_type or "application/octet-stream",
            "content_encoding": "utf-8",
            "content_language": "en-US",
            "content_disposition": f"attachment; filename={file.filename}",
            "cache_control": "max-age=0",
        }
        
        # Upload file to storage
        qp_files.set_data(file_path, file_content, content_settings)
        
        # Generate public URL with current API host
        base_url = str(request.base_url).rstrip('/')
        public_url = f"{base_url}/data/v1/download/{client_id}/{unique_filename}"
        
        logger.info(f"Successfully uploaded file and generated public URL: {file_path}")
        
        return PublicUrlResponse(
            public_url=public_url,
            expires_in_hours=expires_in_hours,
            file_size=len(file_content),
            content_type=file.content_type or "application/octet-stream"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to upload file for public URL: {str(e)}")
        raise HTTPException(500, f"Failed to upload file: {str(e)}")

# Alternative: Get file info as JSON (for API clients)
@app.get("/data/v1/download/{client_id}/{filename}/info")
async def get_file_info_public(
    request: Request,
    client_id: str,
    filename: str,
    expires_in_hours: int = Query(default=24, description="Number of hours until the URL expires")
):
    """Get file information and public URL as JSON without authentication
    
    Args:
        request: FastAPI request object to get base URL
        client_id: Client identifier
        filename: The unique filename (e.g., 20250804_122433_TTTMWeb.pdf)
        expires_in_hours: Number of hours until the URL expires (default: 24)
        
    Returns:
        JSON response with file information and public URL
    """
    logger.info(f"Public file info request for filename: {filename}, client_id={client_id}, expires_in_hours={expires_in_hours}")
    
    try:
        # Validate filename format
        if not filename or len(filename) < 10:
            raise HTTPException(400, "Invalid filename format")
        
        # Search for the file in the specific client directory
        found_file_path = None
        
        try:
            all_files = qp_files.get_files(f"storage/public-uploads/{client_id}")
            
            # Find the file with matching filename
            for file_path in all_files:
                if file_path.endswith(f"/{filename}"):
                    found_file_path = file_path
                    break
                    
        except Exception as e:
            logger.error(f"Error searching for file {filename}: {str(e)}")
            raise HTTPException(500, "Error searching for file")
        
        if not found_file_path:
            logger.warning(f"File not found: {filename}")
            raise HTTPException(404, "File not found")
        
        # Get file properties
        file_props = qp_files.get_properties(found_file_path)
        
        # Generate public URL with current API host
        base_url = str(request.base_url).rstrip('/')
        public_url = f"{base_url}/data/v1/download/{client_id}/{filename}"
        
        logger.info(f"Successfully retrieved file info for: {found_file_path}")
        
        return {
            "filename": filename,
            "public_url": public_url,
            "expires_in_hours": expires_in_hours,
            "file_size": file_props.get('content_length', 0),
            "content_type": file_props.get('content_type', 'application/octet-stream'),
            "last_modified": file_props.get('last_modified'),
            "exists": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get file info for {filename}: {str(e)}")
        raise HTTPException(500, f"Failed to get file info: {str(e)}")

# Alternative: Stream file content directly
@app.get("/data/v1/download/{client_id}/{filename}")
async def download_file_content_public(
    client_id: str,
    filename: str
):
    """Download file content directly without authentication
    
    Args:
        filename: The unique filename (e.g., 20250804_122433_TTTMWeb.pdf)
        
    Returns:
        File content as streaming response
    """
    logger.info(f"Public file content request for filename: {filename}")
    
    try:
        # Validate filename format
        if not filename or len(filename) < 10:
            raise HTTPException(400, "Invalid filename format")
        
        # Search for the file in all client directories
        found_file_path = None
        
        try:
            all_files = qp_files.get_files(f"storage/public-uploads/{client_id}")
            
            # Find the file with matching filename
            for file_path in all_files:
                if file_path.endswith(f"/{filename}"):
                    found_file_path = file_path
                    break
                    
        except Exception as e:
            logger.error(f"Error searching for file {filename}: {str(e)}")
            raise HTTPException(500, "Error searching for file")
        
        if not found_file_path:
            logger.warning(f"File not found: {filename}")
            raise HTTPException(404, "File not found")
        
        # Get file properties for content type
        file_props = qp_files.get_properties(found_file_path)
        content_type = file_props.get('content_type', 'application/octet-stream')
        
        # Get file content
        file_content = qp_files.get_data(found_file_path)
        
        logger.info(f"Successfully streaming file content for: {found_file_path}")
        
        return Response(
            content=file_content,
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Length": str(len(file_content))
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stream file content for {filename}: {str(e)}")
        raise HTTPException(500, f"Failed to stream file content: {str(e)}")
