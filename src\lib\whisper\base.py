from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from enum import Enum
from .providers import WhisperProvider

class WhisperModelType(str, Enum):
    """Enum for whisper model types"""
    DEFAULT = 'default'
    DIARIZATION = 'diarization'

class BaseWhisper(ABC):
    """Base class for Whisper audio transcription providers"""
    
    @property
    @abstractmethod
    def provider(self) -> WhisperProvider:
        """The provider enum for this embedding implementation."""
        pass

    @abstractmethod
    def get_segments(self, model_version: str, audio_data: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get transcription segments from audio data
        
        Args:
            model_version: The model to use for transcription
            audio_data: Audio data as bytes
            meta: Dictionary containing metadata
            
        Returns:
            Dictionary with transcription segments and metadata
        """
        pass
