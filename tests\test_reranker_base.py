import pytest
from typing import List, Dict, Any
from src.lib.reranker.base import BaseReranker

class MockReranker(BaseReranker):
    """Mock reranker for testing"""
    def get_results(self, model_version: str, query: str, documents: List[Dict[str, Any]], top_n: int = None) -> List[Dict[str, Any]]:
        return documents[:top_n] if top_n else documents

def test_base_reranker_abstract():
    """Test that BaseReranker cannot be instantiated directly"""
    with pytest.raises(TypeError):
        BaseReranker()

def test_base_reranker_implementation():
    """Test that implementing get_results makes the class instantiable"""
    reranker = MockReranker()
    assert isinstance(reranker, BaseReranker)

def test_mock_reranker_get_results():
    """Test mock reranker implementation"""
    reranker = MockReranker()
    documents = [{"id": 1}, {"id": 2}, {"id": 3}]
    
    # Test without top_n
    results = reranker.get_results("test-model", "query", documents)
    assert results == documents
    
    # Test with top_n
    results = reranker.get_results("test-model", "query", documents, top_n=2)
    assert results == documents[:2]
    assert len(results) == 2

def test_mock_reranker_cached_results():
    """Test cached results implementation"""
    reranker = MockReranker()
    documents = [{"id": 1}, {"id": 2}, {"id": 3}]
    
    # Test without top_n
    results = reranker.get_cached_results("test-model", "query", documents)
    assert results == documents
    
    # Test with top_n
    results = reranker.get_cached_results("test-model", "query", documents, top_n=2)
    assert results == documents[:2] 