from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

from src.lib.logger import QuepasaLogger

class BaseProcessor(ABC):
    """Base class for all content processors"""
    
    def __init__(self):
        self.logger = QuepasaLogger().get_instance(self.__class__.__name__)

    def process(self, content: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the content and return the result
        
        Args:
            content: Raw content to process
            meta: Metadata about the content
            
        Returns:
            Dictionary containing the processing results and metadata
        """
        try:
            self.logger.info(f"Processing content", extra={'meta': meta})
            result = self._process_impl(content, meta)
            self.logger.info(f"Processing completed successfully", extra={'meta': meta})
            return {
                'status': 'success',
                'result': result,
                'meta': meta
            }

        except Exception as e:
            self.logger.error(f"Processing failed: {str(e)}", 
                            extra={'meta': meta}, 
                            exc_info=True)
            return {
                'status': 'error',
                'error': str(e),
                'meta': meta
            }

    @abstractmethod
    def _process_impl(self, content: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Implementation of the actual processing logic
        
        Args:
            content: Raw content to process
            meta: Metadata about the content
            
        Returns:
            Dictionary containing the processing results
        """
        pass

    def validate_meta(self, meta: Dict[str, Any], required_fields: list) -> Optional[str]:
        """
        Validate that required fields are present in metadata
        
        Args:
            meta: Metadata dictionary to validate
            required_fields: List of required field names
            
        Returns:
            Error message if validation fails, None otherwise
        """
        missing_fields = [field for field in required_fields if field not in meta]
        if missing_fields:
            return f"Missing required metadata fields: {', '.join(missing_fields)}"
        return None

    def get_meta_value(self, meta: Dict[str, Any], key: str, default: Any = None) -> Any:
        """
        Safely get a value from metadata with a default
        
        Args:
            meta: Metadata dictionary
            key: Key to look up
            default: Default value if key is not found
            
        Returns:
            Value from metadata or default
        """
        return meta.get(key, default) 