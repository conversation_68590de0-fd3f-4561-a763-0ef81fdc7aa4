#!/usr/bin/env python3
import os
import sys
import asyncio
from typing import List, Dict

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.lib.llm.anthropic import AnthropicLLM
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance("test_anthropic")

def test_regular_response():
    """Test getting a regular (non-streaming) response from Anthropic."""
    try:
        llm = AnthropicLLM()
        
        # Create a simple prompt
        prompt_list = [
            {"role": "user", "content": "What is the capital of France? Keep it brief."}
        ]
        
        # Get response - no await needed since method is synchronous
        response = llm.get_answer(
            model_version="claude-3-haiku-20240307",
            prompt_list=prompt_list,
            answer_prompt_size=100,
            json_mode=False
        )
        
        logger.info(f"Regular response: {response}")
        return response
    except Exception as e:
        logger.error(f"Error in test_regular_response: {e}")
        raise

def test_streaming_response():
    """Test getting a streaming response from Anthropic."""
    try:
        llm = AnthropicLLM()
        
        # Create a simple prompt
        prompt_list = [
            {"role": "user", "content": "Count from 1 to 5, with each number on a new line."}
        ]
        
        # Get streaming response
        logger.info("Starting streaming response test:")
        stream = llm.get_streaming_answer(
            model_version="claude-3-haiku-20240307",
            prompt_list=prompt_list,
            answer_prompt_size=100,
            json_mode=False
        )
        
        # Process the stream
        full_response = ""
        for chunk in stream:
            logger.info(f"Chunk received: {chunk}")
            full_response += chunk
            
        logger.info(f"Full streaming response: {full_response}")
        return full_response
    except Exception as e:
        logger.error(f"Error in test_streaming_response: {e}")
        raise

"""Run all tests."""
logger.info("Testing Anthropic API...")

# Test regular response - no await needed
logger.info("Testing regular (non-streaming) response...")
test_regular_response()

# Test streaming response
logger.info("Testing streaming response...")
test_streaming_response()

logger.info("All tests completed.")

