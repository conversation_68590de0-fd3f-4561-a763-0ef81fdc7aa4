from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Optional

from .models import HistoryI<PERSON>, HistoryFilter
from .storage import FileHistoryStorage
from configuration.main.default import QuepasaConfigurationHub
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)

class HistoryManager:
    """Manages chat history operations"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        self.config = config
        self.storage = FileHistoryStorage(config)

    def save_message(self, user_id: str, role: str, content: str,
                     tool_calls: Optional[List[Dict[str, Any]]] = None,
                     tool_call_id: Optional[str] = None,
                     name: Optional[str] = None,
                     references: Optional[Dict[str, Any]] = None) -> None:
        """Save a new message to history
        
        Args:
            user_id: ID of the user
            role: Role of the message sender ('user' or 'assistant')
            content: Message content
            tool_calls: Tools suggestions
            tool_call_id: Tool id
            name: Tool name
            references: References
        """

        request_dict = self.config.request.to_dict()
        if 'history' in request_dict:
            del request_dict['history']

        item = HistoryItem(
            request=request_dict,
            ts=datetime.now(),
            role=role,
            content=content,
            tool_calls=tool_calls,
            tool_call_id=tool_call_id,
            name=name,
            references=references
        )
        self.storage.save_item(user_id, item)
        
    def get_conversation_history(self, user_id: str) -> List[HistoryItem]:
        """Get conversation history for a user
        
        Args:
            user_id: ID of the user
            
        Returns:
            List of history items matching the criteria
        """
        config = self.config.get_history_config()
            
        # Calculate time window
        end_time = datetime.now()
        start_time = None
        if config.forget_after_seconds:
            start_time = end_time - timedelta(seconds=config.forget_after_seconds)
            
        # Create filter
        filter = HistoryFilter(
            user_id=user_id,
            start_time=start_time,
            end_time=end_time,
            roles=config.use_roles,
            max_items=config.max_last_messages
        )
        logger.info(f"Get conversation history for filter: {filter}")
        
        # Get and process items
        items = self.storage.get_items(filter)
        
        # Sort by timestamp
        items.sort(key=lambda x: x.ts)
        
        return items
        
    def clear_user_history(self, user_id: str) -> None:
        """Clear all history for a user
        
        Args:
            user_id: ID of the user
        """
        self.storage.clear_history(user_id)
        
    def get_related_history(self, user_id: str) -> List[HistoryItem]:
        """Get history items
        
        Args:
            user_id: ID of the user
            
        Returns:
            List of related history items
        """
        history = self.get_conversation_history(user_id)
        
        # Filter out current request and unrelated items
        filtered_history = []
        for item in history:
            if item.request.get('question') != self.config.request.question:
                # Check if item is marked as related
                if (item.request.get('classification', {}).get('related', '').lower() == 'yes'):
                    filtered_history.append(item)
                    
        return filtered_history 