from abc import ABC, abstractmethod
from typing import List, Optional

from .providers import EmbeddingProvider

class BaseEmbedding(ABC):
    """Base class for embedding providers."""
    
    @property
    @abstractmethod
    def provider(self) -> EmbeddingProvider:
        """The provider enum for this embedding implementation."""
        pass

    @abstractmethod
    def get_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get embedding for the given text."""
        pass