{"permissions": {"allow": ["Bash(python -m pytest tests/**)", "Bash(python -m pytest tests/** -v)", "mcp__sequential-thinking__sequentialthinking", "Bash(uv cache:*)", "Bash(python -m pytest tests/ -k \"document\" -v --tb=short)", "Bash(python -m pytest tests/test_data_models.py -v)", "Bash(python -m pytest tests/configuration/ tests/processors/ tests/test_answer_formatter.py tests/test_crawler.py tests/test_llm_factory.py -v --tb=short)", "Bash(python -m pytest tests/ -m \"not slow\" -v --tb=short)", "Bash(python -m pytest tests/configuration/test_yaml_validation.py tests/test_answer_formatter.py -v)", "Bash(python -m pytest tests/configuration/test_yaml_validation.py tests/test_answer_formatter.py tests/test_data_models.py -v)", "Bash(python -m pytest tests/test_data_models.py::TestQuepasaDocumentBehavioral -v)", "Bash(python -m pytest tests/test_data_models.py::TestDocumentAPIModel -v)"], "deny": [], "ask": []}}