# API Documentation

## Overview

This folder contains API documentation and specifications for the Quepasa system.

## Important Note

⚠️ **These specifications have different purposes and sources of truth:**

### GroupBy Retail Search API
The GroupBy Retail Search API specification can be accessed at:
- **Live API Documentation**: https://search.gbiqa-lo.groupbycloud.com/api/swagger-ui#/Search/search

### Conversational Shopping Assistant API (`conversational_shopping_assistant_v1.yaml`)
This represents **our planned API design** and serves as the specification for how we intend the Quepasa conversational API to work. This may be generated with Swagger in the future, but currently represents our design intentions.

## Files in this Directory

- `conversational_shopping_assistant_v1.yaml` - OpenAPI 3.0.3 specification for the conversational shopping assistant API
- `request_example.md` - Example API requests with different formats and options
- `response_example.md` - Example API responses showing product data and actions

## Usage

These files are intended for:
- Developer reference during integration
- Understanding expected request/response formats
- API contract documentation
- Frontend development guidance

## Keeping Documentation Updated

While these are static files, they should be periodically updated to reflect major API changes. For GroupBy Retail Search API, always refer to the live API documentation linked above for the most current specifications.