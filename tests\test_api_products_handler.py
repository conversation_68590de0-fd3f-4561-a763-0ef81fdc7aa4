import os
from unittest.mock import Mock, patch

import pytest
import pytest_asyncio

# Ensure client-key auth is validated against tenant config by mocking the config lookup in this module
@pytest.fixture(autouse=True)
def _mock_tenant_config_for_products(mocker):
    """Patch get_tenant_config used by RequestAuthenticatorManager to return a tenant
    config with the expected client-key for these tests.

    This keeps validation strict in production code while avoiding seeding the
    global in-memory store in unit tests for the products API.
    """
    from quepasa.common.config_models import TenantConfig, TenantModel

    def _make_config(tenant_name: str) -> TenantConfig:
        tenant = TenantModel(
            id=1,
            name=tenant_name,
            enabled=True,
            settings={},
            activeKey_1="secret-key-123",
            activeKey_2=None,
        )
        return TenantConfig(
            tenant=tenant,
            collections=[],
            areas=[],
            attributes=[],
            conversational_search_configs=[],
            conversational_prompt_configs=[],
            conversational_filterable_attributes=[],
        )

    mocker.patch("quepasa.common.bearer.get_tenant_config", side_effect=_make_config)
    yield

# Set environment variables for testing
os.environ['CELERY_BROKER_URL'] = 'redis://localhost:6379/0'
os.environ['CELERY_RESULT_BACKEND'] = 'redis://localhost:6379/0'
os.environ['MINIO_HOST'] = 'test'
os.environ['MINIO_PORT'] = '9000'
os.environ['MINIO_ACCESS_KEY'] = 'test'
os.environ['MINIO_SECRET_KEY'] = 'test'
os.environ['MINIO_BUCKET_NAME'] = 'test-bucket'
os.environ['REDIS_HOST'] = 'localhost'
os.environ['REDIS_PORT'] = '6379'

from quepasa.api.handlers.products_handler import (
    ProductsHandler,
    ProductIngestionRequest,
    ProductDeletionRequest,
    router,
    MAX_RECORDS
)
from quepasa.api.handlers.document_handler import DocumentHandler
from quepasa.api.models import BatchResult
from src.lib.batch_utils import BatchState, BatchAction


@pytest.fixture
def products_handler():
    """Create a ProductsHandler instance for testing"""
    handler = ProductsHandler()
    # Mock the markdown processor
    handler.markdown_processor = Mock()
    # Mock the files instance to avoid serialization issues
    handler.files = Mock()
    handler.files.exists.return_value = True
    handler.files.get_json.return_value = {}
    handler.files.get_json_zlib.return_value = {}
    handler.files.set_json_zlib.return_value = None
    return handler


@pytest.fixture
def sample_product():
    """Sample product data for testing"""
    return {
        "id": "SKU123",
        "title": "Test Product",
        "description": "A test product description",
        "availability": "in_stock",
        "uri": "http://example.com/product/123",
        "attributes": {
            "color": "blue",
            "size": "M"
        }
    }


@pytest.fixture
def sample_product_without_id():
    """Sample product without ID field"""
    return {
        "title": "Test Product",
        "description": "A test product description",
        "availability": "in_stock"
    }


@pytest.fixture
def sample_markdown_documents():
    """Sample markdown documents returned by products_to_documents"""
    return [
        {
            "id": "SKU123",
            "title": "Test Product",
            "url": "http://example.com/product/123",
            "chunks": [{"language": "en", "text": "# Test Product\n**SKU:** SKU123"}]
        }
    ]


@pytest.fixture
def mock_batch_utils():
    """Mock BatchUtils for testing"""
    # BatchUtils is used in the parent DocumentHandler class  
    with patch('quepasa.api.handlers.document_handler.BatchUtils') as mock, \
         patch('src.lib.batch_utils.app') as mock_celery:
        mock.create_batch.return_value = "test-batch-id-123"
        mock.add_task.return_value = None
        mock.get_document_filenames.return_value = ("test.json.zlib", "test.json")
        mock.get_generated_batch_id.return_value = "test-batch-id-123"
        # Mock celery app to avoid serialization issues
        mock_celery.send_task.return_value = Mock()
        yield mock


@pytest.fixture
def mock_products_to_documents():
    """Mock products_to_documents function to return document dicts"""
    with patch('quepasa.api.handlers.products_handler.products_to_documents') as mock:
        mock.return_value = [
            {
                "id": "SKU123",
                "url": "http://example.com/product/123",
                "title": "Test Product",
                "sku": "SKU123",
                "price_from": None,
                "price_to": None,
                "metadata": "{}",
                "chunks": [{"language": "en", "text": "# Test Product\n**SKU:** SKU123"}]
            }
        ]
        yield mock


@pytest_asyncio.fixture
async def test_client():
    """Create an async test client for the router with client-key authentication"""
    from fastapi import FastAPI
    from httpx import AsyncClient, ASGITransport
    
    app = FastAPI()
    app.include_router(router, prefix="/data/v1/products")
    
    transport = ASGITransport(app=app)
    async with AsyncClient(
        transport=transport, 
        base_url="http://test", 
        headers={
            "X-Customer-Id": "customer123",
            "Authorization": "client-key secret-key-123"
        }
    ) as client:
        yield client


@pytest_asyncio.fixture
async def invalid_auth_client():
    """Create an async test client without valid auth for testing unauthorized access"""
    from fastapi import FastAPI
    from httpx import AsyncClient, ASGITransport
    
    app = FastAPI()
    app.include_router(router, prefix="/data/v1/products")
    
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        yield client

# Test client-key authentication properly
class TestHeaderValidation:
    """Test client-key authentication through BearerAuth system"""

    def test_valid_client_key_auth(self):
        """Test that valid client-key headers work through BearerAuth"""
        from quepasa.common.bearer import RequestAuthenticatorManager
        
        auth_manager = RequestAuthenticatorManager()
        headers = {
            "X-Customer-Id": "customer123",
            "authorization": "client-key secret-key-123"
        }
        result = auth_manager.authenticate(headers)
        
        assert result.is_authorized is True
        assert result.client_id == "customer123"

    def test_missing_customer_id_header(self):
        """Test validation fails when X-Customer-Id header is missing"""
        from quepasa.common.bearer import RequestAuthenticatorManager
        
        auth_manager = RequestAuthenticatorManager()
        headers = {"authorization": "client-key secret-key-123"}
        result = auth_manager.authenticate(headers)
        
        # Should fail because no X-Customer-Id header, so it tries Bearer parsing
        assert result.is_authorized is False
        assert "Invalid Authorization header format" in result.error

    def test_missing_authorization_header(self):
        """Test validation fails when authorization header is missing"""
        from quepasa.common.bearer import RequestAuthenticatorManager
        
        auth_manager = RequestAuthenticatorManager()
        headers = {"X-Customer-Id": "customer123"}
        result = auth_manager.authenticate(headers)
        
        assert result.is_authorized is False
        assert "Missing Authorization header" in result.error

    def test_invalid_authorization_format(self):
        """Test validation fails with invalid authorization format"""
        from quepasa.common.bearer import RequestAuthenticatorManager
        
        auth_manager = RequestAuthenticatorManager()
        headers = {
            "X-Customer-Id": "customer123",
            "authorization": "bearer invalid-token"
        }
        result = auth_manager.authenticate(headers)
        
        # Should try Bearer auth parsing and fail
        assert result.is_authorized is False

    def test_empty_client_key(self):
        """Test validation fails with empty client key"""
        from quepasa.common.bearer import RequestAuthenticatorManager
        
        auth_manager = RequestAuthenticatorManager()
        headers = {
            "X-Customer-Id": "customer123",
            "authorization": "client-key "
        }
        result = auth_manager.authenticate(headers)
        
        assert result.is_authorized is False
        assert "Invalid or missing client key" in result.error

    def test_whitespace_customer_id(self):
        """Test that customer ID with whitespace is properly handled"""
        from quepasa.common.bearer import RequestAuthenticatorManager
        
        auth_manager = RequestAuthenticatorManager()
        headers = {
            "X-Customer-Id": "  customer123  ",
            "authorization": "client-key secret-key-123"
        }
        result = auth_manager.authenticate(headers)
        
        assert result.is_authorized is True
        assert result.client_id == "customer123"  # Should be stripped

# Test ProductsHandler class
class TestProductsHandler:
    """Test ProductsHandler methods"""

    @pytest.mark.asyncio
    async def test_ingest_products_success(self, products_handler, sample_product,
                                           mock_batch_utils, mock_products_to_documents):
        """Test successful product ingestion"""
        request = ProductIngestionRequest(
            records=[sample_product]
        )

        result = await products_handler.ingest_products("customer123", "test_collection", request)

        # Verify BatchResult
        assert isinstance(result, BatchResult)
        assert result.batch_id == "test-batch-id-123"
        assert result.processed_ids == ["SKU123"]

        # Verify products_to_documents was called
        mock_products_to_documents.assert_called_once_with([sample_product])

        # Verify batch creation
        mock_batch_utils.create_batch.assert_called_once()
        call_args = mock_batch_utils.create_batch.call_args[0]
        assert call_args[0] == "customer123"  # customer_id
        assert call_args[1] == BatchState.BACKLOG
        batch_data = call_args[2]
        assert batch_data["client_id"] == "customer123"
        assert batch_data["domain"] == "customer123_test_collection"
        assert batch_data["action"] == BatchAction.UPSERT.value

        # Verify task creation
        mock_batch_utils.add_task.assert_called_once_with(
            "data-processor", "customer123", "test-batch-id-123"
        )

    @pytest.mark.asyncio
    async def test_ingest_products_empty_records(self, products_handler):
        """Test with empty records array"""
        request = ProductIngestionRequest(
            records=[]
        )

        with pytest.raises(ValueError) as exc_info:
            await products_handler.ingest_products("customer123", "test_collection", request)
        assert str(exc_info.value) == "No product records provided"

    @pytest.mark.asyncio
    async def test_ingest_products_exceeds_limit(self, products_handler):
        """Test with more than MAX_RECORDS"""
        # Create MAX_RECORDS + 1 products
        products = [{"id": f"SKU{i}", "title": f"Product {i}"} for i in range(MAX_RECORDS + 1)]

        # Pydantic will raise ValidationError when creating the request
        from pydantic import ValidationError
        with pytest.raises(ValidationError) as exc_info:
            ProductIngestionRequest(
                records=products
            )
        assert f"at most {MAX_RECORDS} items" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_ingest_products_exactly_max_records(self, products_handler,
                                                       mock_batch_utils, mock_products_to_documents):
        """Test with exactly MAX_RECORDS (boundary condition)"""
        from quepasa.api.handlers.document_handler import Document
        # Create exactly MAX_RECORDS products
        products = [{"id": f"SKU{i}", "title": f"Product {i}"} for i in range(MAX_RECORDS)]

        # Mock products_to_documents to return corresponding document dicts
        mock_documents = [
            {
                "id": f"SKU{i}",
                "url": f"http://example.com/product/{i}",
                "title": f"Product {i}",
                "sku": f"SKU{i}",
                "price_from": None,
                "price_to": None,
                "metadata": "{}",
                "chunks": [{"language": "en", "text": f"Product {i}"}]
            }
            for i in range(MAX_RECORDS)
        ]
        mock_products_to_documents.return_value = mock_documents

        request = ProductIngestionRequest(
            records=products
        )

        result = await products_handler.ingest_products("customer123", "test_collection", request)

        assert isinstance(result, BatchResult)
        assert len(result.processed_ids) == MAX_RECORDS
        assert result.processed_ids[0] == "SKU0"
        assert result.processed_ids[MAX_RECORDS - 1] == "SKU999"

    @pytest.mark.asyncio
    async def test_ingest_products_no_valid_products(self, products_handler, mock_products_to_documents):
        """Test when products_to_documents returns no valid products"""
        mock_products_to_documents.return_value = []

        request = ProductIngestionRequest(
            records=[{"invalid": "product"}]
        )

        with pytest.raises(ValueError) as exc_info:
            await products_handler.ingest_products("customer123", "test_collection", request)
        assert str(exc_info.value) == "No valid products could be processed"

    @pytest.mark.asyncio
    async def test_ingest_products_markdown_exception(self, products_handler, mock_products_to_documents):
        """Test when products_to_documents throws an exception"""
        mock_products_to_documents.side_effect = Exception("Markdown conversion failed")

        request = ProductIngestionRequest(
            records=[{"id": "SKU123", "title": "Test"}]
        )

        with pytest.raises(Exception) as exc_info:
            await products_handler.ingest_products("customer123", "test_collection", request)
        assert str(exc_info.value) == "Markdown conversion failed"

    @pytest.mark.asyncio
    async def test_ingest_products_batch_creation_failure(self, products_handler,
                                                          mock_batch_utils, mock_products_to_documents):
        """Test when BatchUtils.create_batch fails"""
        mock_batch_utils.create_batch.side_effect = Exception("Batch creation failed")

        request = ProductIngestionRequest(
            records=[{"id": "SKU123", "title": "Test"}]
        )

        with pytest.raises(Exception) as exc_info:
            await products_handler.ingest_products("customer123", "test_collection", request)
        assert str(exc_info.value) == "Batch creation failed"

    @pytest.mark.asyncio
    async def test_ingest_products_task_creation_failure(self, products_handler,
                                                         mock_batch_utils, mock_products_to_documents):
        """Test when BatchUtils.add_task fails"""
        mock_batch_utils.add_task.side_effect = Exception("Task creation failed")

        request = ProductIngestionRequest(
            records=[{"id": "SKU123", "title": "Test"}]
        )

        with pytest.raises(Exception) as exc_info:
            await products_handler.ingest_products("customer123", "test_collection", request)
        assert str(exc_info.value) == "Task creation failed"

    @pytest.mark.asyncio
    async def test_ingest_products_with_invalid_product_data(self, products_handler,
                                                             mock_batch_utils, mock_products_to_documents):
        """Test with products missing required fields"""
        # Mock products_to_documents to skip invalid products
        mock_products_to_documents.return_value = [
            {
                "id": "SKU456",
                "url": "http://example.com/product/456",
                "title": "Valid Product",
                "sku": "SKU456",
                "price_from": None,
                "price_to": None,
                "metadata": "{}",
                "chunks": [{"language": "en", "text": "Valid"}]
            }
        ]

        request = ProductIngestionRequest(
            records=[
                {"title": "No ID Product"},  # Missing id
                {"id": "SKU456", "title": "Valid Product"}  # Valid
            ]
        )

        result = await products_handler.ingest_products("customer123", "test_collection", request)

        # Only the valid product should be processed
        assert result.processed_ids == ["SKU456"]
        mock_products_to_documents.assert_called_once_with(request.records)

    @pytest.mark.asyncio
    async def test_domain_name_construction(self, products_handler,
                                            mock_batch_utils, mock_products_to_documents):
        """Test that domain name is correctly constructed"""
        request = ProductIngestionRequest(
            records=[{"id": "SKU123", "title": "Test"}]
        )

        await products_handler.ingest_products("customer_abc", "my_products", request)

        # Check the domain in batch_data
        call_args = mock_batch_utils.create_batch.call_args[0]
        batch_data = call_args[2]
        assert batch_data["domain"] == "customer_abc_my_products"


# Test API endpoint
class TestProductsEndpoint:
    """Test the products ingestion endpoint"""

    @pytest.mark.asyncio
    async def test_endpoint_success(self, test_client, mock_batch_utils, mock_products_to_documents):
        """Test successful API call"""
        response = await test_client.post(
            "/data/v1/products/test_collection",
            json={
                "records": [{"id": "SKU123", "title": "Test Product"}]
            }
        )

        assert response.status_code == 200
        data = response.json()
        assert data["batch_id"] == "test-batch-id-123"
        assert data["processed_ids"] == ["SKU123"]

    @pytest.mark.asyncio
    async def test_endpoint_missing_customer_id(self, invalid_auth_client):
        """Test API call with missing X-Customer-Id header"""
        response = await invalid_auth_client.post(
            "/data/v1/products/test_collection",
            json={
                "records": [{"id": "SKU123", "title": "Test Product"}]
            },
            headers={
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 401
        assert "Invalid Authorization header format" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_endpoint_missing_authorization(self, invalid_auth_client):
        """Test API call with missing Authorization header"""
        response = await invalid_auth_client.post(
            "/data/v1/products/test_collection",
            json={
                "records": [{"id": "SKU123", "title": "Test Product"}]
            },
            headers={
                "X-Customer-Id": "customer123"
            }
        )

        assert response.status_code == 401
        assert "Missing Authorization header" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_endpoint_invalid_authorization_format(self, invalid_auth_client):
        """Test API call with invalid Authorization format"""
        response = await invalid_auth_client.post(
            "/data/v1/products/test_collection",
            json={
                "records": [{"id": "SKU123", "title": "Test Product"}]
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "Bearer token123"
            }
        )

        assert response.status_code == 401
        assert "Invalid Authorization header format" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_endpoint_empty_records(self, test_client):
        """Test API call with empty records"""
        response = await test_client.post(
            "/data/v1/products/test_collection",
            json={
                "records": []
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 400
        assert "No product records provided" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_endpoint_exceeds_limit(self, test_client):
        """Test API call with more than MAX_RECORDS"""
        products = [{"id": f"SKU{i}", "title": f"Product {i}"} for i in range(MAX_RECORDS + 1)]

        response = await test_client.post(
            "/data/v1/products/test_collection",
            json={
                "records": products
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        # Pydantic validation should catch this
        assert response.status_code == 422
        assert f"at most {MAX_RECORDS} items" in str(response.json()["detail"])

    @pytest.mark.asyncio
    async def test_endpoint_invalid_json(self, test_client):
        """Test API call with invalid JSON payload"""
        response = await test_client.post(
            "/data/v1/products/test_collection",
            data="invalid json{",
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123",
                "Content-Type": "application/json"
            }
        )

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_endpoint_missing_collection(self, test_client):
        """Test API call without collection in path - should be a 404"""
        response = await test_client.post(
            "/data/v1/products/",
            json={
                "records": [{"id": "SKU123", "title": "Test Product"}]
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_endpoint_products_to_documents_exception(self, test_client, mock_products_to_documents):
        """Test API call when products_to_documents throws exception"""
        mock_products_to_documents.side_effect = Exception("Conversion error")

        response = await test_client.post(
            "/data/v1/products/test_collection",
            json={
                "records": [{"id": "SKU123", "title": "Test Product"}]
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 500
        assert "Conversion error" in response.json()["detail"]


# Test Deletion endpoints
def verify_batch_args(call_args):
    assert call_args[0] == "customer123"  # customer_id
    assert call_args[1] == BatchState.BACKLOG
    batch_data = call_args[2]
    assert batch_data["client_id"] == "customer123"
    assert batch_data["domain"] == "customer123_test_collection"
    assert batch_data["action"] == BatchAction.DELETE.value
    return batch_data


class TestProductDeletion:
    """Test product deletion functionality"""

    @pytest.mark.asyncio
    async def test_delete_single_product_success(self, products_handler, mock_batch_utils):
        """Test successful single product deletion"""
        result = await products_handler.delete_product("customer123", "test_collection", "SKU123")

        # Verify BatchResult
        assert isinstance(result, BatchResult)
        assert result.batch_id == "test-batch-id-123"
        assert result.processed_ids == ["SKU123"]

        # Verify batch creation
        mock_batch_utils.create_batch.assert_called_once()
        call_args = mock_batch_utils.create_batch.call_args[0]
        batch_data = verify_batch_args(call_args)
        assert batch_data["documents"] == [{"id": "SKU123"}]

        # Verify task creation
        mock_batch_utils.add_task.assert_called_once_with(
            "data-processor", "customer123", "test-batch-id-123"
        )

    @pytest.mark.asyncio
    async def test_delete_multiple_products_success(self, products_handler, mock_batch_utils):
        """Test successful batch product deletion"""
        request = ProductDeletionRequest(
            productIds=["SKU123", "SKU456", "SKU789"]
        )

        result = await products_handler.delete_products("customer123", "test_collection", request)

        # Verify BatchResult
        assert isinstance(result, BatchResult)
        assert result.batch_id == "test-batch-id-123"
        assert result.processed_ids == ["SKU123", "SKU456", "SKU789"]

        # Verify batch creation
        mock_batch_utils.create_batch.assert_called_once()
        batch_data = verify_batch_args(mock_batch_utils.create_batch.call_args[0])
        assert batch_data["documents"] == [
            {"id": "SKU123"},
            {"id": "SKU456"},
            {"id": "SKU789"}
        ]

        # Verify task creation
        mock_batch_utils.add_task.assert_called_once_with(
            "data-processor", "customer123", "test-batch-id-123"
        )

    @pytest.mark.asyncio
    async def test_delete_products_empty_list(self, products_handler):
        """Test deletion with empty product IDs list"""
        request = ProductDeletionRequest(
            productIds=[]
        )

        with pytest.raises(ValueError) as exc_info:
            await products_handler.delete_products("customer123", "test_collection", request)
        assert str(exc_info.value) == "No product IDs provided for deletion"

    @pytest.mark.asyncio
    async def test_delete_products_exceeds_limit(self, products_handler):
        """Test deletion with more than MAX_RECORDS"""
        # Create 1001 product IDs
        product_ids = [f"SKU{i}" for i in range(MAX_RECORDS + 1)]

        # Pydantic will raise ValidationError when creating the request
        from pydantic import ValidationError
        with pytest.raises(ValidationError) as exc_info:
            ProductDeletionRequest(
                productIds=product_ids
            )
        assert f"at most {MAX_RECORDS} items" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_delete_products_exactly_max_records(self, products_handler, mock_batch_utils):
        """Test deletion with exactly MAX_RECORDS (boundary condition)"""
        # Create exactly MAX_RECORDS product IDs
        product_ids = [f"SKU{i}" for i in range(MAX_RECORDS)]

        request = ProductDeletionRequest(
            productIds=product_ids
        )

        result = await products_handler.delete_products("customer123", "test_collection", request)

        assert isinstance(result, BatchResult)
        assert len(result.processed_ids) == MAX_RECORDS
        assert result.processed_ids[0] == "SKU0"
        assert result.processed_ids[MAX_RECORDS - 1] == "SKU999"

    @pytest.mark.asyncio
    async def test_delete_single_product_batch_creation_failure(self, products_handler, mock_batch_utils):
        """Test when BatchUtils.create_batch fails during single deletion"""
        mock_batch_utils.create_batch.side_effect = Exception("Batch creation failed")

        with pytest.raises(Exception) as exc_info:
            await products_handler.delete_product("customer123", "test_collection", "SKU123")
        assert str(exc_info.value) == "Batch creation failed"

    @pytest.mark.asyncio
    async def test_delete_products_batch_creation_failure(self, products_handler, mock_batch_utils):
        """Test when BatchUtils.create_batch fails during batch deletion"""
        mock_batch_utils.create_batch.side_effect = Exception("Batch creation failed")

        request = ProductDeletionRequest(
            productIds=["SKU123", "SKU456"]
        )

        with pytest.raises(Exception) as exc_info:
            await products_handler.delete_products("customer123", "test_collection", request)
        assert str(exc_info.value) == "Batch creation failed"


# Test deletion API endpoints
class TestDeletionEndpoints:
    """Test deletion API endpoints"""

    @pytest.mark.asyncio
    async def test_delete_single_product_endpoint_success(self, test_client, mock_batch_utils):
        """Test successful single product deletion via API"""
        response = await test_client.delete(
            "/data/v1/products/test_collection/SKU123",
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 200
        result = response.json()
        assert result["batch_id"] == "test-batch-id-123"
        assert result["processed_ids"] == ["SKU123"]

    @pytest.mark.asyncio
    async def test_delete_single_product_missing_collection(self, test_client):
        """Test single deletion with only one path segment - this actually matches /{collection} route for batch deletion"""
        response = await test_client.delete(
            "/data/v1/products/SKU123",
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 422  # Missing request body for batch deletion

    @pytest.mark.asyncio
    async def test_delete_single_product_missing_customer_id(self, invalid_auth_client):
        """Test single deletion without X-Customer-Id header"""
        response = await invalid_auth_client.delete(
            "/data/v1/products/test_collection/SKU123",
            headers={
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 401
        assert "Invalid Authorization header format" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_delete_single_product_missing_authorization(self, invalid_auth_client):
        """Test single deletion without Authorization header"""
        response = await invalid_auth_client.delete(
            "/data/v1/products/test_collection/SKU123",
            headers={
                "X-Customer-Id": "customer123"
            }
        )

        assert response.status_code == 401
        assert "Missing Authorization header" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_delete_single_product_invalid_authorization(self, invalid_auth_client):
        """Test single deletion with invalid Authorization format"""
        response = await invalid_auth_client.delete(
            "/data/v1/products/test_collection/SKU123",
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "Bearer token123"
            }
        )

        assert response.status_code == 401
        assert "Invalid Authorization header format" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_delete_batch_products_endpoint_success(self, test_client, mock_batch_utils):
        """Test successful batch product deletion via API"""
        response = await test_client.request(
            "DELETE",
            "/data/v1/products/test_collection",
            json={
                "productIds": ["SKU123", "SKU456", "SKU789"]
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 200
        result = response.json()
        assert result["batch_id"] == "test-batch-id-123"
        assert result["processed_ids"] == ["SKU123", "SKU456", "SKU789"]

    @pytest.mark.asyncio
    async def test_delete_batch_products_empty_list(self, test_client):
        """Test batch deletion with empty product IDs"""
        response = await test_client.request(
            "DELETE",
            "/data/v1/products/test_collection",
            json={
                "productIds": []
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 400
        assert "No product IDs provided for deletion" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_delete_batch_products_missing_collection(self, test_client):
        """Test batch deletion with base path only - should be 404 as no routes match /data/v1/products/ exactly"""
        response = await test_client.request(
            "DELETE",
            "/data/v1/products/",
            json={
                "productIds": ["SKU123", "SKU456"]
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_batch_products_missing_product_ids(self, test_client):
        """Test batch deletion without productIds field"""
        response = await test_client.request(
            "DELETE",
            "/data/v1/products/test_collection",
            json={},
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 422
        assert "Field required" in str(response.json()["detail"])

    @pytest.mark.asyncio
    async def test_delete_batch_products_exceeds_limit(self, test_client):
        """Test batch deletion with more than MAX_RECORDS"""
        product_ids = [f"SKU{i}" for i in range(MAX_RECORDS + 1)]

        response = await test_client.request(
            "DELETE",
            "/data/v1/products/test_collection",
            json={
                "productIds": product_ids
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        # Pydantic validation should catch this
        assert response.status_code == 422
        assert f"at most {MAX_RECORDS} items" in str(response.json()["detail"])

    @pytest.mark.asyncio
    async def test_delete_batch_products_invalid_json(self, test_client):
        """Test batch deletion with invalid JSON payload"""
        response = await test_client.request(
            "DELETE",
            "/data/v1/products/test_collection",
            content="invalid json{",
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123",
                "Content-Type": "application/json"
            }
        )

        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_delete_batch_products_batch_creation_failure(self, test_client, mock_batch_utils):
        """Test batch deletion when BatchUtils.create_batch fails"""
        mock_batch_utils.create_batch.side_effect = Exception("Batch creation failed")

        response = await test_client.request(
            "DELETE",
            "/data/v1/products/test_collection",
            json={
                "productIds": ["SKU123", "SKU456"]
            },
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 500
        assert "Batch creation failed" in response.json()["detail"]


class TestGetEndpoints:
    """Test GET endpoints functionality"""

    @pytest.mark.asyncio
    async def test_list_products_success(self, products_handler, mock_batch_utils):
        """Test successful product listing"""
        # Mock the underlying files.get_json method to return test data
        expected_products = {
            "SKU123": "Test Product 1",
            "SKU456": "Test Product 2"
        }
        test_data = {
            "customer123_test_collection": expected_products
        }
        products_handler.files.get_json = Mock(return_value=test_data)
        products_handler.files.exists = Mock(return_value=True)
        
        result = await products_handler.list_documents_by_domain("customer123", "customer123_test_collection")
        
        assert result == expected_products
        products_handler.files.get_json.assert_called_once()
        products_handler.files.exists.assert_called_once()

    @pytest.mark.asyncio
    async def test_list_products_empty_domain(self, products_handler, mock_batch_utils):
        """Test listing products from empty domain"""
        # Mock files to return empty domain
        products_handler.files.get_json = Mock(return_value={})
        products_handler.files.exists = Mock(return_value=True)
        
        result = await products_handler.list_documents_by_domain("customer123", "customer123_empty_collection")
        
        assert result == {}
        products_handler.files.exists.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_product_success(self, products_handler, mock_batch_utils):
        """Test successful product retrieval"""
        expected_product = {
            "id": "SKU123",
            "title": "Test Product",
            "url": "http://example.com/SKU123",
            "chunks": [{"language": "en", "text": "Test content"}]
        }
        # Mock the underlying file operations
        products_handler.files.exists = Mock(return_value=True)
        products_handler.files.get_json_zlib = Mock(return_value=expected_product)
        
        result = await products_handler.get_document("customer123", "customer123_test_collection", "SKU123")
        
        assert result == expected_product
        products_handler.files.exists.assert_called_once()
        products_handler.files.get_json_zlib.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_product_not_found(self, products_handler, mock_batch_utils):
        """Test retrieving non-existent product"""
        # Mock file doesn't exist
        products_handler.files.exists = Mock(return_value=False)
        
        result = await products_handler.get_document("customer123", "customer123_test_collection", "nonexistent")
        
        assert result is None
        products_handler.files.exists.assert_called_once()


class TestGetEndpointsAPI:
    """Test GET endpoints via API"""

    @pytest.mark.asyncio
    async def test_list_products_endpoint_success(self, test_client, mock_batch_utils):
        """Test successful list products API call"""
        # Mock the handler's list_documents_by_domain method
        expected_products = {
            "SKU123": "Test Product 1",
            "SKU456": "Test Product 2"
        }
        
        with patch.object(DocumentHandler, 'list_documents_by_domain', return_value=expected_products):
            response = await test_client.get(
                "/data/v1/products/test_collection",
                headers={
                    "X-Customer-Id": "customer123",
                    "Authorization": "client-key secret-key-123"
                }
            )

            assert response.status_code == 200
            result = response.json()
            assert result == expected_products

    @pytest.mark.asyncio
    async def test_list_products_endpoint_empty(self, test_client, mock_batch_utils):
        """Test list products API call with empty results"""
        with patch.object(DocumentHandler, 'list_documents_by_domain', return_value={}):
            response = await test_client.get(
                "/data/v1/products/empty_collection",
                headers={
                    "X-Customer-Id": "customer123",
                    "Authorization": "client-key secret-key-123"
                }
            )

            assert response.status_code == 200
            result = response.json()
            assert result == {}

    @pytest.mark.asyncio
    async def test_list_products_missing_collection(self, test_client):
        """Test list products with base path only - should be 404 as no routes match /data/v1/products/ exactly"""
        response = await test_client.get(
            "/data/v1/products/",
            headers={
                "X-Customer-Id": "customer123",
                "Authorization": "client-key secret-key-123"
            }
        )

        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_products_handler_exception(self, test_client, mock_batch_utils):
        """Test list products when handler raises exception"""
        with patch.object(DocumentHandler, 'list_documents_by_domain', side_effect=Exception("Storage error")):
            response = await test_client.get(
                "/data/v1/products/test_collection",
                headers={
                    "X-Customer-Id": "customer123",
                    "Authorization": "client-key secret-key-123"
                }
            )

            assert response.status_code == 500
            assert "Storage error" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_get_product_success(self, test_client, mock_batch_utils):
        """Test successful get product API call"""
        expected_product = {
            "id": "SKU123",
            "title": "Test Product",
            "url": "http://example.com/SKU123",
            "chunks": [{"language": "en", "text": "Test content"}]
        }
        
        with patch.object(DocumentHandler, 'get_document', return_value=expected_product):
            response = await test_client.get(
                "/data/v1/products/test_collection/SKU123",
                headers={
                    "X-Customer-Id": "customer123",
                    "Authorization": "client-key secret-key-123"
                }
            )

            assert response.status_code == 200
            result = response.json()
            assert result == expected_product

    @pytest.mark.asyncio
    async def test_get_product_not_found(self, test_client, mock_batch_utils):
        """Test get product API call for non-existent product"""
        with patch.object(DocumentHandler, 'get_document', return_value=None):
            response = await test_client.get(
                "/data/v1/products/test_collection/nonexistent",
                headers={
                    "X-Customer-Id": "customer123",
                    "Authorization": "client-key secret-key-123"
                }
            )

            assert response.status_code == 200
            result = response.json()
            assert result is None

    @pytest.mark.asyncio
    async def test_get_product_missing_collection(self, test_client):
        """Test get product with only one path segment - this actually matches /{collection} route for list products"""
        # This will try to list products from collection "SKU123"
        with patch.object(DocumentHandler, 'list_documents_by_domain', return_value={}):
            response = await test_client.get(
                "/data/v1/products/SKU123",
                headers={
                    "X-Customer-Id": "customer123",
                    "Authorization": "client-key secret-key-123"
                }
            )

        assert response.status_code == 200  # Successfully lists products from collection "SKU123"

    @pytest.mark.asyncio
    async def test_get_product_handler_exception(self, test_client, mock_batch_utils):
        """Test get product when handler raises exception"""
        with patch.object(DocumentHandler, 'get_document', side_effect=Exception("Storage error")):
            response = await test_client.get(
                "/data/v1/products/test_collection/SKU123",
                headers={
                    "X-Customer-Id": "customer123",
                    "Authorization": "client-key secret-key-123"
                }
            )

            assert response.status_code == 500
            assert "Storage error" in response.json()["detail"]
