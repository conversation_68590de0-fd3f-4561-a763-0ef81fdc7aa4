#!/usr/bin/env python3

import os
import sys
import time
import argparse
from pathlib import Path
import tempfile
import json

# Add project to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import whisper utils
from src.lib.whisper_utils import get_segments, get_cached_segments
from src.lib.whisper.base import WhisperModelType

def read_audio_file(file_path):
    """Read audio/video file as bytes."""
    with open(file_path, 'rb') as f:
        return f.read()

def format_time(seconds):
    """Format seconds as mm:ss."""
    minutes, seconds = divmod(seconds, 60)
    return f"{int(minutes):02d}:{seconds:05.2f}"

def test_whisper(file_path, model_type="default", language=None, use_cache=True):
    """Test whisper transcription on a file."""
    print(f"\n{'='*80}")
    print(f"Testing Whisper Transcription")
    print(f"{'='*80}")
    print(f"File: {file_path}")
    print(f"Model: {model_type}")
    print(f"Language: {language or 'auto-detect'}")
    print(f"Using cache: {use_cache}")
    print(f"{'-'*80}")
    
    # Check API key
    api_key = os.getenv("REPLICATE_API_KEY")
    if not api_key:
        print("Error: REPLICATE_API_KEY environment variable must be set")
        return
    
    # Ensure file exists
    file_path = Path(file_path)
    if not file_path.exists():
        print(f"Error: File not found: {file_path}")
        return
    
    try:
        # Read file content
        print(f"Reading file: {file_path}...")
        audio_data = read_audio_file(file_path)
        print(f"File size: {len(audio_data)/1024/1024:.2f} MB")
        
        # Create meta dictionary
        meta = {
            'language': language,
            'extension': file_path.suffix
        }

        # Start timing
        start_time = time.time()
        
        # Get transcription
        print(f"Getting transcription...")
        if use_cache:
            result = get_cached_segments("replicate", model_type, audio_data, meta)
        else:
            result = get_segments("replicate", model_type, audio_data, meta)
        
        # Measure elapsed time
        elapsed = time.time() - start_time
        print(f"Transcription completed in {elapsed:.2f} seconds")
        
        # Check for success
        if not result.get("success", False):
            print(f"Error: {result.get('error', 'Unknown error')}")
            return
        
        # Print results
        print(f"\nTranscription Results:")
        print(f"{'-'*80}")
        
        full_text = result.get("text", "")
        print(f"Full text: {len(full_text)} characters")
        preview_len = min(200, len(full_text))
        print(f"Preview: {full_text[:preview_len]}{'...' if len(full_text) > preview_len else ''}")
        
        # Print segments
        segments = result.get("segments", [])
        print(f"\nSegments: {len(segments)}")
        
        if segments:
            # Print up to 5 segments
            for i, segment in enumerate(segments[:5]):
                start = segment.get("start", 0)
                end = segment.get("end", 0)
                speaker = segment.get("speaker", "")
                text = segment.get("text", "")
                
                print(f"\n[{format_time(start)} - {format_time(end)}] {speaker}")
                print(f"  {text}")
            
            if len(segments) > 5:
                print(f"\n... and {len(segments) - 5} more segments")
        
        # Save results to a temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            json.dump(result, tmp, indent=2)
            print(f"\nFull results saved to: {tmp.name}")
    
    except Exception as e:
        import traceback
        print(f"Error: {str(e)}")
        traceback.print_exc()

def main():
    parser = argparse.ArgumentParser(description="Test Whisper transcription")
    parser.add_argument("file", help="Path to audio or video file")
    parser.add_argument("--model", choices=["default", "diarization"], default="default", 
                        help="Model type to use (default or diarization)")
    parser.add_argument("--language", help="Language code (e.g., 'en', 'ru')")
    parser.add_argument("--no-cache", action="store_true", help="Disable cache")
    
    args = parser.parse_args()
    
    test_whisper(args.file, args.model, args.language, not args.no_cache)

if __name__ == "__main__":
    main() 