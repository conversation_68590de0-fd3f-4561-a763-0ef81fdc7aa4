DEPLOYMENT_STAGE_DEV = 'dev'
DEPLOYMENT_STAGE_DEMO = 'demo'
DEPLOYMENT_STAGE_PROD = 'prod'

API_USER_RETRIEVE_ONLY_POSTFIX = ':retrieve-only'
API_USER_RETRIEVE_ONLY = 'system' + API_USER_RETRIEVE_ONLY_POSTFIX

DEFAULT_TOKENIZER_MODEL = "gpt-3.5-turbo"

DOCUMENT_TYPE_DOCUMENT = "document"
# DOCUMENT_TYPE_DOCUMENT = "webpage"
DOCUMENT_TYPE_DIALOG = "dialog"

KIND_TEXT = "text"
KIND_SUMMARY = "summary"
KIND_ALL = "all"

SOURCE_ALL       = "all"
SOURCE_WEB       = "web"
SOURCE_DOCUMENTS = "documents"
SOURCE_META_SEARCH = "meta_search"
SOURCE_SPD_SEARCH = "spd_search"
SOURCE_DIALOGS   = "dialogs"
SOURCE_WATERFALL = "waterfall"
SOURCE_GPT       = "gpt"
SOURCE_LIVEWIKI  = "livewiki"
SOURCE_AGENTIC   = "agentic"

SOURCE_EXTERNAL  = "external"

ANSWER_TYPE_STRICT   = "strict"
ANSWER_TYPE_WEAK     = "weak"
ANSWER_TYPE_NO_LINKS = "no-links"
ANSWER_TYPE_NONE     = "none"

ANSWER_INTENT_UNKNOWN      = "unknown"

# answer classes
ANSWER_INTENT_GENERAL      = "general"
ANSWER_INTENT_ARCHITECTURE = "architecture"
ANSWER_INTENT_ILLEGAL      = "illegal"
ANSWER_INTENT_TRANSLATE    = "translate"
ANSWER_INTENT_SEARCH       = "search"

ANSWER_GPT_INTENTS = [
    ANSWER_INTENT_GENERAL,
    ANSWER_INTENT_ARCHITECTURE,
    ANSWER_INTENT_TRANSLATE,
    ANSWER_INTENT_ILLEGAL,
]


ALL_ANSWER_GPT_INTENTS = [
    ANSWER_INTENT_GENERAL,
    ANSWER_INTENT_ARCHITECTURE,
    ANSWER_INTENT_ILLEGAL,
]

# factoids
FACTOID_KIND_TEXT = "text"
FACTOID_KIND_FACTOID = "factoid"
FACTOID_LEVEL_DOCUMENT = "document"
FACTOID_LEVEL_CHUNK = "chunk"

# protocols
HTTP_PROTOCOL = "http"
SSE_PROTOCOL = "sse"
WEBHOOK_PROTOCOL = "webhook"
TELEGRAM_PROTOCOL = "telegram"

# endpoints
SEARCH_ENDPOINT = "search"
ANSWER_ENDPOINT = "answer"
ANSWER_STREAM_ENDPOINT = "stream"
HISTORY_ENDPOINT = "history"

VALID_ENDPOINTS = [
    SEARCH_ENDPOINT,
    ANSWER_ENDPOINT,
    ANSWER_STREAM_ENDPOINT,
    HISTORY_ENDPOINT,
]

# headers
X_CUSTOMER_ID_HEADER = "X-Customer-Id"
AUTHORIZATION_HEADER = "Authorization"

# telegram
ANSWER_TYPE_STRICT = "strict"
ANSWER_TYPE_WEAK = "weak"
ANSWER_TYPE_NO_LINKS = "no_links"
ANSWER_TYPE_ERROR = "error"

AUTH_TYPE_BEARER = "bearer"
AUTH_TYPE_CLIENT_KEY = "client-key"
AUTH_TYPE_EXTERNAL = "external"

VALID_PROTOCOLS = [
    HTTP_PROTOCOL,
    SSE_PROTOCOL,
    WEBHOOK_PROTOCOL,
    TELEGRAM_PROTOCOL,
]

VALID_SOURCES = [
    SOURCE_WEB,
    SOURCE_DOCUMENTS,
    SOURCE_DIALOGS,
    SOURCE_WATERFALL,
]
