import os
import time
import random
from functools import wraps

import numpy as np
from typing import Dict, Any, List, Union, Tuple, Optional
from datetime import datetime, timezone
from elasticsearch import Elasticsearch, helpers
from celery import chain, group
# Will leave this here for now, but it's not used
# from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
# import threading

from src.lib.files import QuepasaFiles
from src.lib.logger import <PERSON><PERSON>aLog<PERSON>
from configuration.main.default import QuepasaConfigurationHub
from src.lib.constants import (
    DOCUMENT_TYPE_DOCUMENT,
    FACTOID_KIND_TEXT,
    FACTOID_LEVEL_DOCUMENT,
    FACTOID_LEVEL_CHUNK
)
from src.lib.batch_utils import (
    BatchStorage,
    BatchUtils,
    BatchState,
    DataProcessorAction,
    INDEXER_STORAGE
)
from src.lib.utils import ALLOWED_LANGUAGES
from src.lib.utils import get_elasticsearch_config
from src.lib.embedding.providers import EmbeddingProvider
from src.lib.gateway import get_embedding_from_gateway, get_llm_answer_from_gateway
from src.lib.embedding_service import get_embedding_from_service
from src.lib.embedding_utils import get_cached_embedding
from src.lib.llm_utils import get_cached_llm_answer
from src.lib.utils import INDEX_NAME_PREFIX, get_tokenizer

# Import celery app and the container executor
from src.lib.celery_app import app


# Initialize shared resources
qp_files = QuepasaFiles()

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize Elasticsearch
es = Elasticsearch(**get_elasticsearch_config()) 

# Constants
PROVIDER_API_V2 = "api-v2"


class IndexerHelper:
    """Helper class for indexer operations"""
    def __init__(self, client_id: str):
        self.client_id = client_id
        self.config = QuepasaConfigurationHub.from_client_code(client_id)

    def get_index_name(self) -> str:
        """Get Elasticsearch index name for the client"""
        return f"{INDEX_NAME_PREFIX}-{self.config.get_index_name()}"

    def get_embeddings_config(self) -> List[str]:
        """Get embedding configurations for the client"""
        embeddings = []
        for doc_type in [DOCUMENT_TYPE_DOCUMENT]:
            for provider, model_version in self.config.get_embedding_model_versions(doc_type):
                embedding_field = f"embedding__{model_version.replace('/', '__')}"
                if (provider, model_version, embedding_field) not in embeddings:
                    embeddings.append((provider, model_version, embedding_field))
        return embeddings
    
    def get_llm_summary_model_name(self, domain: Optional[str] = None) -> Tuple[str, str]:
        """Get LLM summary model name"""
        return self.config.get_llm_summary_model_name(domain)

# Add a retry decorator for external API calls
def retry_with_backoff(max_retries=10, initial_backoff=1, backoff_factor=2):
    """Retry decorator with exponential backoff"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            current_backoff = initial_backoff
            
            while True:
                try:
                    return func(*args, **kwargs)
                
                except Exception as e:
                    retries += 1
                    if retries > max_retries:
                        raise  # Re-raise the last exception when retries exhausted
                    
                    # Add jitter to avoid thundering herd
                    jitter = random.uniform(0, 0.1 * current_backoff)
                    sleep_time = current_backoff + jitter
                    
                    # Log the retry
                    logger.warning(f"Retrying {func.__name__} after error: {str(e)}. "
                                  f"Retry {retries}/{max_retries}, sleeping for {sleep_time:.2f}s")
                    
                    time.sleep(sleep_time)
                    current_backoff *= backoff_factor
        return wrapper
    return decorator

# Create retryable versions of our external API calls
QUEPASA_LLM_GATEWAY_API_KEY = os.environ.get("QUEPASA_LLM_GATEWAY_API_KEY")

@retry_with_backoff(max_retries=10, initial_backoff=2, backoff_factor=2)
def get_embedding_with_retry(provider, model_version, text):
    """Get embedding with retry logic"""
    text_vector = None 
    if QUEPASA_LLM_GATEWAY_API_KEY:
        text_vector = get_embedding_from_gateway(provider, model_version, text)
    elif provider in [EmbeddingProvider.SBERT]:
        text_vector = get_embedding_from_service(provider, model_version, text) 
    else:
        text_vector = get_cached_embedding(provider, model_version, text)
    text_vector_float = [float(f) for f in text_vector]
    return text_vector_float

@retry_with_backoff(max_retries=10, initial_backoff=2, backoff_factor=2)
def get_llm_answer_with_retry(provider, model_name, messages, max_tokens):
    """Get LLM answer with retry logic"""
    if QUEPASA_LLM_GATEWAY_API_KEY:
        return get_llm_answer_from_gateway(provider, model_name, messages, max_tokens)
    else:
        return get_cached_llm_answer(provider, model_name, messages, max_tokens)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='indexer', 
    name='indexer.process_batch',
    time_limit=14400,  # 4 hours
    soft_time_limit=14300  # 3 hours 55 minutes
)
def process_batch(self, client_id: str, batch_id: str, refresh: bool = False) -> Dict:
    """Process documents in a batch"""
    batch_file = BatchUtils.get_batch_filename(client_id, batch_id, BatchState.IN_PROGRESS)
    if not qp_files.exists(batch_file):
        logger.error(f"[{client_id}, {batch_id}] Batch file {batch_file} does not exist")
        return {
            'status': 'error',
            'message': f"Batch file {batch_file} does not exist"
        }
    

    try:
        batch = qp_files.get_json_zlib(batch_file) 
        if 'domain' not in batch:
            raise ValueError("'domain' is required in batch")
        
        if 'action' not in batch:
            raise ValueError("'action' is required in batch")
        
        action = batch['action']
        domain = batch['domain']
        
        logger.info(f"[{client_id}, {batch_id}] Running process_batch for domain={domain}, action={action}")

        # Collect files to process
        upsert_files = []
        remove_ids = []

        if refresh:
            # Get all documents from the data processor storage
            data_dir = BatchUtils.get_storage_dir(BatchStorage.DATA_PROCESSOR, client_id, domain)
            for file in qp_files.get_files(data_dir, True):
                if ".zlib.json" in file:
                    upsert_files.append(file)
                elif ".meta.json" in file:
                    doc_meta = qp_files.get_json(file)
                    if doc_meta['status'] == "removed":
                        remove_ids.append(doc_meta['id'])

        else:
            # Get document ids from the batch
            data_dir = BatchUtils.get_storage_dir(BatchStorage.DATA_PROCESSOR, client_id, domain)
            for action, doc_ids in batch['changes'].items():
                if action == DataProcessorAction.UPSERT:
                    for doc_id in doc_ids:
                        file, _ = BatchUtils.get_document_filenames(client_id, domain, doc_id)
                        upsert_files.append(file)
                elif action == DataProcessorAction.DELETE:
                    remove_ids.extend(doc_ids)

        # Create task chains
        remove_batch_size = 100
        upsert_batch_size = 10
        workflow = chain(
            group([
                # Remove documents in chunks
                group([
                    remove_documents.si(client_id, batch_id, domain, chunk)
                    for chunk in [remove_ids[i:i + remove_batch_size] for i in range(0, len(remove_ids), remove_batch_size)]
                ]),

                # Process documents in chunks
                group([
                    group([upsert_document.si(client_id, batch_id, domain, file) for file in chunk])
                    for chunk in [upsert_files[i:i + upsert_batch_size] for i in range(0, len(upsert_files), upsert_batch_size)]
                ]),
            ]),

            # Save batch result
            save_results.s(client_id, batch_id, domain, action),

            # Wait for ES operations to complete and then update document lists
            update_document_lists.s(client_id, batch_id)
        )
        async_result = workflow.apply_async()
        
        return {
            'client_id': client_id,
            'task_id': async_result.id,
            'status': 'pending',
            'started_at': datetime.now(timezone.utc).isoformat(),
            'stats': {
                'total_tasks': 1,
                'processed_tasks': 0
            }
        }

    except Exception as e:
        logger.error(f"[{client_id}, {batch_id}] Failed to process batch, domain={domain}, action={action}, error: {str(e)}")
        raise self.retry(exc=e, countdown=3)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='indexer', 
    name='indexer.upsert_document',
    time_limit=14400,  # 4 hours
    soft_time_limit=14300  # 3 hours 55 minutes
)
def upsert_document(self, client_id: str, batch_id: str, domain: str, document_file: str) -> Tuple[List[Dict], str]:
    """Process a single document for indexing"""
    helper = IndexerHelper(client_id)
    documents = []
    
    # Process document
    try:
        logger.info(f"[{client_id}, {batch_id}] Processing document: {document_file}")        
        embeddings = helper.get_embeddings_config()

        # Load document
        logger.info(f"[{client_id}, {batch_id}] Before processing document: {document_file}")
        doc = qp_files.get_json_zlib(document_file)
        if not doc:
            return []
        
        logger.info(f"[{client_id}, {batch_id}] After reading document: {document_file}")
        root_id = f"{client_id}:{domain}:{doc['id']}"
        doc_embeddings = {v: [] for _, _, v in embeddings}

        # Create main document
        text_for_summary = ""
        for chunk in doc['chunks']:
            text_for_summary += chunk.get('text', '').strip() + "\n"
            if len(text_for_summary) > 4000:
                break

        # Ensure access_tags is a list and limit to 5000 tags
        access_tags = []
        if (
            'access' in doc
            and doc['access'] != None
            and isinstance(doc['access'], dict)
        ):
            if 'user_ids' in doc['access']:
                for user_id in doc['access']['user_ids']:
                    access_tags.append(f"user:{user_id}")
            if 'groups' in doc['access']:
                for group in doc['access']['groups']:
                    access_tags.append(f"group:{group}")
            if 'roles' in doc['access']:
                for role in doc['access']['roles']:
                    access_tags.append(f"role:{role}")
            access_tags = access_tags[:5000]

        # Create title and summary
        language_name = ALLOWED_LANGUAGES[doc['languages'][0]]
        provider, model_name = helper.get_llm_summary_model_name(domain)

        prompt = f"Generate a brief summary in {language_name} (4-5 sentences) of this text:\n\n{text_for_summary[:4000]}..."
        document_summary = get_llm_answer_with_retry(provider, model_name, [{'role': 'user', 'content': prompt}], 1000)

        url = doc.get('url', '').strip()
        doc_title = doc.get('title', '').strip()
        if (
            url.startswith("file://")
            or url.endswith(".pdf")
            or url.endswith(".docx")
            or url.endswith(".doc")
            or url.endswith(".xls")
            or url.endswith(".xlsx")
            or url.endswith(".ppt")
            or url.endswith(".pptx")
            or url.endswith(".csv")
            or url.endswith(".htm")
            or url.endswith(".html")
            or url.endswith(".md")
            or url.endswith(".markdown")
            or url.endswith(".txt")
        ):
            original_filename = url.split("/")[-1]
            prompt = f"""
Generate a title in {language_name} for the document file:
- Original title: {doc_title}
- Original filename: {original_filename}
- Document summary: {document_summary}

Title should be 1-2 sentences, maximum 100 characters.
            """.strip()
            doc_title = get_llm_answer_with_retry(provider, model_name, [{'role': 'user', 'content': prompt}], 500)

        logger.info(f"[{client_id}, {batch_id}] After generating title and summary: {document_file}")
        
        # Will leave this here for now, but it's not used
        """
        # Thread-safe access to doc_embeddings
        embedding_lock = threading.Lock()
        """
        
        # Helper function to process a single chunk
        def process_chunk(
            embeddings: List[Tuple[str, str, str]],
            client_id: str, 
            domain: str, 
            access_tags: List[str], 
            root_id: str, 
            doc_title: str, 
            created_at: str,
            updated_at: str,
            chunk_data: Tuple[int, Dict, int]
        ):
            i, chunk, start_pos = chunk_data
            chunk_id = f"{root_id}:{i}"
            
            # Prepare text for embedding
            article_texts = []
            if doc_title:
                article_texts.append(f"Subject: {doc_title}")
            
            page_text = chunk.get('text', '').strip()
            if page_text:
                article_texts.append(f"Body:\n{page_text}")
            
            article_text = "\n\n".join(article_texts)
            tokens = len(get_tokenizer().encode(article_text))
            
            # Create page document
            document_page = {
                'root_id': root_id,
                'id': chunk_id,
                'chunk_index': i,
                'client': client_id,
                'domain': domain,
                'access_tags': access_tags,
                'provider': PROVIDER_API_V2,
                'type': DOCUMENT_TYPE_DOCUMENT,
                'kind': FACTOID_KIND_TEXT,
                'level': FACTOID_LEVEL_CHUNK,
                'url': doc['url'],
                'language': [chunk['language']],
                'title': doc_title,
                'keywords': chunk.get('keywords', ''),
                'text': page_text,
                'tokens': tokens,
                'chunks': 1,
                'start_position': start_pos,
                'end_position': start_pos + len(chunk.get('text', '')),
                'created_at': created_at,
                'updated_at': updated_at,
            }
            
            # Generate embeddings with retry
            chunk_embeddings = {}
            for provider, model_version, embedding_field in embeddings:
                text_vector_float = get_embedding_with_retry(provider, model_version, article_text)
                document_page[embedding_field] = text_vector_float
                chunk_embeddings[embedding_field] = np.array(text_vector_float)
            
            return document_page, chunk_embeddings, len(page_text)
        
        # Prepare chunk data with start positions
        start_position = 0
        chunk_data = []
        for i, chunk in enumerate(doc['chunks']):
            chunk_data.append((i, chunk, start_position))
            start_position += len(chunk.get('text', '').strip())
        
        # Process chunks based on document size
        processed_pages = []
        
        # Will leave this here for now, but it's not used
        """
        # For large documents, use a thread pool with more workers
        max_workers = min(4, len(doc['chunks']))  # Cap at 8 workers to avoid too much overhead
        logger.info(f"[{client_id}, {batch_id}] Processing large document with {len(doc['chunks'])} chunks using {max_workers} workers")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_chunk, data): data[0] for data in chunk_data}
            
            for future in as_completed(futures):
                try:
                    document_page, chunk_embeddings, _ = future.result()
                    processed_pages.append((futures[future], document_page))
                    
                    # Thread-safely update doc_embeddings
                    with embedding_lock:
                        for field, vector in chunk_embeddings.items():
                            doc_embeddings[field].append(vector)

                except Exception as e:
                    logger.error(f"[{client_id}, {batch_id}] Error processing chunk {futures[future]}: {str(e)}")
        """

        # For smaller documents, process sequentially to avoid thread overhead
        logger.info(f"[{client_id}, {batch_id}] Processing small document with {len(doc['chunks'])} chunks sequentially")
        
        for i, chunk_data_item in enumerate(chunk_data):
            try:
                document_page, chunk_embeddings, _ = process_chunk(
                    embeddings=embeddings,
                    client_id=client_id,
                    domain=domain,
                    access_tags=access_tags,
                    root_id=root_id,
                    doc_title=doc_title,
                    created_at=doc['created_at'],
                    updated_at=doc['updated_at'],
                    chunk_data=chunk_data_item
                )
                processed_pages.append((chunk_data_item[0], document_page))
                
                # Update doc_embeddings (no lock needed in sequential processing)
                for field, vector in chunk_embeddings.items():
                    doc_embeddings[field].append(vector)
                    
            except Exception as e:
                logger.error(f"[{client_id}, {batch_id}] ERROR: Failed to process chunk {chunk_data_item[0]}, error: {str(e)}")
        
        # Sort processed pages by chunk index to maintain order
        processed_pages.sort(key=lambda x: x[0])
        documents = [page for _, page in processed_pages]

        if len(chunk_data) == len(processed_pages):
            logger.info(f"[{client_id}, {batch_id}] Successfully processed all chunks: {document_file}")
        else:
            logger.error(f"[{client_id}, {batch_id}] ERROR: Failed to process some chunks: {document_file}")
            raise self.retry(exc=e, countdown=3)

        logger.info(f"[{client_id}, {batch_id}] After processing chunks: {document_file}, chunks: {len(documents)}")

        # Create main document
        document = {
            'root_id': root_id,
            'id': root_id,
            'chunk_index': 0,
            'client': client_id,
            'domain': domain,
            'access_tags': access_tags,
            'provider': PROVIDER_API_V2,
            'type': DOCUMENT_TYPE_DOCUMENT,
            'kind': FACTOID_KIND_TEXT,
            'level': FACTOID_LEVEL_DOCUMENT,
            'url': doc['url'],
            'language': doc['languages'],
            'title': doc_title,
            'keywords': doc.get('keywords', ''),
            'text': document_summary,
            'tokens': len(get_tokenizer().encode(document_summary)),
            'chunks': len(doc['chunks']),
            'start_position': 0,
            'end_position': start_position,
            'created_at': doc['created_at'],
            'updated_at': doc['updated_at'],
        }

        # Add document embeddings
        for provider, _, embedding_field in embeddings:
            vector_sum = np.sum(doc_embeddings[embedding_field], axis=0)
            num_vectors = len(doc_embeddings[embedding_field])
            document[embedding_field] = list(vector_sum / num_vectors)

        documents.append(document)

        logger.info(f"[{client_id}, {batch_id}] After processing document: {document_file}, documents: {len(documents)}")

    except Exception as e:
        logger.error(f"[{client_id}, {batch_id}] ERROR: Failed to process document, document_file: {document_file}, error: {str(e)}")
        raise self.retry(exc=e, countdown=3)

    # Index documents in Elasticsearch
    root_document_ids = set()
    try:
        index_name = helper.get_index_name()
        logger.info(f"[{client_id}, {batch_id}] Indexing documents, index_name: {index_name}")

        # Flatten the list of documents since upsert_document returns a list
        flattened_docs = []
        for doc_list in documents:
            if isinstance(doc_list, list):
                flattened_docs.extend(doc_list)
            else:
                flattened_docs.append(doc_list)

        del documents

        # Process in chunks of 20
        index_batch_size = 20
        for i in range(0, len(flattened_docs), index_batch_size):
            chunk = flattened_docs[i:i + index_batch_size]
            items = [{
                '_id': doc['id'],
                '_index': index_name,
                'pipeline': 'ent-search-generic-ingestion',
                '_source': doc,
            } for doc in chunk]

            try:
                helpers.bulk(es, items)
                root_document_ids.update([item['_source']['root_id'] for item in items])

            except Exception as e:
                logger.warning(f"[{client_id}, {batch_id}] ERROR: Bulk index failed, trying individual documents {str(e)}")

                for item in items:
                    try:
                        helpers.bulk(es, [item])
                        root_document_ids.add(item['_source']['root_id'])

                    except Exception as e2:
                        logger.error(f"[{client_id}, {batch_id}] ERROR: Failed to index document {item['_id']}, title length: {len(item['_source']['title'])}, text length: {len(item['_source']['text'])}, keywords length: {len(item['_source']['keywords'])}, error: {str(e2)}")

            logger.info(f"[{client_id}, {batch_id}] Indexed documents, count: {len(chunk)}")

        # Remove extra chunks
        try:
            # Get the last chunk index from the processed documents
            last_chunk_index = max(doc['chunk_index'] for doc in flattened_docs if doc['level'] == FACTOID_LEVEL_CHUNK)
            
            query = {
                "query": {
                    "bool": {
                        "filter": [
                            {"term": {"client": client_id}},
                            {"term": {"type": DOCUMENT_TYPE_DOCUMENT}},
                            {"term": {"provider": PROVIDER_API_V2}},
                            {"term": {"root_id": root_id}},
                            {"term": {"kind": FACTOID_KIND_TEXT}},
                            {"term": {"level": FACTOID_LEVEL_CHUNK}},
                            {"range": {"chunk_index": {"gte": last_chunk_index + 1}}}
                        ]
                    }
                }
            }

            es.delete_by_query(index=index_name, body=query)
            logger.info(f"[{client_id}, {batch_id}] Removed extra chunks after index {last_chunk_index}")

        except Exception as e:
            logger.error(f"[{client_id}, {batch_id}] ERROR: Failed to remove extra chunks: {str(e)}")

    except Exception as e:
        logger.error(f"[{client_id}, {batch_id}] ERROR: Failed to index documents, error: {str(e)}")
        raise self.retry(exc=e, countdown=3)
    
    return [(doc_id[len(client_id) + 1 + len(domain) + 1:], DataProcessorAction.UPSERT) for doc_id in root_document_ids]

@app.task(bind=True, max_retries=3, queue='indexer', name='indexer.remove_documents')
def remove_documents(self, client_id: str, batch_id: str, domain: str, root_document_ids: List[str]) -> None:
    """Remove documents from Elasticsearch"""
    try:
        helper = IndexerHelper(client_id)
        index_name = helper.get_index_name()
        logger.info(f"[{client_id}, {batch_id}] Removing documents, index_name: {index_name}")
        
        # Format document IDs to include client_id and domain
        formatted_root_ids = [f"{client_id}:{domain}:{doc_id}" for doc_id in root_document_ids]
        
        logger.info(f"[{client_id}, {batch_id}] Removing documents, client_id: {client_id}, domain: {domain}, root_document_ids: {root_document_ids}")
        logger.info(f"[{client_id}, {batch_id}] Formatted root IDs: {formatted_root_ids}")

        query = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"client": client_id}},
                        {"term": {"type": DOCUMENT_TYPE_DOCUMENT}},
                        {"term": {"provider": PROVIDER_API_V2}},
                        {"term": {"domain": domain}},
                        {"terms": {"root_id": formatted_root_ids}},
                    ]
                }
            }
        }

        es.delete_by_query(index=index_name, body=query)
        logger.info(f"[{client_id}, {batch_id}] Removed documents, count: {len(root_document_ids)}")

    except Exception as e:
        logger.error(f"[{client_id}, {batch_id}] Failed to remove documents: {str(e)}")
        pass

    return root_document_ids

@app.task(
    bind=True, 
    max_retries=3, 
    queue='indexer-save', 
    name='indexer-save.update_document_lists'
)
def update_document_lists(self, processed_results: List[Tuple[str, DataProcessorAction]], client_id: str, batch_id: str) -> bool:
    """Update document lists and statistics"""
    try:
        logger.info(f"[{client_id}, {batch_id}] Updating document lists because of {len(processed_results)} processed results")

        # Get the index name
        helper = IndexerHelper(client_id)
        index_name = helper.get_index_name()
        logger.info(f"[{client_id}, {batch_id}] Updating document lists, index_name: {index_name}")
        
        # Get document list and languages
        list_documents = {}
        list_languages = {}
        
        query = {
            "_source": ["id", "title", "domain", "language"],
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"client": client_id}},
                        {"term": {"type": DOCUMENT_TYPE_DOCUMENT}},
                        {"term": {"provider": PROVIDER_API_V2}},
                        {"term": {"kind": FACTOID_KIND_TEXT}},
                        {"term": {"level": FACTOID_LEVEL_DOCUMENT}},
                    ]
                }
            },
            "size": 1000
        }

        # Scroll through results
        response = es.search(index=index_name, body=query, scroll='1m')
        scroll_id = response['_scroll_id']
        
        while True:
            for hit in response['hits']['hits']:
                source = hit['_source']
                domain = source['domain']
                
                if domain not in list_documents:
                    list_documents[domain] = {}
                if domain not in list_languages:
                    list_languages[domain] = []
                    
                doc_id = source['id'][len(client_id) + 1 + len(domain) + 1:]
                doc_title = source['title']
                list_documents[domain][doc_id] = doc_title
                
                for lang in source['language']:
                    if lang not in list_languages[domain]:
                        list_languages[domain].append(lang)
            
            response = es.scroll(scroll_id=scroll_id, scroll='1m')
            if not response['hits']['hits']:
                break

        # Save results
        remote_out_dir = f"{INDEXER_STORAGE}/{client_id}"
        qp_files.set_json(f"{remote_out_dir}/list_documents.json", list_documents)
        qp_files.set_json(f"{remote_out_dir}/list_languages.json", list_languages)

        logger.info(f"[{client_id}, {batch_id}] Updated document lists, domains: {len(list_documents)}")
        return {
            'status': 'success',
            'client_id': client_id,
            'batch_id': batch_id,
            'processed_count': len(processed_results),
            'state': BatchState.DONE
        }

    except Exception as e:
        logger.error(f"[{client_id}, {batch_id}] Failed to update document lists, error: {str(e)}")

        # Retry with exponential backoff
        raise self.retry(exc=e, countdown=60)

@app.task(
    bind=True, 
    max_retries=3, 
    queue='indexer-save', 
    name='indexer-save.save_results'
)
def save_results(self, processed_results: List[Tuple[str, DataProcessorAction]], client_id: str, batch_id: str, domain: str, action: str) -> Dict:
    """Save the final batch results"""
    logger.info(f"[{client_id}, {batch_id}] Saving results for domain={domain}, action={action}")
    
    new_batch_data = {
        'client_id': client_id,
        'domain': domain,
        'action': action,
        'processed_ids': [],
        'changes': {
            DataProcessorAction.UPSERT.value: [],
            DataProcessorAction.DELETE.value: []
        },
    }
    
    # Flatten processed_results to handle possible nested structures
    flat_results = []
    for item in processed_results:
        # Handle nested lists of lists
        if isinstance(item, list):
            for subitem in item:
                if isinstance(subitem, list) and len(subitem) == 2:
                    flat_results.append(tuple(subitem))
                elif isinstance(subitem, tuple) and len(subitem) == 2:
                    flat_results.append(subitem)
        # Handle direct tuples
        elif isinstance(item, tuple) and len(item) == 2:
            flat_results.append(item)
    
    logger.info(f"[{client_id}, {batch_id}] Flattened results: {flat_results}")
    
    # Process the flattened results
    for doc_id, act in flat_results:
        action_value = act if isinstance(act, str) else act.value
        new_batch_data['processed_ids'].append(f"{domain}:{doc_id}")
        if action_value in new_batch_data['changes']:
            new_batch_data['changes'][action_value].append(doc_id)

    logger.info(f"[{client_id}, {batch_id}] New batch data generated")

    batch_file = BatchUtils.get_batch_filename(client_id, batch_id, BatchState.IN_PROGRESS)
    if qp_files.exists(batch_file):
        qp_files.delete_file(batch_file)

    done_batch_file = BatchUtils.get_batch_filename(client_id, batch_id, BatchState.DONE)
    qp_files.set_json_zlib(done_batch_file, new_batch_data)
    
    logger.info(f"[{client_id}, {batch_id}] Batch processed successfully, action={action}, processed_ids={len(new_batch_data['processed_ids'])}")
    return  processed_results
