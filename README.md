# Quepasa

Quepasa is a microservices-based RAG (Retrieval-Augmented Generation) system for document processing, indexing, and intelligent search.

## Quick Start with Docker

### Prerequisites
- Docker and Docker Compose
- OpenAI API key
- Nebius API key

### Setup
1. **Clone and setup environment:**
```bash
git pull
cp .env.example .env
# Edit .env and set your API keys:
# OPENAI_API_KEY=your-openai-api-key
# NEBIUS_API_KEY=your-nebius-api-key
```

2. **Launch services:**

- **Build & Run (Docker):**
```bash
# Build and start all services
docker compose up --build

# View logs for a specific service
docker compose logs -f <service>

# Restart a specific service
docker compose restart <service>

# Stop all services
docker compose down
```

- **Local Python:**
```bash
# Create and activate virtual environment
python -m venv venv

# Activate virtual environment (Linux/Mac)
source venv/bin/activate

# Activate virtual environment (Windows)
# venv\Scripts\activate

# Install dependencies and development packages
pip install -r requirements.txt && pip install -e .[dev]
```

- **Testing:**
Tests use `pytest` with asyncio and extensive mocking for S3/MinIO, Redis, and batch utilities.

```bash
# Install base test requirements
pip install -r tests/requirements-test.txt

# Install service-specific dependencies (as needed by your tests)
pip install -r quepasa/api/requirements.txt
pip install -r quepasa/crawler/requirements.txt
pip install -r quepasa/embedding/requirements.txt
pip install -r quepasa/indexer/requirements.txt
pip install -r quepasa/searcher/requirements.txt
pip install -r src/lib/requirements.txt

# Run all tests
pytest

# Run specific test files
pytest tests/test_crawler.py

# Run tests with coverage report (HTML format)
pytest --cov=quepasa --cov-report=html

# Linting/Formatting:
black .
flake8 .
mypy quepasa/
```

### Slow tests marker

Some tests are marked as `slow` and are skipped by default in CI.

- Run fast tests only (exclude slow):

```bash
pytest -m "not slow"
```

- Run only slow tests:

```bash
pytest -m slow
```

- Duration reporting: by default, pytest shows duration only for tests taking ≥ 10s (see pytest.ini). Override if needed:

```bash
pytest --durations=0 --durations-min=1.0
```


### Services
- **API**: http://localhost:8000 - Main API endpoint
- **Searcher**: http://localhost:8080 - Query processing
- **Embedding**: http://localhost:8001 - Vector embeddings
- **MinIO Console**: http://localhost:9001 - Object storage UI
- **Elasticsearch**: http://localhost:9200 - Search engine

### Getting Started
See the [Quepasa Cookbook](dev/QuepasaCookbook-1.ipynb) for examples of:
- Uploading documents to ingestion pipeline
- Retrieving data from inference pipeline
See the [Ingestion Cookbook](dev/IngestionCookbook.ipynb) for examples of products' ingestion and deletion.

## Architecture
- **Crawler**: Document processing and ingestion
- **Data Processor**: Document transformation
- **Indexer**: Elasticsearch indexing
- **Searcher**: Query processing and retrieval
- **Embedding**: Vector embedding generation
- **API**: FastAPI backend

## Storage
- **Redis**: Message broker and caching
- **MinIO**: Object storage
- **Elasticsearch**: Search and vector storage