import os
from typing import Optional, List, Dict, Any
import redis
import json
import hashlib
import traceback  # Import traceback module at the top level
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from transformers import pipeline  # Import transformers pipeline
import numpy as np  # Add numpy import

from configuration.main.default import QuepasaConfigurationHub
from src.lib.constants import DOCUMENT_TYPE_DOCUMENT, DOCUMENT_TYPE_PRODUCT
from src.lib.embedding_utils import get_cached_embedding
from src.lib.utils import ALLOWED_LANGUAGES
from src.lib.logger import QuepasaLogger

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

QUEPASA_LLM_GATEWAY_API_KEY = os.environ.get("QUEPASA_LLM_GATEWAY_API_KEY")

app = FastAPI(title="Embedding Service")

# Redis configuration
REDIS_HOST = os.getenv("REDIS_HOST", "redis")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_PREFIX = os.getenv("REDIS_PREFIX", "embedding:")
REDIS_EXPIRY = int(os.getenv("REDIS_EXPIRY", "86400"))  # 24 hours default

# Create Redis client
redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    decode_responses=False
)

# Initialize language detection model globally
language_detector = None

def get_language_detector():
    global language_detector
    if language_detector is None:
        local_model_dir = "/app/models/xlm-roberta"
        language_detector = pipeline(
            "text-classification",
            model=local_model_dir,
            tokenizer=local_model_dir,
            truncation=True,
            max_length=512
        )
    return language_detector

class EmbeddingRequest(BaseModel):
    provider: str
    model_version: str
    text: str
    bypass_cache: bool = Field(default=False)

class EmbeddingResponse(BaseModel):
    embedding: List[float]
    cached: bool = Field(default=False)

class LanguageDetectionRequest(BaseModel):
    text: str
    allowed_languages: List[str] = Field(default=list(ALLOWED_LANGUAGES.keys()))

class LanguageDetectionResponse(BaseModel):
    language: str
    confidence: float

def get_cache_key(provider: str, model_version: str, text: str) -> str:
    """Generate a cache key for the embedding request."""
    text_hash = hashlib.md5(text.encode()).hexdigest()
    return f"{REDIS_PREFIX}{provider}:{model_version}:{text_hash}"

@app.post("/api/embedding", response_model=EmbeddingResponse)
async def get_embedding_endpoint(request: EmbeddingRequest):
    # If bypass_cache is True, skip the Redis cache check
    if request.bypass_cache:
        # Get embedding directly from the provider
        embedding = get_cached_embedding(
            request.provider,
            request.model_version, 
            request.text
        )
        
        if embedding is None or len(embedding) == 0:
            raise HTTPException(status_code=500, detail="Failed to get embedding from provider")
        
        # Convert numpy array to list if needed
        if isinstance(embedding, np.ndarray):
            embedding = embedding.tolist()
        
        return EmbeddingResponse(
            embedding=embedding,
            cached=False
        )
    
    # Normal flow with caching
    cache_key = get_cache_key(request.provider, request.model_version, request.text)
    
    # Try to get from cache first
    cached_embedding = redis_client.get(cache_key)
    if cached_embedding:
        return EmbeddingResponse(
            embedding=json.loads(cached_embedding),
            cached=True
        )
    
    # If not in cache, get from provider using the direct method
    embedding = get_cached_embedding(
        request.provider, 
        request.model_version, 
        request.text
    )
    
    if embedding is None or len(embedding) == 0:
        logger.error(f"Failed to get embedding from provider: {request.provider} {request.model_version} {request.text}")
        raise HTTPException(status_code=500, detail="Failed to get embedding from provider")
    
    # Convert numpy array to list if needed before caching
    if isinstance(embedding, np.ndarray):
        embedding_to_cache = embedding.tolist()
    else:
        embedding_to_cache = embedding
    
    # Cache the result
    redis_client.setex(
        cache_key,
        REDIS_EXPIRY,
        json.dumps(embedding_to_cache)
    )
    
    return EmbeddingResponse(
        embedding=embedding_to_cache,
        cached=False
    )

@app.post("/api/detect-language", response_model=LanguageDetectionResponse)
async def detect_language(request: LanguageDetectionRequest):
    """
    Detect the language of the provided text using XLM-RoBERTa model.
    
    Returns the detected language code and confidence score from the allowed language list.
    """
    if not request.text:
        raise HTTPException(status_code=400, detail="Text cannot be empty")
    
    try:
        # Get or initialize the language detector
        detector = get_language_detector()
        
        # Truncate text if it's too long (for extra safety)
        # Most XLM-RoBERTa models have a max length of 512 tokens
        # Taking first 1000 characters should be sufficient for language detection
        # while avoiding model context length errors
        truncated_text = request.text[:1000]
        
        # Perform language detection
        results = detector(truncated_text, top_k=None)  # Get all predictions
        
        # Filter results to only include allowed languages
        filtered_results = [r for r in results if r["label"] in request.allowed_languages]
        
        if filtered_results:
            # Sort by confidence score (highest first)
            filtered_results.sort(key=lambda x: x["score"], reverse=True)
            best_match = filtered_results[0]
            
            logger.info(f"Language detection result: {best_match}")
            return LanguageDetectionResponse(
                language=best_match["label"],
                confidence=best_match["score"]
            )
        
        else:
            # If no allowed language was detected, default to English
            logger.warning(f"No allowed language detected, defaulting to English")
            return LanguageDetectionResponse(
                language="en",
                confidence=0.0
            )
    
    except Exception as e:
        logger.error(f"Language detection error: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error during language detection: {str(e)}"
        )

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/startup")
async def startup(client_id: str = "default"):
    """
    Endpoint that gets called at container initialization to warm up the embedding cache.
    Uses embedding models from the configuration for the specified client.
    
    Args:
        client_id: Client ID to use for configuration, defaults to "default"
    """    
    try:
        # Initialize language detection model
        language_model_status = {"name": "language_detection", "status": "success"}
        try:
            detector = get_language_detector()
            # Test with a simple example
            test_result = detector("Hello world")
            if not test_result or len(test_result) == 0:
                language_model_status["status"] = "error"
                language_model_status["error"] = "Model failed to return valid results"
                
        except Exception as e:
            language_model_status["status"] = "error"
            language_model_status["error"] = str(e)

        # Initialize configuration with the specified client ID
        config = QuepasaConfigurationHub.from_client_code(client_id)
        
        # Get all embedding configurations for the client
        embeddings = []
        success_models = []
        failed_models = []
        
        try:
            # Get embedding models from the embedding_model_versions config
            for doc_type in [DOCUMENT_TYPE_DOCUMENT, DOCUMENT_TYPE_PRODUCT]:
                for provider, model_version in config.get_embedding_model_versions(doc_type):
                    if (provider, model_version) not in embeddings:
                        embeddings.append((provider, model_version))
            
            # Also add the search embedding model
            search_provider, search_model = config.get_search_embedding_model("documents")
            if (search_provider, search_model) not in embeddings:
                embeddings.append((search_provider, search_model))
            
            # No models found, use default from get_search_embedding_model as fallback
            if not embeddings:
                embeddings = [(search_provider, search_model)]

        except Exception as config_error:
            return {
                "status": "error",
                "message": f"Error retrieving embedding configuration: {str(config_error)}",
                "client_id": client_id,
                "error_detail": str(config_error),
                "traceback": traceback.format_exc()
            }
            
        if QUEPASA_LLM_GATEWAY_API_KEY:
            return {
                "status": "success", 
                "message": f"Initialized with LLM Gateway API Key",
                "client_id": client_id,
                "language_detection": language_model_status
            }

        # Initialize each embedding model
        text = "Quepasa"
        for provider, model_version in embeddings:
            try:
                embedding = get_cached_embedding(provider, model_version, text)
                if embedding:
                    success_models.append({"provider": provider, "model_version": model_version})
                else:
                    failed_models.append({"provider": provider, "model_version": model_version})
                    
            except Exception as e:
                failed_models.append({
                    "provider": provider, 
                    "model_version": model_version,
                    "error": str(e)
                })
                
        return {
            "status": "success" if success_models else "error", 
            "message": f"Initialized {len(success_models)} embedding models, {len(failed_models)} failed",
            "client_id": client_id,
            "success_models": success_models,
            "failed_models": failed_models,
            "language_detection": language_model_status
        }
    
    except Exception as e:
        # Use the traceback module that was imported at the top level
        return {
            "status": "error",
            "message": f"Startup error: {str(e)}",
            "client_id": client_id,
            "error_detail": str(e),
            "traceback": traceback.format_exc()
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080) 