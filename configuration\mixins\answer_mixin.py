from typing import Dict, <PERSON>, Any, Tu<PERSON>, Union
from quepasa.searcher.models.request import QuepasaRequest
from ..base.answer_constants import AnswerConstantsConfig
from ..base.answer import AnswerConfig
from ..base.agentic import AgenticConfig

class AnswerMixin(AnswerConstantsConfig, AnswerConfig, AgenticConfig):
    """Mixin that implements answer generation functionality."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)