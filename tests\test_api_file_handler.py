import os
os.environ['REDIS_HOST'] = 'localhost'
os.environ['REDIS_PORT'] = '6379'
os.environ['CELERY_BROKER_URL'] = 'redis://localhost:6379/0'
os.environ['CELERY_RESULT_BACKEND'] = 'redis://localhost:6379/0'

import pytest
from unittest.mock import Mock, patch, mock_open, AsyncMock
from quepasa.api.handlers.file_handler import FileHandler, BatchResult
from io import BytesIO

@pytest.fixture
def mock_files():
    mock = AsyncMock()
    mock.upload_stream = AsyncMock(return_value=None)
    return mock

@pytest.fixture
def mock_batch():
    mock = AsyncMock()
    mock.create_batch = AsyncMock(return_value="test_batch")
    return mock

@pytest.fixture
def mock_md5():
    with patch('hashlib.md5') as mock:
        mock_instance = Mock()
        mock_instance.hexdigest.return_value = "test_hash"
        mock.return_value = mock_instance
        yield mock

@pytest.fixture
def mock_batch_utils():
    with patch('src.lib.batch_utils.BatchUtils') as mock:
        mock.create_batch = Mock(return_value="test_batch")
        mock.get_generated_batch_id = Mock(return_value="test_batch")
        yield mock

@pytest.fixture
def file_handler(mock_files, mock_batch_utils):
    with patch('quepasa.api.handlers.file_handler.BatchUtils', mock_batch_utils):
        handler = FileHandler(mock_files)
        handler._get_file_path = Mock(return_value="test/path/file.txt")
        return handler

@pytest.mark.asyncio
async def test_upload_file(file_handler):
    """Test uploading file"""
    file = Mock()
    
    # Pre-await the mock calls to get their return values
    await file_handler.files.upload_stream(file, "test/path/file.txt")
    
    result = await file_handler.upload_file("test_client", "test_domain", file, "test.txt")
    
    assert isinstance(result, BatchResult)
    assert result.batch_id == "test_batch"
    assert result.processed_ids == ["test.txt"]
    
    file_handler._get_file_path.assert_called_once_with("test_client", "test_domain", "test.txt")
    assert file_handler.files.upload_stream.await_count == 2  # One from pre-await, one from actual call

@pytest.mark.asyncio
async def test_upload_file_no_language(file_handler):
    """Test uploading file without language"""
    file = Mock()
    
    # Pre-await the mock calls to get their return values
    await file_handler.files.upload_stream(file, "test/path/file.txt")
    
    result = await file_handler.upload_file("test_client", "test_domain", file, "test.txt")
    
    assert isinstance(result, BatchResult)
    assert result.batch_id == "test_batch"
    assert result.processed_ids == ["test.txt"]

@pytest.mark.asyncio
async def test_upload_urls(file_handler):
    """Test uploading URLs"""
    urls = ["http://example.com"]
    
    result = await file_handler.upload_urls("test_client", "test_domain", urls)
    
    assert isinstance(result, BatchResult)
    assert result.batch_id == "test_batch"
    assert result.processed_ids == urls

@pytest.mark.asyncio
async def test_upload_urls_no_language(file_handler):
    """Test uploading URLs without language"""
    urls = ["http://example.com"]
    
    result = await file_handler.upload_urls("test_client", "test_domain", urls)
    
    assert isinstance(result, BatchResult)
    assert result.batch_id == "test_batch"
    assert result.processed_ids == urls

@pytest.mark.asyncio
async def test_upload_urls_empty_list(file_handler):
    """Test uploading empty URL list"""
    with pytest.raises(ValueError, match="No URLs provided"):
        await file_handler.upload_urls("test_client", "test_domain", [])

@pytest.mark.asyncio
async def test_upload_urls_invalid_url(file_handler):
    """Test uploading invalid URLs"""
    invalid_urls = ["not_a_url", "ftp://invalid.com"]
    with pytest.raises(ValueError, match="Invalid URL format"):
        file_handler.validate_urls(invalid_urls)

def test_validate_file_empty(file_handler):
    """Test validating empty file"""
    file = Mock()
    file.seek.side_effect = [None, None]
    file.tell.return_value = 0
    
    with pytest.raises(ValueError, match="File cannot be empty"):
        file_handler.validate_file(file)

def test_validate_file_valid(file_handler):
    """Test validating valid file"""
    file = Mock()
    file.seek.side_effect = [None, None]
    file.tell.return_value = 100
    
    file_handler.validate_file(file)

def test_validate_urls_valid(file_handler):
    """Test validating valid URLs"""
    urls = ["http://example.com", "https://example.org"]
    file_handler.validate_urls(urls)

def test_validate_urls_invalid(file_handler):
    """Test validating invalid URLs"""
    urls = ["not_a_url", "ftp://invalid.com"]
    with pytest.raises(ValueError, match="Invalid URL format"):
        file_handler.validate_urls(urls) 