# CON-76: Add structured product metadata into conversational response

## Requirements

1. We need to introduce a new `/conversation` endpoint which will output data in the new format. 
The new endpoint contract is described in the following documentation:
- [API Documentation](/docs/api/conversational_shopping_assistant_v1.yaml)
- [API Request Examples](/docs/api/request_example.md)
- [API Response Examples](/docs/api/response_example.md)
- [Current API Contract](/docs/api/openapi-searcher.yaml)

2. Support new authorization mechanism via the `X-Customer-Id` header and `client-key` in the Authorization header.
- there is a working client-key auth in FastAPI products_handler via BearerAuth class; searcher should reuse it:
  - `/quepasa/auth/bearer.py`
  - `/quepasa/api/auth.py`

3. Refactor `searcher` service to handle the new `/conversation` endpoint.
- the internal request handling flow should be similar to the current `/retrieve/answer(/stream)` endpoints 
- always use `source: agentic` for that endpoint
- support markdown and plain_text formats
- return products in the new format
- for variants: return all variant attributes, not just size and availability as in the API response example.

4. Add streaming support for the new endpoint.
- Add streaming trigger logic in the `/conversation` handler:
   - Parse query param stream=true or Accept == text/event-stream
   - When streaming, instantiate SSEHandler with a /conversation-aware shaping function
- Introduce a response shaper for streaming mode that:
   - Generates a responseId at request start; keep it constant
   - Emits ChatResponse-shaped chunks with:
      - responseId (constant)
      - sessionId (echo request)
      - timestamp (ISO 8601 per chunk)
      - content: progressively built (if applicable) or interim messages
      - stream: true
      - status: IN_PROGRESS for interim chunks, COMPLETE on final, ERROR on errors
   - Optionally include partial components/actions (if available), or keep them for final COMPLETE event
- Ensure the last SSE event is marked with status=COMPLETE and stream=true.
- Continue to set correct SSE headers and CORS for text/event-stream.

5. Session Conversation history should be supported for the new endpoint.
- `sessionId` is required, and it should be used instead of `user_id` in the current implementation for history tracking
- `visitorId` is optional. If not provided we generate a new one and return it in the response.
- for error handling and logging purposes we will use generated `requestId` that will be reused as a `responseId` for streaming

6. Add unit and integration tests for the new endpoint.
