apiVersion: apps/v1
kind: Deployment
metadata:
  name: searcher
  namespace: quepasa
spec:
  replicas: 1
  selector:
    matchLabels:
      app: searcher
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: searcher
    spec:
      containers:
      - name: searcher
        image: qpreg.azurecr.io/quepasa/searcher:v1.0.184
        imagePullPolicy: Always
        command: ["/bin/bash", "-c"]
        args:
          - |
            echo "Starting container..."
            export FLASK_APP=quepasa.searcher.main:app
            flask run --host=0.0.0.0 --port=8080
        env:
        - name: MINIO_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_HOST
        - name: MINIO_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_PORT
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_BUCKET_NAME
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_BUCKET_NAME
        - name: MINIO_DEBUG
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: MINIO_DEBUG
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: REDIS_PORT
        - name: CELERY_BROKER_URL
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: CELERY_BROKER_URL
        - name: CELERY_RESULT_BACKEND
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: CELERY_RESULT_BACKEND
        - name: ELASTICSEARCH_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: ELASTICSEARCH_HOST
        - name: ELASTICSEARCH_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: ELASTICSEARCH_PORT
        - name: ELASTICSEARCH_USERNAME
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ELASTICSEARCH_USERNAME
        - name: ELASTICSEARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ELASTICSEARCH_PASSWORD
        - name: REPLICATE_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: REPLICATE_API_KEY
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: OPENAI_API_KEY
        - name: NEBIUS_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: NEBIUS_API_KEY
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ANTHROPIC_API_KEY
        - name: HUGGINGFACE_API_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: HUGGINGFACE_API_KEY
        - name: SPD_API_URL
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: SPD_API_URL
        - name: PRIVATE_EMBEDDING_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: PRIVATE_EMBEDDING_ENDPOINT
        - name: PRIVATE_EMBEDDING_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: PRIVATE_EMBEDDING_AUTH_TOKEN
        - name: DEBUG
          value: "true"
        - name: PYTHONPATH
          value: /app
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8080"
        envFrom:
        - configMapRef:
            name: shared-config
        - secretRef:
            name: shared-secrets
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 10"]
        volumeMounts:
        - name: searcher-cache
          mountPath: /app/cache
        - name: config-volume
          mountPath: /app/configuration/main
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
      terminationGracePeriodSeconds: 30
      volumes:
      - name: searcher-cache
        emptyDir:
          sizeLimit: 25Gi
      - name: config-volume
        configMap:
          name: quepasa-config 
