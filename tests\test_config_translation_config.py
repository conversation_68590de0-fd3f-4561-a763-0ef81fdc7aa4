import pytest
from unittest.mock import Mock, patch
from configuration.base.translation import TranslationConfig
from quepasa.searcher.models.request import QuepasaRequest

@pytest.fixture
def translation_config():
    return TranslationConfig("test_client")

@pytest.fixture
def base_request():
    return QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram",
        language="en"
    )

def test_translation_init(translation_config):
    """Test TranslationConfig initialization."""
    assert translation_config.client_code == "test_client"

def test_get_language_options(translation_config):
    """Test getting language options."""
    options = translation_config.get_language_options()
    assert isinstance(options, str)
    assert len(options) > 0

def test_get_translation(translation_config):
    """Test translation retrieval"""
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram",
        language="en"
    )
    translation_config.set_request(request)
    text = "Hello"
    target_lang = "es"
    source_lang = "en"
    
    mock_llm = Mock()
    mock_llm.get_answer.return_value = "Hola"
    mock_llm.get_cached_answer.return_value = "Hola"
    
    with patch('src.lib.llm.factory.LLMFactory.get_llm', return_value=mock_llm), \
         patch('src.lib.llm_utils.get_llm_answer', side_effect=lambda p, m, msg, t: mock_llm.get_answer()), \
         patch('src.lib.llm_utils.get_cached_llm_answer', side_effect=lambda p, m, msg, t: mock_llm.get_cached_answer()):
        result = translation_config.get_translation(
            text=text,
            target_lang=target_lang,
            source_lang=source_lang
        )
        assert isinstance(result, str)
        assert len(result) > 0
        assert result == "Hola"

def test_get_translation_object(translation_config):
    """Test translation object retrieval"""
    request = QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram",
        language="en"
    )
    translation_config.set_request(request)
    text = "Hello"
    target_lang = "es"
    source_lang = "en"
    
    mock_llm = Mock()
    mock_llm.get_answer.return_value = "Hola"
    mock_llm.get_cached_answer.return_value = "Hola"
    
    with patch('src.lib.llm.factory.LLMFactory.get_llm', return_value=mock_llm), \
         patch('src.lib.llm_utils.get_llm_answer', side_effect=lambda p, m, msg, t: mock_llm.get_answer()), \
         patch('src.lib.llm_utils.get_cached_llm_answer', side_effect=lambda p, m, msg, t: mock_llm.get_cached_answer()):
        result = translation_config.get_translation_object(
            text=text,
            target_lang=target_lang,
            source_lang=source_lang
        )
        assert isinstance(result, dict)
        assert target_lang in result
        assert result[target_lang] == "Hola" 