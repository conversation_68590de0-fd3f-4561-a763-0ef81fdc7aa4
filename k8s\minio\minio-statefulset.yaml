apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: minio
  namespace: quepasa
spec:
  serviceName: minio
  replicas: 1
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
    spec:
      containers:
      - name: minio
        image: minio/minio
        args:
        - server
        - /data
        - --console-address
        - ":9001"
        env:
        - name: MINIO_ROOT_USER
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_SECRET_KEY
        - name: MINIO_DOMAIN
          value: minio
        - name: MINIO_ADDRESS
          value: ":9000"
        - name: MINIO_CONSOLE_ADDRESS
          value: ":9001"
        envFrom:
        - configMapRef:
            name: shared-config
        - secretRef:
            name: shared-secrets
        ports:
        - containerPort: 9000
          name: api
        - containerPort: 9001
          name: console
        volumeMounts:
        - name: minio-data
          mountPath: /data
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
  volumeClaimTemplates:
  - metadata:
      name: minio-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: managed-csi
      resources:
        requests:
          storage: 100Gi 