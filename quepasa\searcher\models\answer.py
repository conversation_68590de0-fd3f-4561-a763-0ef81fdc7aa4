import json
import re
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any

from configuration.main.default import QuepasaConfigurationHub
from src.lib.constants import DOCUMENT_TYPE_PRODUCT
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .document import Que<PERSON>aDocument
from .request import QuepasaRequest
from .response import AnswerType, Reference, ProductItem
from .spd_result import SPDSearchResult
from ..utils import universal_escape_markdown, unescape_markdown_links, get_fixed_markdown_with_refs

logger = QuepasaLogger().get_instance(__name__)

@dataclass
class FormattedAnswer:
    """Represents a formatted answer with its metadata"""
    type: AnswerType
    text: str
    markdown: str
    references: Dict[int, Reference]
    products: Optional[List[ProductItem]] = None

class AnswerFormatter:
    """Handles formatting and processing of answers"""
    
    def __init__(self):
        pass

    def format_answer(self, request: QuepasaRequest, text: str,
                      source_hash: Optional[Dict[int, Any]] = None) -> FormattedAnswer:
        """Format an answer with links and markdown
        
        Args:
            request: QuepasaRequest object
            text: Raw answer text
            source_hash: Dictionary of source data
            
        Returns:
            FormattedAnswer object with processed text and metadata
        """
        # Process links and get markdown
        answer_type, markdown, references, products = self._process_markdown_and_references(request, text, source_hash)

        return FormattedAnswer(
            type=answer_type,
            text=text,
            markdown=markdown,
            references=references,
            products=products
        )

    def _extract_urls_from_markdown(self, markdown: str) -> List[str]:
        """Extract URLs from markdown text
        
        Args:
            markdown: Markdown text to extract URLs from
            
        Returns:
            List of URLs found in the markdown
        """
        # Pattern to match URLs in markdown links [text](url) and plain URLs
        url_pattern = r'https?://[^\s\)\]\>]+'
        urls = re.findall(url_pattern, markdown)
        return urls

    def _match_urls_with_documents(self, urls: List[str], source_hash: Dict[int, QuepasaDocument]) -> Dict[str, int]:
        """Match URLs with documents in source_hash
        
        Args:
            urls: List of URLs to match
            source_hash: Dictionary of documents indexed by integer keys
            
        Returns:
            Dictionary mapping URLs to document indices
        """
        url_to_index = {}
        
        for index, document in source_hash.items():
            # Check both url field and text field for URL matches
            document_urls = []
            
            # Add URL from url field if it exists
            if hasattr(document, 'url') and document.url:
                document_urls.append(document.url)
            
            # Extract URLs from text field if it exists
            if hasattr(document, 'text') and document.text:
                text_urls = self._extract_urls_from_markdown(document.text)
                document_urls.extend(text_urls)
            
            # Normalize and match URLs
            for doc_url in document_urls:
                normalized_doc_url = doc_url.rstrip('/').lower()
                
                for url in urls:
                    normalized_url = url.rstrip('/').lower()
                    if normalized_doc_url == normalized_url:
                        url_to_index[url] = index
                        break
                        
        return url_to_index

    def _process_markdown_and_references(
            self, request: QuepasaRequest, text: str, source_hash: Optional[Dict[int, Any]] = None
    ) -> Tuple[AnswerType, str, Dict[int, Reference], Optional[List[ProductItem]]]:
        """Process and format links in the answer text
        
        Args:
            request: QuepasaRequest object
            text: Raw answer text
            source_hash: Dictionary of referenced documents
            
        Returns:
            Tuple of (processed links dict, products metadata, and Markdown text with formatted links)
        """
        # Get config
        config = QuepasaConfigurationHub.from_request(request)
        source = request.source
        collection = request.collection

        # Initialize tracking
        new_refs = {}
        references = {}
        products = []

        def set_data_from_document(doc: Any, idx: int):
            doc_type = getattr(doc, 'type', None)

            references[idx] = Reference(
                type=doc_type,
                url=getattr(doc, 'url', None),
                title=getattr(doc, 'title', None),
                text=getattr(doc, 'text', None),
                created_at=getattr(doc, 'created_at', None),
                start_position=getattr(doc, 'start_position', None),
                end_position=getattr(doc, 'end_position', None),
                score=getattr(doc, 'score', None),
                sku=getattr(doc, 'sku', None),
                metadata=getattr(doc, 'metadata', None)
            )

            if doc_type == DOCUMENT_TYPE_PRODUCT:
                metadata_dict = {}
                if hasattr(doc, 'metadata') and doc.metadata:
                    try:
                        metadata_dict = json.loads(doc.metadata)

                    except json.JSONDecodeError:
                        # Handle malformed JSON gracefully - use empty dict
                        metadata_dict = {}

                if isinstance(doc, QuepasaDocument):
                    try:
                        product_item = ProductItem(
                            id=doc.sku or doc.id or '',
                            title=doc.title or '',
                            url=doc.url or '',
                            collection=collection,
                            allMeta=metadata_dict
                        )
                        products.append(product_item)

                    except json.JSONDecodeError:
                        # Handle malformed JSON gracefully - use empty dict
                        products.append(ProductItem(
                            id=doc.sku or doc.id or '',
                            title=doc.title or '',
                            url=doc.url or '',
                            collection='',
                            allMeta=metadata_dict
                        ))

                elif isinstance(doc, SPDSearchResult):
                    # Convert SPDSearchResult to ProductItem
                    products.append(ProductItem(
                        id=doc.sku or '',
                        title=doc.title or '',
                        url=doc.url or '',
                        collection=collection,
                        allMeta=metadata_dict
                    ))

                elif isinstance(doc, Reference):
                    # Convert Reference to ProductItem
                    products.append(ProductItem(
                        id=doc.sku or doc.id or '',
                        title=doc.title or '',
                        url=doc.url or '',
                        collection=collection,
                        allMeta=metadata_dict
                    ))

        # Replace links with [index]
        updated_text = get_fixed_markdown_with_refs(text, source_hash)

        # Set default text
        markdown = updated_text

        # Save marked up answer
        markdown = universal_escape_markdown(request.protocol, markdown)

        # Unescape link
        markdown = unescape_markdown_links(markdown)

        # Answer type
        answer_type = AnswerType.STRICT

        if source_hash:
            # First pass - collect references and get source references
            for index, document in source_hash.items():
                for link_ref in [f"\\[{index}\\]"]:
                    if link_ref in markdown:
                        # Convert document to dict if needed
                        doc_dict = document.to_dict() if hasattr(document, 'to_dict') else vars(document)
                        
                        # Get source reference object
                        source_ref = config.get_source_reference_object(source, doc_dict)
                        
                        # Store reference mapping
                        new_refs[index] = source_ref
                    
            # Second pass - format links
            for index, document in source_hash.items():
                for link_ref in [f"[{index}]"]:
                    if (
                        link_ref in updated_text 
                        and hasattr(document, 'url')
                    ):
                        if index in new_refs:
                            source_ref = new_refs[index]

                            set_data_from_document(document, index)

                            # Replace reference in markdown
                            # Convert document to dict if needed
                            doc_dict = document.to_dict() if hasattr(document, 'to_dict') else vars(document)

                            # Get formatted reference text
                            ref_text = config.get_source_reference(source, doc_dict)
                            link_md_ref = universal_escape_markdown(request.protocol, link_ref)
                            if link_md_ref in markdown:
                                markdown = markdown.replace(link_md_ref, f"[\\[{ref_text}\\]]({document.url})")

            # Additional pass - match URLs from markdown with source_hash documents
            
            # Extract URLs from the current text
            urls_in_markdown = self._extract_urls_from_markdown(updated_text)
            
            # Match URLs with documents
            url_to_index = self._match_urls_with_documents(urls_in_markdown, source_hash)
            
            # Process matched URLs
            for url, index in url_to_index.items():
                if index not in references and index in source_hash:
                    document = source_hash[index]
                    set_data_from_document(document, index)

        # Check for no answer
        if config.is_no_answer_response(source, updated_text, references):
            answer_type = AnswerType.WEAK

        elif len(references) == 0:
            answer_type = AnswerType.NO_LINKS

        # Return None for products if empty list (backward compatibility)
        return answer_type, markdown, references, products if products else None
