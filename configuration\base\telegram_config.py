import time
from typing import Optional, Dict, Any, Union
from quepasa.searcher.models.request import QuepasaRequest
from src.lib.files import QuepasaFiles
from .base_config import BaseConfig

qp_files = QuepasaFiles()

class TelegramConfig(BaseConfig):
    """Base provider for Telegram configuration functionality."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
        self._telegram_config = None
        self._telegram_config_last_updated = None 

    def _get_telegram_config(self) -> Optional[Dict[str, Any]]:
        """Get Telegram configuration from file with caching.
        
        Configuration is cached for 10 minutes to reduce file system access.
        
        Returns:
            Dict with Telegram configuration or None if not found/invalid
        """
        if (self._telegram_config_last_updated is None or 
            time.time() - self._telegram_config_last_updated > 600):  # 10 min cache
            
            try:
                config_file = f"prod/shared/telegram/saas/{self.client_code}.json"
                if qp_files.exists(config_file):
                    self._telegram_config = qp_files.get_json(config_file)
            except:
                pass

            self._telegram_config_last_updated = time.time()

        return self._telegram_config