from typing import List

import pytest

from configuration.base.searcher import SearchEngineRatios
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.sources.base import STATUS_SUCCESS
from quepasa.searcher.sources.meta import MetaSource


@pytest.fixture
def spd_env(monkeypatch):
    monkeypatch.setenv("SPD_API_URL", "https://search.example.groupbycloud.com/api/search")
    return "https://search.example.groupbycloud.com/api/search"


def mock_ratio_config(mocker, rag: int = 1, spd: int = 1, only_promoted: bool = False):
    config = mocker.Mock()
    config.get_spd_engine_ratios.return_value = SearchEngineRatios(rag=rag, spd=spd)
    config.get_spd_include_promos_only.return_value = only_promoted
    mocker.patch('configuration.main.default.QuepasaConfigurationHub.from_request', return_value=config)


def meta_service(return_rag_records: List, return_spd_records: List):
    class MockSearchEngine:
        def __init__(self, return_records):
            self.return_records = return_records

        def search(self, _):
            return self.return_records

    ms = MetaSource()
    ms.search_engine_rag = MockSearchEngine(return_rag_records)
    ms.search_engine_spd = MockSearchEngine(return_spd_records)
    return ms


def request(**kwargs):
    req = QuepasaRequest(question="test")
    for k, v in kwargs.items():
        setattr(req, k, v)
    return req


def records_gen(_id: str, n: int) -> list:
    return [record(f"{_id}{i}") for i in range(1, n + 1)]


def records_for_ids(ids: List[str]) -> list:
    return [{"id": _id} for _id in ids]


def record(_id: str) -> dict:
    return {"id": _id}


@pytest.mark.parametrize(
    "rag, spd, ratio_rag, ratio_spd, expected",
    [
        (
                records_gen("r", 10),
                records_gen("s", 10),
                5,
                5,
                ["r1", "s1", "r2", "s2", "r3", "s3", "r4", "s4", "r5", "s5", "r6", "s6", "r7", "s7", "r8", "s8", "r9", "s9", "r10", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10),
                8,
                2,
                ["r1", "r2", "r3", "r4", "s1", "r5", "r6", "r7", "r8", "s2", "r9", "r10", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10),
                80,
                20,
                ["r1", "r2", "r3", "r4", "s1", "r5", "r6", "r7", "r8", "s2", "r9", "r10", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10),
                2,
                8,
                ["s1", "s2", "s3", "s4", "r1", "s5", "s6", "s7", "s8", "r2", "s9", "s10", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "r10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10),
                4,
                6,
                ["s1", "s2", "s3", "r1", "r2", "s4", "s5", "s6", "r3", "r4", "s7", "s8", "s9", "r5", "r6", "s10", "r7", "r8", "r9", "r10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10),
                9,
                1,
                ["r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "s1", "r10", "s2", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 5),
                5,
                5,
                ["r1", "s1", "r2", "s2", "r3", "s3", "r4", "s4", "r5", "s5", "r6", "r7", "r8", "r9", "r10"]
        ),
        (
                records_gen("r", 5),
                records_gen("s", 10),
                6,
                4,
                ["r1", "r2", "r3", "s1", "s2", "r4", "r5", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("r", 10),
                5,
                5,
                ["r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "r10"]
        ),
        (
                records_gen("r", 10),
                records_gen("r", 10),
                8,
                2,
                ["r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "r10"]
        ),
        (
                [record("sku_01"), record("sku_03"), record("sku_05"), record("sku_07"), record("sku_09")],
                [record("sku_02"), record("sku_03"), record("sku_05"), record("sku_06"), record("sku_08")],
                5,
                5,
                ["sku_01", "sku_02", "sku_03", "sku_05", "sku_07", "sku_06", "sku_09", "sku_08"]
        ),
        (
                [record("sku_01"), record("sku_03"), record("sku_05"), record("sku_07"), record("sku_09")],
                [record("sku_02"), record("sku_03"), record("sku_05"), record("sku_06"), record("sku_08")],
                2,
                3,
                ["sku_02", "sku_03", "sku_05", "sku_01", "sku_07", "sku_06", "sku_08", "sku_09"]
        ),
        (
                [record("sku_01"), record("sku_03"), record("sku_05"), record("sku_07"), record("sku_09")],
                [record("sku_02"), record("sku_03"), record("sku_05"), record("sku_06"), record("sku_08")],
                20,
                30,
                ["sku_02", "sku_03", "sku_05", "sku_01", "sku_07", "sku_06", "sku_08", "sku_09"]
        ),
    ]
)
def test_mix_results(spd_env, rag, spd, ratio_rag, ratio_spd, expected):
    ids = [r['id'] for r in MetaSource._mix_results(rag, spd, ratio_rag, ratio_spd)]
    assert ids == expected


@pytest.mark.parametrize(
    "ratio_rag, ratio_spd",
    [
        (None, None),
        (5, None),
        (None, 5),
        (1, -1),
        (0, -1),
        (-1, 1),
        (-1, 0),
        (0, 0),
    ]
)
def test_search_invalid_ratios_falls_back_to_equal_interleaving(spd_env, monkeypatch, ratio_rag, ratio_spd):
    rag_records = records_for_ids(["R1", "R2", "R3"])
    spd_records = records_for_ids(["S1", "S2", "S3"])

    resp = meta_service(return_rag_records=rag_records, return_spd_records=spd_records).search(request())

    assert resp.status == STATUS_SUCCESS
    assert [d["id"] for d in resp.data] == ["R1", "S1", "R2", "S2", "R3", "S3"]


def test_search_interleaves_both(mocker, spd_env, monkeypatch):
    mock_ratio_config(mocker, rag=2, spd=1)

    rag_records = records_for_ids(["R1", "R2", "R3", "R4", "R5"])
    spd_records = records_for_ids(["S1", "S2", "S3", "S4", "S5"])

    resp = meta_service(return_rag_records=rag_records, return_spd_records=spd_records).search(request())

    assert resp.status == STATUS_SUCCESS
    assert [d["id"] for d in resp.data] == ["R1", "R2", "S1", "R3", "R4", "S2", "R5", "S3", "S4", "S5"]


@pytest.mark.parametrize(
    "ratio_rag, ratio_spd",
    [
        (1, 1),
        (5, 5),
        (999, 999),
    ]
)
def test_search_interleaves_both_equal_ratios(mocker, spd_env, monkeypatch, ratio_rag, ratio_spd):
    mock_ratio_config(mocker, rag=ratio_rag, spd=ratio_spd)

    rag_records = records_for_ids(["R1", "R2", "R3", "R4", "R5", "R6"])
    spd_records = records_for_ids(["S1", "S2", "S3", "S4", "S5", "S6"])

    resp = meta_service(return_rag_records=rag_records, return_spd_records=spd_records).search(request())

    assert resp.status == STATUS_SUCCESS
    assert [d["id"] for d in resp.data] == ["R1", "S1", "R2", "S2", "R3", "S3", "R4", "S4", "R5", "S5", "R6", "S6"]


def test_search_only_promoted_filters_spd(mocker, spd_env, monkeypatch):
    mock_ratio_config(mocker, rag=1, spd=2, only_promoted=True)

    rag_records = records_for_ids(["R1", "R2", "R3", "R4", "R5"])
    spd_records = [
        {"id": "S1", "labels": ["PINNED"]},
        {"id": "S2", "labels": ["BOOSTED"]},
        {"id": "S3", "labels": ["BURIED"]},
        {"id": "S4", "labels": []},
        {"id": "S5", "labels": []},
    ]

    resp = meta_service(return_rag_records=rag_records, return_spd_records=spd_records).search(request())

    assert resp.status == STATUS_SUCCESS
    assert [d["id"] for d in resp.data] == ["S1", "S2", "R1", "R2", "R3", "R4", "R5"]
