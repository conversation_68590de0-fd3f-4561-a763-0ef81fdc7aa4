from typing import List, Union

import pytest

from configuration.base.searcher import SearchEngineRatios
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.spd_result import SPDSearchResult
from quepasa.searcher.sources.base import STATUS_SUCCESS
from quepasa.searcher.sources.meta import MetaSource


# --------------------- Helpers ---------------------

class QuepasaDocumentMock(QuepasaDocument):

    def __init__(self, _id: str):
        super().__init__(
            root_id=None,
            id=None,
            sku=_id,
            chunk_index=None,
            client=None,
            domain=None,
            provider=None,
            type="product",
            kind=None,
            level=None,
            url=None,
            language=None,
            title=None,
            keywords=None,
            text=None,
            tokens=None,
            chunks=None,
            start_position=None,
            end_position=None,
            created_at=None,
            updated_at=None,
            embeddings=None,
            score=None,
            metadata=None
        )


def request(**kwargs):
    req = QuepasaRequest(question="test")
    for k, v in kwargs.items():
        setattr(req, k, v)
    return req


def records_gen(_id: str, n: int, is_spd: bool = False) -> List[Union[QuepasaDocument, SPDSearchResult]]:
    return [record(f"{_id}{i}", is_spd) for i in range(1, n + 1)]


def records_for_ids(ids: List[str], is_spd: bool = False) -> List[Union[QuepasaDocument, SPDSearchResult]]:
    return [record(_id, is_spd) for _id in ids]


def record(_id: str, is_spd: bool = False) -> Union[QuepasaDocument, SPDSearchResult]:
    if is_spd:
        return SPDSearchResult(sku=_id, title=None, url=None, metadata={})
    return QuepasaDocumentMock(_id)


# --------------------- Init/Mocks ---------------------

@pytest.fixture
def spd_env(monkeypatch):
    monkeypatch.setenv("SPD_API_URL", "https://search.example.groupbycloud.com/api/search")
    return "https://search.example.groupbycloud.com/api/search"


def mock_ratio_config(mocker, rag: int = 1, spd: int = 1, only_promoted: bool = False):
    config = mocker.Mock()
    config.get_spd_engine_ratios.return_value = SearchEngineRatios(rag=rag, spd=spd)
    config.get_spd_include_promos_only.return_value = only_promoted
    mocker.patch('configuration.main.default.QuepasaConfigurationHub.from_request', return_value=config)


def meta_service(return_rag_records: List, return_spd_records: List):
    class MockSearchEngine:
        def __init__(self, return_records):
            self.return_records = return_records

        def search(self, _):
            return self.return_records

    ms = MetaSource()
    ms.search_engine_rag = MockSearchEngine(return_rag_records)
    ms.search_engine_spd = MockSearchEngine(return_spd_records)
    return ms


# --------------------- Tests ---------------------


@pytest.mark.parametrize(
    "rag, spd, ratio_rag, ratio_spd, expected",
    [
        (
                records_gen("r", 10),
                records_gen("s", 10, True),
                5,
                5,
                ["r1", "s1", "r2", "s2", "r3", "s3", "r4", "s4", "r5", "s5", "r6", "s6", "r7", "s7", "r8", "s8", "r9", "s9", "r10", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10, True),
                8,
                2,
                ["r1", "r2", "r3", "r4", "s1", "r5", "r6", "r7", "r8", "s2", "r9", "r10", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10, True),
                80,
                20,
                ["r1", "r2", "r3", "r4", "s1", "r5", "r6", "r7", "r8", "s2", "r9", "r10", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10, True),
                2,
                8,
                ["s1", "s2", "s3", "s4", "r1", "s5", "s6", "s7", "s8", "r2", "s9", "s10", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "r10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10, True),
                4,
                6,
                ["s1", "s2", "s3", "r1", "r2", "s4", "s5", "s6", "r3", "r4", "s7", "s8", "s9", "r5", "r6", "s10", "r7", "r8", "r9", "r10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 10, True),
                9,
                1,
                ["r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "s1", "r10", "s2", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("s", 5, True),
                5,
                5,
                ["r1", "s1", "r2", "s2", "r3", "s3", "r4", "s4", "r5", "s5", "r6", "r7", "r8", "r9", "r10"]
        ),
        (
                records_gen("r", 5),
                records_gen("s", 10, True),
                6,
                4,
                ["r1", "r2", "r3", "s1", "s2", "r4", "r5", "s3", "s4", "s5", "s6", "s7", "s8", "s9", "s10"]
        ),
        (
                records_gen("r", 10),
                records_gen("r", 10, True),
                5,
                5,
                ["r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "r10"]
        ),
        (
                records_gen("r", 10),
                records_gen("r", 10, True),
                8,
                2,
                ["r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "r10"]
        ),
        (
                records_for_ids(["sku_01", "sku_03", "sku_05", "sku_07", "sku_09"]),
                records_for_ids(["sku_02", "sku_03", "sku_05", "sku_06", "sku_08"], True),
                5,
                5,
                ["sku_01", "sku_02", "sku_03", "sku_05", "sku_07", "sku_06", "sku_09", "sku_08"]
        ),
        (
                records_for_ids(["sku_01", "sku_03", "sku_05", "sku_07", "sku_09"]),
                records_for_ids(["sku_02", "sku_03", "sku_05", "sku_06", "sku_08"], True),
                2,
                3,
                ["sku_02", "sku_03", "sku_05", "sku_01", "sku_07", "sku_06", "sku_08", "sku_09"]
        ),
        (
                records_for_ids(["sku_01", "sku_03", "sku_05", "sku_07", "sku_09"]),
                records_for_ids(["sku_02", "sku_03", "sku_05", "sku_06", "sku_08"], True),
                20,
                30,
                ["sku_02", "sku_03", "sku_05", "sku_01", "sku_07", "sku_06", "sku_08", "sku_09"]
        ),
    ]
)
def test_mix_results(spd_env, rag, spd, ratio_rag, ratio_spd, expected):
    ids = [r.get_product_id() for r in MetaSource._mix_results(rag, spd, ratio_rag, ratio_spd)]
    assert ids == expected


@pytest.mark.parametrize(
    "ratio_rag, ratio_spd",
    [
        (None, None),
        (5, None),
        (None, 5),
        (1, -1),
        (0, -1),
        (-1, 1),
        (-1, 0),
        (0, 0),
    ]
)
def test_search_invalid_ratios_falls_back_to_equal_interleaving(spd_env, monkeypatch, ratio_rag, ratio_spd):
    rag_records = records_for_ids(["R1", "R2", "R3"])
    spd_records = records_for_ids(["S1", "S2", "S3"], True)

    resp = meta_service(return_rag_records=rag_records, return_spd_records=spd_records).search(request())

    assert resp.status == STATUS_SUCCESS
    assert [d.get_product_id() for d in resp.data] == ["R1", "S1", "R2", "S2", "R3", "S3"]


def test_search_interleaves_both(mocker, spd_env, monkeypatch):
    mock_ratio_config(mocker, rag=2, spd=1)

    rag_records = records_for_ids(["R1", "R2", "R3", "R4", "R5"])
    spd_records = records_for_ids(["S1", "S2", "S3", "S4", "S5"], True)

    resp = meta_service(return_rag_records=rag_records, return_spd_records=spd_records).search(request())

    assert resp.status == STATUS_SUCCESS
    assert [d.get_product_id() for d in resp.data] == ["R1", "R2", "S1", "R3", "R4", "S2", "R5", "S3", "S4", "S5"]


@pytest.mark.parametrize(
    "ratio_rag, ratio_spd",
    [
        (1, 1),
        (5, 5),
        (999, 999),
    ]
)
def test_search_interleaves_both_equal_ratios(mocker, spd_env, monkeypatch, ratio_rag, ratio_spd):
    mock_ratio_config(mocker, rag=ratio_rag, spd=ratio_spd)

    rag_records = records_for_ids(["R1", "R2", "R3", "R4", "R5", "R6"])
    spd_records = records_for_ids(["S1", "S2", "S3", "S4", "S5", "S6"], True)

    resp = meta_service(return_rag_records=rag_records, return_spd_records=spd_records).search(request())

    assert resp.status == STATUS_SUCCESS
    assert [d.get_product_id() for d in resp.data] == ["R1", "S1", "R2", "S2", "R3", "S3", "R4", "S4", "R5", "S5", "R6", "S6"]


def test_search_only_promoted_filters_spd(mocker, spd_env, monkeypatch):
    mock_ratio_config(mocker, rag=1, spd=2, only_promoted=True)

    rag_records = records_for_ids(["R1", "R2", "R3", "R4", "R5"])
    spd_records = records_for_ids(["S1", "S2", "S3", "S4", "S5"], True)

    spd_records[0].metadata = {"labels": ["PINNED"]}
    spd_records[1].metadata = {"labels": ["BOOSTED"]}
    spd_records[2].metadata = {"labels": ["SPONSORED"]}
    spd_records[3].metadata = {"labels": ["BURIED"]}
    spd_records[4].metadata = {"labels": []}

    resp = meta_service(return_rag_records=rag_records, return_spd_records=spd_records).search(request())

    assert resp.status == STATUS_SUCCESS
    assert [d.get_product_id() for d in resp.data] == ["S1", "S2", "R1", "S3", "R2", "R3", "R4", "R5"]
