import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, Generator, List, Tuple
from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse, QuepasaAnswer, QuepasaStreamAnswer
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from configuration.main.default import QuepasaConfigurationHub
from src.lib.constants import ANSWER_TYPE_ERROR
from ..models.answer import AnswerFormatter

logger = QuepasaLogger().get_instance(__name__)

STATUS_SUCCESS = "ok"
STATUS_ERROR = "error"

ERROR_MESSAGE_SEARCH_NOT_SUPPORTED = "Search not supported for this source"
ERROR_MESSAGE_NO_SEARCH_RESULTS = "No search results found"
ERROR_MESSAGE_NO_RELEVANT_DOCUMENTS = "No relevant documents found"
ERROR_MESSAGE_PROMPT_TOO_LONG = "Prompt too long for model context window"
ERROR_MESSAGE_NO_RELEVANT_ANSWER = "No relevant answer found in documents"

class BaseSource(ABC):
    """Base class for all search sources"""

    def __init__(self):
        pass

    @abstractmethod
    def search(self, request: QuepasaRequest) -> QuepasaResponse:
        """Execute search"""
        pass

    @abstractmethod
    def get_answer(self, request: QuepasaRequest, stream: bool = False) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """Get answer"""
        pass
    
    def _handle_error(self, request: QuepasaRequest, error: str, stream: bool = False) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """Handle error by returning appropriate error response"""
        if stream:
            return self._handle_streaming_error(request, error)
        else:
            return self._handle_non_streaming_error(request, error)
    
    def _handle_non_streaming_error(self, request: QuepasaRequest, error: str) -> QuepasaResponse:
        """Handle error for non-streaming responses"""
        logger.error(f"Error in source: {error}")
        
        # Get appropriate error message
        config = QuepasaConfigurationHub.from_request(request)
        source = request.source
        language_code = config.get_language_code()

        if error in [
            ERROR_MESSAGE_NO_SEARCH_RESULTS,
            ERROR_MESSAGE_NO_RELEVANT_DOCUMENTS,
            ERROR_MESSAGE_NO_RELEVANT_ANSWER
        ]:
            answer = config.get_I_DONT_KNOW_constant(source, language_code)
        else:
            answer = config.get_SMTH_WENT_WRONG_constant(source, language_code)

        formatter = AnswerFormatter()
        formatted_answer = formatter.format_answer(request, answer)
            
        response = {
            'type': ANSWER_TYPE_ERROR,
            'text': formatted_answer.text,
            'markdown': formatted_answer.markdown,
            'references': formatted_answer.references,
        }
        
        return QuepasaResponse(
            status=STATUS_ERROR,
            data=response,
            error=error
        )
    
    def _handle_streaming_error(self, request: QuepasaRequest, error: str) -> Generator[QuepasaStreamAnswer, None, None]:
        """Handle error for streaming responses"""
        logger.error(f"Error in source: {error}")
        
        # Get appropriate error message
        config = QuepasaConfigurationHub.from_request(request)
        source = request.source
        language_code = config.get_language_code()

        if error in [
            ERROR_MESSAGE_NO_SEARCH_RESULTS,
            ERROR_MESSAGE_NO_RELEVANT_DOCUMENTS
        ]:
            answer = config.get_I_DONT_KNOW_constant(source, language_code)
        else:
            answer = config.get_SMTH_WENT_WRONG_constant(source, language_code)

        formatter = AnswerFormatter()
        formatted_answer = formatter.format_answer(request, answer)
            
        response = {
            'type': ANSWER_TYPE_ERROR,
            'text': formatted_answer.text,
            'markdown': formatted_answer.markdown,
            'references': formatted_answer.references,
        }

        yield QuepasaStreamAnswer(
            loading=False,
            streaming=False,
            created_at=int(time.time()),
            **response.to_dict()
        )
