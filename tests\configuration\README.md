# Configuration Validation

This folder contains scripts and utilities for validating YAML configuration files and ensuring they match the original hardcoded values.

## Validation Scripts

### Core Validation
- **`validate_config.py`** - Main validation script for all client configurations
  ```bash
  python configuration/validation/validate_config.py
  ```

### Detailed Testing
- **`validate_yaml_values.py`** - Direct YAML structure and value validation
  ```bash
  python configuration/validation/validate_yaml_values.py
  ```

- **`test_yaml_prompts.py`** - Test prompt template functionality and variable substitution
  ```bash
  python configuration/validation/test_yaml_prompts.py
  ```

- **`test_config_comparison.py`** - Compare YAML vs original hardcoded values (requires dependencies)
  ```bash
  python configuration/validation/test_config_comparison.py
  ```

### Reference Files
- **`original_hardcoded.py`** - Backup of original hardcoded configuration for testing comparisons

## Usage

### Quick Validation
For a quick check that all YAML configurations are valid:
```bash
python configuration/validation/validate_config.py
```

### Full Testing Suite
Run all validation tests:
```bash
python configuration/validation/validate_yaml_values.py
python configuration/validation/test_yaml_prompts.py
```

### Development Workflow
1. After making changes to YAML configuration files
2. Run validation scripts to ensure correctness
3. All tests should pass before deploying

## Test Coverage
- ✅ 41 YAML structure validation tests
- ✅ 21 prompt template tests  
- ✅ Configuration consistency tests
- ✅ Variable substitution tests
- ✅ Complete configuration loading tests