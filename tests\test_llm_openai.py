import os
import pytest
from unittest.mock import Mock, patch, AsyncMock
from src.lib.llm.openai import OpenAILLM
from src.lib.llm.providers import LLMProvider

@pytest.fixture
def llm():
    """Fixture that provides an OpenAI LLM instance with mocked client."""
    with patch.dict('os.environ', {'OPENAI_API_KEY': 'test-key'}):
        llm = OpenAILLM()
        with patch.object(llm, 'client') as mock_client, \
             patch.object(llm, 'async_client') as mock_async_client:
            
            # Mock sync responses
            mock_response = Mock()
            mock_response.choices = [Mock(message=Mock(content="Test response"))]
            mock_client.chat.completions.create.return_value = mock_response
            
            # Mock async responses
            mock_chunks = [
                Mock(choices=[Mock(delta=Mock(content="Hello"))]),
                <PERSON><PERSON>(choices=[Mock(delta=Mock(content=" world"))]),
                <PERSON><PERSON>(choices=[Mock(delta=Mock(content="!"))])
            ]
            mock_stream = AsyncMock()
            mock_stream.__aiter__.return_value = mock_chunks
            mock_async_client.chat.completions.create = AsyncMock(return_value=mock_stream)
            
            yield llm

def test_provider(llm):
    """Test provider property."""
    assert llm.provider == LLMProvider.OPENAI

def test_get_answer_success(llm):
    """Test successful answer generation."""
    test_response = "Test response"
    prompts = [{"role": "user", "content": "test prompt"}]
    
    mock_response = Mock()
    mock_response.choices = [Mock(message=Mock(content=test_response))]
    llm.client.chat.completions.create = Mock(return_value=mock_response)
    
    result = llm.get_answer("gpt-4-turbo-preview", prompts, 100)
    
    assert result == test_response
    llm.client.chat.completions.create.assert_called_once()

def test_get_answer_with_json_mode(llm):
    """Test answer generation with JSON mode enabled."""
    test_response = '{"key": "value"}'
    prompts = [{"role": "user", "content": "test prompt"}]
    
    mock_response = Mock()
    mock_response.choices = [Mock(message=Mock(content=test_response))]
    llm.client.chat.completions.create = Mock(return_value=mock_response)
    
    result = llm.get_answer("gpt-4-turbo-preview", prompts, 100, json_mode=True)
    
    assert result == test_response
    llm.client.chat.completions.create.assert_called_once()

def test_get_answer_failure(llm):
    """Test handling of API errors."""
    prompts = [{"role": "user", "content": "test prompt"}]
    
    llm.client.chat.completions.create = Mock(side_effect=Exception("API Error"))
    
    with pytest.raises(Exception, match="API Error"):
        llm.get_answer("gpt-4-turbo-preview", prompts, 100)

def test_missing_api_key():
    """Test initialization with missing API key."""
    with patch.dict('os.environ', clear=True):
        with pytest.raises(ValueError, match="OPENAI_API_KEY"):
            OpenAILLM()

@pytest.mark.asyncio
async def test_get_streaming_answer(llm):
    """Test streaming answer generation."""
    prompts = [{"role": "user", "content": "test"}]
    chunks = []
    async for chunk in llm.get_streaming_answer("gpt-4-turbo-preview", prompts, 100):
        chunks.append(chunk)
    assert chunks == ["Hello", " world", "!"]

@pytest.mark.slow
@pytest.mark.asyncio
async def test_get_cached_streaming_chunks(llm):
    """Test getting cached streaming chunks."""
    model = "gpt-4-turbo-preview"
    prompts = [{"role": "user", "content": "test"}]
    size = 100
    
    # Mock streaming response for cache miss
    mock_response = AsyncMock()
    mock_response.__aiter__.return_value = [
        Mock(choices=[Mock(delta=Mock(content=chunk))]) 
        for chunk in ["Hello", " world", "!"]
    ]
    
    # Test when cache exists
    with patch('os.path.exists', return_value=True), \
         patch('builtins.open', create=True) as mock_open, \
         patch.object(llm.async_client.chat.completions, 'create', return_value=mock_response):
        # Return the joined chunks as a single string
        mock_open.return_value.__enter__.return_value.read.return_value = "Hello world!"
        
        chunks = []
        async for chunk in llm.get_cached_streaming_chunks(model, prompts, size):
            chunks.append(chunk)
        
        # Expect a single chunk with the complete response
        assert chunks == ["Hello world!"]
    
    # Test when cache doesn't exist
    with patch('os.path.exists', return_value=False), \
         patch.object(llm.async_client.chat.completions, 'create', return_value=mock_response) as mock_create:
        chunks = []
        async for chunk in llm.get_cached_streaming_chunks(model, prompts, size):
            chunks.append(chunk)
        
        # When no cache exists, we get individual chunks
        assert chunks == ["Hello", " world", "!"]
        mock_create.assert_called_once() 