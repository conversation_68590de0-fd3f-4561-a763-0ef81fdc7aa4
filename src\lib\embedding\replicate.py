import os
import replicate
from typing import Optional, List

from .base import <PERSON><PERSON>mbe<PERSON>
from .cache import EmbeddingCacheMixin
from .providers import EmbeddingProvider
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>ogger
from src.lib.utils import get_tokenizer, get_max_embedding_model_token_length

logger = QuepasaLogger().get_instance(__name__)

class ReplicateEmbedding(BaseEmbedding, EmbeddingCacheMixin):
    """Replicate embedding provider.
    
    This class provides access to Replicate's embedding models.
    """
    def __init__(self):
        """Initialize Replicate embedding provider."""
        super().__init__()

        # Check for API key
        self.api_key = os.getenv('REPLICATE_API_KEY')
        if not self.api_key or self.api_key == '':
            raise ValueError("REPLICATE_API_KEY environment variable is not set")
        
        # Initialize the client
        self.client = replicate.Client(api_token=self.api_key)
        
    @property
    def provider(self) -> EmbeddingProvider:
        return EmbeddingProvider.REPLICATE

    def _truncate_text(self, model_version: str, text: str) -> str:
        """Truncate text to fit within token limit.
        
        Args:
            text: Text to truncate
            
        Returns:
            Truncated text that fits within the model's token limit
        """
        max_tokens = get_max_embedding_model_token_length(model_version)
        if len(get_tokenizer().encode(text)) > max_tokens:
            new_text = ""
            for line in text.split("\n"):
                line_nl = "\n" + line
                if len(get_tokenizer().encode((new_text + line_nl).strip())) > max_tokens:
                    break
                new_text += line_nl
            return new_text.strip()
        return text

    def get_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get embedding from Replicate API.
        
        Args:
            model_version: The model version to use for embeddings
                          (e.g., 'cuuupid/gte-qwen2-7b-instruct:67b1736bae9312a321217b2e10547882943b9e4a285eac4cba4043fab954b954')
            text: The text to get embedding for
            
        Returns:
            List of floats representing the embedding, or None if the request fails
        """
        text = self._truncate_text(model_version, text)
        
        try:
            output = self.client.run(
                model_version,
                input={
                    "text": [text]
                }
            )
            
            # Return the first result as embedding
            if output and len(output) > 0:
                return output[0]
            
            return None
        
        except Exception as e:
            logger.error(f"Failed to get embedding from Replicate: {str(e)}")
            return None 