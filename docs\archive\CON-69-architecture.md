# CON-69 Brownfield Enhancement Architecture

## Introduction

This document outlines the architectural approach for enhancing the Quepasa project with structured product metadata, as specified in `/docs/prd.md`. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring seamless and safe integration with the existing microservices system.

## Existing Project Analysis

**Current Project State:** The system is a distributed microservices architecture (API, Crawler, Data-Processor, Indexer, Searcher) orchestrated with Celery, using Elasticsearch as the primary data store.

**Analysis Source:** This architecture is based on a fresh analysis of repomix-output.xml, prd.md, and CON-69-tests.md. The existing technical documentation is sufficient for this enhancement.

**Identified Constraints:** The key constraint is maintaining 100% backward compatibility for the existing conversational API. All changes must be additive. A critical gap was identified where the sku field was not added to the quepasa/searcher/models/document.py::QuepasaDocument model in a previous implementation; this must be rectified along with the addition of the new metadata field.

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-09-09 | 1.0 | Initial draft of Brownfield Architecture for CON-69. | <PERSON> (Architect) |

## Enhancement Scope and Integration Strategy

### Enhancement Overview

**Enhancement Type:** New Feature Addition & Major Feature Modification.

**Scope:** To introduce a new product document type and a metadata field. This will flow through the entire data pipeline to be stored in Elasticsearch and ultimately served in a new products array in the conversational API response.

**Integration Impact:** Significant. Changes are required in the data ingestion library, data processor, Elasticsearch schema, searcher models, search logic, and API response models.

### Integration Approach

**Code Integration Strategy:** Modifications will be made to existing files to maintain the current microservice architecture. A new filtering method will be added to src/lib/markdown_converter.py.

**Database Integration:** The Elasticsearch mapping will be updated to include a new non-indexed metadata text field. This requires updating configuration/main/cli_create_index.py.

**API Integration:** The QuepasaAnswer model in quepasa/searcher/models/response.py will be updated with a new optional products field, ensuring no breaking changes for existing consumers.

**UI Integration:** N/A. This is a backend-only change.

### Compatibility Requirements

**Existing API Compatibility:** The /retrieve/answer endpoint must remain fully backward compatible. Clients not aware of the new products field will continue to function correctly.

**Database Schema Compatibility:** The addition of the metadata field is non-breaking. A re-index will be required to populate this field for existing product documents.

**Performance Impact:** The metadata field will be non-indexed ("index": false) in Elasticsearch to avoid performance degradation on the search index.

### Tech Stack Alignment

The proposed changes align with the existing technology stack. No new technologies are being introduced.

| Category | Current Technology | Version | Usage in Enhancement | Notes |
|----------|-------------------|---------|---------------------|-------|
| Language | Python | 3.9/3.11 | All new logic will be in Python. | Maintain existing coding standards. |
| Frameworks | FastAPI, Celery, Flask | latest | Existing FastAPI and Celery tasks will be modified. | No changes to framework usage patterns. |
| Database | Elasticsearch | 8.12.2 | The index mapping will be updated to include the metadata field. | Change is additive. |

## Data Models and Schema Changes

### 1. Elasticsearch Mapping (configuration/main/cli_create_index.py)

The Elasticsearch mapping requires the addition of the metadata field. This field should be of type text and explicitly marked as non-indexed to prevent performance issues.

**Required Change:**

```python
# In configuration/main/cli_create_index.py, within the "mappings" -> "properties" section:
"mappings": {
    "properties": {
        # ... existing fields like root_id, id, sku ...
        "sku": {
            "type": "keyword",
            # ... existing sku fields
        },
        "metadata": {
            "type": "text",
            "index": false
        },
        # ... other existing fields
    }
}
```

### 2. Data Models (quepasa/searcher/models/)

#### document.py

The QuepasaDocument model is critical for RAG search. It was missing the sku field from a previous implementation, and now requires the metadata field. Both must be added.

**Required Change in quepasa/searcher/models/document.py:**

```python
@dataclass
class QuepasaDocument:
    # ... existing fields
    title: Optional[str]
    sku: Optional[str] = None      # CRITICAL FIX & NEW
    metadata: Optional[str] = None # NEW
    keywords: Optional[List[str]]
    # ... existing fields
```

#### response.py

A new ProductItem model is needed for structured API responses. The main QuepasaAnswer model must also be updated to include a list of these items.

**Required Changes in quepasa/searcher/models/response.py:**

```python
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field

# ... existing Reference dataclass ...

@dataclass
class ProductItem:
    id: str
    title: str
    url: str
    collection: str
    allMeta: Dict[str, Any] # This will be the parsed JSON from the metadata string

@dataclass
class QuepasaAnswer:
    type: Optional[str] = field(default=None)
    text: Optional[str] = field(default=None)
    references: Optional[Dict[str, Reference]] = field(default=None)
    markdown: Optional[str] = field(default=None)
    products: Optional[List[ProductItem]] = field(default=None) # NEW FIELD
    think: Optional[str] = field(default=None)
    price: Optional[float] = field(default=None)
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

# ... rest of the file ...
```

Component Architecture
The enhancement touches several components in the data pipeline.

1. Ingestion Library (src/lib/markdown_converter.py)
This library converts raw product data into the QuepasaDocument format for ingestion. It needs to be updated to set the document type to product and add the filtered metadata field.

Required Changes:

Define Exclusion Keys:

METADATA_EXCLUDE_KEYS = { "_id", "indexables", "samplePrecision", "dynamicFacets" }

**Create New Filtering Method:**

```python
def _filter_product_for_metadata(product: Dict[str, Any]) -> Dict[str, Any]:
    """Filters the raw product data to be stored in the metadata field."""
    return {k: v for k, v in product.items() if k not in METADATA_EXCLUDE_KEYS}
```

**Update products_to_documents:**

```python
def products_to_documents(products: List[Dict[str, Any]], ...) -> List[Dict[str, Any]]:
    # ... inside the loop for each product ...

    # Preprocess for markdown (existing logic)
    md_ready_product = _preprocess_product(product, ...)

    # Create markdown chunks (existing logic)
    chunks = _create_product_chunks(md_ready_product)

    # Filter for metadata (NEW)
    metadata_dict = _filter_product_for_metadata(product)

    document_data = {
        'id': md_ready_product['SKU'],
        'url': md_ready_product.get('uri', ''),
        'title': md_ready_product.get('title', ''),
        'type': 'product',  # NEW
        'sku': str(md_ready_product['SKU']),
        'metadata': json.dumps(metadata_dict), # NEW
        'price_from': price_from,
        'price_to': price_to,
        'chunks': chunks
    }
    documents.append(document_data)
    # ... rest of the function ...
```

### 2. Data Processor (quepasa/data_processor/tasks.py)

The data processor cleans and prepares documents before they are sent to the indexer. The metadata field must be added to the list of fields persisted in the final document object.

**Required Change in process_document_upsert:**

```python
# ... inside process_document_upsert ...
# In the clean_doc dictionary comprehension, add 'metadata'
clean_doc = {
    key: doc[key] for key in [
        'provider', 'domain', 'access', 'type',
        'id', 'url', 'title', 'keywords',
        'sku', 'price_from', 'price_to',
        'metadata', # ADD THIS
        'chunks', 'languages', 'created_at', 'updated_at'
    ] if key in doc
}
# ... rest of the task ...
```

### 3. Searcher (quepasa/searcher/)

#### Core RAG Search Logic (core/rag_search.py)

The RAG search logic needs to be updated to retrieve the sku and metadata fields from Elasticsearch and populate them into the QuepasaDocument objects.

**Required Changes:**
- In _get_document_scores and _get_chunk_scores, ensure the _source configuration includes sku and metadata.
- When constructing QuepasaDocument objects from Elasticsearch hits, map the sku and metadata fields from hit["_source"].

#### Meta Source (sources/meta.py) & Answer Retriever (sources/mixins.py)

The AnswerRetrieverMixin is the primary component responsible for constructing the final QuepasaAnswer. It needs to be modified to handle the new product document type.

**Required Changes in AnswerRetrieverMixin::retrieve_answer:**

1. **Initialize products list:** Before looping through documents, create an empty list `products = []`.

2. **Differentiate Document Types:** Inside the loop that processes documents, check the type of each document.
   - If `isinstance(document, QuepasaDocument) and document.type == 'product'`:
     - Parse `document.metadata` from a JSON string into a dictionary.
     - Create a ProductItem instance.
     - Append it to the products list.
     - Continue populating source_hash for references in the markdown.
   
   - If `isinstance(document, SPDSearchResult)`:
     - Create a ProductItem instance from the SPDSearchResult fields.
     - Append it to the products list.
   
   - For all other document types, continue with the existing logic of populating source_hash for the references field.

3. **Populate QuepasaAnswer:** When creating the final QuepasaAnswer (or QuepasaStreamAnswer), pass the populated products list to the products field.

## Testing Strategy

The testing strategy is detailed in CON-69-tests.md. The plan involves modifying existing unit and integration tests and adding new ones to cover:

- **Ingestion:** metadata and type fields are correctly added and filtered.
- **Elasticsearch:** The metadata field is correctly mapped as non-indexed and populated.
- **RAG Search:** The sku and metadata fields are correctly retrieved and populated into QuepasaDocument objects.
- **API Response:** The products array is correctly structured with ProductItem objects for both RAG and SPD results, and backward compatibility is maintained.

## Handoff to Agents

### Scrum Manager / Product Owner Handoff

The PRD `/docs/prd.md` has broken down the work into four logical user stories. Please use this architecture document to create detailed, actionable story files in docs/stories/. Ensure that the acceptance criteria for each story align with the technical specifications and testing requirements outlined here and in `/docs/CON-69-tests.md`.

### Developer Handoff

This architecture document provides the blueprint for implementation. The key areas of change are:

- **src/lib/markdown_converter.py:** Implement the new _filter_product_for_metadata function and update products_to_documents.
- **configuration/main/cli_create_index.py:** Add the non-indexed metadata field to the Elasticsearch mapping.
- **quepasa/data_processor/tasks.py:** Update the clean_doc dictionary to include the metadata field.
- **quepasa/searcher/models/document.py:** Add sku and metadata fields to the QuepasaDocument dataclass.
- **quepasa/searcher/models/response.py:** Define the new ProductItem dataclass and add the products list to QuepasaAnswer.
- **quepasa/searcher/core/rag_search.py:** Update search queries to retrieve sku and metadata and populate the QuepasaDocument model.
- **quepasa/searcher/sources/mixins.py:** Update the AnswerRetrieverMixin to process product type documents and populate the products array in the final API response.