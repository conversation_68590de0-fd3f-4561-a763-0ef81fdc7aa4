import os
import hashlib
import logging
import numpy as np
from typing import Optional, List, Tuple
from abc import ABC

from src.lib.files import QuepasaFiles
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)
qp_files = QuepasaFiles()

class EmbeddingCacheMixin(ABC):
    """Mixin class that provides caching functionality for embeddings.
    
    This mixin should be used with classes that inherit from BaseEmbedding
    and implement the following:
    - get_embedding(text: str) -> Optional[List[float]]
    - model property that returns a string identifier
    """
    
    def __init__(self):
        """Initialize the caching mixin.
        
        Note: This should be called via super().__init__() in the implementing class.
        """
        self._cache_dir = "cache/embedding"
        self._local_cache_dir = os.path.join(os.path.dirname(__file__), "../../../cache/embedding")
        os.makedirs(self._local_cache_dir, exist_ok=True)

    def _get_cache_paths(self, model_version: str, text: str) -> Tuple[str, str]:
        """Get local and remote cache paths for the embedding.
        
        Args:
            model_version: The model version to use for embeddings
            text: The text to get cache paths for
            
        Returns:
            Tuple of (local_path, remote_path)
        """
        # Generate MD5 hash of the text
        text_md5 = hashlib.md5(text.encode('utf-8')).hexdigest()
                
        # Remote cache path with hierarchical structure
        path_parts = []
        for i in range(3):  # Create 3 levels of directories
            if i + 1 <= len(text_md5):
                path_parts.append(text_md5[0:i + 1])
        path_parts.append(text_md5)
        
        # Local cache path
        local_path = os.path.join(self._local_cache_dir, model_version, *path_parts) + '.npy'

        # Remote cache path
        remote_path = f"{self._cache_dir}/{model_version}/{'/'.join(path_parts)}.npy"
        return local_path, remote_path

    def _load_local_cache(self, local_path: str) -> Optional[List[float]]:
        """Try to load embedding from local cache.
        
        Args:
            local_path: Path to local cache file
            
        Returns:
            Cached embedding if available, None otherwise
        """
        if os.path.isfile(local_path):
            try:
                return np.load(local_path)
            except Exception as e:
                logger.warning(f"Failed to load local cache {local_path}: {str(e)}")
        return None

    def _load_remote_cache(self, remote_path: str, local_path: str) -> Optional[List[float]]:
        """Try to load embedding from remote cache.
        
        Args:
            remote_path: Path to remote cache file
            local_path: Path to save downloaded file
            
        Returns:
            Cached embedding if available, None otherwise
        """
        if qp_files.exists(remote_path):
            try:
                qp_files.download_file(remote_path, local_path)
                return self._load_local_cache(local_path)
            except Exception as e:
                logger.warning(f"Failed to load remote cache {remote_path}: {str(e)}")
        return None

    def _save_embedding(self, embedding: List[float], local_path: str, remote_path: str) -> None:
        """Save embedding to both local and remote cache.
        
        Args:
            embedding: The embedding to save
            local_path: Path to local cache file
            remote_path: Path to remote cache file
        """
        try:
            # Save locally
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            np.save(local_path, embedding)
            
            # Save remotely
            qp_files.upload_file(local_path, remote_path)
        except Exception as e:
            logger.warning(f"Failed to save embedding cache: {str(e)}")

    def get_cached_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get an embedding from cache or compute a new one
        
        Args:
            model_version: The model version to use
            text: The text to get embedding for
            
        Returns:
            The embedding if available/computable, None otherwise
        """
        # Get cache paths
        local_path, remote_path = self._get_cache_paths(model_version, text)
        
        # Try local cache
        embedding = None
        try:
            embedding = self._load_local_cache(local_path)
            if embedding is not None:
                return embedding
        except Exception as e:
            logger.warning(f"Error loading from local cache ({local_path}): {str(e)}")
            
        # Try remote cache
        try:
            embedding = self._load_remote_cache(remote_path, local_path)
            if embedding is not None:
                return embedding
        except Exception as e:
            logger.warning(f"Error loading from remote cache ({remote_path}): {str(e)}")
            
        # Compute new embedding
        try:
            embedding = self.get_embedding(model_version, text)
            if embedding is not None:
                self._save_embedding(embedding, local_path, remote_path)
        except Exception as e:
            logger.warning(f"Error computing embedding: {str(e)}")
            
        return embedding 