apiVersion: batch/v1
kind: Job
metadata:
  name: kibana-user-init
  namespace: quepasa
spec:
  backoffLimit: 6
  template:
    spec:
      containers:
      - name: kibana-user-init
        image: curlimages/curl
        imagePullPolicy: IfNotPresent
        command: ["/bin/sh"]
        args:
        - -c
        - |
          # Wait for Elasticsearch to be available
          echo "Waiting for Elasticsearch to become available..."
          until curl -s http://elasticsearch:9200 -u "elastic:${ELASTIC_PASSWORD}"; do
            echo "Elasticsearch not ready, retrying in 10 seconds..."
            sleep 10
          done
          
          echo "Elasticsearch is available, setting up kibana_system user..."
          # Configure kibana_system user
          curl -X POST -u "elastic:${ELASTIC_PASSWORD}" \
            "http://elasticsearch:9200/_security/user/kibana_system/_password" \
            -H 'Content-Type: application/json' \
            -d "{\"password\":\"${KIBANA_PASSWORD}\"}"
            
          # Verify user was created successfully
          if curl -s -o /dev/null -w "%{http_code}" \
            -u "kibana_system:${KIBANA_PASSWORD}" \
            http://elasticsearch:9200/_security/_authenticate | grep -q "200"; then
            echo "kibana_system user configured successfully"
            exit 0
          else
            echo "Failed to configure kibana_system user"
            exit 1
          fi
        env:
        - name: ELASTIC_PASSWORD
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: ELASTICSEARCH_PASSWORD
        - name: KIBANA_PASSWORD
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: KIBANA_PASSWORD
      restartPolicy: OnFailure 