import os

SCRIPT_DIR = os.path.realpath( os.path.dirname( __file__ ) )

import json
import math

prices_config = None

PROFIT_MARGIN_QUESTION = 2
PROFIT_MARGIN_DOCUMENT = 3
ELASTIC_SEARCH_PROFIT_MARGIN = 1.25

def get_quepasa_inference_price( model_version, question_tokens, input_tokens, output_tokens ):
    global prices_config

    if prices_config == None:
        prices_config = {}
        with open(f"{SCRIPT_DIR}/prices.json") as f:
            prices_config = json.loads( f.read() )

    # print(f"{model_version = }, {question_tokens = }, {input_tokens = }, {output_tokens = }")

    classification_prompt_size_in_tokens = 500
    embedding_cost_per_million_tokens = 0.02
    input_cost_per_million_tokens = 60
    output_cost_per_million_tokens = 120
    if model_version in prices_config:
        if (
            'input' in prices_config[ model_version ]
            and prices_config[ model_version ]['input'] != None
            and prices_config[ model_version ]['input'] > 0.0
        ):
            input_cost_per_million_tokens = prices_config[ model_version ]['input']

        if (
            'output' in prices_config[ model_version ]
            and prices_config[ model_version ]['output'] != None
            and prices_config[ model_version ]['output'] > 0.0
        ):
            output_cost_per_million_tokens = prices_config[ model_version ]['output']

    # print(f"{input_cost_per_million_tokens = }")
    # print(f"{output_cost_per_million_tokens = }")

    cost_per_one_call_for_backend = 0.00343750

    return "{:.2f}".format(
        PROFIT_MARGIN_QUESTION
        * (
            question_tokens * (
                embedding_cost_per_million_tokens
                + classification_prompt_size_in_tokens
            )
            + input_tokens * input_cost_per_million_tokens
            + output_tokens * output_cost_per_million_tokens
        )
        / 1_000_000
        + cost_per_one_call_for_backend
    )

def ceil_to_cents( value ):
    return "{:.2f}".format( math.ceil( value * 100 ) / 100 )

def get_raw_quepasa_document_price_per_document( tokens ):
    cost_per_million_tokens_to_index_the_data = 0.02
    avg_tokens_per_page = 900
    cost_per_page = 0.001

    price_per_tokens = PROFIT_MARGIN_DOCUMENT * (
        cost_per_million_tokens_to_index_the_data * tokens / 1_000_000
        + tokens / avg_tokens_per_page * cost_per_page
    )
    # print(f"{price_per_tokens = }")
    return price_per_tokens

def get_raw_quepasa_document_price_for_monthly_subscription( chunks, tokens ):
    bytes_per_chunks = 1536 * 8
    additional_fixed_bytes = 1024
    additional_dynamic_bytes = 1.05
    average_chars_per_token = 7
    average_bytes_per_char = 2.5

    approx_bytes_len = tokens * average_chars_per_token * average_bytes_per_char
    total_bytes = ( approx_bytes_len + ( bytes_per_chunks + additional_fixed_bytes ) * chunks ) * additional_dynamic_bytes
    # print(f"{total_bytes / 1024 / 1024 = }")

    hour_rate = 0.3768 / 280  # Per Gb
    month_rate = 365.25 / 12 * 24 * hour_rate  # Per Gb
    # print(f"{month_rate = }")

    elastic_price = ELASTIC_SEARCH_PROFIT_MARGIN * month_rate * total_bytes / 1024 / 1024 / 1024
    # print(f"{elastic_price = }")
    return elastic_price

def get_quepasa_document_price_per_document( tokens ):
    price_per_tokens = get_raw_quepasa_document_price_per_document( tokens )
    return ceil_to_cents( price_per_tokens )

def get_quepasa_document_price_for_monthly_subscription( chunks, tokens ):
    elastic_price = get_raw_quepasa_document_price_for_monthly_subscription( chunks, tokens )
    return ceil_to_cents( elastic_price )

def get_quepasa_document_price( chunks, tokens ):
    price_per_tokens = get_raw_quepasa_document_price_per_document( tokens )
    elastic_price = get_raw_quepasa_document_price_for_monthly_subscription( chunks, tokens )
    return ceil_to_cents( price_per_tokens + elastic_price )
