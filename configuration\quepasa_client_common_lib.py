import re

#
# Typography helpers
#
MIN_TEXT_LEN_BEFORE_LINKS = 100
MAX_TEXT_LEN_BEFORE_LINKS = 500

def get_markdown_typography(text, raw_links):
    links = {}
    for index in raw_links:
        links[str(index)] = raw_links[index]

    new_indexes = get_url_to_new_index(links)
    paragraps = split_to_paragraphs(text)  # example return [str1, str2, str3]
    paragraphs_with_links = []
    for paragraph in paragraps:
        # "[1], [2]" => "[1] [2]"
        paragraph = remove_comma_between_links(paragraph)
        # example return: [2, 3, 5]
        used_links = find_used_links(paragraph)
        if len(used_links) > 0:
            # "text [3] [5]." => "text"
            paragraph = remove_links(paragraph)
        else:
            paragraph = update_links_index(paragraph, used_links, new_indexes)
            used_links = []

        paragraphs_with_links.append((paragraph, used_links))

    # add sources
    text_len = 0
    merged_links = []
    paragraphs_with_new_links = []
    # merge links with tiny paragraphs:
    for (paragraph, used_links) in paragraphs_with_links:
        new_links = to_new_links(used_links, new_indexes)
        text_len += len(paragraph)
        if text_len < MIN_TEXT_LEN_BEFORE_LINKS:
            merged_links = merge_links(merged_links, new_links)
            new_links = []
        else:
            text_len = 0
            new_links = merge_links(merged_links, new_links)
            merged_links = []
        paragraphs_with_new_links.append((paragraph, new_links))

    # add links to last paragraph
    merged_links = merge_links(merged_links, new_links)
    paragraphs_with_new_links[-1] = (paragraphs_with_new_links[-1][0], merged_links)

    text_len = 0
    prev_new_links = []
    new_paragraphs = []
    for (paragraph, used_links) in reversed(paragraphs_with_new_links):
        if text_len > MAX_TEXT_LEN_BEFORE_LINKS or used_links != prev_new_links:
            paragraph = add_sources(paragraph, used_links)
            text_len = len(paragraph)
            prev_new_links = used_links
        else:
            text_len += len(paragraph)

        new_paragraphs.append(paragraph)

    new_paragraphs.reverse()
    return "\n\n".join(new_paragraphs)

def get_url_to_new_index(links):
    # Use same indexes for same URLs
    url_to_index = {}
    old_to_new_indexes = {}
    for key, link in links.items():
        url = link["url"]
        if url not in url_to_index:
            url_to_index[url] = key

        old_to_new_indexes[key] = url_to_index[url]
    return old_to_new_indexes

def remove_comma_between_links(text):
    # "[1], [2]" => "[1] [2]"
    return re.sub(r'(\d+\])(?:, | и )\[', r'\1 [', text)

def split_to_paragraphs(text):
    # cases: \n\n
    return re.split(r'\n\n', text)

def find_used_links(text):
    # example return: [2, 3, 5]
    links = re.findall(r'\[(\d+)\]', text)
    return links

def remove_links(text):
    # "text [3] [5]." => "text."
    rx = r'\s*\[(\d+)\]'
    return re.sub(rx, '', text)

def add_sources(text, links):
    if len(links) == 0:
        return text
    src_list = ", ".join([f"[{new_index}]" for new_index in links])
    return f"""
{text}
{src_list}
    """.strip()

def update_links_index(text, used_links, new_indexes):
    for link in used_links:
        new_index = new_indexes[link]
        text = text.replace(f"[{link}]", f"[{new_index}]")
    return text

def is_the_same_links(links1, links2, new_indexes):
    new_links1 = [new_indexes[link] for link in links1]
    new_links2 = [new_indexes[link] for link in links2]
    return new_links1 == new_links2

def to_new_links(links, url_to_new_index):
    added = set()
    for link in links:
        new_index = url_to_new_index[link]
        added.add(new_index)
    return sorted(added)

def merge_links(links1, links2):
    return sorted(set(links1 + links2))
