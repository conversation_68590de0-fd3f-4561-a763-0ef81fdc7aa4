# Quepasa Docker Compose Environment Configuration
# Copy this file to .env and customize the values

# ElasticSearch Configuration
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_HOSTS=http://elasticsearch:9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=elastic123

# MinIO Configuration (required by docker-compose)
MINIO_HOST=minio
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=quepasa-files
MINIO_DEBUG=false

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# External AI Services (optional - remove if not using)
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
MISTRAL_API_KEY=your-mistral-api-key-here
NEBIUS_API_KEY=your-nebius-api-key-here
REPLICATE_API_KEY=your-replicate-api-key-here
HUGGINGFACE_API_KEY=your-huggingface-api-key-here

# Rezolve services
SPD_API_URL=https://search.{gcp-env}.groupbycloud.com/api/search

# Logging Configuration
LOG_LEVEL=INFO
# json, plain
LOG_FORMAT=json

API_HOST=0.0.0.0
API_PORT=8000
SECRET_KEY=your-secret-key-here-change-this-in-production
# If set to true, the searcher will use working directory for local storage isntead of system root
LOCAL_NO_DOCKER=false
