from typing import Dict, Any, List, Optional, Union
from quepasa.searcher.models.request import QuepasaRequest
from .base_config import BaseConfig

class CrawlerConfig(BaseConfig):
    """Base configuration for crawler functionality."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
        
    def get_telegram_sources(self) -> List[str]:
        """Get list of Telegram channels and chats to crawl.
        
        Previously: get_telegram_sources()
        
        Returns:
            List of TelegramSource objects
        """
        return []
        
    def get_pipeline_priority(self) -> int:
        """Get priority configuration for crawler pipeline.
        
        Previously: get_pipeline_priority()
        
        Returns:
            List of PipelinePriority objects
        """
        return 1