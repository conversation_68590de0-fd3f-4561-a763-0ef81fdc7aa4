from typing import Optional, List
from sentence_transformers import SentenceTransformer

from .base import <PERSON><PERSON>mbedding
from .cache import EmbeddingCacheMixin
from .providers import EmbeddingProvider
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)

DEFAULT_MODEL = "multi-qa-mpnet-base-dot-v1"

class SBERTEmbedding(BaseEmbedding, EmbeddingCacheMixin):
    """Sentence-BERT (SBERT) embedding provider.
    
    This class provides access to local SBERT models for generating embeddings.
    Default model is multi-qa-mpnet-base-dot-v1 which is optimized for semantic search.
    """
    
    def __init__(self):
        """Initialize SBERT embedding provider."""
        super().__init__()
        self._models = {}  # Cache for loaded models
        
    @property
    def provider(self) -> EmbeddingProvider:
        return EmbeddingProvider.SBERT

    def _get_model(self, model_version: str) -> Optional[SentenceTransformer]:
        """Get or load the SBERT model.
        
        Args:
            model_version: Name of the model to load
            
        Returns:
            Loaded SentenceTransformer model or None if loading fails
        """
        if model_version not in self._models:
            try:
                self._models[model_version] = SentenceTransformer(model_version)
            except Exception as e:
                logger.error(f"Failed to load SBERT model {model_version}: {str(e)}")
                # Fallback to default model if specified model fails
                if model_version != DEFAULT_MODEL:
                    logger.info(f"Falling back to default model {DEFAULT_MODEL}")
                    return self._get_model(DEFAULT_MODEL)
                return None
        return self._models[model_version]

    def get_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get embedding using SBERT model.
        
        Args:
            model_version: The model version to use for embeddings
            text: The text to get embedding for
            
        Returns:
            List of floats representing the embedding, or None if the request fails
        """
        if not text or not text.strip():
            return None
            
        try:
            model = self._get_model(model_version or DEFAULT_MODEL)
            if model is None:
                return None
            embedding = model.encode(text, convert_to_numpy=True).tolist()
            return embedding
        except Exception as e:
            logger.error(f"Failed to get embedding from SBERT: {str(e)}")
            return None 