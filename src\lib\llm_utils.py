import os
import traceback
import async<PERSON>
from typing import Generator, <PERSON><PERSON>, Tu<PERSON>
from src.lib.llm.factory import LLMFactory
from src.lib.logger import QuepasaLogger

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

def get_llm_answer(provider: str, model_version: str, prompt_list: list, answer_prompt_size: Optional[int] = None, json_mode: bool = False) -> str:
    """Get an answer from the specified LLM provider.
    
    Args:
        provider: The LLM provider to use (e.g., 'openai', 'anthropic')
        model_version: Model version to use (e.g., 'openai/gpt-4-turbo-preview')
        prompt_list: List of prompts with 'role' and 'content'
        answer_prompt_size: Maximum response size
        json_mode: Whether to force JSON output format
        
    Returns:
        Generated response text
    """
    llm = LLMFactory.get_llm(provider)
    return llm.get_answer(model_version, prompt_list, answer_prompt_size, json_mode)

def get_streaming_chunks(provider: str, model_version: str, prompt_list: list, answer_prompt_size: Optional[int] = None, json_mode: bool = False) -> Generator[str, None, None]:
    """Get streaming chunks from the specified LLM provider.
    
    Args:
        provider: The LLM provider to use (e.g., 'openai', 'anthropic')
        model_version: Model version to use (e.g., 'openai/gpt-4-turbo-preview')
        prompt_list: List of prompts with 'role' and 'content'
        answer_prompt_size: Maximum response size
        json_mode: Whether to force JSON output format
        
    Yields:
        Generated response chunks
    """
    try:
        llm = LLMFactory.get_llm(provider)
        
        # Get the generator
        gen = llm.get_streaming_answer(model_version, prompt_list, answer_prompt_size, json_mode)
        
        # More robust async generator detection
        is_async = False
        try:
            # Check if it's an async generator by checking for __aiter__ attribute
            is_async = hasattr(gen, '__aiter__')
            logger.info(f"Generator type: {type(gen).__name__}, is_async: {is_async}")
            
        except Exception as e:
            logger.error(f"Error checking generator type: {str(e)}")
            logger.error("Traceback:")
            for line in traceback.format_exc().splitlines():
                logger.error(line)
        
        if is_async:
            async def collect_chunks():
                chunks = []
                try:
                    async for chunk in gen:
                        chunks.append(chunk)
                        
                except Exception as e:
                    logger.error(f"Error in async chunk collection: {str(e)}")
                    logger.error("Traceback:")
                    for line in traceback.format_exc().splitlines():
                        logger.error(line)
                    raise
                    
                return chunks
                
            # Create new event loop and run until complete
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                chunks = loop.run_until_complete(collect_chunks())
                for chunk in chunks:
                    yield chunk
                    
            except Exception as e:
                logger.error(f"Error in event loop execution: {str(e)}")
                logger.error("Traceback:")
                for line in traceback.format_exc().splitlines():
                    logger.error(line)
                raise
                
            finally:
                loop.close()
                
        # For sync generators, just yield from the generator
        else:
            try:
                for chunk in gen:
                    yield chunk
                
            except Exception as e:
                logger.error(f"Error in sync chunk generation: {str(e)}")
                logger.error("Traceback:")
                for line in traceback.format_exc().splitlines():
                    logger.error(line)
                raise
                
    except Exception as e:
        logger.error(f"Top-level error in get_streaming_chunks: {str(e)}")
        logger.error("Traceback:")
        for line in traceback.format_exc().splitlines():
            logger.error(line)
        raise

def get_cached_llm_answer(provider: str, model_version: str, prompt_list: list, answer_prompt_size: Optional[int] = None, json_mode: bool = False) -> str:
    """Get a cached answer from the specified LLM provider.
    
    Args:
        provider: The LLM provider to use (e.g., 'openai', 'anthropic')
        model_version: Model version to use (e.g., 'gpt-4-turbo-preview')
        prompt_list: List of prompts with 'role' and 'content'
        answer_prompt_size: Maximum response size
        json_mode: Whether to force JSON output format
        
    Returns:
        Generated response text from cache or computed fresh
    """
    llm = LLMFactory.get_llm(provider)
    return llm.get_cached_answer(model_version, prompt_list, answer_prompt_size, json_mode)

def get_cached_streaming_chunks(provider: str, model_version: str, prompt_list: list, answer_prompt_size: Optional[int] = None, json_mode: bool = False) -> Generator[str, None, None]:
    """Get cached streaming chunks from the specified LLM provider."""
    try:
        llm = LLMFactory.get_llm(provider)
        
        # Get the generator
        gen = llm.get_cached_streaming_chunks(model_version, prompt_list, answer_prompt_size, json_mode)
        
        # More robust async generator detection
        is_async = False
        try:
            # Check if it's an async generator by checking for __aiter__ attribute
            is_async = hasattr(gen, '__aiter__')
            logger.info(f"Generator type: {type(gen).__name__}, is_async: {is_async}")
            
        except Exception as e:
            logger.error(f"Error checking generator type: {str(e)}")
            logger.error("Traceback:")
            for line in traceback.format_exc().splitlines():
                logger.error(line)
        
        if is_async:
            async def collect_chunks():
                chunks = []
                try:
                    async for chunk in gen:
                        chunks.append(chunk)
                        
                except Exception as e:
                    logger.error(f"Error in async chunk collection: {str(e)}")
                    logger.error("Traceback:")
                    for line in traceback.format_exc().splitlines():
                        logger.error(line)
                    raise
                    
                return chunks
                
            # Create new event loop and run until complete
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                chunks = loop.run_until_complete(collect_chunks())
                for chunk in chunks:
                    yield chunk
                    
            except Exception as e:
                logger.error(f"Error in event loop execution: {str(e)}")
                logger.error("Traceback:")
                for line in traceback.format_exc().splitlines():
                    logger.error(line)
                raise
                
            finally:
                loop.close()
                
        # For sync generators, just yield from the generator
        else:
            try:
                for chunk in gen:
                    yield chunk
                
            except Exception as e:
                logger.error(f"Error in sync chunk generation: {str(e)}")
                logger.error("Traceback:")
                for line in traceback.format_exc().splitlines():
                    logger.error(line)
                raise
                
    except Exception as e:
        logger.error(f"Top-level error in get_cached_streaming_chunks: {str(e)}")
        logger.error("Traceback:")
        for line in traceback.format_exc().splitlines():
            logger.error(line)
        raise 

def get_llm_tools_answer(provider: str, model_version: str, prompt_list: list, tools: list, answer_prompt_size: Optional[int] = None) -> dict:
    """Get an answer from the specified LLM provider.
    
    Args:
        provider: The LLM provider to use (e.g., 'openai', 'anthropic')
        model_version: Model version to use (e.g., 'openai/gpt-4-turbo-preview')
        prompt_list: List of prompts with 'role' and 'content'
        tools: Tools used for Agentic/Function calling
        answer_prompt_size: Maximum response size
        
    Returns:
        Generated response text
    """
    llm = LLMFactory.get_llm(provider)
    return llm.get_tools_answer(model_version, prompt_list, tools, answer_prompt_size)

def get_streaming_tools_chunks(provider: str, model_version: str, prompt_list: list, tools: list, answer_prompt_size: Optional[int] = None) -> Generator[Tuple[str, list], None, None]:
    """Get streaming chunks from the specified LLM provider with tool calling.
    
    Args:
        provider: The LLM provider to use (e.g., 'openai', 'anthropic')
        model_version: Model version to use (e.g., 'openai/gpt-4-turbo-preview')
        prompt_list: List of prompts with 'role' and 'content'
        tools: Tools used for Agentic/Function calling
        answer_prompt_size: Maximum response size
        
    Yields:
        Generated response chunks with tool calls
    """
    try:
        llm = LLMFactory.get_llm(provider)
        
        # Get the generator
        gen = llm.get_streaming_tools_answer(model_version, prompt_list, tools, answer_prompt_size)
        
        # More robust async generator detection
        is_async = False
        try:
            # Check if it's an async generator by checking for __aiter__ attribute
            is_async = hasattr(gen, '__aiter__')
            logger.info(f"Generator type: {type(gen).__name__}, is_async: {is_async}")
            
        except Exception as e:
            logger.error(f"Error checking generator type: {str(e)}")
            logger.error("Traceback:")
            for line in traceback.format_exc().splitlines():
                logger.error(line)
        
        if is_async:
            async def collect_chunks():
                chunks = []
                try:
                    async for chunk in gen:
                        chunks.append(chunk)
                        
                except Exception as e:
                    logger.error(f"Error in async chunk collection: {str(e)}")
                    logger.error("Traceback:")
                    for line in traceback.format_exc().splitlines():
                        logger.error(line)
                    raise
                    
                return chunks
                
            # Create new event loop and run until complete
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                chunks = loop.run_until_complete(collect_chunks())
                for chunk in chunks:
                    yield chunk
                    
            except Exception as e:
                logger.error(f"Error in event loop execution: {str(e)}")
                logger.error("Traceback:")
                for line in traceback.format_exc().splitlines():
                    logger.error(line)
                raise
                
            finally:
                loop.close()
                
        # For sync generators, just yield from the generator
        else:
            try:
                for chunk in gen:
                    yield chunk
                
            except Exception as e:
                logger.error(f"Error in sync chunk generation: {str(e)}")
                logger.error("Traceback:")
                for line in traceback.format_exc().splitlines():
                    logger.error(line)
                raise
                
    except Exception as e:
        logger.error(f"Top-level error in get_streaming_tools_chunks: {str(e)}")
        logger.error("Traceback:")
        for line in traceback.format_exc().splitlines():
            logger.error(line)
        raise
