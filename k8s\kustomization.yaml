apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: quepasa

resources:
# Storage
- storage/storage-classes.yaml

# Common configs
- common/shared-config.yaml
- common/shared-secrets.yaml
- common/quepasa-config.yaml

# RBAC permissions
- rbac/rbac-job-permissions.yaml

# Core services
- redis/redis-deployment.yaml
- redis/redis-service.yaml

- minio/minio-statefulset.yaml
- minio/minio-service.yaml

- elasticsearch/es-statefulset.yaml
- elasticsearch/es-service.yaml

# Kibana
- kibana/kibana-deployment.yaml
- kibana/kibana-service.yaml

# Init jobs
- init/elasticsearch-init-job.yaml
- init/minio-init-job.yaml
- init/kibana-user-init-job.yaml

# Application services
- crawler/crawler-deployment.yaml
- data-processor/data-processor-deployment.yaml
- indexer/indexer-deployment.yaml

- celery/celery-flower-deployment.yaml
- celery/celery-flower-service.yaml

- api/api-deployment.yaml
- api/api-service.yaml

- searcher/searcher-deployment.yaml
- searcher/searcher-service.yaml

# Ingress
- ingress.yaml

# New embedding service
- embedding/embedding-deployment.yaml
- embedding/embedding-service.yaml

images:
- name: qpreg.azurecr.io/quepasa/crawler
  newName: qpreg.azurecr.io/quepasa/crawler
  newTag: v1.0.184
- name: qpreg.azurecr.io/quepasa/indexer
  newName: qpreg.azurecr.io/quepasa/indexer
  newTag: v1.0.184
- name: qpreg.azurecr.io/quepasa/data_processor
  newName: qpreg.azurecr.io/quepasa/data_processor
  newTag: v1.0.184
- name: qpreg.azurecr.io/quepasa/searcher
  newName: qpreg.azurecr.io/quepasa/searcher
  newTag: v1.0.184
- name: qpreg.azurecr.io/quepasa/api
  newName: qpreg.azurecr.io/quepasa/api
  newTag: v1.0.184
- name: qpreg.azurecr.io/quepasa/elasticsearch_init
  newName: qpreg.azurecr.io/quepasa/elasticsearch_init
  newTag: v1.0.184
- name: qpreg.azurecr.io/quepasa/embedding
  newName: qpreg.azurecr.io/quepasa/embedding
  newTag: v1.0.184
