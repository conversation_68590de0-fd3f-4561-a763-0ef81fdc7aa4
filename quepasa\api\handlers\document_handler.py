import hashlib
import json
from typing import List, Dict, Any, Union, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Header
from src.lib.files import QuepasaFiles
from quepasa.api.auth import verify_auth
from quepasa.api.models import <PERSON>ch<PERSON><PERSON><PERSON>, BatchR<PERSON>ult
from quepasa.crawler.processors.txt_processor import TXTProcessor
from quepasa.crawler.processors.markdown_processor import MarkdownProcessor
from quepasa.crawler.processors.web_processor import WebProcessor

from src.lib.logger import QuepasaLogger
from src.lib.batch_utils import (
    BatchUtils,
    BatchState,
    BatchAction,
    INDEXER_STORAGE
)
from src.lib.constants import DOCUMENT_TYPE_PRODUCT

# Logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize files
qp_files = QuepasaFiles()

# Create router
router = APIRouter()

@dataclass
class Document:
    """Document model"""
    id: str
    url: str
    type: Optional[str] = None
    sku: Optional[str] = None
    price_from: Optional[float] = None
    price_to: Optional[float] = None
    metadata: Optional[str] = None
    access: Optional[BatchAccess] = None
    language: Optional[str] = None
    title: Optional[str] = None
    keywords: Optional[List[str]] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    chunks: Optional[List[Dict[str, str]]] = None
    text_content: Optional[str] = None
    markdown_content: Optional[str] = None
    html_content: Optional[str] = None

class DocumentHandler:
    """Handles document operations"""
    
    def __init__(self, files=None):
        """Initialize DocumentHandler
        
        Args:
            files: Optional QuepasaFiles instance. If not provided, uses global instance.
        """
        self.files = files or qp_files
        
    def _validate_document(self, doc: Document) -> bool:
        """Validate document fields"""
        if not doc.id or not doc.url:
            return False
            
        # Require at least one valid chunk
        if not doc.chunks and not doc.text_content and not doc.markdown_content and not doc.html_content:
            return False
            
        if doc.chunks:
            for chunk in doc.chunks:
                if not chunk.get('language') or not chunk.get('text'):
                    return False
                
        if doc.created_at and not self._is_valid_timestamp(doc.created_at):
            return False
            
        if doc.updated_at and not self._is_valid_timestamp(doc.updated_at):
            return False
            
        return True
        
    def _is_valid_timestamp(self, timestamp: str) -> bool:
        """Check if timestamp has valid format"""
        try:
            datetime.strptime(timestamp, "%Y-%m-%dT%H:%M:%SZ")
            return True
        except:
            return False
            
    def _get_domain_list_path(self, client_id: str) -> str:
        """Get path for domain document list"""
        return f"{INDEXER_STORAGE}/{client_id}/list_documents.json"
    
    def _get_chunks(self, doc: Document, client_id: str) -> List[Dict[str, str]]:
        """Get chunks from text"""
        chunks = None
        if doc.chunks:
            chunks = doc.chunks

        elif doc.text_content:
            processor = TXTProcessor(client_id)
            chunks = processor.process(doc.text_content.encode('utf-8'), {'filename': doc.id + ".txt"})['result']['chunks']

        elif doc.markdown_content:
            processor = MarkdownProcessor()
            chunks = processor.process(doc.markdown_content.encode('utf-8'), {'filename': doc.id + ".md"})['result']['chunks']

        elif doc.html_content:
            processor = WebProcessor()
            chunks = processor.process(doc.html_content.encode('utf-8'), {'filename': doc.id + ".html"})['result']['chunks']

        if chunks and doc.language:
            for chunk in chunks:
                if not chunk.get('language'):
                    chunk['language'] = doc.language

        return chunks
        
    async def upsert_documents(self, client_id: str, domain: str, documents: List[Document]) -> BatchResult:
        """Upsert documents into storage
        
        Args:
            client_id: Client identifier
            domain: Document domain
            documents: List of documents to upsert
            
        Returns:
            BatchResult with batch ID and processed document IDs
        """
        # Validate documents
        valid_docs = [doc for doc in documents if self._validate_document(doc)]
        
        if not valid_docs:
            raise ValueError("No valid documents provided")
                
        batch_data = {
            'client_id': client_id,
            'domain': domain,
            'action': BatchAction.UPSERT.value,
            'documents': []
        }

        for doc in valid_docs:
            document = {
                'id': doc.id,
                'url': doc.url
            }

            if doc.type:
                document['type'] = doc.type
                if doc.type == DOCUMENT_TYPE_PRODUCT:
                    document['sku'] = doc.sku
                    if doc.price_from:
                        document['price_from'] = doc.price_from
                    if doc.price_to:
                        document['price_to'] = doc.price_to
                    if doc.metadata:
                        document['metadata'] = doc.metadata

            if doc.access:
                document['access'] = asdict(doc.access)
            if doc.language:
                document['language'] = doc.language
            if doc.title:
                document['title'] = doc.title
            if doc.keywords:
                document['keywords'] = doc.keywords
            if doc.created_at:
                document['created_at'] = doc.created_at
            if doc.updated_at:
                document['updated_at'] = doc.updated_at

            chunks = self._get_chunks(doc, client_id)
            if chunks:
                document['chunks'] = chunks

            batch_data['documents'].append(document)

        # Create batch in UPLOADED state
        batch_id = BatchUtils.create_batch(client_id, BatchState.BACKLOG, batch_data, files=self.files)
        logger.info(f"Created test batch with ID: {batch_id}")
            
        # Create batch and trigger data processor
        BatchUtils.add_task('data-processor', client_id, batch_id)
        logger.info(f"Created data processor task {client_id} {batch_id}")
        
        return BatchResult(
            batch_id=batch_id,
            processed_ids=[doc.id for doc in valid_docs]
        )
        
    async def get_document(self, client_id: str, domain: str, doc_id: str) -> Optional[Union[Dict[str, Any], None]]:
        """Get document by ID
        
        Args:
            client_id: Client identifier
            domain: Document domain
            doc_id: Document identifier
            
        Returns:
            Document if found, None otherwise
        """
        document_file_zlib_json, _ = BatchUtils.get_document_filenames(client_id, domain, doc_id)
        if not self.files.exists(document_file_zlib_json):
            return None
            
        doc_data = self.files.get_json_zlib(document_file_zlib_json)
        return doc_data
        
    async def list_documents(self, client_id: str) -> Dict[str, Dict[str, str]]:
        """List documents by domain
        
        Args:
            client_id: Client identifier
            
        Returns:
            Dictionary mapping domains to document dictionaries (id -> title)
        """
        path = self._get_domain_list_path(client_id)
        
        if not self.files.exists(path):
            return {}
            
        return self.files.get_json(path) 

    async def list_documents_by_domain(self, client_id: str, domain: str) -> Dict[str, str]:
        """List documents by specific domain
        
        Args:
            client_id: Client identifier
            domain: Domain to filter by
            
        Returns:
            Dictionary mapping document IDs to document titles
        """
        path = self._get_domain_list_path(client_id)
        
        if not self.files.exists(path):
            return {}
            
        docs = self.files.get_json(path)
        return docs.get(domain, {})

    async def replace_documents(self, client_id: str, domain: str, documents: List[Document]) -> BatchResult:
        """Replace all documents in a domain with new ones
        
        Args:
            client_id: Client identifier
            domain: Document domain
            documents: List of documents to replace existing ones
            
        Returns:
            BatchResult with batch ID and processed document IDs
        """
        # Validate documents
        valid_docs = [doc for doc in documents if self._validate_document(doc)]
        
        if not valid_docs:
            raise ValueError("No valid documents provided")
                
        batch_data = {
            'client_id': client_id,
            'domain': domain,
            'action': BatchAction.REPLACE.value,
            'documents': []
        }

        for doc in valid_docs:
            document = {
                'id': doc.id,
                'url': doc.url
            }

            if doc.type:
                document['type'] = doc.type
                if doc.type == DOCUMENT_TYPE_PRODUCT:
                    document['sku'] = doc.sku
                    if doc.price_from:
                        document['price_from'] = doc.price_from
                    if doc.price_to:
                        document['price_to'] = doc.price_to
                    if doc.metadata:
                        document['metadata'] = doc.metadata

            if doc.access:
                document['access'] = doc.access
            if doc.language:
                document['language'] = doc.language
            if doc.title:
                document['title'] = doc.title
            if doc.keywords:
                document['keywords'] = doc.keywords
            if doc.created_at:
                document['created_at'] = doc.created_at
            if doc.updated_at:
                document['updated_at'] = doc.updated_at

            chunks = self._get_chunks(doc, client_id)
            if chunks:
                document['chunks'] = chunks

            batch_data['documents'].append(document)

        # Create batch in UPLOADED state
        batch_id = BatchUtils.create_batch(client_id, BatchState.BACKLOG, batch_data, files=self.files)
        logger.info(f"Created batch for domain replacement with ID: {batch_id}")
            
        # Create batch and trigger data processor
        BatchUtils.add_task('data-processor', client_id, batch_id)
        logger.info(f"Created data processor task {client_id} {batch_id}")
        
        return BatchResult(
            batch_id=batch_id,
            processed_ids=[doc.id for doc in valid_docs]
        )
        
    async def remove_document(self, client_id: str, domain: str, doc_id: str) -> BatchResult:
        """Remove a specific document by ID
        
        Args:
            client_id: Client identifier
            domain: Document domain
            doc_id: Document ID to remove
            
        Returns:
            BatchResult with batch ID and processed document IDs
        """
        return await self.remove_documents(client_id, domain, [doc_id])

    async def remove_documents(self, client_id: str, domain: str, document_ids: List[str]) -> BatchResult:
        """Remove a specific document by ID

        Args:
            client_id: Client identifier
            domain: Document domain
            document_ids: Document IDs to remove

        Returns:
            BatchResult with batch ID and processed document IDs
        """
        batch_data = {
            'client_id': client_id,
            'domain': domain,
            'action': BatchAction.DELETE.value,
            'documents': [{'id': doc_id} for doc_id in document_ids]
        }
        
        # Create batch in BACKLOG state
        batch_id = BatchUtils.create_batch(client_id, BatchState.BACKLOG, batch_data, files=self.files)
        logger.info(f"Created batch for document removal with ID: {batch_id}")
            
        # Create batch and trigger data processor
        BatchUtils.add_task('data-processor', client_id, batch_id)
        logger.info(f"Created data processor task for {client_id} {batch_id}")
        
        return BatchResult(
            batch_id=batch_id,
            processed_ids=document_ids
        )
        
    async def remove_domain(self, client_id: str, domain: str) -> BatchResult:
        """Remove all documents from a domain
        
        Args:
            client_id: Client identifier
            domain: Document domain to clear
            
        Returns:
            BatchResult with batch ID
        """
        batch_data = {
            'client_id': client_id,
            'domain': domain,
            'action': BatchAction.RESET.value,
            'documents': []
        }
        
        # Create batch in BACKLOG state
        batch_id = BatchUtils.create_batch(client_id, BatchState.BACKLOG, batch_data, files=self.files)
        logger.info(f"Created batch for domain removal with ID: {batch_id}")
            
        # Create batch and trigger data processor
        BatchUtils.add_task('data-processor', client_id, batch_id)
        logger.info(f"Created data processor task for {client_id} {batch_id}")
        
        return BatchResult(
            batch_id=batch_id,
            processed_ids=[]
        )

# Initialize handler instance
handler = DocumentHandler()

def get_client_id_from_header(authorization: str = Header(None)) -> str:
    """Extract client_id from Authorization header"""
    if authorization and authorization.startswith('Bearer '):
        token_parts = authorization.split(' ')[1].split(':')
        if len(token_parts) == 2:
            return token_parts[0]
    return "default"

# Router endpoints
@router.get("/")
async def list_documents(
    client_id: str = Depends(verify_auth)
) -> Dict[str, Dict[str, str]]:
    """List documents in storage"""
    logger.info(f"[list_documents] Listing documents for client {client_id}")
    return await handler.list_documents(client_id)

@router.get("/{domain}")
async def list_documents_by_domain(
    domain: str,
    client_id: str = Depends(verify_auth)
) -> Dict[str, str]:
    """List documents in storage by domain"""
    logger.info(f"[list_documents_by_domain] Listing documents for client {client_id} and domain {domain}")
    return await handler.list_documents_by_domain(client_id, domain)

@router.get("/{domain}/{document_id}")
async def get_document(
    domain: str,
    document_id: str,
    client_id: str = Depends(verify_auth)
) -> Dict[str, Any]:
    """Get document from storage"""
    doc = None
    try:
        doc = await handler.get_document(client_id, domain, document_id)
    except Exception as e:
        raise HTTPException(500, str(e))
    
    if not doc:
        raise HTTPException(404, f"[app] Document {document_id} not found for domain {domain} and client {client_id}")
    return doc

@router.post("/{domain}")
async def upsert_documents(
    domain: str,
    documents: List[Document],
    client_id: str = Depends(verify_auth)
) -> BatchResult:
    """Upsert documents into storage"""
    return await handler.upsert_documents(client_id, domain, documents)

@router.put("/{domain}")
async def replace_documents(
    domain: str,
    documents: List[Document],
    client_id: str = Depends(verify_auth)
) -> BatchResult:
    """Replace all documents in a domain with new ones"""
    return await handler.replace_documents(client_id, domain, documents)

@router.delete("/{domain}")
async def remove_domain(
    domain: str,
    client_id: str = Depends(verify_auth)
) -> BatchResult:
    """Remove all documents from a domain"""
    return await handler.remove_domain(client_id, domain)

@router.delete("/{domain}/{document_id}")
async def remove_document(
    domain: str,
    document_id: str,
    client_id: str = Depends(verify_auth)
) -> BatchResult:
    """Remove a specific document by ID"""
    return await handler.remove_document(client_id, domain, document_id) 