import pytest
import os
import time
from unittest.mock import patch, MagicMock
from datetime import datetime
from celery import Celery, current_app

from quepasa.crawler.orchestrator import CrawlerOrchestrator
from quepasa.crawler.tasks import process_batch, app
from src.lib.files import QuepasaFiles
from src.lib.batch_utils import BatchState, BatchAction, BATCH_STORAGE_DIR

# Test configuration
TEST_ENV = 'test'
TEST_CONTAINER_ID = 'test-container'
TEST_CLIENT_ID = 'test-client'
TEST_DOMAIN = 'test-domain.com'

@pytest.fixture
def mock_qp_files():
    with patch('quepasa.crawler.orchestrator.QuepasaFiles') as mock:
        instance = mock.return_value
        instance.get_files.return_value = []
        instance.exists.return_value = True
        instance.get_file_mtime.return_value = time.time()  # Current timestamp
        yield instance

@pytest.fixture
def test_files(mock_qp_files):
    """Create test files for processing"""
    qp_files = QuepasaFiles(
        bucket_name='quepasa-files',
        endpoint_url='http://localhost:9000',
        aws_access_key_id='minioadmin',
        aws_secret_access_key='minioadmin',
        debug_flag=False
    )
    
    # Create test PDF
    pdf_content = b'%PDF-1.4\nTest PDF content'
    pdf_path = f"{BATCH_STORAGE_DIR[BatchState.UPLOADED]}/{TEST_CLIENT_ID}/test.pdf"
    qp_files.set_data(pdf_path, pdf_content)
    
    # Create test HTML
    html_content = b'<html><body><h1>Test HTML</h1><p>Test content</p></body></html>'
    html_path = f"{BATCH_STORAGE_DIR[BatchState.UPLOADED]}/{TEST_CLIENT_ID}/test.html"
    qp_files.set_data(html_path, html_content)
    
    # Configure mock file listing
    mock_qp_files.get_files.return_value = [pdf_path, html_path]
    
    yield {
        'pdf': pdf_path,
        'html': html_path
    }
    
    # Cleanup
    qp_files.delete_file(pdf_path)
    qp_files.delete_file(html_path)

@pytest.fixture(autouse=True)
def configure_celery():
    """Configure Celery for testing."""
    current_config = app.conf.copy()  # Save current config
    app.conf.update(
        task_always_eager=True,
        task_eager_propagates=True,
        broker_url='memory://localhost/',
        backend='memory://localhost/',
        broker_connection_retry=False,
        broker_connection_max_retries=0
    )
    yield
    # Reset config after test
    app.conf = current_config  # Restore original config

@pytest.fixture
def mock_celery():
    """Mock Celery for testing."""
    with patch('quepasa.crawler.tasks.app') as mock_app:
        # Configure Celery for testing
        mock_app.conf.task_always_eager = True
        mock_app.conf.task_eager_propagates = True
        
        # Mock AsyncResult
        mock_result = MagicMock()
        mock_result.ready.return_value = True
        mock_result.successful.return_value = True
        mock_result.state = 'SUCCESS'
        mock_result.id = 'test_task_id'
        
        # Configure the mock app
        mock_app.AsyncResult.return_value = mock_result
        mock_app.send_task.return_value = mock_result
        
        # Mock task execution
        def mock_delay(*args, **kwargs):
            return mock_result
        
        mock_app.task.return_value = lambda *args, **kwargs: mock_delay
        
        yield mock_app

@pytest.fixture
def mock_processors():
    with patch('quepasa.crawler.tasks.PDFProcessor') as pdf_mock, \
         patch('quepasa.crawler.tasks.WebProcessor') as web_mock:
        
        # Configure mock processors
        processor_result = {
            'title': 'Test Document',
            'chunks': [{'text': 'Test content', 'language': 'en'}]
        }
        
        pdf_mock.return_value.process.return_value = processor_result
        web_mock.return_value.process.return_value = processor_result
        
        yield {
            'pdf': pdf_mock,
            'web': web_mock
        }

@pytest.mark.skip(reason="Skipping test_batch_processing_workflow")
def test_batch_processing_workflow(test_files, mock_celery, mock_processors, mock_qp_files):
    """Test end-to-end batch processing workflow"""
    # Initialize orchestrator
    orchestrator = CrawlerOrchestrator()
    
    # Create test batch data
    batch_data = {
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': BatchAction.UPSERT,
        'urls': ['https://example.com/test.pdf'],
        'files': [test_files['pdf'], test_files['html']]
    }
    
    # Configure mock for initial file listing
    mock_qp_files.get_files.return_value = [
        f"{BATCH_STORAGE_DIR[BatchState.UPLOADED]}/1234567890.123.{TEST_CLIENT_ID}.json"
    ]
    
    # Mock get_json_zlib for batch data
    def mock_get_json_zlib(file_path):
        if BatchState.UPLOADED in file_path:
            return batch_data
        
        return {
            'client_id': TEST_CLIENT_ID,
            'domain': TEST_DOMAIN,
            'action': BatchAction.UPSERT,
            'processed_ids': ['test-domain.com:test.pdf', 'test-domain.com:test.html'],
            'documents': [
                'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json',
                'test/storage/backlog/test-client/test-domain.com/1/17/173/173fd2ebeae5f7ae24b8603b5a4d4d89.zlib.json'
            ]
        }
    mock_qp_files.get_json_zlib.side_effect = mock_get_json_zlib
    
    # Configure mock for successful task result
    mock_task_result = {
        'task_id': 'test_task_id',
        'status': 'success',
        'results': [
            {
                'id': 'test-domain.com:test.pdf',
                'status': 'success',
                'file': 'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json'
            },
            {
                'id': 'test-domain.com:test.html',
                'status': 'success',
                'file': 'test/storage/backlog/test-client/test-domain.com/1/17/173/173fd2ebeae5f7ae24b8603b5a4d4d89.zlib.json'
            }
        ]
    }
    
    # Configure Celery task mocks
    mock_celery.AsyncResult.return_value.get.return_value = mock_task_result
    
    # Get initial batch files
    batch_files = orchestrator.get_batch_files_by_client(TEST_CLIENT_ID)
    assert TEST_CLIENT_ID in batch_files
    assert len(batch_files[TEST_CLIENT_ID]) == 1  # One batch file
    
    # Process client batches
    orchestrator.process_client_batches(TEST_CLIENT_ID, batch_files[TEST_CLIENT_ID])
    
    # Verify file operations
    # 1. Original batch file should be deleted
    mock_qp_files.delete_file.assert_called_with(f"{BATCH_STORAGE_DIR[BatchState.UPLOADED]}/1234567890.123.{TEST_CLIENT_ID}.json")
    
    # 2. New batch file should be created in BACKLOG with correct path structure
    expected_backlog_data = {
        'env': TEST_ENV,
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': 'upsert',
        'processed_ids': ['test-domain.com:test.pdf', 'test-domain.com:test.html'],
        'documents': [
            'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json',
            'test/storage/backlog/test-client/test-domain.com/1/17/173/173fd2ebeae5f7ae24b8603b5a4d4d89.zlib.json'
        ]
    }
    
    # Verify set_json_zlib was called with correct data for BACKLOG state
    mock_qp_files.set_json_zlib.assert_called_with(
        f"{BATCH_STORAGE_DIR[BatchState.BACKLOG]}/12345/123456/1234567/1234567890.123.{TEST_CLIENT_ID}.json",
        expected_backlog_data
    )

@pytest.mark.skip(reason="Skipping test_parallel_batch_processing")
def test_parallel_batch_processing(test_files, mock_celery, mock_processors, mock_qp_files):
    """Test processing multiple batches in parallel"""
    # Create multiple test batches
    batch_count = 3
    batch_files = []
    
    for i in range(batch_count):
        batch_data = {
            'env': TEST_ENV,
            'client_id': TEST_CLIENT_ID,
            'domain': TEST_DOMAIN,
            'action': 'upsert',
            'files': [test_files['pdf'], test_files['html']]
        }
        batch_file = f"{BATCH_STORAGE_DIR[BatchState.UPLOADED]}/1234567890.12{i}.{TEST_CLIENT_ID}.json"
        batch_files.append(batch_file)
    
    # Configure mock for uploaded files
    mock_qp_files.get_files.return_value = batch_files
    
    # Mock get_json_zlib for batch data
    def mock_get_json_zlib(file_path):
        return {
            'env': TEST_ENV,
            'client_id': TEST_CLIENT_ID,
            'domain': TEST_DOMAIN,
            'action': 'upsert',
            'files': [test_files['pdf'], test_files['html']]
        }
    mock_qp_files.get_json_zlib.side_effect = mock_get_json_zlib
    
    # Configure mock for successful task result
    mock_task_result = {
        'task_id': 'test_task_id',
        'status': 'success',
        'results': [
            {
                'id': 'test-domain.com:test.pdf',
                'status': 'success',
                'file': 'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json'
            },
            {
                'id': 'test-domain.com:test.html',
                'status': 'success',
                'file': 'test/storage/backlog/test-client/test-domain.com/1/17/173/173fd2ebeae5f7ae24b8603b5a4d4d89.zlib.json'
            }
        ]
    }
    
    # Configure Celery task mocks
    mock_celery.AsyncResult.return_value.get.return_value = mock_task_result
    
    # Initialize orchestrator
    orchestrator = CrawlerOrchestrator(
        env=TEST_ENV,
        container_id=TEST_CONTAINER_ID,
        client_id=TEST_CLIENT_ID
    )
    
    # Process batches
    batch_files = orchestrator.get_batch_files_by_client()
    orchestrator.process_client_batches(TEST_CLIENT_ID, batch_files[TEST_CLIENT_ID])
    
    # Configure mock for backlog files
    backlog_files = [
        f"{BATCH_STORAGE_DIR[BatchState.BACKLOG]}/12345/123456/1234567/1234567890.12{i}.{TEST_CLIENT_ID}.json"
        for i in range(batch_count)
    ]
    mock_qp_files.get_files.return_value = backlog_files
    
    # Verify all batches were processed
    assert len(backlog_files) == batch_count  # Each batch should be in backlog
    
    # Verify each backlog file was created with correct data
    expected_backlog_data = {
        'env': TEST_ENV,
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': 'upsert',
        'processed_ids': ['test-domain.com:test.pdf', 'test-domain.com:test.html'],
        'documents': [
            'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json',
            'test/storage/backlog/test-client/test-domain.com/1/17/173/173fd2ebeae5f7ae24b8603b5a4d4d89.zlib.json'
        ]
    }
    
    for i in range(batch_count):
        mock_qp_files.set_json_zlib.assert_any_call(
            f"{BATCH_STORAGE_DIR[BatchState.BACKLOG]}/12345/123456/1234567/1234567890.12{i}.{TEST_CLIENT_ID}.json",
            expected_backlog_data
        )

@pytest.mark.skip(reason="Skipping test_error_recovery")
def test_error_recovery(test_files, mock_celery, mock_processors, mock_qp_files):
    """Test system recovery from processing errors"""
    # Create a batch with invalid file
    batch_data = {
        'env': TEST_ENV,
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': 'upsert',
        'files': [
            test_files['pdf'],
            'invalid/file/path.xyz'  # Invalid file
        ]
    }
    
    batch_file = f"{BATCH_STORAGE_DIR[BatchState.UPLOADED]}/1234567890.123.{TEST_CLIENT_ID}.json"
    
    # Configure mock for uploaded files
    mock_qp_files.get_files.return_value = [batch_file]
    
    # Mock get_json_zlib for batch data
    def mock_get_json_zlib(file_path):
        if BatchState.UPLOADED in file_path:
            return batch_data
        return {
            'env': TEST_ENV,
            'client_id': TEST_CLIENT_ID,
            'domain': TEST_DOMAIN,
            'action': 'upsert',
            'processed_ids': ['test-domain.com:test.pdf'],
            'documents': [
                'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json'
            ]
        }
    mock_qp_files.get_json_zlib.side_effect = mock_get_json_zlib
    
    # Configure mock for successful task result (only PDF processed)
    mock_task_result = {
        'task_id': 'test_task_id',
        'status': 'success',
        'results': [
            {
                'id': 'test-domain.com:test.pdf',
                'status': 'success',
                'file': 'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json'
            },
            {
                'id': 'invalid/file/path.xyz',
                'status': 'error',
                'error': 'File not found'
            }
        ]
    }
    
    # Configure Celery task mocks
    mock_celery.send_task.return_value = mock_celery.AsyncResult.return_value
    mock_celery.AsyncResult.return_value.get.return_value = mock_task_result
    mock_celery.AsyncResult.return_value.ready.return_value = True
    mock_celery.AsyncResult.return_value.successful.return_value = True
    
    # Initialize orchestrator
    orchestrator = CrawlerOrchestrator(
        env=TEST_ENV,
        container_id=TEST_CONTAINER_ID,
        client_id=TEST_CLIENT_ID
    )
    
    # Process batch
    batch_files = orchestrator.get_batch_files_by_client()
    orchestrator.process_client_batches(TEST_CLIENT_ID, batch_files[TEST_CLIENT_ID])
    
    # Configure mock for backlog and failed files
    backlog_files = [f"{BATCH_STORAGE_DIR[BatchState.BACKLOG]}/12345/123456/1234567/1234567890.123.{TEST_CLIENT_ID}.json"]
    mock_qp_files.get_files.return_value = backlog_files
    
    # Verify results
    assert len(backlog_files) == 1  # PDF file processed successfully
    
    # Verify backlog file was created with correct data
    expected_backlog_data = {
        'env': TEST_ENV,
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': 'upsert',
        'processed_ids': ['test-domain.com:test.pdf'],
        'documents': [
            'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json'
        ]
    }
    
    mock_qp_files.set_json_zlib.assert_called_with(
        f"{BATCH_STORAGE_DIR[BatchState.BACKLOG]}/12345/123456/1234567/1234567890.123.{TEST_CLIENT_ID}.json",
        expected_backlog_data
    )

@pytest.mark.skip(reason="Skipping test_batch_state_transitions")
def test_batch_state_transitions(test_files, mock_celery, mock_processors, mock_qp_files):
    """Test batch state transitions during processing"""
    # Initialize orchestrator
    orchestrator = CrawlerOrchestrator(
        env=TEST_ENV,
        container_id=TEST_CONTAINER_ID,
        client_id=TEST_CLIENT_ID
    )
    
    # Create test batch data
    batch_data = {
        'env': TEST_ENV,
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': 'upsert',
        'files': [test_files['pdf'], test_files['html']]
    }
    
    # Configure mock for initial state
    mock_qp_files.get_files.return_value = [
        f"{BATCH_STORAGE_DIR[BatchState.UPLOADED]}/1234567890.123.{TEST_CLIENT_ID}.json",
        f"{BATCH_STORAGE_DIR[BatchState.UPLOADED]}/1234567890.124.{TEST_CLIENT_ID}.json"
    ]
    
    # Mock get_json_zlib for batch data
    def mock_get_json_zlib(file_path):
        if BatchState.UPLOADED in file_path:
            return batch_data
        return {
            'env': TEST_ENV,
            'client_id': TEST_CLIENT_ID,
            'domain': TEST_DOMAIN,
            'action': 'upsert',
            'processed_ids': ['test-domain.com:test.pdf', 'test-domain.com:test.html'],
            'documents': [
                'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json',
                'test/storage/backlog/test-client/test-domain.com/1/17/173/173fd2ebeae5f7ae24b8603b5a4d4d89.zlib.json'
            ]
        }
    mock_qp_files.get_json_zlib.side_effect = mock_get_json_zlib
    
    # Configure mock for successful task result
    mock_task_result = {
        'task_id': 'test_task_id',
        'status': 'success',
        'results': [
            {
                'id': 'test-domain.com:test.pdf',
                'status': 'success',
                'file': 'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json'
            },
            {
                'id': 'test-domain.com:test.html',
                'status': 'success',
                'file': 'test/storage/backlog/test-client/test-domain.com/1/17/173/173fd2ebeae5f7ae24b8603b5a4d4d89.zlib.json'
            }
        ]
    }
    
    # Configure Celery task mocks
    mock_celery.AsyncResult.return_value.get.return_value = mock_task_result
    
    # Get initial state
    initial_files = orchestrator.get_batch_files_by_client()
    assert TEST_CLIENT_ID in initial_files
    assert len(initial_files[TEST_CLIENT_ID]) == 2  # Two batch files
    
    # Process batches
    orchestrator.process_client_batches(TEST_CLIENT_ID, initial_files[TEST_CLIENT_ID])
    
    # Configure mock for state verification
    backlog_files = [
        f"{BATCH_STORAGE_DIR[BatchState.BACKLOG]}/12345/123456/1234567/1234567890.123.{TEST_CLIENT_ID}.json",
        f"{BATCH_STORAGE_DIR[BatchState.BACKLOG]}/12345/123456/1234567/1234567890.124.{TEST_CLIENT_ID}.json"
    ]
    mock_qp_files.get_files.return_value = backlog_files
    
    # Verify state transitions
    # 1. Should be no files in UPLOADED
    mock_qp_files.get_files.assert_any_call(f"{BATCH_STORAGE_DIR[BatchState.UPLOADED]}", recursive=True)
    
    # 2. Should be files in BACKLOG
    assert len(backlog_files) == 2
    
    # 3. Verify each backlog file was created with correct data
    expected_backlog_data = {
        'env': TEST_ENV,
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': 'upsert',
        'processed_ids': ['test-domain.com:test.pdf', 'test-domain.com:test.html'],
        'documents': [
            'test/storage/backlog/test-client/test-domain.com/a/ac/ac8/ac8ec6f22e040325a46279cd78cc17e1.zlib.json',
            'test/storage/backlog/test-client/test-domain.com/1/17/173/173fd2ebeae5f7ae24b8603b5a4d4d89.zlib.json'
        ]
    }
    
    for batch_id in ['123', '124']:
        mock_qp_files.set_json_zlib.assert_any_call(
            f"{BATCH_STORAGE_DIR[BatchState.BACKLOG]}/12345/123456/1234567/1234567890.{batch_id}.{TEST_CLIENT_ID}.json",
            expected_backlog_data
        ) 