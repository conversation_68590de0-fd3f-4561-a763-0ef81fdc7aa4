from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from src.lib.batch_utils import Batch<PERSON>tils, BatchState
from quepasa.api.auth import verify_auth

# Create router
router = APIRouter()

class BatchHandler:
    """Handler for batch operations"""
    def __init__(self):
        pass

    async def get_batch_status(self, client_id: str, batch_id: str) -> Dict[str, Any]:
        """Get batch status"""
        try:
            state, data = BatchUtils().get_batch_status(client_id, batch_id)
            return {
                "status": f"Batch state: {state}",
                "state": state,
                "data": data
            }
        
        except Exception as e:
            raise HTTPException(500, str(e))
            
    async def relaunch_batch(self, client_id: str, batch_id: str) -> Dict[str, Any]:
        """Relaunch a batch based on its current state"""
        try:
            # Get current batch state
            state, _ = BatchUtils().get_batch_status(client_id, batch_id)
            
            # Default values
            reloaded = (state in [BatchState.UPLOADED, BatchState.BACKLOG, BatchState.IN_PROGRESS])
            task_type = None
            
            # Determine action based on current state
            if state == "uploaded":
                task_type = "crawler"
                reloaded = True
            elif state == "backlog":
                task_type = "data-processor"
                reloaded = True
            elif state == "in_progress":
                task_type = "indexer"
                reloaded = True
            
            # Relaunch the batch if applicable
            if reloaded:
                BatchUtils().add_task(task_type, client_id, batch_id)
            
            return {
                "status": f"Batch state: {state}",
                "state": state,
                "reloaded": reloaded,
                "task_type": task_type
            }
            
        except Exception as e:
            raise HTTPException(500, str(e))

# Initialize handler
handler = BatchHandler()

@router.get("/{batch_id}", response_model=Dict[str, Any])
async def get_batch_status(
    batch_id: str,
    client_id: str = Depends(verify_auth)
) -> Dict[str, Any]:
    """Get batch status"""
    return await handler.get_batch_status(client_id, batch_id) 

@router.post("/{batch_id}/relaunch", response_model=Dict[str, Any])
async def relaunch_batch(
    batch_id: str,
    client_id: str = Depends(verify_auth)
) -> Dict[str, Any]:
    """Relaunch a batch based on its current state"""
    return await handler.relaunch_batch(client_id, batch_id) 