from typing import Optional, List, Dict, Any, Union, Tuple
from dataclasses import dataclass
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.response import <PERSON>pasaAnswer, QuepasaStreamAnswer
from src.lib.constants import SOURCE_DOCUMENTS, SOURCE_ALL, SOURCE_WATERFALL
from src.lib.embedding.providers import EmbeddingProvider
from .base_config import BaseConfig

@dataclass
class KindWeights:
    """Weights for different aspects of search relevance scoring."""
    document: float
    chunk: float

@dataclass
class SearchWeights:
    """Weights for different aspects of search relevance scoring."""
    text: float
    semantic: float

@dataclass
class SearchEngineRatios:
    """Ratios for different search engines."""
    rag: int
    spd: int


class SearcherConfig(BaseConfig):
    """Base configuration for search functionality."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
        
    def get_waterfall_config(self) -> Optional[Any]:
        """Get configuration for waterfall search.
        
        Args:
            source: Source type
            
        Returns:
            Optional[Any]
        """
        return None
    
    def get_waterfall_step_metadata(self, request: QuepasaRequest, response: Union[QuepasaAnswer, QuepasaStreamAnswer]) -> Optional[Dict[str, Any]]:
        """Get information about the waterfall step.
        
        Args:
            source: Source type
            
        Returns:
            Optional[Dict[str, Any]]
        """
        return {
            'step_name': request.waterfall_step_name,
            'answer': response.text if hasattr(response, 'text') else response['text'],
        }

    def get_primary_search_source(self) -> str:
        """Get the primary source to search in.

        Previously: get_client_source()

        Returns:
            Primary source string
        """
        return SOURCE_DOCUMENTS

    def get_max_results_limit(self, source: str) -> int:
        """Get maximum number of search results to return.

        Previously: get_maximum_search_results()
        
        Args:
            source: Source type
            
        Returns:
            Maximum number of search results
        """
        return 50

    def get_fuzzy_match_prefix_length(self, source: str) -> int:
        """Get minimum prefix length for fuzzy matching.
        
        Previously: get_search_fuzziness_prefix_length()
        
        Args:
            source: Source type
            
        Returns:
            Minimum prefix length for fuzzy matching
        """
        return 3

    def get_search_embedding_model(self, source: str) -> str:
        """Get embedding model to use for semantic search.
        
        Previously: get_search_embedding_version()
        
        Args:
            source: Source type
            
        Returns:
            Embedding model string
        """
        return EmbeddingProvider.SBERT, "sentence-transformers/multi-qa-mpnet-base-dot-v1"

    def get_excluded_search_fields(self, source: str) -> List[str]:
        """Get fields to exclude from search results for performance.
        
        Previously: get_exclude_fields()
        
        Args:
            source: Source type
            
        Returns:
            List of fields to exclude
        """
        return [
            "client",
            "embedding__*",
        ]

    def should_filter_by_date(self, source: str) -> bool:
        """Whether to filter results by classification date.
        
        Previously: use_classification_date()
        
        Args:
            source: Source type
            
        Returns:
            Whether to filter results by classification date
        """
        return source in [SOURCE_DOCUMENTS]

    def get_additional_search_filters(self, source: str) -> List[Dict[str, Any]]:
        """Get additional filters to apply to search.
        
        Previously: get_search_filters()
        
        Args:
            source: Source type
            
        Returns:
            List of additional filters
        """
        return []

    def rerank_search_results(self, source: str, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Reorder search results based on custom logic.
        
        Previously: get_reordered_search_results()
        
        Args:
            source: Source type
            items: List of search results
            
        Returns:
            List of reordered search results
        """
        return items

    def format_document(self, source: str, document: Dict[str, Any], item: Dict[str, Any]) -> Dict[str, Any]:
        """Format document in results.
        
        Previously: get_document_item()
        
        Args:
            source: Source type
            document: Document to format
            item: Item to format
            
        Returns:
            Formatted item
        """
        return item

    def should_deduplicate_results(self, source: str) -> bool:
        """Whether to remove duplicate documents based on content.
        
        Previously: use_unique_document_check()
        
        Args:
            source: Source type
            
        Returns:
            Whether to remove duplicate documents based on content
        """
        return True

    def get_relevance_weights(self, source: str) -> Dict[str, float]:
        """Get weights for combining document and chunk scores.
        
        Previously: get_relevance_weights()
        
        Args:
            source: Source type
            
        Returns:
            Dict with weights for document and chunk scores
        """
        return KindWeights(document=0.5, chunk=0.5)

    def get_document_relevance_weights(self, source: str) -> SearchWeights:
        """Get weights for document-level relevance scoring.
        
        Previously: get_document_relevance_weights()
        
        Args:
            source: Source type
            
        Returns:
            Dict with weights for text and semantic scores
        """
        return SearchWeights(text=0.2, semantic=0.8)

    def get_chunk_relevance_weights(self, source: str) -> SearchWeights:
        """Get weights for chunk-level relevance scoring.
        
        Previously: get_chunk_relevance_weights()
        
        Args:
            source: Source type
            
        Returns:
            Dict with weights for text and semantic scores
        """
        return SearchWeights(text=0.2, semantic=0.8)

    def should_use_missed_links(self, source: str) -> bool:
        """Whether to use documents that didn't fit in context as next documents.
        
        Previously: use_missed_links_as_skipped_documents()
        
        Args:
            source: Source type
            
        Returns:
            Whether to use documents that didn't fit in context as next documents
        """
        return False
    
    def get_search_reranker_model(self, source: str) -> Tuple[Optional[str], Optional[str]]:
        """Get reranker model to use for reranking results.
        
        Previously: get_reranker_model()
        
        Args:
            source: Source type
            
        Returns:
            Tuple with reranker model and version
        """
        return (None, None)
    
    def get_reranker_prompt(self, source: str) -> Optional[str]:
        """Get base prompt for reranking results.
        
        Previously: get_reranker_prompt()
        
        Args:
            source: Source type
            
        Returns:
            Base reranker prompt template
        """
        return None

    def get_document_reranker_prompt(self, source: str) -> Optional[str]:
        """Get prompt for reranking document-level results.
        
        Previously: get_document_reranker_prompt()
        
        Args:
            source: Source type
            
        Returns:
            Document reranker prompt template
        """
        return self.get_reranker_prompt(source)

    def get_chunk_reranker_prompt(self, source: str) -> Optional[str]:
        """Get prompt for reranking chunk-level results.
        
        Previously: get_chunk_reranker_prompt()
        
        Args:
            source: Source type
            
        Returns:
            Chunk reranker prompt template
        """
        return self.get_reranker_prompt(source)
    
    def get_reranker_confidence_threshold(self, source: str) -> Optional[float]:
        """Get confidence threshold for reranking results.
        
        Args:
            source: Source type
            
        Returns:
            Confidence threshold for reranking results
        """
        return None
    
    def get_reranker_confidence_threshold_document(self, source: str) -> Optional[float]:
        """Get confidence threshold for reranking document-level results.
        
        Args:
            source: Source type
            
        Returns:
            Confidence threshold for reranking document-level results
        """
        return self.get_reranker_confidence_threshold(source)

    def get_reranker_confidence_threshold_chunk(self, source: str) -> Optional[float]:
        """Get confidence threshold for reranking chunk-level results.
        
        Args:
            source: Source type
            
        Returns:
            Confidence threshold for reranking chunk-level results
        """
        return self.get_reranker_confidence_threshold(source)

    def get_knn_and_rescore_parameters(self, source: str) -> Dict[str, int]:
        """Get parameters for KNN search and rescoring.
        
        Previously: get_knn_and_rescore_parameters()
        
        Args:
            source: Source type
            
        Returns:
            Dictionary with KNN parameters (k, num_candidates, window_size)
        """
        return {
            'k': 1000,
            'num_candidates': 10000,
            'window_size': 1000,
        }

    def should_apply_document_filter(self, source: str) -> bool:
        """Whether to apply additional document filtering logic.
        
        Previously: use_document_filter()
        
        Args:
            source: Source type
            
        Returns:
            Whether to apply document filtering
        """
        return False

    def get_spd_client_code(self) -> str:
        """Get SPD client code."""
        # todo: implement from BS config
        raise NotImplementedError("get_spd_client_code is not yet implemented")

    def get_spd_filtering_attributes(self) -> Dict[str, List[Any]]:
        """Get SPD filtering attributes."""
        # todo: implement from BS config
        return {}

    def get_spd_engine_ratios(self) -> SearchEngineRatios:
        """Get search engine ratios."""
        # todo: implement from BS config
        return SearchEngineRatios(rag=1, spd=1)

    def get_spd_include_promos_only(self) -> bool:
        """Whether to include only promoted items in SPD search."""
        # todo: implement from BS config
        return False
