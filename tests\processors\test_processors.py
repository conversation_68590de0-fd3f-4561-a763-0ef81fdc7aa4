import os
import pytest
from unittest.mock import Mock, patch, MagicMock
import io
import csv
from bs4 import BeautifulSoup

from quepasa.crawler.processors.base import BaseProcessor
from quepasa.crawler.processors.csv_processor import CSVProcessor
from quepasa.crawler.processors.txt_processor import TXTProcessor
from quepasa.crawler.processors.web_processor import WebProcessor
from quepasa.crawler.processors.pdf_processor import PDFProcessor
from quepasa.crawler.processors.pptx_processor import PPTXProcessor

# Fixtures
@pytest.fixture
def sample_csv_content():
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerow(['Header1', 'Header2'])
    writer.writerow(['Value1', 'Value2'])
    writer.writerow(['Value3', 'Value4'])
    return output.getvalue().encode('utf-8')

@pytest.fixture
def sample_txt_content():
    return """Title
First paragraph with
multiple lines.

Second paragraph
also with multiple
lines of text.""".encode('utf-8')

@pytest.fixture
def sample_metadata():
    """Fixture for sample metadata"""
    return {
        'filename': 'test.txt',
        'client_id': 'test_client',
        'url': 'http://example.com/test.txt',
        'extension': 'txt'
    }

@pytest.fixture
def mock_quepasa_extraction_lib():
    """Mock the quepasa_extraction_lib module"""
    mock = MagicMock()
    
    def mock_split_text(text, token_limit=1024):
        return ["Sample text chunk 1", "Sample text chunk 2"]
    
    mock.split_text = mock_split_text
    
    return mock

# Tests for CSVProcessor
class TestCSVProcessor:
    def test_process_valid_csv(self, sample_csv_content):
        processor = CSVProcessor()
        meta = {'filename': 'sample_data.test.csv'}
        result = processor.process(sample_csv_content, meta)['result']
        
        assert 'chunks' in result
        assert 'title' in result
        assert result['filename'] == 'sample_data.test.csv'
        assert result['title'] == 'sample data test'
        assert len(result['chunks']) > 0
        
        # Verify chunk structure and position format
        for chunk in result['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('lines ')
            
            # Extract line numbers and verify format
            position = chunk['position'].replace('lines ', '')
            start, end = map(int, position.split('-'))
            assert start > 0
            assert end >= start

    def test_invalid_metadata(self):
        processor = CSVProcessor()
        result = processor.process(b'', {})
        assert result['status'] == 'error'
        assert 'Missing required metadata fields' in result['error']

# Tests for TXTProcessor
class TestTXTProcessor:
    def test_process_valid_txt(self, sample_txt_content):
        processor = TXTProcessor()
        meta = {'filename': 'sample_file_name.test.txt'}
        result = processor.process(sample_txt_content, meta)['result']
        
        assert 'chunks' in result
        assert 'title' in result
        assert result['filename'] == 'sample_file_name.test.txt'
        assert result['title'] == 'sample file name test'
        
        # Verify chunk structure and position format
        for chunk in result['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('lines ')
            
            # Extract line numbers and verify format
            position = chunk['position'].replace('lines ', '')
            start, end = map(int, position.split('-'))
            assert start > 0
            assert end >= start
            
            # Verify line count matches content
            line_count = chunk['text'].count('\n') + 1
            assert end - start + 1 == line_count

    def test_process_empty_txt(self):
        processor = TXTProcessor()
        meta = {'filename': 'empty.txt'}
        result = processor.process(b'', meta)['result']
        
        assert 'chunks' in result
        assert len(result['chunks']) == 0

    @pytest.mark.skip(reason="TXT processor error handling needs to be revisited")
    def test_invalid_encoding(self, sample_metadata):
        processor = TXTProcessor()
        sample_metadata['filename'] = 'test.txt'
        result = processor.process(b'\xff\xfe', sample_metadata)
        
        assert result is not None
        assert result['status'] == 'error'
        assert 'error' in result
        assert 'Could not decode text content with any supported encoding' in result['error']
