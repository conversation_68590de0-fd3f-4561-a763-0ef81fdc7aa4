from dataclasses import dataclass
from typing import Optional, List, Dict, Any, Union
from datetime import datetime

@dataclass
class QuepasaDocument:
    root_id: Optional[str]
    id: Optional[str]
    chunk_index: Optional[int]
    client: Optional[str]
    domain: Optional[str]
    provider: Optional[str]
    type: Optional[str]
    kind: Optional[str]
    level: Optional[str]
    url: Optional[str]
    language: Optional[Union[List[str], str]]
    title: Optional[str]
    keywords: Optional[List[str]]
    text: Optional[str]
    tokens: Optional[int]
    chunks: Optional[int]
    start_position: Optional[int]
    end_position: Optional[int]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    embeddings: Optional[Dict[str, Any]]
    score: Optional[float]
    sku: Optional[str] = None
    metadata: Optional[str] = None

    def get_product_id(self) -> Optional[str]:
        """Extract product ID"""
        return self.sku

    def get(self, key: str, default: Any = None) -> Any:
        """Get attribute value by key with default fallback"""
        return getattr(self, key, default)

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary for JSON serialization"""
        data = {}
        for field in self.__dataclass_fields__:
            value = getattr(self, field)
            if value is not None:
                if isinstance(value, datetime):
                    data[field] = value.isoformat()
                else:
                    data[field] = value
        return data
