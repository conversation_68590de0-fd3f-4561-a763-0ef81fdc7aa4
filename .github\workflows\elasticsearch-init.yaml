name: "Elasticsearch init Build and Deploy"

on:
  push:
    branches:
      - "main"
    paths:
      - "quepasa/elasticsearch_init/"
      - ".github/workflows/elasticsearch-init.yaml"
  workflow_dispatch:

concurrency:
  group: elasticsearch-init-${{ github.event.repository.name }}-${{ github.head_ref }}
  cancel-in-progress: true

env:
  GITHUB_SHA: ${{ github.sha }}

jobs:
  DefineEnvironment:
    timeout-minutes: 5
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
      checks: write
      id-token: "write"
    outputs:
      DOCKER_TAG: ${{ steps.setdockertag.outputs.DOCKER_TAG }}
    steps:
    - name: Set Docker Tag
      id: setdockertag
      run: |
        _GITHUB_SHA_SHORT="${GITHUB_SHA::8}"
        _UNIX_TIMESTAMP=$(date +%s)
        _DOCKER_TAG="${_GITHUB_SHA_SHORT}"-"${_UNIX_TIMESTAMP}"

        # Set OUTPUT
        echo "DOCKER_TAG=${_DOCKER_TAG}" >> $GITHUB_OUTPUT

        # Show outputs on GHA run
        echo "GITHUB_SHA_SHORT: ${_GITHUB_SHA_SHORT}"
        echo "UNIX_TIMESTAMP: ${_UNIX_TIMESTAMP}"
        echo "DOCKER_TAG: ${_DOCKER_TAG}"

  DockerBuildPush:
    needs: [DefineEnvironment]
    timeout-minutes: 30
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
      checks: write
      id-token: "write"
    env:
      DOCKER_TAG: ${{ needs.DefineEnvironment.outputs.DOCKER_TAG }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        submodules: 'recursive'

    - uses: docker/setup-buildx-action@v3

    - id: auth-docker
      uses: google-github-actions/auth@v2
      with:
        token_format: access_token
        workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
        service_account: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER_SVC }}
        access_token_lifetime: 600s

    - name: Login to docker
      uses: docker/login-action@v3
      with:
        registry: europe-west2-docker.pkg.dev
        username: oauth2accesstoken
        password: ${{ steps.auth-docker.outputs.access_token }}

    - name: Build and Push Docker image
      uses: docker/build-push-action@v6
      with:
        context: ./
        file: ./quepasa/elasticsearch_init/Dockerfile
        tags: europe-west2-docker.pkg.dev/rezolve-sas-infra/quepasa/elasticsearch-init:dev-${{ env.DOCKER_TAG }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        provenance: false
        push: true 