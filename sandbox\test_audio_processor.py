#!/usr/bin/env python3

import os
import sys
import tempfile
import argparse
from pathlib import Path

# Add quepasa package to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from quepasa.crawler.processors.audio_processor import AudioProcessor
from src.lib.logger import QuepasaLogger

# Configure logger
logger = QuepasaLogger().get_instance("test_audio_processor")

def test_audio_processor(audio_file_path, language=None):
    """Test the AudioProcessor with a given audio file."""
    print(f"\n=== Testing AudioProcessor on {audio_file_path} ===")
    
    # Check that file exists
    audio_path = Path(audio_file_path)
    if not audio_path.exists():
        print(f"Error: File {audio_file_path} does not exist!")
        return
    
    try:
        # Initialize processor
        processor = AudioProcessor()
        
        # Read audio file
        with open(audio_path, 'rb') as f:
            content = f.read()
        
        # Create metadata
        meta = {
            'filename': audio_path.name,
            'extension': audio_path.suffix,
            'id': audio_path.stem,
            'url': f"file://{audio_path.name}"
        }
        
        # Add language if specified
        if language:
            meta['language'] = language
            print(f"Using language: {language}")
        
        # Process audio
        print("Processing audio file...")
        result = processor.process(content, meta)
        
        # Print results
        if result.get('status') == 'success':
            print("\n=== Processing Successful ===")
            result_data = result.get('result', {})
            
            # Print title
            print(f"\nTitle: {result_data.get('title', 'N/A')}")
            
            # Print chunks info
            chunks = result_data.get('chunks', [])
            print(f"\nExtracted {len(chunks)} text chunks:")
            
            # Print first few chunks
            max_chunks_to_show = 3
            for i, chunk in enumerate(chunks[:max_chunks_to_show]):
                print(f"\nChunk {i+1}:")
                print(f"Position: {chunk.get('position', 'N/A')}")
                
                # Truncate text if too long
                text = chunk.get('text', '')
                if len(text) > 300:
                    text = text[:300] + "..."
                print(f"Text: {text}")
            
            # Indicate if there are more chunks
            if len(chunks) > max_chunks_to_show:
                print(f"\n... and {len(chunks) - max_chunks_to_show} more chunks")
            
            # Save the result as a temporary JSON file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
                import json
                json.dump(result, tmp, indent=2, ensure_ascii=False)
                print(f"\nFull results saved to: {tmp.name}")
        else:
            print(f"\n=== Processing Failed ===")
            print(f"Error: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"Error during processing: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    parser = argparse.ArgumentParser(description='Test QuePasa AudioProcessor')
    parser.add_argument('audio_file', help='Path to the audio file to test')
    parser.add_argument('--language', help='Language code (e.g., "ru", "en")')
    
    args = parser.parse_args()
    
    # Check REPLICATE_API_KEY
    if not os.getenv('REPLICATE_API_KEY'):
        print("Error: REPLICATE_API_KEY environment variable is not set.")
        print("Please set it before running this script.")
        sys.exit(1)
    
    test_audio_processor(args.audio_file, args.language)

if __name__ == "__main__":
    main() 