from typing import Dict, Any, List, Union
from dataclasses import dataclass
from quepasa.searcher.models.request import QuepasaRequest
from .base_config import BaseConfig
@dataclass
class HistoryConfig:
    """Configuration for user history management."""
    forget_after_seconds: int  # Time after which to forget history
    max_last_messages: int  # Maximum number of messages to keep
    use_roles: List[str]  # Roles to include in history

class UserHistoryConfig(BaseConfig):
    """Base configuration for user history."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
    
    def get_history_config(self) -> HistoryConfig:
        """Get configuration for user history management.
        
        Previously: get_history_config()
                    
        Returns:
            HistoryConfig with history settings
        """
        return HistoryConfig(
            forget_after_seconds=4 * 3600,  # 4 hours
            max_last_messages=5,
            use_roles=["user", "assistant"]
        ) 