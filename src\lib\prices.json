{"gpt-4o-2024-05-13": {"input": 5.0, "output": 15.0}, "gpt-4o-mini-2024-07-18": {"input": 0.15, "output": 0.6}, "gpt-4-0125-preview": {"input": 10.0, "output": 30.0}, "gpt-4-1106-preview": {"input": 10.0, "output": 30.0}, "gpt-4-1106-vision-preview": {"input": 10.0, "output": 30.0}, "gpt-4-turbo-2024-04-09": {"input": 10.0, "output": 30.0}, "gpt-4": {"input": 30.0, "output": 60.0}, "gpt-4-32k": {"input": 60.0, "output": 120.0}, "gpt-3.5-turbo-0125": {"input": 0.5, "output": 1.5}, "gpt-3.5-turbo-instruct": {"input": 1.5, "output": 2.0}, "gpt-3.5-turbo-1106": {"input": 1.0, "output": 2.0}, "gpt-3.5-turbo-0613": {"input": 1.5, "output": 2.0}, "gpt-3.5-turbo-16k-0613": {"input": 3.0, "output": 4.0}, "gpt-3.5-turbo-0301": {"input": 1.5, "output": 2.0}, "mistral:open-mistral-7b": {"input": 0.25, "output": 0.25}, "mistral:open-mixtral-8x7b": {"input": 0.7, "output": 0.7}, "mistral:open-mixtral-8x22b": {"input": 2.0, "output": 6.0}, "mistral:open-mistral-nemo-2407": {"input": 0.3, "output": 0.3}, "mistral:mistral-nemo": {"input": 0.15, "output": 0.15}, "mistral:ministral-3b-latest": {"input": 0.04, "output": 0.04}, "mistral:ministral-8b-latest": {"input": 0.1, "output": 0.1}, "mistral:mistral-small-2409": {"input": 0.2, "output": 0.6}, "mistral:codestral-2405": {"input": 0.2, "output": 0.6}, "mistral:mistral-large-2402": {"input": 3.0, "output": 9.0}, "mistral:mistral-large-2407": {"input": 2.0, "output": 6.0}, "anthropic:claude-instant-1.2": {"input": 0.8, "output": 2.4}, "anthropic:claude-2.0": {"input": 8.0, "output": 24.0}, "anthropic:claude-2.1": {"input": 8.0, "output": 24.0}, "anthropic:claude-3-haiku-20240307": {"input": 0.25, "output": 1.25}, "anthropic:claude-3-5-haiku-20241022": {"input": 1.0, "output": 5.0}, "anthropic:claude-3-sonnet-20240229": {"input": 3.0, "output": 15.0}, "anthropic:claude-3-5-sonnet-20241022": {"input": 3.0, "output": 15.0}, "anthropic:claude-3-5-sonnet-20240620": {"input": 3.0, "output": 15.0}, "anthropic:claude-3-opus-20240229": {"input": 15.0, "output": 75.0}, "replicate:meta-llama-3-8b-instruct": {"input": 0.05, "output": 0.25}, "replicate:meta-llama-3-70b-instruct": {"input": 0.65, "output": 2.75}, "replicate:meta-llama-3.1-405b-instruct": {"input": 9.5, "output": 9.5}}