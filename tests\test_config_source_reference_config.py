import pytest
from unittest.mock import patch
from configuration.base.source_reference import (
    SourceReferenceConfig,
    ReferenceFormat,
    SourceReference,
)
from src.lib.constants import SOURCE_ALL, DOCUMENT_TYPE_DIALOG, DOCUMENT_TYPE_DOCUMENT
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.web import WebSearchResult

def test_reference_format_dataclass():
    format = ReferenceFormat(emoji="💬", format="date")
    assert format.emoji == "💬"
    assert format.format == "date"

def test_source_reference_dataclass():
    ref = SourceReference(
        label="💬 Jan 8",
        url="https://example.com",
        source_type="dialog",
        created_at="2024-01-08"
    )
    assert ref.label == "💬 Jan 8"
    assert ref.url == "https://example.com"
    assert ref.source_type == "dialog"
    assert ref.created_at == "2024-01-08"

@patch('src.lib.utils.get_link_index_from_date')
def test_get_source_reference_dialog(mock_date_index):
    mock_date_index.return_value = "Jan 8"
    config = SourceReferenceConfig("test_client")
    item = QuepasaDocument(
        root_id="1",
        id="1",
        chunk_index=0,
        client="test_client",
        domain="example.com",
        provider="test_provider",
        type=DOCUMENT_TYPE_DIALOG,
        kind="dialog",
        level="info",
        url="https://example.com",
        language="en",
        title=None,
        keywords=[],
        text="Example",
        tokens=0,
        chunks=1,
        start_position=0,
        end_position=0,
        created_at="2024-01-08",
        updated_at=None,
        embeddings=None,
        score=0.8
    )
    ref = config.get_source_reference(SOURCE_ALL, item)
    assert "💬" in ref
    assert "Jan 8" in ref

@patch('src.lib.utils.get_link_index_from_url')
def test_get_source_reference_webpage(mock_url_index):
    mock_url_index.return_value = "example.com"
    config = SourceReferenceConfig("test_client")
    item =  WebSearchResult(
        url="https://example.com/page",
        title="Example",
        snippet="Example",
        text="Example"
    )
    ref = config.get_source_reference(SOURCE_ALL, item)
    assert "🌐" in ref
    assert "example.com" in ref

@patch('src.lib.utils.get_link_index_from_date')
def test_get_source_reference_malformed_date(mock_date_index):
    mock_date_index.return_value = "invalid-date"
    config = SourceReferenceConfig("test_client")
    item = QuepasaDocument(
        root_id="1",
        id="1",
        chunk_index=0,
        client="test_client",
        domain="example.com",
        provider="test_provider",
        type=DOCUMENT_TYPE_DIALOG,
        kind="dialog",
        level="info",
        url="https://example.com",
        language="en",
        title=None,
        keywords=[],
        text="Example",
        tokens=0,
        chunks=1,
        start_position=0,
        end_position=0,
        created_at="invalid-date",
        updated_at=None,
        embeddings=None,
        score=0.8
    )
    ref = config.get_source_reference(SOURCE_ALL, item)
    assert "💬" in ref
    assert "invalid-date" in ref

@patch('src.lib.utils.get_link_index_from_url')
def test_get_source_reference_complex_url(mock_url_index):
    mock_url_index.return_value = "sub.example.com:8080"
    config = SourceReferenceConfig("test_client")
    item = WebSearchResult(
        url="https://sub.example.com:8080/path?query=1",
        title="Example",
        snippet="Example",
        text="Example"
    )
    ref = config.get_source_reference(SOURCE_ALL, item)
    assert "🌐" in ref
    assert "sub.example.com:8080" in ref

@pytest.mark.skip(reason="This test is not needed as it's covered by the default behavior")
@patch('src.lib.utils.get_link_index_from_url')
def test_get_source_reference_invalid_url(mock_url_index):
    # Simulate URL parsing failure
    invalid_url = "not-a-url"
    mock_url_index.side_effect = ValueError("Invalid URL")
    
    config = SourceReferenceConfig("test_client")
    item = WebSearchResult(
        url=invalid_url,
        title="Example",
        snippet="Example",
        text="Example"
    )
    ref = config.get_source_reference(SOURCE_ALL, item)
    assert ref == f"🌐 {invalid_url}"  # Expect emoji + space + original URL

@patch('src.lib.utils.get_link_index_from_date')
def test_get_source_reference_object(mock_date_index):
    mock_date_index.return_value = "Jan 8"
    config = SourceReferenceConfig("test_client")
    item = QuepasaDocument(
        root_id="1",
        id="1",
        chunk_index=0,
        client="test_client",
        domain="example.com",
        provider="test_provider",
        type=DOCUMENT_TYPE_DIALOG,
        kind="dialog",
        level="info",
        url="https://example.com",
        language="en",
        title=None,
        keywords=[],
        text="Example",
        tokens=0,
        chunks=1,
        start_position=0,
        end_position=0,
        created_at="2024-01-08",
        updated_at=None,
        embeddings=None,
        score=0.8
    )
    ref_obj = config.get_source_reference_object(SOURCE_ALL, item)
    assert isinstance(ref_obj, SourceReference)
    assert ref_obj.source_type == DOCUMENT_TYPE_DIALOG
    assert ref_obj.url == "https://example.com"
    assert ref_obj.created_at == "2024-01-08"

def test_get_label_format():
    config = SourceReferenceConfig("test_client")
    
    # Test dialog format
    dialog_format = config._get_label_format(SOURCE_ALL, QuepasaDocument(
        root_id="1",
        id="1",
        chunk_index=0,
        client="test_client",
        domain="example.com",
        provider="test_provider",
        type=DOCUMENT_TYPE_DIALOG,
        kind="dialog",
        level="info",
        url="https://example.com",
        language="en",
        title=None,
        keywords=[],
        text="Example",
        tokens=0,
        chunks=1,
        start_position=0,
        end_position=0,
        created_at="2024-01-08",
        updated_at=None,
        embeddings=None,
        score=0.8
    ))
    assert dialog_format.emoji == "💬"
    assert dialog_format.format == "date"
    
    # Test webpage format
    webpage_format = config._get_label_format(SOURCE_ALL, WebSearchResult(url="https://example.com", title="Example", snippet="Example", text="Example"))
    assert webpage_format.emoji == "🌐"
    assert webpage_format.format == "host"
    
    # Test default format
    default_format = config._get_label_format(SOURCE_ALL, QuepasaDocument(
        root_id="1",
        id="1",
        chunk_index=0,
        client="test_client",
        domain="example.com",
        provider="test_provider",
        type="unknown",
        kind="document",
        level="info",
        url="https://example.com",
        language="en",
        title=None,
        keywords=[],
        text="Example",
        tokens=0,
        chunks=1,
        start_position=0,
        end_position=0,
        created_at="2024-01-08",
        updated_at=None,
        embeddings=None,
        score=0.8
    ))
    assert default_format.emoji == ""
    assert default_format.format == "title"

@pytest.mark.parametrize("source_type,expected_prefix,expected_format", [
    (DOCUMENT_TYPE_DIALOG, "💬", "date"),
    (DOCUMENT_TYPE_DOCUMENT, "🌐", "host"),
    ("unknown", "", "title"),
    ("", "", "title"),
    (None, "", "title"),
])
def test_get_label_format_various_types(source_type, expected_prefix, expected_format):
    config = SourceReferenceConfig("test_client")
    format = config._get_label_format(SOURCE_ALL, QuepasaDocument(
        root_id="1",
        id="1",
        chunk_index=0,
        client="test_client",
        domain="example.com",
        provider="test_provider",
        type=source_type,
        kind="document",
        level="info",
        url="https://example.com",
        language="en",
        title=None,
        keywords=[],
        text="Example",
        tokens=0,
        chunks=1,
        start_position=0,
        end_position=0,
        created_at="2024-01-08",
        updated_at=None,
        embeddings=None,
        score=0.8
    ))
    assert format.emoji == expected_prefix
    assert format.format == expected_format
