import os
import pytest

from quepasa.common import config_client as cc


def test_login_get_bearer_missing_env(monkeypatch):
    for var in [
        "BRAIN_STUDIO_SUPERADMIN_EMAIL",
        "BRAIN_STUDIO_SUPERADMIN_PASSWORD",
        "BRAIN_STUDIO_LOGIN_URL",
    ]:
        monkeypatch.delenv(var, raising=False)
    with pytest.raises(ValueError) as ei:
        cc.login_get_bearer()
    msg = str(ei.value)
    assert "Missing environment variables" in msg
    assert "BRAIN_STUDIO_SUPERADMIN_EMAIL" in msg
    assert "BRAIN_STUDIO_SUPERADMIN_PASSWORD" in msg
    assert "BRAIN_STUDIO_LOGIN_URL" in msg


def test_fetch_dynamic_config_missing_env(monkeypatch):
    monkeypatch.delenv("BRAIN_STUDIO_CONFIGURATION_URL", raising=False)
    monkeypatch.delenv("BRAIN_STUDIO_CONFIGURATION_URL_SUFFIX", raising=False)
    with pytest.raises(ValueError) as ei:
        cc.fetch_dynamic_config("token")
    msg = str(ei.value)
    assert "Missing environment variables" in msg
    assert "BRAIN_STUDIO_CONFIGURATION_URL" in msg
    assert "BRAIN_STUDIO_CONFIGURATION_URL_SUFFIX" in msg
