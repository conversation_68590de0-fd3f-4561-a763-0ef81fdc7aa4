import pytest
from unittest.mock import patch
from flask import Flask
from quepasa.searcher.main import app, get_endpoint_from_path
from quepasa.searcher.models.request import QuepasaRequest

@pytest.fixture
def mock_config():
    with patch('configuration.main.default.QuepasaConfigurationHub') as mock:
        instance = mock.return_value
        instance.is_client_authorized.return_value = True
        instance.get_telegram_access_token.return_value = "test_token"
        instance.is_telegram_user_authorized.return_value = True
        instance.get_language_code.return_value = "en"
        yield mock

@pytest.fixture
def client(mock_config):
    with patch('quepasa.searcher.api.base.HistoryManager'):
        app.config['TESTING'] = True
        with app.test_client() as client:
            yield client

@pytest.fixture
def mock_handler():
    with patch('quepasa.searcher.main.HTTPHandler') as mock:
        instance = mock.return_value
        instance.handle_request.return_value = ({"status": "ok"}, 200, {})
        yield mock

def test_get_endpoint_from_path():
    assert get_endpoint_from_path('/search') == 'search'
    assert get_endpoint_from_path('/api/v1/search/') == 'search'
    assert get_endpoint_from_path('/') is None
    assert get_endpoint_from_path('/search?query=test') == 'search'

def test_options_request(client):
    response = client.options('/search')
    assert response.status_code == 200
    assert 'Access-Control-Allow-Origin' in response.headers

def test_invalid_endpoint(client):
    response = client.post('/invalid', json={
        'question': 'test query',
        'source': 'web',
        'client': 'test_client'
    })
    assert response.status_code == 404

def test_missing_protocol(client):
    response = client.post('/search', json={
        'question': 'test query',
        'source': 'web',
        'client': 'test_client'
    }, headers={'Authorization': 'Bearer test_client:token'})
    assert response.status_code == 401

def test_valid_request(client, mock_handler):
    response = client.post('/search', json={
        'question': 'test query',
        'protocol': 'http',
        'source': 'web',
        'client': 'test_client'
    }, headers={'Authorization': 'Bearer test_client:token'})
    assert response.status_code == 200 