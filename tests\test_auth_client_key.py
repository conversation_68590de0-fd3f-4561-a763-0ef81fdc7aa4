import pytest
from quepasa.common.bearer import Request<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Auth<PERSON><PERSON>ult
from quepasa.common import config_client as cc
from quepasa.common.config_models import TenantConfig, TenantModel


def make_tenant(tenant_name: str, key1: str = "k1", key2: str = None) -> TenantConfig:
    tenant = TenantModel(id=1, name=tenant_name, enabled=True, settings={}, activeKey_1=key1, activeKey_2=key2)
    return TenantConfig(
        tenant=tenant,
        collections=[],
        areas=[],
        attributes=[],
        conversational_search_configs=[],
        conversational_prompt_configs=[],
        conversational_filterable_attributes=[],
    )


@pytest.fixture(autouse=True)
def clear_store():
    # ensure clean in-memory store
    cc._MERCHANT_STORE.clear()
    yield
    cc._MERCHANT_STORE.clear()


def test_client_key_success_with_headers(monkeypatch):
    # Seed tenant config
    cc._MERCHANT_STORE.set_tenant_view("acme", make_tenant("acme", key1="abc"))

    headers = {
        "X-Customer-Id": "acme",
        "Authorization": "client-key abc",
    }
    res = RequestAuthenticatorManager().authenticate(headers)
    assert res.is_authorized is True
    assert res.client_id == "acme"


def test_client_key_invalid_key(monkeypatch):
    cc._MERCHANT_STORE.set_tenant_view("acme", make_tenant("acme", key1="expected"))

    headers = {
        "x-customer-id": "acme",
        "Authorization": "client-key wrong",
    }
    res = RequestAuthenticatorManager().authenticate(headers)
    assert res.is_authorized is False
    assert "Invalid or missing client key" in (res.error or "")


def test_client_key_missing_customer_id():
    headers = {
        "Authorization": "client-key somekey",
    }
    res = RequestAuthenticatorManager().authenticate(headers)
    assert res.is_authorized is False
    # Without X-Customer-Id, the manager treats this as unsupported type in this path
    assert "Invalid Authorization header format" in (res.error or "")


def test_bearer_requires_client_id():
    # "Bearer tokenonly" should not be accepted because format requires client:token
    headers = {
        "Authorization": "Bearer tokenonly",
    }
    res = RequestAuthenticatorManager().authenticate(headers)
    assert res.is_authorized is False
    assert "Invalid Authorization header format" in (res.error or "")
