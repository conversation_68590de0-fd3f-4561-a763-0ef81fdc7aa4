"""
YAML Configuration Validation Tests - Updated for Simplified Structure

Tests for the new simplified YAML configuration structure where:
- Basic config is in YAML (language, search, models, store_info, prompts)
- Complex structures are in Python (agentic_tools, thinking_messages)
"""

import pytest
import sys
import yaml
from pathlib import Path
from typing import Dict, Any

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from configuration.yaml_loader import YAMLConfigurationLoader, ConfigurationValidationError, load_client_config


class TestYAMLValidationSimplified:
    """Test YAML configuration loading and validation for simplified structure."""
    
    @pytest.fixture
    def yaml_loader(self):
        """Create YAML configuration loader."""
        return YAMLConfigurationLoader()
    
    @pytest.fixture
    def shoeby_config(self):
        """Load Shoeby configuration for testing."""
        return load_client_config("rezolve_shoeby_openai")
    
    def test_yaml_loader_initialization(self, yaml_loader):
        """Test YAML loader initialization."""
        assert yaml_loader is not None
        assert hasattr(yaml_loader, 'load_client_config')
        assert hasattr(yaml_loader, 'validate_all_clients')
    
    def test_shoeby_config_loads_successfully(self, shoeby_config):
        """Test that Shoeby configuration loads without errors."""
        assert shoeby_config is not None
        assert isinstance(shoeby_config, dict)
        assert shoeby_config['client_code'] == "rezolve_shoeby_openai"
    
    def test_required_top_level_fields(self, shoeby_config):
        """Test that all required top-level fields are present in simplified structure."""
        required_fields = [
            'client_code', 'query_expansions', 'language',
            'search', 'user_history', 'llm_models', 'store_info'
        ]
        
        for field in required_fields:
            assert field in shoeby_config, f"Missing required field: {field}"
    
    def test_client_code_value(self, shoeby_config):
        """Test client code value."""
        assert shoeby_config['client_code'] == "rezolve_shoeby_openai"
    
    def test_query_expansions_structure(self, shoeby_config):
        """Test query expansions structure and content."""
        expansions = shoeby_config['query_expansions']
        assert isinstance(expansions, list)
        assert len(expansions) == 2
        
        # Test first expansion
        assert expansions[0]['keywords'] == "trainers"
        assert expansions[0]['query'] == "sneakers"
        
        # Test second expansion
        assert expansions[1]['keywords'] == "azure"
        assert expansions[1]['query'] == "light blue cyan"
    
    def test_language_configuration(self, shoeby_config):
        """Test language configuration structure."""
        language = shoeby_config['language']
        
        assert language['fallback_language'] == 'en'
        assert language['language_mapping']['en'] == "English"
        assert language['language_mapping']['nl'] == "Dutch"
        assert 'en' in language['indexed_languages']['document']
        assert 'nl' in language['indexed_languages']['document']
    
    def test_search_configuration(self, shoeby_config):
        """Test search configuration."""
        search = shoeby_config['search']
        assert search['max_results_limit'] == 25
    
    def test_user_history_configuration(self, shoeby_config):
        """Test user history configuration."""
        history = shoeby_config['user_history']
        
        assert history['forget_after_seconds'] == 28800  # 8 hours
        assert history['max_last_messages'] == 64
        assert set(history['use_roles']) == {"user", "assistant", "tool"}
    
    def test_llm_models_configuration(self, shoeby_config):
        """Test LLM models configuration."""
        models = shoeby_config['llm_models']
        
        assert models['agentic_source'] == ["openai", "gpt-4o"]
        assert models['document_source'] == ["nebius", "Qwen/Qwen2.5-72B-Instruct-fast"]
    
    def test_store_info_structure(self, shoeby_config):
        """Test store information structure."""
        store_info = shoeby_config['store_info']
        
        assert store_info['name'] == "Shoeby"
        assert isinstance(store_info['offerings'], list)
        assert len(store_info['offerings']) == 3
        
        expected_offerings = [
            "clothing, footwear, and accessories for women and children (girls and boys)",
            "footwear is available only for women and girls",
            "a smaller selection of clothing and accessories for men"
        ]
        assert store_info['offerings'] == expected_offerings
    
    def test_python_agentic_prompt_has_placeholders(self):
        """Test that Python-generated agentic prompt has proper template placeholders."""
        # Test that the Python configuration code generates prompts with placeholders
        from configuration.main.default import RezolveShoebyConfiguration
        from src.lib.constants import SOURCE_AGENTIC
        
        config = RezolveShoebyConfiguration('rezolve_shoeby_openai')
        prompt_template = config.get_llm_prompt_template(SOURCE_AGENTIC)
        prompt = prompt_template[0]['content']
        
        # Should have essential content and proper formatting
        assert isinstance(prompt, str)
        assert len(prompt) > 1000  # Should be substantial
        assert "You are a friendly and helpful AI sales assistant" in prompt
        assert "Shoeby online store" in prompt  # Store name should be filled in
        assert "warmth, empathy, and a great sense of style 😊" in prompt
        assert "shoeby_catalog_rag" in prompt
    
    def test_python_document_rag_prompt_structure(self):
        """Test that Python-generated document RAG prompt has proper structure."""
        from configuration.main.default import RezolveShoebyConfiguration
        from src.lib.constants import SOURCE_DOCUMENTS
        
        config = RezolveShoebyConfiguration('rezolve_shoeby_openai')
        prompt_template = config.get_llm_prompt_template(SOURCE_DOCUMENTS)
        prompt = prompt_template[0]['content']
        
        # Should have essential RAG content
        assert isinstance(prompt, str)
        assert len(prompt) > 500  # Should be substantial
        assert "bot-assistant that answers user requests" in prompt
        assert "approximately relevant" in prompt
        assert "{{LANGUAGE}}" in prompt  # Fixed format
        assert "{{SOURCES}}" in prompt   # Fixed format 
        assert "black suit, a dark blue suit" in prompt
    
    def test_embedding_configuration_exists(self, shoeby_config):
        """Test that embedding configuration exists for variants."""
        # This should be present for variant configs like rezolve_shoeby_openai
        assert 'embedding' in shoeby_config
        
        embedding = shoeby_config['embedding']
        assert 'index_suffix' in embedding
        assert 'search_model' in embedding
        assert embedding['search_model']['provider'] == 'openai'
        assert embedding['search_model']['model'] == 'text-embedding-3-small'
    
    def test_yaml_loader_validation(self, yaml_loader):
        """Test that YAML loader validation works correctly."""
        # Should load successfully
        config = yaml_loader.load_client_config("rezolve_shoeby_openai")
        assert config is not None
        
        # Should cache the config
        config2 = yaml_loader.load_client_config("rezolve_shoeby_openai")
        assert config2 is config  # Same object reference due to caching
    
    def test_yaml_loader_available_clients(self, yaml_loader):
        """Test getting available clients."""
        clients = yaml_loader.get_available_clients()
        assert isinstance(clients, list)
        assert "rezolve_shoeby_openai" in clients
        assert "shoeby_base" in clients
    
    def test_yaml_loader_validation_all_clients(self, yaml_loader):
        """Test validating all available clients."""
        available_clients = yaml_loader.get_available_clients()
        results = yaml_loader.validate_all_clients(available_clients)
        
        assert isinstance(results, dict)
        for client_code, result in results.items():
            assert result["status"] == "valid", f"Client {client_code} should be valid: {result}"
    
    def test_invalid_client_raises_error(self, yaml_loader):
        """Test that invalid client code raises appropriate error."""
        with pytest.raises(ConfigurationValidationError):
            yaml_loader.load_client_config("nonexistent_client")
    
    def test_yaml_syntax_validity(self):
        """Test that YAML files have valid syntax."""
        yaml_files = [
            "configuration/clients/shoeby_base.yaml",
            "configuration/clients/rezolve_shoeby_openai.yaml",
            "configuration/clients/rezolve_shoeby_bge.yaml"
        ]
        
        for yaml_path in yaml_files:
            with open(yaml_path, 'r') as f:
                # This will raise an exception if YAML is invalid
                config = yaml.safe_load(f)
            
            assert config is not None, f"YAML file {yaml_path} should load successfully"
    
    def test_inheritance_system_works(self):
        """Test that configuration inheritance from base works."""
        # Load base config
        base_config = load_client_config("shoeby_base")
        assert base_config['client_code'] == "shoeby_base"
        
        # Load variant config
        variant_config = load_client_config("rezolve_shoeby_openai")
        assert variant_config['client_code'] == "rezolve_shoeby_openai"
        
        # Variant should inherit from base
        assert variant_config['store_info'] == base_config['store_info']
        assert variant_config['query_expansions'] == base_config['query_expansions']
        assert variant_config['language'] == base_config['language']
        
        # But should have its own embedding config
        assert 'embedding' in variant_config
        assert variant_config['embedding']['index_suffix'] == 'openai'


class TestPromptIntegrity:
    """Test that prompts maintain their integrity after YAML processing."""
    
    def test_agentic_prompt_formatting(self):
        """Test that agentic prompt formats correctly with store information."""
        from configuration.main.default import RezolveShoebyConfiguration
        from src.lib.constants import SOURCE_AGENTIC
        
        config = RezolveShoebyConfiguration('rezolve_shoeby_openai')
        prompt_template = config.get_llm_prompt_template(SOURCE_AGENTIC)
        prompt_content = prompt_template[0]['content']
        
        # Should have store name substituted
        assert "Shoeby online store" in prompt_content
        
        # Should have formatted offerings
        assert "- clothing, footwear, and accessories for women and children (girls and boys);" in prompt_content
        assert "- a smaller selection of clothing and accessories for men." in prompt_content
        
        # Should preserve original personality and instructions
        assert "warmth, empathy, and a great sense of style 😊" in prompt_content
        assert "Track what you've already learned" in prompt_content or "Track what you\u2019ve already learned" in prompt_content
        assert "Pink unicorn pyjamas → girl" in prompt_content or "Pink unicorn pyjamas \u2192 girl" in prompt_content
    
    def test_document_rag_prompt_preserved(self):
        """Test that document RAG prompt is preserved correctly."""
        from configuration.main.default import RezolveShoebyConfiguration
        from src.lib.constants import SOURCE_DOCUMENTS
        
        config = RezolveShoebyConfiguration('rezolve_shoeby_openai')
        prompt_template = config.get_llm_prompt_template(SOURCE_DOCUMENTS)
        prompt_content = prompt_template[0]['content']
        
        # Should have sophisticated RAG logic
        assert "approximately relevant" in prompt_content
        assert "black suit, a dark blue suit" in prompt_content
        assert "[#1] or [#2][#4]" in prompt_content
        assert "{{LANGUAGE}}" in prompt_content  # Fixed format
        assert "{{SOURCES}}" in prompt_content   # Fixed format