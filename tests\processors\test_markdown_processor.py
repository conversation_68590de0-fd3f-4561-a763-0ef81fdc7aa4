import pytest
from unittest.mock import patch, MagicMock
from quepasa.crawler.processors.markdown_processor import MarkdownProcessor

@pytest.fixture
def mock_markdown_content():
    """Fixture for mock Markdown content"""
    return """
    # Test Title
    
    This is a test markdown content.
    
    ## Section
    More content here.
    """.encode('utf-8')

@pytest.fixture
def sample_metadata():
    """Fixture for sample metadata"""
    return {
        'filename': 'test.md',
        'client_id': 'test_client',
        'url': 'http://example.com/test.md',
        'extension': 'md'
    }

@pytest.fixture
def mock_quepasa_extraction_lib():
    """Mock the quepasa_extraction_lib module"""
    mock = MagicMock()
    
    def mock_markdown_to_pages_recursive(content, minimal_number_of_words=8):
        return [{"text": "# Test Title\nSample markdown page content"}]
    
    mock.markdown_to_pages_recursive = mock_markdown_to_pages_recursive
    return mock

class TestMarkdownProcessor:
    @patch('quepasa.crawler.processors.markdown_processor.markdown_to_pages_recursive')
    def test_process_valid_markdown(self, mock_markdown_to_pages, sample_metadata, mock_markdown_content, mock_quepasa_extraction_lib):
        mock_markdown_to_pages.return_value = [{"text": "# Test Title\nSample markdown page content"}]
        
        processor = MarkdownProcessor()
        result = processor.process(mock_markdown_content, sample_metadata)
        
        assert result is not None
        assert result['status'] == 'success'
        assert 'meta' in result
        assert 'result' in result
        assert isinstance(result['result']['chunks'], list)
        assert len(result['result']['chunks']) > 0
        
        # Verify chunk structure
        for chunk in result['result']['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('lines ')
            assert '-' in chunk['position']  # Check for line range format
        
        assert result['result']['title'] == 'Test Title'
        assert result['result']['filename'] == sample_metadata['filename'] 