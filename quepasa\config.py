from typing import Dict, List

# File Processing
ALLOWED_MIME_TYPES = {
    "application/msword": "doc",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
    "application/vnd.ms-powerpoint": "ppt",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
    "application/vnd.ms-excel": "xls",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
    "application/pdf": "pdf",
    "text/markdown": "md",
    "text/plain": "txt",
    "text/csv": "csv",
    "text/html": "html",
    # Audio types
    "audio/mpeg": "mp3",
    "audio/mp3": "mp3",
    "audio/mp4": "m4a",
    "audio/wav": "wav",
    "audio/x-wav": "wav",
    "audio/ogg": "ogg",
    "audio/flac": "flac",
    "audio/aac": "aac",
    # Video types
    "video/mp4": "mp4",
    "video/x-msvideo": "avi",
    "video/quicktime": "mov",
    "video/x-matroska": "mkv",
    "video/webm": "webm",
    "video/x-flv": "flv",
    "video/x-ms-wmv": "wmv"
}

ALLOWED_EXTENSIONS = list(ALLOWED_MIME_TYPES.values())

# Text Processing
VS_CHUNK_SIZE = 1024
VS_CHUNK_OVERLAP = 30
MAX_FORMATED_CHUNK_SIZE = VS_CHUNK_SIZE * 2

# Proxy Configuration
PROXY_LIST_CONFIG_FILE = "prod/config/proxylist.json"

# User Agents for Web Requests
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:102.0) Gecko/20100101 Firefox/102.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
]

ACCEPT_LANGUAGES = [
    "en-US,en;q=0.9",
    "en-GB,en;q=0.8",
    "en-US,en;q=0.7,es;q=0.3",
    "en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
] 