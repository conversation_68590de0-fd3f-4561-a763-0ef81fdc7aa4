#!/usr/bin/env python3
import os
import sys
import time
from typing import List, Optional
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.lib.embedding.openai import OpenAIEmbedding
from src.lib.embedding.providers import EmbeddingProvider
from src.lib.embedding_utils import get_cached_embedding
from src.lib.logger import <PERSON><PERSON><PERSON>Logger

# Set up logging
logger = QuepasaLogger().get_instance(__name__)

def test_openai_embedding():
    """Test OpenAI embedding with proper error handling."""
    test_text = "This is a test of the OpenAI embedding service."
    model_version = "text-embedding-3-small"
    
    print(f"Testing OpenAI embedding with model: {model_version}")
    print(f"Testing text: '{test_text}'")
    
    # Create the embedding provider
    embedding_provider = OpenAIEmbedding()

    # Check for API key
    api_key = os.getenv('OPENAI_API_KEY')
    # if not api_key:
    #     print("ERROR: OPENAI_API_KEY environment variable is not set.")
    #     return False

    # print(f"API key is set (begins with {api_key[:4]}...)")
    
    # Test direct embedding (without caching)
    try:
        print("\nTesting direct embedding (without caching)...")
        start_time = time.time()
        embedding_result = get_cached_embedding(EmbeddingProvider.OPENAI, model_version, test_text)
        duration = time.time() - start_time
        
        if embedding_result is None:
            print("ERROR: get_cached_embedding returned None")
            return False
            
        print(f"✅ Direct embedding successful - received {len(embedding_result)} dimensions")
        print(f"Duration: {duration:.2f} seconds")
        print(f"First 5 dimensions: {embedding_result[:5]}")
        
    except Exception as e:
        print(f"ERROR: Direct embedding failed with exception: {str(e)}")
        return False
    
    # Test direct embedding (without caching)
    try:
        print("\nTesting direct embedding (without caching)...")
        start_time = time.time()
        embedding_result = embedding_provider.get_embedding(model_version, test_text)
        duration = time.time() - start_time
        
        if embedding_result is None:
            print("ERROR: get_embedding returned None")
            return False
            
        print(f"✅ Direct embedding successful - received {len(embedding_result)} dimensions")
        print(f"Duration: {duration:.2f} seconds")
        print(f"First 5 dimensions: {embedding_result[:5]}")
        
    except Exception as e:
        print(f"ERROR: Direct embedding failed with exception: {str(e)}")
        return False
    
    # Test cached embedding
    try:
        print("\nTesting cached embedding...")
        start_time = time.time()
        cached_result = embedding_provider.get_cached_embedding(model_version, test_text)
        duration = time.time() - start_time
        
        if cached_result is None:
            print("ERROR: get_cached_embedding returned None")
            return False
            
        print(f"✅ Cached embedding successful - received {len(cached_result)} dimensions")
        print(f"Duration: {duration:.2f} seconds")
        print(f"First 5 dimensions: {cached_result[:5]}")
        
    except Exception as e:
        print(f"ERROR: Cached embedding failed with exception: {str(e)}")
        return False
    
    # Test the get_embedding_with_retry function from indexer.tasks
    print("\nTesting embedding with retry logic (mimicking the actual code path)...")
    
    def get_embedding_with_retry_mockup(provider, model_version, text):
        """Simplified version of the get_embedding_with_retry function."""
        text_vector = None
        
        # This mimics what happens in the actual code
        text_vector = embedding_provider.get_cached_embedding(model_version, text)
        
        # Here's the fix for the 'NoneType' object is not iterable error:
        # Only proceed if we got a valid embedding
        if text_vector is None:
            raise ValueError("Failed to get embedding - received None")
        
        # This is where the original error occurs:
        # If text_vector is None, this line raises "'NoneType' object is not iterable"
        text_vector_float = [float(f) for f in text_vector]
        return text_vector_float
    
    try:
        start_time = time.time()
        retry_result = get_embedding_with_retry_mockup(EmbeddingProvider.OPENAI, model_version, test_text)
        duration = time.time() - start_time
        
        print(f"✅ Embedding with retry successful - received {len(retry_result)} dimensions")
        print(f"Duration: {duration:.2f} seconds")
        print(f"First 5 dimensions: {retry_result[:5]}")
        
    except Exception as e:
        print(f"ERROR: Embedding with retry failed with exception: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("OpenAI Embedding Test")
    print("---------------------")
    
    if test_openai_embedding():
        print("\n✅ All tests passed successfully")
    else:
        print("\n❌ Tests failed - see errors above") 