from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from .document import QuepasaDocument
from .spd_result import SPDSearchResult
from .web import WebSearchResult

class AnswerType(Enum):
    """Types of search answers"""
    STRICT = "strict"
    WEAK = "weak"
    NO_LINKS = "no-links"
    NONE = "none"

@dataclass
class Reference:
    """Link model for search results"""
    url: str
    text: str
    type: Optional[str] = None
    title: Optional[str] = None
    created_at: Optional[str] = None
    start_position: Optional[str] = None
    end_position: Optional[str] = None
    score: Optional[float] = None

    def to_dict(self):
        return {
            "url": self.url,
            "text": self.text,
            "type": self.type,
            "title": self.title,
            "created_at": self.created_at,
            "start_position": self.start_position,
            "end_position": self.end_position,
            "score": self.score,
        }

@dataclass
class QuepasaAnswer:
    """Quepasa response model"""
    type: Optional[str] = field(default=None)
    text: Optional[str] = field(default=None)
    references: Optional[Dict[str, Reference]] = field(default=None)
    markdown: Optional[str] = field(default=None)
    think: Optional[str] = field(default=None)
    price: Optional[float] = field(default=None)
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QuepasaAnswer':
        """Create QuepasaResponse from dictionary"""
        # Convert links to Link objects if present
        references = None
        if 'references' in data:
            references = {
                k: Reference(**v) if isinstance(v, dict) else v
                for k, v in data['references'].items()
            }

        response_data = {
            'type': data.get('type'),
            'text': data.get('text'),
            'markdown': data.get('markdown'),
            'references': references,
            'think': data.get('think'),
            'price': data.get('price'),
            'data': data.get('data', {}),
            'metadata': data.get('metadata', {})
        }

        return cls(**response_data)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = {}

        # Add optional fields if present
        if self.type:
            data['type'] = self.type
        if self.text:
            data['text'] = self.text
        if self.markdown:
            data['markdown'] = self.markdown
        if self.references:
            data['references'] = {
                k: v.to_dict() if isinstance(v, Reference) else v
                for k, v in self.references.items()
            }
        if self.think:
            data['think'] = self.think
        if self.price:
            data['price'] = self.price
        if self.data:
            data['data'] = self.data
        if self.metadata:
            data['metadata'] = self.metadata

        return data

@dataclass
class QuepasaStreamAnswer(QuepasaAnswer):
    """Quepasa stream response model"""
    loading: bool = field(default=False)
    streaming: bool = field(default=False)
    created_at: int = field(default=int(datetime.now().timestamp()))

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QuepasaStreamAnswer':
        """Create QuepasaStreamAnswer from dictionary"""
        answer = QuepasaAnswer.from_dict(data)
        return cls(
            loading = data.get('loading', False),
            streaming = data.get('streaming', False),
            created_at = data.get('created_at', int(datetime.now().timestamp())),
            **answer.to_dict()
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = super().to_dict()
        data['loading'] = self.loading
        data['streaming'] = self.streaming
        data['created_at'] = self.created_at
        return data

@dataclass
class QuepasaResponse:
    status: str
    data: Optional[Union[QuepasaAnswer, List[Union[QuepasaDocument, WebSearchResult, SPDSearchResult]]]] = field(default=None)
    error: Optional[str] = field(default=None)

    def __post_init__(self):
        if self.status == "error" and not self.error:
            raise ValueError("Error status requires an error message")

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QuepasaResponse':
        """Create QuepasaResponse from dictionary"""
        return cls(
            status = data.get('status'),
            data = QuepasaAnswer.from_dict(data.get('data', {})),
            error = data.get('error')
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = {}
        data['status'] = self.status
        if self.data:
            if isinstance(self.data, (list, tuple)):
                data['data'] = [
                    item.to_dict() if hasattr(item, 'to_dict') else item 
                    for item in self.data
                ]
            else:
                data['data'] = self.data.to_dict() if hasattr(self.data, 'to_dict') else self.data
        if self.error:
            data['error'] = self.error
        return data
