from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from .document import QuepasaDocument
from .spd_result import SPDSearchResult
from .web import WebSearchResult

class AnswerType(Enum):
    """Types of search answers"""
    STRICT = "strict"
    WEAK = "weak"
    NO_LINKS = "no-links"
    NONE = "none"


class ComponentType(Enum):
    """Component types for ChatResponse per OpenAPI spec"""
    PRODUCT_SET = "PRODUCT_SET"
    REFERENCE_ITEM = "REFERENCE_ITEM"


class ActionType(Enum):
    """Action types for cart operations per OpenAPI spec"""
    ADD_TO_CART = "ADD_TO_CART"
    REMOVE_FROM_CART = "REMOVE_FROM_CART"


class ResponseStatus(Enum):
    """Response status for streaming responses per OpenAPI spec"""
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETE = "COMPLETE"
    ERROR = "ERROR"

@dataclass
class Reference:
    """Link model for search results"""
    url: str
    text: str
    type: Optional[str] = None
    title: Optional[str] = None
    created_at: Optional[str] = None
    start_position: Optional[str] = None
    end_position: Optional[str] = None
    score: Optional[float] = None
    sku: Optional[str] = None
    metadata: Optional[str] = None

    def to_dict(self):
        return {
            "url": self.url,
            "text": self.text,
            "type": self.type,
            "title": self.title,
            "created_at": self.created_at,
            "start_position": self.start_position,
            "end_position": self.end_position,
            "score": self.score,
            "sku": self.sku,
            "metadata": self.metadata,
        }

@dataclass
class ProductItem:
    """Product model for structured API responses"""
    id: str
    title: str
    url: str
    collection: str
    allMeta: Dict[str, Any]

    def to_dict(self):
        return {
            "id": self.id,
            "title": self.title,
            "url": self.url,
            "collection": self.collection,
            "allMeta": self.allMeta,
        }

@dataclass
class QuepasaAnswer:
    """Quepasa response model"""
    type: Optional[str] = field(default=None)
    text: Optional[str] = field(default=None)
    references: Optional[Dict[str, Reference]] = field(default=None)
    markdown: Optional[str] = field(default=None)
    products: Optional[List[ProductItem]] = field(default=None)
    think: Optional[str] = field(default=None)
    price: Optional[float] = field(default=None)
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QuepasaAnswer':
        """Create QuepasaResponse from dictionary"""
        # Convert links to Link objects if present
        references = None
        if 'references' in data:
            references = {
                k: Reference(**v) if isinstance(v, dict) else v
                for k, v in data['references'].items()
            }

        # Convert products to ProductItem objects if present
        products = None
        if 'products' in data and data['products'] is not None:
            products = [
                ProductItem(**p) if isinstance(p, dict) else p
                for p in data['products']
            ]

        response_data = {
            'type': data.get('type'),
            'text': data.get('text'),
            'markdown': data.get('markdown'),
            'references': references,
            'products': products,
            'think': data.get('think'),
            'price': data.get('price'),
            'data': data.get('data', {}),
            'metadata': data.get('metadata', {})
        }

        return cls(**response_data)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = {}

        # Add optional fields if present
        if self.type:
            data['type'] = self.type
        if self.text:
            data['text'] = self.text
        if self.markdown:
            data['markdown'] = self.markdown
        if self.references:
            data['references'] = {
                k: v.to_dict() if isinstance(v, Reference) else v
                for k, v in self.references.items()
            }
        if self.products:
            data['products'] = [
                p.to_dict() if isinstance(p, ProductItem) else p
                for p in self.products
            ]
        if self.think:
            data['think'] = self.think
        if self.price:
            data['price'] = self.price
        if self.data:
            data['data'] = self.data
        if self.metadata:
            data['metadata'] = self.metadata

        return data

@dataclass
class QuepasaStreamAnswer(QuepasaAnswer):
    """Quepasa stream response model"""
    loading: bool = field(default=False)
    streaming: bool = field(default=False)
    created_at: int = field(default=int(datetime.now().timestamp()))

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QuepasaStreamAnswer':
        """Create QuepasaStreamAnswer from dictionary"""
        answer = QuepasaAnswer.from_dict(data)
        return cls(
            loading = data.get('loading', False),
            streaming = data.get('streaming', False),
            created_at = data.get('created_at', int(datetime.now().timestamp())),
            **answer.to_dict()
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = super().to_dict()
        data['loading'] = self.loading
        data['streaming'] = self.streaming
        data['created_at'] = self.created_at
        return data

@dataclass
class QuepasaResponse:
    status: str
    data: Optional[Union[QuepasaAnswer, List[Union[QuepasaDocument, WebSearchResult, SPDSearchResult]]]] = field(default=None)
    error: Optional[str] = field(default=None)

    def __post_init__(self):
        if self.status == "error" and not self.error:
            raise ValueError("Error status requires an error message")

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QuepasaResponse':
        """Create QuepasaResponse from dictionary"""
        return cls(
            status = data.get('status'),
            data = QuepasaAnswer.from_dict(data.get('data', {})),
            error = data.get('error')
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = {}
        data['status'] = self.status
        if self.data:
            if isinstance(self.data, (list, tuple)):
                data['data'] = [
                    item.to_dict() if hasattr(item, 'to_dict') else item 
                    for item in self.data
                ]
            else:
                data['data'] = self.data.to_dict() if hasattr(self.data, 'to_dict') else self.data
        if self.error:
            data['error'] = self.error
        return data


# ChatRequest and ChatResponse models for /conversation endpoint

@dataclass
class UserPreferences:
    """User preferences from request context"""
    name: Optional[str] = None

@dataclass
class Context:
    """Request context information"""
    userInfo: Optional[UserPreferences] = None

@dataclass
class Attachment:
    """File attachment in request"""
    type: str = ""
    url: str = ""

@dataclass
class Options:
    """Request options"""
    includeHistory: Optional[bool] = None
    debug: Optional[bool] = None

@dataclass
class ChatRequest:
    """Chat request model matching OpenAPI schema"""
    message: str = ""
    sessionId: str = ""
    collection: str = ""
    area: str = ""
    visitorId: Optional[str] = None
    attachments: Optional[List[Attachment]] = None
    context: Optional[Context] = None
    options: Optional[Options] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatRequest':
        """Create ChatRequest from dictionary"""
        attachments = None
        if data.get('attachments'):
            attachments = [Attachment(**a) for a in data['attachments']]
        
        context = None
        if data.get('context'):
            context_data = data['context']
            user_info = None
            if context_data.get('userInfo'):
                user_info = UserPreferences(**context_data['userInfo'])
            context = Context(userInfo=user_info)
        
        options = None
        if data.get('options'):
            options = Options(**data['options'])
        
        return cls(
            message=data['message'],
            sessionId=data['sessionId'],
            collection=data['collection'],
            area=data['area'],
            visitorId=data.get('visitorId'),
            attachments=attachments,
            context=context,
            options=options
        )

@dataclass
class ComponentSchema:
    """Base component schema"""
    componentType: ComponentType

@dataclass
class ProductSet(ComponentSchema):
    """Product set component"""
    componentType: ComponentType = ComponentType.PRODUCT_SET
    products: List[ProductItem] = field(default_factory=list)
    comment: Optional[str] = None

@dataclass
class ReferenceItem(ComponentSchema):
    """Reference item component"""
    componentType: ComponentType = ComponentType.REFERENCE_ITEM
    url: str = ""
    text: str = ""
    title: Optional[str] = None

@dataclass
class ActionItem:
    """Action item for cart operations"""
    type: ActionType = ActionType.ADD_TO_CART
    payload: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ChatResponse:
    """Chat response model matching OpenAPI schema"""
    sessionId: str = ""
    responseId: str = ""
    timestamp: str = ""
    content: str = ""
    stream: bool = False
    visitorId: Optional[str] = None
    status: Optional[ResponseStatus] = None
    components: Optional[List[Union[ProductSet, ReferenceItem]]] = None
    actions: Optional[List[ActionItem]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = {
            'sessionId': self.sessionId,
            'responseId': self.responseId,
            'timestamp': self.timestamp,
            'content': self.content,
            'stream': self.stream
        }
        
        # Always include visitorId (required field per OpenAPI spec)
        data['visitorId'] = self.visitorId
        if self.status:
            data['status'] = self.status.value
            
        # Always include components array (empty if None)
        data['components'] = []
        if self.components:
            for comp in self.components:
                if isinstance(comp, ProductSet):
                    comp_dict = {
                        'componentType': comp.componentType.value,
                        'products': [p.to_dict() for p in comp.products]
                    }
                    if comp.comment:
                        comp_dict['comment'] = comp.comment
                    data['components'].append(comp_dict)
                elif isinstance(comp, ReferenceItem):
                    comp_dict = {
                        'componentType': comp.componentType.value,
                        'url': comp.url,
                        'text': comp.text
                    }
                    if comp.title:
                        comp_dict['title'] = comp.title
                    data['components'].append(comp_dict)
        
        # Always include actions array (empty if None)
        data['actions'] = []
        if self.actions:
            data['actions'] = [{'type': a.type.value, 'payload': a.payload} for a in self.actions]
        
        return data
