# Shoeby Base Configuration
client_code: "shoeby_base"

# Query Expansion Rules
query_expansions:
  - keywords: "trainers"
    query: 'sneakers'
  - keywords: "azure"  
    query: 'light blue cyan'

# Language Configuration  
language:
  fallback_language: 'en'
  language_mapping:
    en: "English"
    nl: "Dutch"
  indexed_languages:
    document:
      - "en"
      - "nl"

# Search Configuration
search:
  max_results_limit: 25

# User History Configuration
user_history:
  forget_after_seconds: 28800  # 8 hours
  max_last_messages: 64
  use_roles:
    - "user"
    - "assistant" 
    - "tool"

# LLM Model Configuration
llm_models:
  agentic_source:
    - "openai"
    - "gpt-4o"
  document_source:
    - "nebius"
    - "Qwen/Qwen3-32B-fast"

# Store Information - configurable part
store_info:
  name: "Shoeby"
  offerings:
    - "clothing, footwear, and accessories for women and children (girls and boys)"
    - "footwear is available only for women and girls" 
    - "a smaller selection of clothing and accessories for men"

