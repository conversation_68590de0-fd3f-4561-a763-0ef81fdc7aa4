# Test Design: Story 1.1 - Foundational Endpoint and Authorization

**Date:** 2025-01-11  
**Designer:*<PERSON> <PERSON> (Test Architect)  
**Story:** 1.1 - Foundational Endpoint and Authorization  

## Test Strategy Overview

- **Total test scenarios:** 12
- **Unit tests:** 6 (50%)
- **Integration tests:** 4 (33%)
- **E2E tests:** 2 (17%)
- **Priority distribution:** P0: 8, P1: 3, P2: 1

## Core Testing Philosophy - Anti-Superficial Approach

**What we're NOT testing (avoiding superficial coverage):**
- Framework behavior (Flask routing, HTTP status codes)
- Standard library functions (UUID generation, datetime parsing)
- Well-tested third-party components (BearerAuth internal logic)
- Configuration parsing (already covered by existing tests)

**What we ARE testing (essential business-critical functionality):**
- Authentication security boundaries
- Endpoint routing logic
- Request/response contract adherence
- Backward compatibility preservation
- Error handling for security scenarios

## Test Scenarios by Acceptance Criteria

### AC1: POST /conversation route exists
**Risk Level:** Low - Routing is framework-level concern  
**Test Strategy:** Integration-level verification only

| ID | Level | Priority | Test | Justification |
|----|-------|----------|------|---------------|
| 1.1-INT-001 | Integration | P1 | Route exists and is accessible | Validates endpoint registration without testing framework behavior |

### AC2: Dual-header authentication (X-Customer-Id + client-key)
**Risk Level:** High - Security-critical functionality  
**Test Strategy:** Comprehensive coverage at multiple levels

| ID | Level | Priority | Test | Justification |
|----|-------|----------|------|---------------|
| 1.1-UNIT-001 | Unit | P0 | Endpoint detection logic for auth selection | Tests core routing logic between AuthManager/BearerAuth |
| 1.1-UNIT-002 | Unit | P0 | BearerAuth integration with conversation endpoint | Validates correct auth component selection |
| 1.1-INT-002 | Integration | P0 | Valid X-Customer-Id + client-key → 200 success | End-to-end auth flow validation |
| 1.1-INT-003 | Integration | P0 | Missing X-Customer-Id → 401 with ErrorResponse | Security boundary validation |
| 1.1-INT-004 | Integration | P0 | Missing Authorization header → 401 with ErrorResponse | Security boundary validation |

### AC3: BearerAuth class reuse
**Risk Level:** Medium - Integration concern, not functionality  
**Test Strategy:** Integration-level verification

| ID | Level | Priority | Test | Justification |
|----|-------|----------|------|---------------|
| 1.1-UNIT-003 | Unit | P1 | BearerAuth instantiation in BaseAPIHandler | Validates correct component wiring |

### AC4: 401 errors follow ErrorResponse schema
**Risk Level:** High - API contract compliance  
**Test Strategy:** Integration testing for response structure

| ID | Level | Priority | Test | Justification |
|----|-------|----------|------|---------------|
| 1.1-INT-005 | Integration | P0 | 401 response contains required ErrorResponse fields | API contract validation |

### AC5: Minimal valid ChatResponse for authorized requests
**Risk Level:** Medium - Response structure validation  
**Test Strategy:** Integration testing for contract compliance

| ID | Level | Priority | Test | Justification |
|----|-------|----------|------|---------------|
| 1.1-UNIT-004 | Unit | P1 | ChatResponse structure generation | Validates response building logic |
| 1.1-INT-006 | Integration | P1 | Authorized request returns valid ChatResponse | End-to-end response validation |

### AC6: Existing endpoints remain functional
**Risk Level:** High - Regression prevention  
**Test Strategy:** Integration testing with existing auth patterns

| ID | Level | Priority | Test | Justification |
|----|-------|----------|------|---------------|
| 1.1-UNIT-005 | Unit | P0 | Non-conversation endpoints use AuthManager | Validates auth routing doesn't break existing paths |
| 1.1-E2E-001 | E2E | P0 | /retrieve/answer works with Bearer token | Critical regression test for existing functionality |

### AC7: RequestId generation and inclusion
**Risk Level:** Medium - Observability and debugging  
**Test Strategy:** Unit and integration testing

| ID | Level | Priority | Test | Justification |
|----|-------|----------|------|---------------|
| 1.1-UNIT-006 | Unit | P1 | RequestId generation and storage | Validates ID generation logic |
| 1.1-E2E-002 | E2E | P2 | RequestId present in both success and error responses | End-to-end observability validation |

## Detailed Test Scenarios

### Unit Tests (6 scenarios)

#### 1.1-UNIT-001: Endpoint detection logic for auth selection
```python
def test_authenticate_request_routes_conversation_to_bearerauth():
    """Test that conversation endpoint uses BearerAuth while others use AuthManager"""
    handler = BaseAPIHandler(config)
    
    # Test conversation endpoint routing
    with patch.object(handler, 'bearer_auth') as mock_bearer:
        handler._authenticate_request(headers, endpoint='conversation')
        mock_bearer.authenticate_request.assert_called_once()
    
    # Test non-conversation endpoint routing  
    with patch.object(handler, 'auth_manager') as mock_auth:
        handler._authenticate_request(headers, endpoint='answer')
        mock_auth.authenticate_request.assert_called_once()
```

#### 1.1-UNIT-002: BearerAuth integration with conversation endpoint

```python
def test_conversation_endpoint_uses_bearerauth():
    """Validate BearerAuth is correctly instantiated and used for conversation endpoint"""
    handler = BaseAPIHandler(config)

    assert hasattr(handler, 'bearer_auth')
    assert isinstance(handler.request_auth_manager, BearerAuth)
```

#### 1.1-UNIT-003: BearerAuth instantiation in BaseAPIHandler
```python
def test_base_handler_imports_bearerauth():
    """Verify BearerAuth is properly imported and instantiated"""
    handler = BaseAPIHandler(config)
    
    # Verify both auth components exist
    assert hasattr(handler, 'auth_manager')  # Existing
    assert hasattr(handler, 'bearer_auth')    # New
```

#### 1.1-UNIT-004: ChatResponse structure generation
```python
def test_minimal_chatresponse_structure():
    """Validate minimal ChatResponse contains all required fields"""
    handler = HTTPHandler(config)
    
    response = handler._build_minimal_chatresponse(request_data, request_id)
    
    # Validate required fields
    assert 'sessionId' in response
    assert 'responseId' in response  
    assert 'timestamp' in response
    assert 'content' in response
    assert 'stream' in response
```

#### 1.1-UNIT-005: Non-conversation endpoints use AuthManager
```python
def test_existing_endpoints_use_authmanager():
    """Ensure existing endpoints continue using AuthManager"""
    handler = BaseAPIHandler(config)
    
    with patch.object(handler, 'auth_manager') as mock_auth:
        # Test existing endpoints
        for endpoint in ['answer', 'search', 'history', 'stream']:
            handler._authenticate_request(headers, endpoint=endpoint)
            mock_auth.authenticate_request.assert_called()
```

#### 1.1-UNIT-006: RequestId generation and storage
```python
def test_request_id_generation():
    """Validate requestId is generated and stored correctly"""
    handler = BaseAPIHandler(config)
    
    request_id = handler._generate_request_id()
    
    # Validate UUID format
    UUID(request_id)  # Raises ValueError if invalid
    
    # Validate storage in config
    assert hasattr(handler.config.request, 'request_id')
```

### Integration Tests (4 scenarios)

#### 1.1-INT-001: Route exists and is accessible
```python
def test_conversation_endpoint_accessible():
    """Verify /conversation endpoint is properly registered"""
    response = client.post('/conversation', 
                          headers=valid_headers,
                          json=valid_request)
    
    # Should not get 404 (route exists)
    assert response.status_code != 404
```

#### 1.1-INT-002: Valid authentication → 200 success
```python
def test_valid_auth_headers_succeed():
    """Test successful authentication with X-Customer-Id and client-key"""
    headers = {
        'Authorization': 'client-key test-key-123',
        'X-Customer-Id': 'test-client',
        'Content-Type': 'application/json'
    }
    
    response = client.post('/conversation', 
                          headers=headers,
                          json=minimal_chat_request)
    
    assert response.status_code == 200
    assert 'sessionId' in response.json()
```

#### 1.1-INT-003: Missing X-Customer-Id → 401 error
```python
def test_missing_customer_id_returns_401():
    """Test authentication failure without X-Customer-Id header"""
    headers = {
        'Authorization': 'client-key test-key-123',
        'Content-Type': 'application/json'
    }
    
    response = client.post('/conversation', 
                          headers=headers,
                          json=minimal_chat_request)
    
    assert response.status_code == 401
    error_data = response.json()
    assert 'error' in error_data
    assert 'requestId' in error_data
```

#### 1.1-INT-004: Missing Authorization → 401 error  
```python
def test_missing_authorization_returns_401():
    """Test authentication failure without Authorization header"""
    headers = {
        'X-Customer-Id': 'test-client',
        'Content-Type': 'application/json'
    }
    
    response = client.post('/conversation', 
                          headers=headers,
                          json=minimal_chat_request)
    
    assert response.status_code == 401
```

### E2E Tests (2 scenarios)

#### 1.1-E2E-001: Existing endpoint compatibility
```python
def test_retrieve_answer_still_works():
    """Critical regression test - existing Bearer auth must work"""
    headers = {
        'Authorization': 'Bearer client-id:token-value'
    }
    
    response = client.post('/retrieve/answer',
                          headers=headers, 
                          json=legacy_request)
    
    # Should work exactly as before
    assert response.status_code in [200, 401]  # 401 = auth failed, but route works
    if response.status_code == 401:
        # Ensure it's auth failure, not routing failure
        assert 'authorization' in response.json().get('error', '').lower()
```

#### 1.1-E2E-002: RequestId in responses
```python  
def test_request_id_in_responses():
    """Validate requestId appears in both success and error responses"""
    # Test success response
    response = client.post('/conversation', 
                          headers=valid_headers,
                          json=valid_request)
    if response.status_code == 200:
        assert 'responseId' in response.json()
    
    # Test error response  
    response = client.post('/conversation',
                          headers=invalid_headers,
                          json=valid_request)
    if response.status_code == 401:
        assert 'requestId' in response.json()
```

## Risk Coverage Analysis

**Primary Risks Mitigated:**
- **RISK-AUTH-001:** Authentication bypass for new endpoint → Covered by 1.1-INT-002,003,004
- **RISK-REGR-001:** Breaking existing authentication → Covered by 1.1-E2E-001, 1.1-UNIT-005  
- **RISK-CONT-001:** API contract violations → Covered by 1.1-INT-005,006
- **RISK-OBSV-001:** Loss of request traceability → Covered by 1.1-E2E-002

**Acceptable Gaps:**
- Third-party BearerAuth internal logic (not our responsibility)
- Flask framework routing behavior (well-tested framework functionality)
- Configuration parsing edge cases (covered by existing config tests)

## Recommended Execution Order

1. **P0 Unit tests** (1.1-UNIT-001, 002, 005) - Fast feedback on core logic
2. **P0 Integration tests** (1.1-INT-002, 003, 004, 005) - Security boundary validation  
3. **P0 E2E tests** (1.1-E2E-001) - Critical regression prevention
4. **P1 tests** (1.1-UNIT-003, 004, 006, 1.1-INT-001, 006) - Feature completeness
5. **P2 tests** (1.1-E2E-002) - Observability validation

## Anti-Superficial Testing Validation

✅ **Avoided superficial tests:**
- No tests for HTTP status code constants
- No tests for JSON serialization (framework concern)  
- No tests for standard UUID generation
- No tests for datetime formatting

✅ **Focused on business-critical functionality:**
- Authentication security boundaries
- Backward compatibility preservation
- API contract compliance
- Request routing logic

✅ **Risk-driven test selection:**
- Security tests are P0 priority
- Regression tests for existing functionality
- Minimal coverage of low-risk areas
