import pytest
from configuration.base.localization import LocalizationConfig

class TestLocalizationConfig(LocalizationConfig):
    def __init__(self, client_code: str):
        self.client_code = client_code

@pytest.fixture
def config():
    return TestLocalizationConfig("test_client")

def test_localization_config_initialization(config):
    assert isinstance(config, LocalizationConfig)

def test_fallback_language(config):
    fallback = config.get_fallback_language()
    assert fallback == 'en'
    assert isinstance(fallback, str)

def test_language_mapping(config):
    mapping = config.get_language_mapping()
    
    # Test basic structure
    assert isinstance(mapping, dict)
    assert len(mapping) > 0
    
    # Test some key mappings
    assert mapping['en'] == "English"
    assert mapping['ru'] == "Russian"
    assert mapping['he'] == "Hebrew"
    
    # Test all values are strings
    assert all(isinstance(k, str) and isinstance(v, str) for k, v in mapping.items())

def test_language_mapping_consistency(config):
    mapping = config.get_language_mapping()
    
    # Test Hebrew aliases
    assert mapping['he'] == mapping['iw']
    
    # Test no duplicate values (except Hebrew which is aliased)
    values = [v for k, v in mapping.items() if k != 'iw']
    assert len(values) == len(set(values)) 