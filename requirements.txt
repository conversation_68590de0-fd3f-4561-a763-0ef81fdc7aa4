# Core dependencies
boto3>=1.34.0
celery>=5.3.4
redis>=5.0.1
minio>=7.2.0
docker>=7.0.0

# Web scraping
requests>=2.31.0
html2text>=2024.2.26
beautifulsoup4>=4.12.2
readability-lxml>=0.8.1
lxml[html_clean]>=5.3.0

# Text processing
tabulate>=0.9.0
whoosh>=2.7.4
levenshtein==0.25.0

# LLM and AI
openai>=1.12.0
mistralai>=1.2.0
anthropic>=0.42.0
replicate>=1.0.4
tiktoken>=0.7.0
ollama>=0.4.5
together>=0.2.8
google-generativeai==0.8.5

# Dependencies with specific version constraints
pydantic>=2.7.4,<3.0.0
pydantic-settings>=2.1.0
python-multipart>=0.0.9
python-jose[cryptography]==3.3.0
python-dotenv>=1.0.0
python-json-logger>=2.0.7 
passlib[bcrypt]==1.7.4
uvicorn==0.27.1

# Infrastructure
kubernetes>=26.1.0
python-telegram-bot>=20.7