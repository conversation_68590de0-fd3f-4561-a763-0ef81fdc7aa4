name: Retag latest dev→demo for all services

on:
  workflow_dispatch:

concurrency:
  group: retag-demo-${{ github.event.repository.name }}
  cancel-in-progress: true

permissions:
  id-token: write
  contents: read

jobs:
  retag:
    runs-on: ubuntu-latest
    timeout-minutes: 25

    strategy:
      fail-fast: false
      matrix:
        image:
          - data-processor
          - elasticsearch-init
          - embedding
          - indexer
          - searcher
          - api
          - crawler

    env:
      REGISTRY: europe-west2-docker.pkg.dev
      PROJECT: rezolve-sas-infra
      REPO: quepasa

    steps:
      - name: Authenticate to Google (Workload Identity)
        id: auth-docker
        uses: google-github-actions/auth@v2
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER_SVC }}
          access_token_lifetime: 900s

      - name: Setup gcloud (project)
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT }}

      - name: Login to <PERSON><PERSON> (Artifact Registry)
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: oauth2accesstoken
          password: ${{ steps.auth-docker.outputs.access_token }}

      - name: Resolve latest dev tag for ${{ matrix.image }}
        id: resolve
        shell: bash
        env:
          IMG_PATH: ${{ env.REGISTRY }}/${{ env.PROJECT }}/${{ env.REPO }}/${{ matrix.image }}
          GCLOUD_PROJECT: ${{ env.PROJECT }}
        run: |
          set -euo pipefail

          # List dev-* tags
          TAGS=$(gcloud artifacts docker tags list "${IMG_PATH}" \
            --project="${GCLOUD_PROJECT}" \
            --format='value(TAG)' \
            --limit=unlimited || true)

          if [[ -z "${TAGS}" ]]; then
            echo "No tags found for ${IMG_PATH}" >&2
            exit 1
          fi

          # Filter only tags that match dev-<hash>-<epoch>
          DEV_TAGS=$(printf '%s\n' "${TAGS}" | grep -E '^dev-[^-]+-[0-9]{10,}$' || true)

          if [[ -z "${DEV_TAGS}" ]]; then
            echo "No dev-* tags (format dev-<hash>-<epoch>) found for ${IMG_PATH}" >&2
            echo "All tags:" >&2
            printf '%s\n' "${TAGS}" >&2
            exit 1
          fi

          # Pick tag where suffix matches -<epoch> at end; choose the largest epoch
          LATEST_DEV_TAG=$(printf '%s\n' "${DEV_TAGS}" | \
            awk '{
              if (match($0, /-([0-9]{10,})$/)) {
                epoch = substr($0, RSTART + 1, RLENGTH - 1);
                print epoch "\t" $0
              }
            }' | \
            sort -k1,1nr | \
            head -n1 | \
            cut -f2-)

          if [[ -z "${LATEST_DEV_TAG}" ]]; then
            echo "No dev-* tags with trailing epoch found for ${IMG_PATH}" >&2
            echo "All tags:" >&2
            printf '%s\n' "${TAGS}" >&2
            exit 1
          fi

          DEST_TAG="${LATEST_DEV_TAG/dev-/demo-}"

          echo "src_tag=${LATEST_DEV_TAG}" >> "$GITHUB_OUTPUT"
          echo "dst_tag=${DEST_TAG}"       >> "$GITHUB_OUTPUT"
          echo "image_path=${IMG_PATH}"    >> "$GITHUB_OUTPUT"
          echo "Resolved: ${IMG_PATH}:${LATEST_DEV_TAG} → ${IMG_PATH}:${DEST_TAG}"

      - name: Pull, retag, and push ${{ matrix.image }}
        run: |
          SRC_IMAGE="${{ steps.resolve.outputs.image_path }}:${{ steps.resolve.outputs.src_tag }}"
          DST_IMAGE="${{ steps.resolve.outputs.image_path }}:${{ steps.resolve.outputs.dst_tag }}"
          echo "Retagging: $SRC_IMAGE → $DST_IMAGE"
          docker pull "$SRC_IMAGE"
          docker tag  "$SRC_IMAGE" "$DST_IMAGE"
          docker push "$DST_IMAGE"

      - name: Summary
        if: always()
        run: |
          echo "- ${{ matrix.image }}: dev \`${{ steps.resolve.outputs.src_tag }}\` → demo \`${{ steps.resolve.outputs.dst_tag }}\`" >> "$GITHUB_STEP_SUMMARY"
