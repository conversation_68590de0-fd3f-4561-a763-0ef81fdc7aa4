import pytest
from configuration.base.base_config import BaseConfig
from quepasa.searcher.models.request import QuepasaRequest

@pytest.fixture
def base_config():
    return BaseConfig("test_client")

@pytest.fixture
def base_request():
    return QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram"
    )

def test_base_config_init(base_config):
    """Test BaseConfig initialization"""
    assert base_config.client_code == "test_client"

def test_set_request(base_config, base_request):
    """Test setting search request"""
    base_config.set_request(base_request)
    assert base_config.request == base_request

def test_process_response(base_config, base_request):
    """Test response processing"""
    base_config.set_request(base_request)
    response = {"text": "test response"}
    assert base_config.process_response(response) == response 