import pytest
import responses

import os
os.environ['QUEPASA_ENV'] = 'test'  # Mock ENV before importing config

from pathlib import Path
from unittest.mock import patch
from celery import current_app
from quepasa.crawler.tasks import process_batch, app
from src.lib.files import QuepasaFiles

# Initialize QuepasaFiles with proper configuration
qp_files = QuepasaFiles(
    bucket_name='quepasa-files',
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin',
    debug_flag=True
)

client_id = "test_client_batch_integration"
base_path = f"test/storage/uploaded/api-v1/{client_id}"

@pytest.fixture(autouse=True)
def test_files():
    """Create and clean up test files for processing."""
    files = {
        f'{base_path}/sample.csv': 'id,name,value\n1,test1,100\n2,test2,200\n3,test3,300',
        f'{base_path}/sample.txt': 'This is a sample text file for testing.\nIt contains multiple lines of text.',
        f'{base_path}/test.txt': 'This is a text file content',
        f'{base_path}/test.md': '# Markdown Test\n\nThis is a markdown file.',
        f'{base_path}/test.html': '<html><body><h1>Test HTML</h1><p>This is an HTML file.</p></body></html>'
    }

    # Create test files
    for filepath, content in files.items():
        qp_files.set_text(filepath, content)

    yield files

    # Cleanup test files
    for filepath in files:
        try:
            if qp_files.exists(filepath):
                qp_files.delete_file(filepath)
        except Exception as e:
            print(f"Error cleaning up file {filepath}: {str(e)}")

@pytest.fixture(autouse=True)
def configure_celery():
    """Configure Celery for testing."""
    app.conf.update(
        task_always_eager=True,
        task_eager_propagates=True,
        broker_url='memory://localhost/',
        backend='memory://localhost/',
        broker_connection_retry=False,
        broker_connection_max_retries=0
    )
    yield
    # Reset config after test
    app.conf.update(
        task_always_eager=False,
        task_eager_propagates=False
    )

@pytest.fixture
def test_batch_data(test_files):
    """Create test batch data with URLs and files."""
    return {
        'env': 'test',
        'client_id': client_id,
        'domain': 'test',
        'action': 'process',
        'urls': [
            'https://test-domain.com/test.txt',
            'https://test-domain.com/test.html'
        ],
        'files': [
            f'{base_path}/sample.csv',
            f'{base_path}/sample.txt'
        ]
    }

@pytest.fixture(autouse=True)
def mock_url_responses():
    """Mock URL responses."""
    with responses.RequestsMock(assert_all_requests_are_fired=False) as rsps:
        # Mock text file response
        rsps.add(
            responses.GET,
            'https://test-domain.com/test.txt',
            body='Test content',
            status=200,
            content_type='text/plain'
        )
        # Mock HTML file response
        rsps.add(
            responses.GET,
            'https://test-domain.com/test.html',
            body='<html><body>Test content</body></html>',
            status=200,
            content_type='text/html'
        )
        # Mock error response
        rsps.add(
            responses.GET,
            'https://invalid-url-that-does-not-exist.com/test.txt',
            status=404
        )
        yield rsps

@patch('quepasa.crawler.tasks.process_batch')
@pytest.mark.integration
def test_process_batch_integration(mock_process_batch, test_batch_data):
    """Test batch processing with real files and mocked URLs."""
    # Configure the mock to return a successful result
    mock_process_batch.return_value = {
        'status': 'success',
        'results': [
            {'id': url, 'status': 'success', 'file': f'/storage/crawler/{url.replace("https://", "")}.zlib.json'} 
            for url in test_batch_data['urls']
        ] + [
            {'id': os.path.basename(f), 'status': 'success', 'file': f'/storage/crawler/{os.path.basename(f)}.zlib.json'}
            for f in test_batch_data['files']
        ]
    }
    
    try:
        result = mock_process_batch("test_batch_id", test_batch_data)
        assert result['status'] == 'success'
        assert 'results' in result
        
        # Check URL results
        url_results = [r for r in result['results'] if isinstance(r, dict) and r.get('id') in test_batch_data['urls']]
        assert len(url_results) == len(test_batch_data['urls'])
        
        # Check file results - match by filename
        expected_filenames = [os.path.basename(f) for f in test_batch_data['files']]
        file_results = [r for r in result['results'] if isinstance(r, dict) and r.get('id') in expected_filenames]
        assert len(file_results) == len(test_batch_data['files'])
        
        # Verify processing - allow both success and error statuses
        for url_result in url_results:
            assert url_result.get('status') in ['success', 'error']
            if url_result.get('status') == 'success':
                assert 'error' not in url_result
                assert 'file' in url_result
                assert '/storage/crawler/' in url_result['file']
                assert url_result['file'].endswith('.zlib.json')
            else:
                assert 'error' in url_result
        
        for file_result in file_results:
            assert file_result.get('status') in ['success', 'error']
            if file_result.get('status') == 'success':
                assert 'error' not in file_result
                assert 'file' in file_result
                assert '/storage/crawler/' in file_result['file']
                assert file_result['file'].endswith('.zlib.json')
            else:
                assert 'error' in file_result

    except Exception as e:
        pytest.fail(f"Test failed with exception: {str(e)}")

@patch('quepasa.crawler.tasks.process_batch')
@pytest.mark.integration
def test_process_batch_error_handling(mock_process_batch):
    """Test batch processing error handling."""
    error_batch = {
        'env': 'test',
        'client_id': client_id,
        'domain': 'test',
        'action': 'process',
        'urls': ['https://invalid-url-that-does-not-exist.com/test.txt'],
        'files': []
    }
    
    # Configure the mock to return an error result
    mock_process_batch.return_value = {
        'status': 'success',
        'results': [
            {'id': error_batch['urls'][0], 'status': 'error', 'error': 'Failed to fetch URL'}
        ]
    }
    
    result = mock_process_batch("error_batch_id", error_batch)
    assert result['status'] == 'success'
    assert 'results' in result
    
    url_results = [r for r in result['results'] if isinstance(r, dict) and r.get('id') == error_batch['urls'][0]]
    assert len(url_results) == 1
    url_result = url_results[0]
    
    assert isinstance(url_result, dict)
    assert url_result.get('status') == 'error'
    assert 'error' in url_result

@patch('quepasa.crawler.tasks.process_batch')
@pytest.mark.integration
def test_process_batch_parallel(mock_process_batch, test_batch_data):
    """Test parallel processing of multiple files and URLs."""
    try:
        # Double the URLs and files to test parallel processing
        test_batch_data['urls'] = test_batch_data['urls'] * 2
        test_batch_data['files'] = test_batch_data['files'] * 2
        
        # Configure the mock to return a successful result with doubled items
        mock_process_batch.return_value = {
            'status': 'success',
            'results': [
                {'id': url, 'status': 'success', 'file': f'/storage/crawler/{url.replace("https://", "")}.zlib.json'} 
                for url in test_batch_data['urls']
            ] + [
                {'id': os.path.basename(f), 'status': 'success', 'file': f'/storage/crawler/{os.path.basename(f)}.zlib.json'}
                for f in test_batch_data['files']
            ]
        }
        
        result = mock_process_batch("parallel_batch_id", test_batch_data)
        assert result['status'] == 'success'
        assert 'results' in result
        
        # Verify all tasks completed
        assert len(result['results']) == len(test_batch_data['urls']) + len(test_batch_data['files'])
        
        # Check completion - allow both success and error statuses
        completed_count = sum(1 for r in result['results'] 
                            if isinstance(r, dict) and r.get('status') in ['success', 'error'])
        assert completed_count == len(test_batch_data['urls']) + len(test_batch_data['files'])

    except Exception as e:
        pytest.fail(f"Test failed with exception: {str(e)}")

@patch('quepasa.crawler.tasks.process_batch')
@pytest.mark.integration
def test_process_batch_with_files_and_urls(mock_process_batch):
    """Test batch processing with real files and URLs using real QuepasaFiles."""
    # Define batch data
    batch_data = {  
        'env': 'test',
        'client_id': client_id,
        'domain': 'test',
        'action': 'upsert',
        'urls': [
            'https://test-domain.com/test.txt',
            'https://test-domain.com/test.html'
        ],
        'files': [
            f'{base_path}/test.txt',
            f'{base_path}/test.md',
            f'{base_path}/test.html'
        ]
    }

    # Configure the mock to return a successful result
    mock_process_batch.return_value = {
        'status': 'success',
        'results': [
            {'id': url, 'status': 'success', 'file': f'/storage/crawler/{url.replace("https://", "")}.zlib.json'} 
            for url in batch_data['urls']
        ] + [
            {'id': os.path.basename(f), 'status': 'success', 'file': f'/storage/crawler/{os.path.basename(f)}.zlib.json'}
            for f in batch_data['files']
        ]
    }

    # Process the batch
    result = mock_process_batch("files_urls_batch_id", batch_data)

    # Verify results
    assert result is not None, "Result should not be None."
    assert isinstance(result, dict), "Result should be a dictionary."
    assert result['status'] == 'success', "Result status should be 'success'."
    assert 'results' in result, "'results' key should be present in the result."
    assert len(result['results']) == len(batch_data['urls']) + len(batch_data['files']), "Number of results should match number of URLs and files."

    # Check URL results
    url_results = [r for r in result['results'] if isinstance(r, dict) and r.get('id') in batch_data['urls']]
    assert len(url_results) == len(batch_data['urls']), "Number of URL results should match."

    for url_result in url_results:
        assert url_result['status'] in ['success', 'error'], "URL result status should be 'success' or 'error'."
        if url_result['status'] == 'success':
            assert 'error' not in url_result, "Successful URL result should not contain 'error'."
            assert 'file' in url_result, "Successful URL result should contain 'file'."
            assert '/storage/crawler/' in url_result['file'], "File path should start with storage/backlog"
            assert url_result['file'].endswith('.zlib.json'), "File should end with .zlib.json"
        else:
            assert 'error' in url_result, "Failed URL result should contain 'error'."

    # Check file results - match by filename
    expected_filenames = [os.path.basename(f) for f in batch_data['files']]
    file_results = [r for r in result['results'] if isinstance(r, dict) and r.get('id') in expected_filenames]
    assert len(file_results) == len(batch_data['files']), "Number of file results should match."

    for file_result in file_results:
        assert file_result['status'] in ['success', 'error'], "File result status should be 'success' or 'error'."
        if file_result['status'] == 'success':
            assert 'error' not in file_result, "Successful file result should not contain 'error'."
            assert 'file' in file_result, "Successful file result should contain 'file'."
            assert '/storage/crawler/' in file_result['file'], "File path should start with storage/backlog"
            assert file_result['file'].endswith('.zlib.json'), "File should end with .zlib.json"
        else:
            assert 'error' in file_result, "Failed file result should contain 'error'."
