import os
import json
import pytest
import shutil
import hashlib
from typing import Dict, Any

from src.lib.utils import get_filename_id, get_basepath
from src.lib.batch_utils import (
    BatchUtils,
    BatchState,
    BatchAction,
    BatchStorage,
    STORAGE_DIR,
    BATCH_STORAGE_DIR,
    DATA_PROCESSOR_STORAGE,
    get_basepath
)
from src.lib.files import QuepasaFiles
from quepasa.indexer.orchestrator import IndexerOrchestrator

# Test configuration
TEST_CLIENT_ID = 'test_client_data_processor'
TEST_DOMAIN = 'test_domain'

qp_files = QuepasaFiles(
    bucket_name='quepasa-files',
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin',
    debug_flag=False
)

DOMAIN_STR = hashlib.md5(str(TEST_DOMAIN).encode('utf-8')).hexdigest()

doc_id = "test-doc-001"
file_id = get_basepath(get_filename_id(doc_id))
doc_file_path = f"{DATA_PROCESSOR_STORAGE}/{TEST_CLIENT_ID}/{DOMAIN_STR}/{file_id}.zlib.json"
qp_files.set_json_zlib(doc_file_path, {
    "id": doc_id,
        "client_id": TEST_CLIENT_ID,
        "domain": TEST_DOMAIN,
        "url": "https://test.com/doc1",
        "title": "Test Document",
        "languages": ["en"],
        "created_at": "2023-12-30T12:00:00Z",
        "updated_at": "2023-12-30T12:00:00Z",
        "chunks": [
            {
                "text": "This is a test document for indexing. It contains important information about testing.",
                "language": "en",
                "keywords": "test, document, indexing"
            },
            {
                "text": "This is the second page of the test document.",
                "language": "en",
                "keywords": "test, document, page2"
            }
        ]
})

# Create test batch


def create_test_batch():
    """Create a test batch file in the backlog"""
    test_batch = {
        "client_id": TEST_CLIENT_ID,
        "domain": TEST_DOMAIN,
        'action': BatchAction.UPSERT,
        "processed_ids": [doc_id],
        "changes": {
            "upsert": [doc_id],
            "delete": []
        }
    }

    # Create batch in UPLOADED state
    batch_id = BatchUtils.create_batch(TEST_CLIENT_ID, BatchState.IN_PROGRESS, test_batch)
    
    print("Created test batch with ID:", batch_id)
    return batch_id

def test_data_processor_orchestrator():
    """Test the DataProcessorOrchestrator functionality"""
    
    # Create test batch
    batch_id = create_test_batch()

    # Initialize orchestrator
    orchestrator = IndexerOrchestrator()
    
    # Run the orchestrator
    orchestrator.run(TEST_CLIENT_ID)

    # Verify batch state transition
    state, data = BatchUtils.get_batch_status(TEST_CLIENT_ID, batch_id)
    print("Final batch state:", state)
    print("Final batch data:", data)
    
    # The batch should move to either IN_PROGRESS or DONE state
    assert state in [BatchState.IN_PROGRESS, BatchState.DONE], f"Unexpected batch state: {state}"

if __name__ == '__main__':
    pytest.main([__file__]) 
