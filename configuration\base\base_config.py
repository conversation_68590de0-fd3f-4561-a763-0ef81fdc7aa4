import re
from typing import Dict, <PERSON>, Optional, <PERSON>, Tuple
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime

from quepasa.common.config_models import TenantConfig
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.spd_result import SPDSearchResult
from quepasa.searcher.utils import get_llm_model_name
from src.lib.llm_utils import get_cached_llm_answer
from src.lib.utils import get_think_and_answer_from_content
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)

class BaseConfig:
    """Base configuration with common functionality."""

    @classmethod
    def from_request(cls, request: QuepasaRequest):
        return cls(request)
    
    @classmethod
    def from_client_code(cls, client_code: str):
        return cls(client_code)

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        """Initialize base configuration.
        
        Args:
            request_or_client: Either a QuepasaRequest object or client identifier string
        """
        self._request: Optional[QuepasaRequest] = None
        if isinstance(request_or_client_code, QuepasaRequest):
            self.client_code = request_or_client_code.client
            self._request = request_or_client_code

        else:
            self.client_code = request_or_client_code

    def set_request(self, request: QuepasaRequest):
        """Set the current request context"""
        self._request = request
        
    @property 
    def request(self) -> Optional[QuepasaRequest]:
        """Get the current request context"""
        return self._request

    @property
    def tenant_config(self) -> Optional[TenantConfig]:
        """Get provided TenantConfig from request context"""
        if self._request and self._request.tenant_config:
            return self._request.tenant_config
        return None
        
    def get_client_code(self) -> str:
        """Get client identifier.
        
        Previously: get_client_id()
        
        Returns:
            Client code string
        """
        return self.client_code
        
    def get_index_version(self) -> str:
        """Get version of database format.
        
        Previously: get_index_version()
        
        Returns:
            Database version string
        """
        return "v2"
        
    def get_index_name(self) -> str:
        """Get default index name for source.
        
        Previously: get_index_name()
        
        Args:
            source: Source type
            data: Request data
            
        Returns:
            Index name string
        """
        return f"{self.get_index_version()}-{self.get_client_code().replace('_', '-')}"
    
    def process_answer(self, source: str, streaming: bool, answer: str, source_hash: Optional[Dict[int, Union[QuepasaDocument, SPDSearchResult]]] = None) -> Tuple[str, Optional[Dict[int, QuepasaDocument]]]:
        """Process answer after request handling.

        Previously: process_response(), get_extended_response()
        
        Args:
            source: Source type
            streaming: Streaming flag
            answer: Answer text 
            source_hash: Source hash
            
        Returns:
            Tuple of answer text and source hash
        """
        provider, model_name = get_llm_model_name(self.request.answer_llm) if self.request and self.request.answer_llm else self.get_llm_model_name(source)

        # Remove Chinese characters
        if not streaming:  # Don't call LLM extra time if streaming
            if 'Qwen' in model_name or 'DeepSeek' in model_name:
                chinese_pattern = re.compile(r'[\u4e00-\u9fff\u3400-\u4dbf\*********-\U0002A6DF\U0002A700-\U0002B73F\U0002B740-\U0002B81F\U0002B820-\U0002CEAF\U0002CEB0-\U0002EBEF]+')

                paragraphs = re.split('\n', answer)
                updated_paragraphs = []

                for paragraph in paragraphs:
                    if not paragraph.strip():
                        updated_paragraphs.append(paragraph)
                        continue

                    contains_chinese = chinese_pattern.search(paragraph)
                    if contains_chinese:
                        lang = self.get_language_mapping(self.get_language_code())
                        instruction = f"Your task is to rewrite the whole text with inclusions of Chinese into {lang} as closely as possible and without changing the formatting."
                        if lang == 'Russian':
                            instruction = "Твоя задача - переписать текст с вкраплениями китайского целиком на русский язык максимально близко и без изменения форматирования."

                        _, paragraph = get_think_and_answer_from_content(get_cached_llm_answer(
                            provider,
                            model_name,
                            [
                                { 'role': 'system', 'content': instruction },
                                { 'role': 'user', 'content': paragraph }
                            ],
                            300
                        ))
        
                    updated_paragraphs.append(paragraph.strip())

                answer = "\n".join(updated_paragraphs)

        return answer, source_hash
        
    def process_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Process response after request handling.
        
        Previously: process_response(), get_extended_response()
        
        Args:
            source: Source type
            data: Request data
            response: Original response data
            
        Returns:
            Processed response data
        """
        if 'markdown' in response and response['markdown'] and response['markdown'].strip() != "":
            markdown = response['markdown']
            paragraphs = re.split('\n\n', markdown)
            updated_paragraphs = []

            for paragraph in paragraphs:
                if not paragraph.strip():
                    updated_paragraphs.append(paragraph)
                    continue

                current_paragraph = paragraph

                # Find all [text](url)
                matches = list(re.finditer(r',?\s?\[\s?(\\\[[^\[\]]*?\\\]\s?)\]\((https?://.*?)\)', current_paragraph))
                seen_pairs = set()

                # Remove duplicate [text](url)
                for match in reversed(matches):
                    text = match.group(1).strip()
                    url = match.group(2).strip()
                    key = (text, url)
                    start, end = match.start(), match.end()

                    if key in seen_pairs:
                        # Remove duplicate
                        current_paragraph = current_paragraph[:start] + current_paragraph[end:]
                    else:
                        seen_pairs.add(key)

                # Clean up spaces and other
                current_paragraph = re.sub(r" +", " ", current_paragraph).strip()
                current_paragraph = re.sub(r"\n{3,}", "\n\n", current_paragraph)

                updated_paragraphs.append(current_paragraph)

            markdown = "\n\n".join(updated_paragraphs)

            ################################################################################################

            paragraphs = re.split('\n', markdown)
            updated_paragraphs = []

            for paragraph in paragraphs:
                if not paragraph.strip():
                    updated_paragraphs.append(paragraph)
                    continue

                current_paragraph = paragraph

                # Group remaining links by text
                matches = list(re.finditer(r',?\s?\[\s?(\\\[[^\[\]]*?\\\]\s?)\]\((https?://.*?)\)', current_paragraph))
                text_to_urls = defaultdict(list)
                text_to_positions = defaultdict(list)

                for match in matches:
                    text = match.group(1).strip()
                    url = match.group(2).strip()
                    text_to_urls[text].append(url)
                    text_to_positions[text].append((match.start(), match.end()))

                # Add additional links [[2]](...) after the first
                insertions = []
                deletions = []

                for text, urls in text_to_urls.items():
                    positions = text_to_positions[text]
                    if len(urls) > 1:
                        first_end = positions[0][1]
                        extra_links = "".join(f", [\[{i+2}\]]({url})" for i, url in enumerate(urls[1:]))
                        insertions.append((first_end, extra_links))

                        # Mark all other links for deletion
                        for start, end in positions[1:]:
                            deletions.append((start, end))

                # Remove duplicates in reverse order
                for start, end in sorted(deletions, reverse=True):
                    current_paragraph = current_paragraph[:start] + current_paragraph[end:]

                # Insert additional links in reverse order
                for pos, addition in sorted(insertions, reverse=True):
                    current_paragraph = current_paragraph[:pos] + addition + current_paragraph[pos:]

                updated_paragraphs.append(current_paragraph)

            markdown = "\n".join(updated_paragraphs)

            response['markdown'] = markdown

        return response 
