# Quepasa - Development Guide

## Quick Start with Docker

### Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Git for pulling latest changes

### Setup Steps

1. **Pull latest changes:**
   ```bash
   git pull
   ```

2. **Environment Configuration:**
   - Copy `.env.example` to `.env` if not already done
   - Set required API keys in `.env`:
     - `OPENAI_API_KEY=your-openai-api-key`
     - `NEBIUS_API_KEY=your-nebius-api-key`

3. **Launch with Docker:**
   ```bash
   docker compose up --build
   ```

## Development Environment

### Project Structure
```
quepasa/
├── quepasa/                    # Main application modules
│   ├── api/                   # FastAPI backend
│   ├── crawler/               # Document processing
│   ├── data_processor/        # Document transformation
│   ├── indexer/              # Elasticsearch indexing
│   ├── searcher/             # Query processing
│   └── embedding/            # Vector embeddings
├── src/lib/                  # Shared libraries
├── configuration/            # Configuration management
├── tests/                    # Test suite (75+ test files)
├── dev/                     # Development tools & notebooks
└── k8s/                     # Kubernetes manifests
```

### Services & Ports
- **API Service**: http://localhost:8000 - Main REST API
- **Searcher Service**: http://localhost:8080 - Query processing
- **Embedding Service**: http://localhost:8001 - Vector embeddings
- **MinIO Console**: http://localhost:9001 - Object storage UI
- **Elasticsearch**: http://localhost:9200 - Search engine

### Testing

**Run all tests:**
```bash
# 1. Start Docker services first (required for full test suite)
docker compose up -d

# 2. Install test dependencies
pip install -r tests/requirements-test.txt

# 3. Run all tests
python -m pytest tests/

# Run with verbose output
python -m pytest tests/ -v

# Run with coverage report
python -m pytest tests/ --cov=quepasa --cov-report=html

# Run specific test file
python -m pytest tests/test_crawler.py

# Run configuration validation tests only
python run_configuration_tests.py
```

**Run fast unit tests (recommended for development):**
```bash
# Install test dependencies
pip install -r tests/requirements-test.txt

# Fast unit tests (62 tests in <1 second) - no external APIs
python -m pytest tests/configuration/ tests/processors/ tests/test_answer_formatter.py tests/test_crawler.py tests/test_llm_factory.py -v

# Core tests without slow integration tests (~300 tests, ~30 seconds)
python -m pytest tests/ --ignore=tests/test_elasticsearch_integration.py --ignore=tests/test_api_endpoints.py --ignore=tests/test_api_file_handler.py --ignore=tests/test_api_document_handler.py --ignore=tests/test_batch_integration.py --ignore=tests/test_searcher_rag.py --ignore=tests/test_embedding_sbert.py
```

**Test Infrastructure:**
- **Total Tests**: 650+ tests across 75+ test files
- **Full Test Suite**: Requires Docker services (Redis, MinIO, Elasticsearch)
- **Core Tests**: ~300 tests run without external dependencies
- **Configuration Tests**: 23 tests validate YAML configuration system
- All dependencies now install cleanly on macOS ARM64 and other systems
- Language detection uses `langdetect` for improved compatibility across platforms


**Slow tests marker:**

- Some long-running integration or I/O heavy tests are marked with the `@pytest.mark.slow` marker.
- CI skips these by default using `-m "not slow"` (see .github/workflows/tests.yaml).
- Developers can run only slow tests via:

```bash
python -m pytest -m slow
```

- Or run fast tests (excluding slow):

```bash
python -m pytest -m "not slow"
```

- Duration summary: pytest.ini is configured to show duration only for tests taking ≥ 10 seconds. You can override per run, e.g.:

```bash
python -m pytest --durations=0 --durations-min=2.0
```

**Test Configuration:**
- Test runner: pytest with asyncio support
- Coverage reporting available
- 75+ test files covering all modules
- Located in `tests/` directory
- Configuration validation tests: 85+ tests in `tests/configuration/`

### Code Quality

**Linting and Formatting:**
```bash
# Format code
black .

# Check code style
flake8 .

# Type checking
mypy quepasa/

# Run all quality checks
black . && flake8 . && mypy quepasa/
```

**Prompt Formatting Guidelines:**
When writing prompt strings in Python configuration files, follow this formatting pattern for readability:

```python
# Good - Start triple quotes on new line, use strip()
instruction = """
You are a helpful AI assistant.

Follow these guidelines:
- Be concise and clear
- Use proper formatting
- End with helpful suggestions

Please assist the user with their request.
""".strip()

# Avoid - Inline prompts or missing strip()
instruction = """You are a helpful AI assistant..."""  # Hard to read
instruction = """
You are a helpful AI assistant.
...
"""  # Leaves leading/trailing whitespace
```

**Development Dependencies:**
Available in `setup.py` extras:
- pytest, pytest-cov, pytest-mock
- black (formatting)
- flake8 (linting)
- mypy (type checking)

### Common Development Workflows

**Docker Development:**
```bash
# Start services in background
docker compose up -d

# View logs for specific service
docker compose logs -f api
docker compose logs -f searcher

# Restart single service
docker compose restart api

# Stop services
docker compose down

# Rebuild and start
docker compose up --build

# Clean rebuild (remove volumes)
docker compose down -v && docker compose up --build
```

**Local Python Development:**
```bash
# Setup virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install -e .

# Install development dependencies
pip install -e .[dev]
```

### Getting Started
See the [Quepasa Cookbook](dev/QuepasaCookbook-1.ipynb) for examples of:
- Uploading documents to ingestion pipeline
- Retrieving data from inference pipeline
See the [Ingestion Cookbook](dev/IngestionCookbook.ipynb) for examples of products' ingestion and deletion.

### Architecture
This is a microservices-based RAG system:

**Core Services:**
- **API**: FastAPI backend for document handling
- **Crawler**: Document processing and ingestion
- **Data Processor**: Document transformation
- **Indexer**: Elasticsearch indexing
- **Searcher**: Query processing and retrieval
- **Embedding**: Vector embedding generation

**Infrastructure:**
- **Redis**: Message broker and caching
- **MinIO**: Object storage for documents
- **Elasticsearch**: Search and vector storage

### Configuration Management
- **Environment variables**: `.env` for secrets and infrastructure settings
- **YAML configurations**: `configuration/clients/` for client-specific settings
- **Service configs**: `configuration/` for application configuration classes
- **Docker setup**: `docker-compose.yml` for container orchestration

**YAML Configuration System:**
```bash
# Validate all YAML configurations
python run_configuration_tests.py

# Validate specific client configuration
python tests/configuration/validate_config.py

# Structure: configuration/clients/{client_code}.yaml
# Example: configuration/clients/rezolve_shoeby_openai.yaml
```

**Configuration Features:**
- Externalized client settings (prompts, LLM models, UI behavior)
- Strict validation with fail-fast approach
- Template-based prompt generation with variable substitution
- 85+ validation tests ensuring configuration consistency

**Domain Management:**
- By default, documents are ingested into the 'default' domain
- Searches are performed across all domains by default
- Multiple domains can exist for one client (e.g., policies, products, videos)
- Domain field is not required in client YAML configurations - domains are handled internally by the application logic

### Key Libraries
- **FastAPI**: Web framework
- **Celery**: Task queue
- **Elasticsearch**: Search engine
- **OpenAI/Anthropic**: LLM providers
- **Pydantic**: Data validation
- **Pytest**: Testing framework

### Other instructions
- don't add any references to claude in the commit messages
- don't mention Claude in creating PRs