import time
import sys
from typing import Dict, List, Optional

from src.lib.batch_utils import (
    BatchUtils,
    BatchState,
    DataProcessorAction
)
from src.lib.files import QuepasaFiles
from src.lib.logger import <PERSON>pasaLogger

from .tasks import process_batch

logger = QuepasaLogger().get_instance(__name__)

# Initialize files and batch
qp_files = QuepasaFiles()


class IndexerOrchestrator:
    """Orchestrates indexer operations"""
    def __init__(self):
        pass

    def process_client_batches(self, client_id: str, batch_files: List[Dict]):
        """Process all batches for a client"""
        logger.info(f"[{client_id}] Processing batches: {len(batch_files)}")
        
        for batch_file_obj in batch_files:
            batch_file = batch_file_obj['file']
            _, batch_id = BatchUtils.parse_batch_filename(batch_file)
            
            # Process batch using Celery
            logger.info(f"[{client_id}, {batch_id}] Batch processing started")
            process_batch.apply_async(args=[client_id, batch_id])
        
    def run(self):
        """Main execution method"""
        try:
            logger.info(f"Starting indexer")
            
            # Get batches to process
            batch_files_by_client = BatchUtils.get_batch_files_by_client(BatchState.IN_PROGRESS)
            if not batch_files_by_client:
                logger.info(f"No batches to process")
                return
                
            # Process batches for each client
            for client_id, batch_files in batch_files_by_client.items():
                logger.info(f"[{client_id}] Processing batches: {len(batch_files)}")
                self.process_client_batches(client_id, batch_files)
                
            logger.info(f"Indexer completed successfully")
            
        except Exception as e:
            logger.error(f"Indexer failed: {str(e)}", exc_info=True)
            sys.exit(1) 