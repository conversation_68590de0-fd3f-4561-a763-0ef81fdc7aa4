from typing import Dict, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Union
from abc import ABC, abstractmethod
from flask import Response
from ..history.manager import HistoryManager
from ..core.auth import AuthManager, AuthResult
from ..sources.factory import SourceFactory
from ..models.request import UserHistoryItem
from ..models.response import QuepasaAnswer, QuepasaStreamAnswer
from configuration.main.default import QuepasaConfigurationHub
from src.lib.logger import QuepasaLogger
from src.lib.constants import (
    ANSWER_ENDPOINT,
    ANSWER_STREAM_ENDPOINT,
    ANSWER_GPT_INTENTS,
    SOURCE_GPT,
)
import traceback

logger = QuepasaLogger().get_instance(__name__)

class BaseAPIHandler(ABC):
    """Base class for all API handlers with common functionality"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        self.config = config
        self.history_manager = HistoryManager(config)
        self.auth_manager = AuthManager(config)
        self.source_factory = SourceFactory(config)

    def handle_request(self, method: str, url: str, headers: Dict[str, str]) -> Union[Response, Tuple[Dict[str, Any], int, Dict[str, str]]]:
        """Main request handler with auth, pre/post processing"""
        try:
            # Get endpoint and protocol
            endpoint = self._get_endpoint(url)

            # Authenticate based on protocol
            auth_result = self._authenticate_request(headers)
            if not auth_result.is_authorized:
                return {'error': auth_result.error or 'Unauthorized'}, 401, {}

            # Add authenticated user ID to request
            user_id = None
            if (
                hasattr(self.config.request, 'user_info')
                and hasattr(self.config.request.user_info, 'id')
            ):
                user_id = self.config.request.user_info.id

            if (
                user_id
                and self.config.request.history == None
            ):
                history_items = self.history_manager.get_conversation_history(user_id)
                logger.info(f"History items: {history_items}")
                    
                # Convert history items to list format expected by classification
                history_list = [
                    UserHistoryItem(
                        request=item.request,
                        role=item.role,
                        content=item.content,
                        tool_calls=item.tool_calls,
                        tool_call_id=item.tool_call_id,
                        name=item.name,
                        references=item.references
                    )
                    for item in sorted(history_items, key=lambda i: i.ts)
                    if item.content and item.content.strip()
                ]

                self.config.request.history = history_list

            # Get question classification if intents configured
            if len(self.config.get_intents(self.config.request.source)) > 0:
                history_list = []
                if (
                    self.config.request.history
                    and len(self.config.request.history) > 0
                ):
                    history_list = [
                        {
                            'role': item.role,
                            'content': item.content,
                            'request': item.request,
                        }
                        for item in self.config.request.history
                    ]

                classification = self.config.get_question_classification(self.config.request.source, history_list)
                self.config.request.classification = classification

                if classification.get('intent') in ANSWER_GPT_INTENTS:
                    self.config.request.source = SOURCE_GPT

            # Pre-process: Save user message for answer protocol
            if endpoint in [ANSWER_ENDPOINT, ANSWER_STREAM_ENDPOINT] and user_id:
                self.save_user_message(user_id)
            
            # Process the request
            response = self._handle_request_internal(method, url, headers)
            
            # If it's a Response object (e.g., SSE), return it directly
            if isinstance(response, Response):
                return response
                
            # Otherwise, unpack the tuple response
            response_object, status_code, headers = response
            
            # Post-process: Save assistant response for successful answer
            if endpoint == ANSWER_ENDPOINT and user_id:
                self.save_assistant_response(user_id, response_object.data)
                
            return response_object, status_code, headers
            
        except Exception as e:
            logger.error(f"Error in request handler: {str(e)}")
            logger.error("Traceback:")
            for line in traceback.format_exc().splitlines():
                logger.error(line)
            return {'error': 'Internal server error', 'status': 'error'}, 500, {}

    @abstractmethod
    def _handle_request_internal(self, method: str, url: str, headers: Dict[str, str]) -> Union[Response, Tuple[Dict[str, Any], int, Dict[str, str]]]:
        """Internal request handler implementation
        
        Args:
            method: HTTP method
            url: Request URL
            headers: Request headers
            
        Returns:
            Union[Response, Tuple]: Response object or tuple with (data, status_code, headers)
        """
        pass

    def _authenticate_request(self, headers: Dict[str, str]) -> AuthResult:
        auth_header = headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return AuthResult(is_authorized=False, error='Missing or invalid Authorization header')

        return self.auth_manager.authenticate_request(headers)

    def save_user_message(self, user_id: str) -> None:
        """Save user message to history"""
        try:
            self.history_manager.save_message(
                user_id=user_id,
                role="user",
                content=self.config.request.question
            )

            logger.info("Saved user message")

        except Exception as e:
            logger.error(f"Error saving user message: {str(e)}")

    def save_assistant_response(self, user_id: str, answer: Union[Dict[str, Any], QuepasaAnswer, QuepasaStreamAnswer]) -> None:
        """Save assistant response to history"""
        try:
            answer_content = None
            if isinstance(answer, dict):
                answer_content = answer['markdown']

            elif isinstance(answer, QuepasaAnswer) or isinstance(answer, QuepasaStreamAnswer):
                answer_content = answer.markdown

            if not answer_content:
                return

            self.history_manager.save_message(
                user_id=user_id,
                role="assistant",
                content=answer_content
            )

            logger.info("Saved assistant message")

        except Exception as e:
            logger.error(f"Error saving assistant response: {str(e)}")

    def _get_endpoint(self, url: str) -> Optional[str]:
        """Extract endpoint from URL"""
        parts = url.split('?')[0].rstrip('/').split('/')
        for part in reversed(parts):
            if part:
                return part
        return None 