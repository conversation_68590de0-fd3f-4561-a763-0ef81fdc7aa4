import json
import traceback
import uuid
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, Union

from flask import Response

from quepasa.common.config_models import TenantConfig
from ..history.manager import HistoryManager
from quepasa.common.config_client import get_tenant_config
from ..core.auth import Auth<PERSON>anager, AuthResult
from ...common.bearer import RequestAuthenticatorManager
from ..sources.factory import SourceFactory
from ..models.request import UserHistoryItem, QuepasaRequest
from ..models.response import QuepasaAnswer, QuepasaStreamAnswer
from configuration.main.default import QuepasaConfigurationHub
from src.lib.logger import QuepasaLogger
from src.lib.constants import (
    ANSWER_ENDPOINT,
    ANSWER_STREAM_ENDPOINT,
    CONVERSATION_ENDPOINT,
    ANSWER_GPT_INTENTS,
    SOURCE_GPT,
)

logger = QuepasaLogger().get_instance(__name__)

class BaseAPIHandler(ABC):
    """Base class for all API handlers with common functionality"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        self.config = config
        self.history_manager = HistoryManager(config)
        self.auth_manager = AuthManager(config)
        self.request_auth_manager = RequestAuthenticatorManager()
        self.source_factory = SourceFactory(config)

    def handle_request(self, method: str, url: str, headers: Dict[str, str]) -> Union[Response, Tuple[Dict[str, Any], int, Dict[str, str]]]:
        """Main request handler with auth, pre/post processing"""
        try:
            # Generate unique request ID
            request_id = str(uuid.uuid4())
            self.config.request.request_id = request_id

            # Get endpoint and protocol
            endpoint = self._get_endpoint(url)

            # Authenticate based on protocol
            auth_result = self._authenticate_request(headers, endpoint)
            if not auth_result.is_authorized:
                return {'error': auth_result.error or 'Unauthorized'}, 401, {}

            if isinstance(self.config.request, QuepasaRequest):
                tenant_name = self.config.request.x_customer_id
                if tenant_name:
                    tenant_config: Optional[TenantConfig] = get_tenant_config(tenant_name=tenant_name)
                    if tenant_config is None:
                        logger.error(f"Failed to resolve tenant config from memory for 'x_customer_id': {tenant_name}.")
                        raise Exception(f"Failed to resolve tenant config from memory for 'x_customer_id': {tenant_name}.")

                    self.config.request.tenant_config = tenant_config

            # Add authenticated user ID to request
            user_id = None
            # Set user_id from the request body sessionId (if present) for conversation endpoint
            if endpoint == CONVERSATION_ENDPOINT:
                user_id = self._extract_session_id_from_request()

            # Add authenticated user ID to request1
            if (
                hasattr(self.config.request, 'user_info')
                and hasattr(self.config.request.user_info, 'id')
            ):
                user_id = self.config.request.user_info.id

            if (
                user_id
                and self.config.request.history == None
            ):
                history_items = self.history_manager.get_conversation_history(user_id)
                logger.info(f"History items: {history_items}")
                    
                # Convert history items to list format expected by classification
                history_list = [
                    UserHistoryItem(
                        request=item.request,
                        role=item.role,
                        content=item.content,
                        tool_calls=item.tool_calls,
                        tool_call_id=item.tool_call_id,
                        name=item.name,
                        references=item.references,
                    )
                    for item in sorted(history_items, key=lambda i: i.ts)
                    if item.content and item.content.strip()
                ]

                self.config.request.history = history_list

            # Get question classification if intents configured
            if len(self.config.get_intents(self.config.request.source)) > 0:
                history_list = []
                if (
                    self.config.request.history
                    and len(self.config.request.history) > 0
                ):
                    history_list = [
                        {
                            'role': item.role,
                            'content': item.content,
                            'request': item.request,
                        }
                        for item in self.config.request.history
                    ]

                classification = self.config.get_question_classification(self.config.request.source, history_list)
                self.config.request.classification = classification

                if classification.get('intent') in ANSWER_GPT_INTENTS:
                    self.config.request.source = SOURCE_GPT

            # Pre-process: Save user message for answer protocol
            if endpoint in [ANSWER_ENDPOINT, ANSWER_STREAM_ENDPOINT, CONVERSATION_ENDPOINT] and user_id:
                self.save_user_message(user_id)
            
            # Process the request
            response = self._handle_request_internal(method, url, headers)
            
            # If it's a Response object (e.g., SSE), return it directly
            if isinstance(response, Response):
                return response
                
            # Otherwise, unpack the tuple response
            response_object, status_code, headers = response
            
            # Post-process: Save assistant response for successful answer
            if endpoint in [ANSWER_ENDPOINT, CONVERSATION_ENDPOINT] and user_id:
                if endpoint == CONVERSATION_ENDPOINT:
                    # For conversation endpoint, response_object is a ChatResponse dict
                    self.save_assistant_response(user_id, response_object.get('content', ''))
                else:
                    # For regular answer endpoint, response_object has .data attribute
                    self.save_assistant_response(user_id, response_object.data)
                
            return response_object, status_code, headers
            
        except Exception as e:
            logger.error(f"Error in request handler: {str(e)}")
            logger.error("Traceback:")
            for line in traceback.format_exc().splitlines():
                logger.error(line)
            return {'error': 'Internal server error', 'status': 'error'}, 500, {}

    @abstractmethod
    def _handle_request_internal(self, method: str, url: str, headers: Dict[str, str]) -> Union[Response, Tuple[Dict[str, Any], int, Dict[str, str]]]:
        """Internal request handler implementation

        Args:
            method: HTTP method
            url: Request URL
            headers: Request headers

        Returns:
            Union[Response, Tuple]: Response object or tuple with (data, status_code, headers)
        """
        pass

    def _authenticate_request(self, headers: Dict[str, str], endpoint: Optional[str] = None) -> AuthResult:
        # Use BearerAuth for conversation endpoint, AuthManager for others
        if endpoint == CONVERSATION_ENDPOINT:
            auth_result = self.request_auth_manager.authenticate(headers)
            if auth_result.is_authorized and auth_result.client_id:
                self.config.client_code = auth_result.client_id
            return auth_result
        else:

            return self.auth_manager.authenticate_request(headers)

    def save_user_message(self, user_id: str) -> None:
        """Save user message to history"""
        try:
            self.history_manager.save_message(
                user_id=user_id,
                role="user",
                content=self.config.request.question
            )

            logger.info("Saved user message")

        except Exception as e:
            logger.error(f"Error saving user message: {str(e)}")

    def save_assistant_response(self, user_id: str, answer: Union[Dict[str, Any], QuepasaAnswer, QuepasaStreamAnswer, str]) -> None:
        """Save assistant response to history"""
        try:
            answer_content = None
            if isinstance(answer, str):
                answer_content = answer

            elif isinstance(answer, dict):
                answer_content = answer['markdown']

            elif isinstance(answer, QuepasaAnswer) or isinstance(answer, QuepasaStreamAnswer):
                answer_content = answer.markdown

            if not answer_content:
                return

            self.history_manager.save_message(
                user_id=user_id,
                role="assistant",
                content=answer_content
            )

            logger.info("Saved assistant message")

        except Exception as e:
            logger.error(f"Error saving assistant response: {str(e)}")

    def _get_endpoint(self, url: str) -> Optional[str]:
        """Extract endpoint from URL"""
        parts = url.split('?')[0].rstrip('/').split('/')
        for part in reversed(parts):
            if part:
                return part
        return None

    def _extract_session_id_from_request(self) -> Optional[str]:
        try:
            # Parse request body to extract user information
            if hasattr(self.config.request, 'body') and self.config.request.body:
                chat_request = json.loads(self.config.request.body)
                session_id = chat_request.get('sessionId')
                return session_id if session_id else None
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"Could not extract sessionId for conversation history: {str(e)}")