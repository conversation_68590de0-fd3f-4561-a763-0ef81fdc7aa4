import pytest
from unittest.mock import patch, AsyncMock
from quepasa.api.handlers.document_handler import Bat<PERSON><PERSON><PERSON>ult
from dataclasses import asdict

# Mock dependencies
mock_files = AsyncMock()
mock_batch = AsyncMock()
mock_document_handler = AsyncMock()
mock_file_handler = AsyncMock()
mock_batch_handler = AsyncMock()

# Set up mock responses
mock_document_handler.upsert_documents = AsyncMock(
    return_value=BatchResult(batch_id="test_batch", processed_ids=["doc1"])
)
mock_document_handler.get_document = AsyncMock(
    return_value={
        "id": "doc1",
        "url": "http://example.com/doc1",
        "language": "en",
        "chunks": [{"text": "test content", "language": "en"}]
    }
)
# TODO check with <PERSON><PERSON><PERSON> and <PERSON>, probably array should be returned, but for now keep as is
mock_document_handler.list_documents = AsyncMock(
    return_value={"test_domain": {"doc1": "Document 1 Title", "doc2": "Document 2 Title"}}
)

mock_file_handler.upload_file = AsyncMock(
    return_value=asdict(BatchResult(batch_id="test_batch", processed_ids=["file1"]))
)
mock_file_handler.upload_urls = AsyncMock(
    return_value=asdict(BatchResult(batch_id="test_batch", processed_ids=["url1"]))
)

mock_batch_handler.get_batch_status = AsyncMock(
    return_value={"state": "completed", "batch_data": {"key": "value"}}
)

# Patch handlers
@pytest.fixture(autouse=True)
def mock_handlers():
    with patch("quepasa.api.handlers.document_handler.handler", mock_document_handler), \
         patch("quepasa.api.handlers.file_handler.handler", mock_file_handler), \
         patch("quepasa.api.handlers.batch_handler.handler", mock_batch_handler):
        yield

@pytest.mark.asyncio
async def test_upsert_documents(test_client):
    """Test upsert_documents endpoint"""
    response = await test_client.post(
        "/api/v1/documents/test_domain",
        json=[{
            "id": "doc1",
            "url": "http://example.com/doc1",
            "language": "en",
            "chunks": [{"text": "test content", "language": "en"}]
        }]
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["batch_id"] == "test_batch"
    assert data["processed_ids"] == ["doc1"]

@pytest.mark.asyncio
async def test_get_document(test_client):
    """Test get_document endpoint"""
    response = await test_client.get(
        "/api/v1/documents/test_domain/doc1"
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == "doc1"
    assert data["chunks"][0]["text"] == "test content"

@pytest.mark.asyncio
async def test_list_documents(test_client):
    """Test list_documents endpoint"""
    response = await test_client.get(
        "/api/v1/documents/?domain=test_domain"
    )
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, dict)
    assert 'test_domain' in data
    assert isinstance(data['test_domain'], dict)
    assert len(data['test_domain']) == 2
    assert 'doc1' in data['test_domain']
    assert 'doc2' in data['test_domain']
    assert data['test_domain']['doc1'] == "Document 1 Title"
    assert data['test_domain']['doc2'] == "Document 2 Title"

@pytest.mark.asyncio
async def test_get_batch_status(test_client):
    """Test get_batch_status endpoint"""
    response = await test_client.get(
        "/api/v1/batches/test_batch"
    )
    assert response.status_code == 200
    data = response.json()
    assert data["state"] == "completed"
    assert data["batch_data"] == {"key": "value"}

@pytest.mark.asyncio
async def test_missing_auth_header(invalid_auth_client):
    """Test request without auth header"""
    response = await invalid_auth_client.get("/api/v1/documents/")
    assert response.status_code == 401
    assert "Missing Authorization header" in response.json()["detail"]

@pytest.mark.asyncio
async def test_invalid_auth_token(test_client):
    """Test request with invalid auth token"""
    test_client.headers["Authorization"] = "Bearer invalid:token"
    response = await test_client.get("/api/v1/documents/")
    assert response.status_code == 401
    assert "Invalid token" in response.json()["detail"] 