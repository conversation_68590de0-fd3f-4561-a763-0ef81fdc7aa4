import os
import pytest
from unittest.mock import Mock, patch
from src.lib.llm.ollama import OllamaLLM
from src.lib.llm.providers import LLMProvider

@pytest.fixture
def llm(tmp_path):
    """Fixture that provides an Ollama LLM instance with mocked client."""
    with patch('src.lib.llm.ollama.Client') as mock_client:
        llm = OllamaLLM()
        llm._local_cache_dir = str(tmp_path / "cache")
        os.makedirs(llm._local_cache_dir, exist_ok=True)
        return llm

def test_provider(llm):
    """Test provider property."""
    assert llm.provider == LLMProvider.OLLAMA

def test_get_answer_success(llm):
    """Test successful answer generation."""
    test_response = "Test response"
    prompts = [{"role": "user", "content": "test prompt"}]
    
    # Mock response object
    mock_response = Mock()
    mock_response.message.content = test_response
    
    # Mock the client's chat method
    llm.client.chat.return_value = mock_response
    
    result = llm.get_answer("llama2", prompts, 100)
    
    assert result == test_response
    llm.client.chat.assert_called_once_with(
        model="llama2",
        messages=prompts,
        options={
            'num_predict': 100,
            'temperature': 0.0,
        }
    )

def test_get_answer_with_json_mode(llm):
    """Test answer generation with JSON mode enabled."""
    test_response = '{"key": "value"}'
    prompts = [{"role": "user", "content": "test prompt"}]
    
    # Mock response object
    mock_response = Mock()
    mock_response.message.content = test_response
    
    # Mock the client's chat method
    llm.client.chat.return_value = mock_response
    
    result = llm.get_answer("llama2", prompts, 100, json_mode=True)
    
    assert result == test_response
    llm.client.chat.assert_called_once()

def test_get_answer_failure(llm):
    """Test error handling in answer generation."""
    prompts = [{"role": "user", "content": "test prompt"}]
    
    # Mock the client to raise an exception
    llm.client.chat.side_effect = Exception("API error")
    
    result = llm.get_answer("llama2", prompts, 100)
    
    assert result == ""

def test_get_chat_completion_with_stream(llm):
    """Test streaming chat completion."""
    prompts = [{"role": "user", "content": "test"}]
    
    # Mock streaming responses
    mock_responses = [Mock(message=Mock(content="Test ")), Mock(message=Mock(content="response"))]
    with patch.object(llm.client, 'chat', return_value=iter(mock_responses)):
        chunks = []
        for chunk in llm.get_streaming_answer("ollama:llama2", prompts, 100):
            chunks.append(chunk)
        assert chunks == ["Test ", "response"] 