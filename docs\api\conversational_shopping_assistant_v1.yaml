openapi: 3.0.3
info:
  title: Conversational Shopping Assistant API
  description: |
    API for conversational shopping experiences with AI-powered product discovery,
    cart management, and checkout assistance.
    
    ## Features
    - Natural language product search and discovery
    - Multi-modal support (text + images)
    - Streaming and non-streaming response modes
    - Session-based conversation history
    
    ## Response Format
    - **V1**: Markdown-only responses (frontend can convert to plain text if needed)
    - **Products**: Structured product data with variants and pricing
    - **Actions**: Declarative cart/add operation for frontend execution
    - **References**: Policy documents, guides, and non-product content
  version: 1.0.0

servers:
  - url: https://quepasa-searcher.dev.az.rezolve.com
    description: Development server
  - url: https://quepasa-searcher.demo.az.rezolve.com
    description: Demo server
  - url: http://localhost:8000/v1
    description: Local development server

security:
  - ClientKeyAuth: []
    CustomerIdAuth: []

paths:
  /conversation:
    post:
      summary: Send a conversational message
      description: |
        Send a message to the conversational shopping assistant and receive a complete response.
        Supports natural language queries, product searches, cart operations, and checkout assistance.
        
        ## Streaming Support
        - Use `?stream=true` query parameter to enable streaming
        - Or set `Accept: text/event-stream` header for streaming
        - Default response is non-streaming JSON
      operationId: sendMessage
      tags:
        - Conversation
      parameters:
        - name: stream
          in: query
          required: false
          schema:
            type: boolean
            default: false
          description: Enable streaming response (alternative to Accept header)
        - name: format
          in: query
          required: false
          schema:
            type: string
            enum: ["markdown", "plain_text"]
            default: "markdown"
          description: Content format (markdown or plain_text)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatResponse'
            text/event-stream:
              schema:
                $ref: '#/components/schemas/ChatResponse'
              examples:
                product_results:
                  summary: Product search results
                  value:
                    visitorId: "550e8400-e29b-41d4-a716-446655440000"
                    sessionId: "session67890_from_beacontracker"
                    responseId: "resp-20250904-002"
                    timestamp: "2025-09-04T13:35:00Z"
                    content: "Great choice! Here's the Refill Round T-shirt in camel color, size M"
                    stream: false
                    products:
                      - id: "a1586a5f9d1e9c5a501dfcab396e1b1f"
                        url: "http://shoeby1products.com/1106526"
                        title: "Black Shirt"
                        collection: "products"
                        allMeta:
                          id: "blackShirt1"
                          images:
                            - uri: "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwdf085933/products/1106526/06_1106526_99.jpg"
                          brands: ["Shoeby"]
                          name: "projects/923708338127/locations/global/catalogs/default_catalog/branches/1/products/1106526"
                          description: "The Printed Crinkle Shirt has a v-neck"
                          title: "Black V-Neck Shirt"
                          variants:
                            - id: "1106526-S-01"
                              sizes: ["S"]
                              availability: "IN_STOCK"
                            - id: "1106526-M-01"
                              sizes: ["M"]
                              availability: "IN_STOCK"
                            - id: "1106526-L-01"
                              sizes: ["L"]
                              availability: "OUT_OF_STOCK"
                cart_action:
                  summary: Response with actions
                  value:
                    visitorId: "550e8400-e29b-41d4-a716-446655440001"
                    sessionId: "session67890_from_beacontracker"
                    responseId: "resp-20250904-003"
                    timestamp: "2025-09-04T13:35:00Z"
                    content: "Great choice, I've added jeans to the cart. Would you be interested in some shirts which match jeans greatly?"
                    stream: false
                    actions:
                      - type: "ADD_TO_CART"
                        payload:
                          sku: "blackshirt1-small"
                          quantity: 1
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


components:
  securitySchemes:
    ClientKeyAuth:
      type: apiKey
      in: header
      name: Authorization
      description: 'Client key authentication from BrainStudio (format: "client-key _key_")'
    CustomerIdAuth:
      type: apiKey
      in: header
      name: X-Customer-Id
      description: Customer identifier for tenant validation

  schemas:
    ChatRequest:
      type: object
      required:
        - message
        - sessionId
        - collection
        - area
      properties:
        visitorId:
          type: string
          description: Unique visitor identifier
          example: "user12345_from_beacontracker"
        sessionId:
          type: string
          description: Session identifier for conversation tracking
          example: "session67890_from_beacontracker"
        collection:
          type: string
          description: Product collection to search within
          example: "housewares"
        area:
          type: string
          description: Environment area (Production, Staging, etc.)
          example: "Production"
        message:
          type: string
          description: The user's query or message
          example: "I'm looking for some summer clothing"
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/Attachment'
          description: Array of attachment objects (images, etc.)
        context:
          $ref: '#/components/schemas/Context'
        options:
          $ref: '#/components/schemas/Options'
          
    Context:
      type: object
      properties:
        userInfo:
          $ref: "#/components/schemas/UserInfo"

    UserInfo:
      type: object
      properties:
        name:
          type: string
          description: Name of user
          example: "John Smith"


    Attachment:
      type: object
      required:
        - type
        - url
      properties:
        type:
          type: string
          description: Type of attachment (image, etc.)
          example: "image"
        url:
          type: string
          format: uri
          description: URL to the attachment resource
          example: "https://cdn.example.com/uploads/inspo.jpg"

    Options:
      type: object
      properties:
        includeHistory:
          type: boolean
          description: Include conversation history in response
          example: true
        debug:
          type: boolean
          description: Enable debug information in response
          example: false


    ChatResponse:
      type: object
      required:
        - sessionId
        - responseId
        - timestamp
        - content
        - stream
      properties:
        visitorId:
          type: string
          description: Visitor identifier (generated if not provided in request)
          example: "550e8400-e29b-41d4-a716-446655440000"
        sessionId:
          type: string
          description: Session identifier from request
          example: "session_12345"
        responseId:
          type: string
          description: Unique response identifier (consistent across all chunks in streaming)
          example: "resp_789"
        timestamp:
          type: string
          format: date-time
          description: Response timestamp (ISO 8601)
          example: "2025-09-04T13:35:00Z"
        content:
          type: string
          description: Response text content (format determined by ?format parameter)
          example: "Great choice! Here's the **Refill Round T-shirt** in camel color, size M"
        stream:
          type: boolean
          description: Whether this was a streaming response
          example: false
        status:
          type: string
          enum: ["IN_PROGRESS", "COMPLETE", "ERROR"]
          description: Streaming status (for streaming responses)
          example: "COMPLETE"
        components:
          type: array
          items:
            $ref: '#/components/schemas/Component'
          description: Array of display components, in order.
        actions:
          type: array
          items:
            $ref: '#/components/schemas/ActionItem'
          description: Suggested or executed actions (cart, checkout, etc.)

    Component:
      oneOf:
        - $ref: "#/components/schemas/ProductSet"
        - $ref: "#/components/schemas/ReferenceItem"
      discriminator:
        propertyName: componentType

    ComponentSchema:
      type: object
      required:
        - componentType
      properties:
        componentType:
          type: string
          enum: ["PRODUCT_SET", "REFERENCE_ITEM"]
          description: Enum to indicate how this component should be processed.
          example: "PRODUCT_SET"
    
    ProductSet:
      allOf:
        - $ref: "#/components/schemas/ComponentSchema"
        - type: object
          required:
            - products
          properties:
            componentType:
              type: string
              description: Enum to indicate how this component should be processed.
              example: "PRODUCT_SET"
            comment:
              type: string
              description: "A comment on this set of products, to be shown alongside the product cards."
              example: "These shoes would be great for your plan of hiking in the woods."
            products:
              type: array
              items:
                $ref: "#/components/schemas/ProductItem"
    
    ProductItem:
      type: object
      required:
        - id
        - url
        - title
        - collection
        - allMeta
      properties:
        id:
          type: string
          description: Identifier of the record as an MD5 hash
          example: "a1586a5f9d1e9c5a501dfcab396e1b1f"
        url:
          type: string
          description: Logical URL of the record
          example: "http://shoeby1products.com/1106526"
        title:
          type: string
          description: Title of the record
          example: "Black Shirt"
        collection:
          type: string
          description: Collection the record was queried from or resides
          example: "products"
        allMeta:
          type: object
          additionalProperties: true
          description: "All other metadata, read fields, the record has. Contains flexible product data including images, pricing, attributes, categories, brands, etc."
        labels:
          type: array
          items:
            $ref: '#/components/schemas/RecordLabel'
          description: "Labels associated with the record, such as 'PINNED', 'SPONSORED' etc."
          nullable: true

    RecordLabel:
      type: string
      enum: ["PINNED", "BOOSTED", "BURIED", "SPONSORED", "PART_NUMBER"]
      description: Labels that can be associated with product records



    ReferenceItem:
      allOf:
        - $ref: "#/components/schemas/ComponentSchema"
        - type: object
          required:
            - url
            - text
          properties:
            componentType:
              type: string
              description: Enum to indicate how this component should be processed.
              example: "REFERENCE_ITEM"
            url:
              type: string
              format: uri
              description: Reference URL
              example: "https://help.example.com/returns"
            text:
              type: string
              description: Full text content from the source
              example: "Return Policy\n\nAll items may be returned within 30 days of purchase..."
            title:
              type: string
              description: Reference title
              example: "Return and Refund Policy"
    
    ActionItem:
      type: object
      required:
        - type
        - payload
      properties:
        type:
          type: string
          enum: ["ADD_TO_CART", "REMOVE_FROM_CART"]
          description: Action type (uppercase enum value)
          example: "ADD_TO_CART"
        payload:
          type: object
          additionalProperties: true
          description: Action-specific data (SKU, quantity, etc.)
          example:
            sku: "blackshirt1-small"
            quantity: 1


    ErrorResponse:
      type: object
      required:
        - error
      properties:
        error:
          type: object
          required:
            - code
            - message
          properties:
            code:
              type: string
              description: Error code
              example: "INVALID_REQUEST"
            message:
              type: string
              description: Error message
              example: "The request is invalid"
            details:
              type: object
              additionalProperties: true
              description: Additional error details
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
          example: "2025-09-08T14:30:00Z"
        requestId:
          type: string
          description: Request identifier for debugging
          example: "req_abc123"

tags:
  - name: Conversation
    description: Conversational shopping assistant endpoints
