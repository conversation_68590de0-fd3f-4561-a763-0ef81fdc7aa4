import pytest
from unittest.mock import patch
from src.lib.embedding.factory import Embedding<PERSON>actory
from src.lib.embedding.providers import EmbeddingProvider
from src.lib.embedding.openai import OpenAIEmbedding
from src.lib.embedding.askrobot import AskRobotEmbedding

@pytest.fixture(autouse=True)
def mock_env():
    """Mock environment variables."""
    with patch.dict('os.environ', {'OPENAI_API_KEY': 'test-key'}):
        yield

def test_get_embedding_with_string():
    """Test getting embedding provider using string identifier."""
    embedding = EmbeddingFactory.get_embedding('openai')
    assert isinstance(embedding, OpenAIEmbedding)
    
    embedding = EmbeddingFactory.get_embedding('askrobot')
    assert isinstance(embedding, AskRobotEmbedding)

def test_get_embedding_with_enum():
    """Test getting embedding provider using enum."""
    embedding = EmbeddingFactory.get_embedding(EmbeddingProvider.OPENAI)
    assert isinstance(embedding, OpenAIEmbedding)
    
    embedding = EmbeddingFactory.get_embedding(EmbeddingProvider.ASKROBOT)
    assert isinstance(embedding, AskRobotEmbedding)

def test_get_embedding_invalid_provider():
    """Test error handling for invalid provider."""
    with pytest.raises(ValueError) as exc_info:
        EmbeddingFactory.get_embedding('invalid_provider')
    assert 'Unknown embedding provider' in str(exc_info.value)

def test_get_embedding_case_insensitive():
    """Test case-insensitive provider strings."""
    embedding = EmbeddingFactory.get_embedding('OPENAI')
    assert isinstance(embedding, OpenAIEmbedding)
    
    embedding = EmbeddingFactory.get_embedding('askRobot')
    assert isinstance(embedding, AskRobotEmbedding) 