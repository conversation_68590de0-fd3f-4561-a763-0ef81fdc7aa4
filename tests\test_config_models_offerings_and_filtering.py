import pytest

from quepasa.common.config_models import (
    TenantConfig,
    TenantModel,
    ConversationalPromptConfigModel,
    ConversationalFilterableAttributeModel,
)


def make_tenant(name="acme"):
    return TenantModel(id=1, name=name, enabled=True, settings={})


def test_get_offerings_precedence_collection_area_then_collection_only_then_tenant():
    tenant = make_tenant()
    # tenant-level offerings
    tenant_level = ConversationalPromptConfigModel(id=1, tenantId=1, collectionId=None, areaId=None, offerings=["t1", "t2"])
    # collection-only (area-agnostic)
    coll_only = ConversationalPromptConfigModel(id=2, tenantId=1, collectionId=1000, areaId=None, offerings=["c-agn"]) 
    # exact collection+area
    exact = ConversationalPromptConfigModel(id=3, tenantId=1, collectionId=1000, areaId=55, offerings=["exact"]) 

    tc = TenantConfig(
        tenant=tenant,
        conversational_prompt_configs=[tenant_level, coll_only, exact],
    )

    # Exact match takes precedence
    offs = tc.get_offerings(area_id=55, collection_id=1000)
    assert offs == ["exact"]

    # Collection-only when area is None
    offs2 = tc.get_offerings(collection_id=1000, area_id=None)
    assert offs2 == ["c-agn"]

    # Tenant-level fallback when no collection provided or no match
    offs3 = tc.get_offerings()
    assert offs3 == ["t1", "t2"]


def test_get_filtering_attributes_collection_first_then_tenant_level():
    tenant = make_tenant()
    refinement_values = ["one","two","three"]
    attrs = [
        # tenant-level filterable keys
        ConversationalFilterableAttributeModel(tenantId=1, key="brand", collectionId=None, areaId=None, refinement_values=refinement_values),
        ConversationalFilterableAttributeModel(tenantId=1, key="color", collectionId=None, areaId=None, refinement_values=refinement_values),
        # collection-specific keys
        ConversationalFilterableAttributeModel(tenantId=1, key="size", collectionId=1000, areaId=None, refinement_values=refinement_values),
        ConversationalFilterableAttributeModel(tenantId=1, key="brand", collectionId=1000, areaId=None, refinement_values=refinement_values),  # duplicate key, should be unique
        # collection and area keys
        ConversationalFilterableAttributeModel(tenantId=1, key="size", collectionId=1000, areaId=2000, refinement_values=refinement_values),
        ConversationalFilterableAttributeModel(tenantId=1, key="brand", collectionId=1000, areaId=2000, refinement_values=refinement_values),
    ]
    tc = TenantConfig(tenant=tenant, conversational_filterable_attributes=attrs)

    collection_area_level_attributes = tc.get_filtering_attributes(collection_id=1000, area_id=2000)
    assert collection_area_level_attributes is not None
    assert set(collection_area_level_attributes.keys()) == {"size", "brand"}
    for refinement_values_returned in collection_area_level_attributes.values():
        assert refinement_values_returned == refinement_values

    collection_level_attributes = tc.get_filtering_attributes(collection_id=1000)
    assert collection_level_attributes is not None
    assert set(collection_level_attributes.keys()) == {"size", "brand"}
    for refinement_values_returned in collection_level_attributes.values():
        assert refinement_values_returned == refinement_values

    tenant_level_attributes = tc.get_filtering_attributes()
    assert tenant_level_attributes is not None
    assert set(tenant_level_attributes.keys()) == {"color", "brand", "size"}
    for refinement_values_returned in tenant_level_attributes.values():
        assert refinement_values_returned == refinement_values


def test_get_filtering_attributes_returns_none_when_empty():
    tenant = make_tenant()
    tc = TenantConfig(tenant=tenant, conversational_filterable_attributes=[])
    assert tc.get_filtering_attributes() is None
