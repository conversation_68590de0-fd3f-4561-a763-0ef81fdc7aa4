# Error Handling Guide

This document outlines error handling patterns, status codes, and best practices for the Conversational Shopping Assistant API.

## Error Response Structure

All errors follow a consistent structure for predictable handling:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error description", 
    "details": {
      "additionalContext": "value"
    }
  },
  "timestamp": "2025-09-08T15:30:00Z",
  "requestId": "req_abc123"
}
```

## HTTP Status Codes

### 2xx Success
- **200 OK**: Request successful, response contains data
- **202 Accepted**: Request accepted for async processing (streaming)

### 4xx Client Errors

#### 400 Bad Request
Invalid request format, missing required fields, or invalid field values.

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "The request is invalid",
    "details": {
      "field": "message.text",
      "reason": "Field is required but was empty",
      "providedValue": ""
    }
  },
  "timestamp": "2025-09-08T15:30:00Z",
  "requestId": "req_bad_001"
}
```

**Common 400 Error Codes:**
- `INVALID_REQUEST`: Malformed request structure
- `MISSING_REQUIRED_FIELD`: Required field not provided
- `INVALID_FIELD_VALUE`: Field value doesn't meet constraints
- `INVALID_SESSION_ID`: Session ID format is invalid
- `MESSAGE_TOO_LONG`: Message text exceeds maximum length
- `UNSUPPORTED_ATTACHMENT_TYPE`: Attachment type not supported

#### 401 Unauthorized
Authentication credentials missing, invalid, or expired.

```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Authentication required",
    "details": {
      "reason": "Bearer token is missing or invalid",
      "authenticationMethods": ["Bearer", "ClientKey"]
    }
  },
  "timestamp": "2025-09-08T15:30:00Z",
  "requestId": "req_auth_001"
}
```

**Common 401 Error Codes:**
- `UNAUTHORIZED`: No authentication provided
- `INVALID_TOKEN`: Authentication token is invalid
- `TOKEN_EXPIRED`: Authentication token has expired
- `INSUFFICIENT_PERMISSIONS`: Token lacks required permissions

#### 403 Forbidden  
Client lacks permission to perform the requested operation.

```json
{
  "error": {
    "code": "FORBIDDEN",
    "message": "Access to this collection is not allowed",
    "details": {
      "collection": "restricted_catalog",
      "requiredPermission": "collection:restricted_catalog:read"
    }
  }
}
```

#### 404 Not Found
Requested resource doesn't exist.

```json
{
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "The requested session was not found",
    "details": {
      "resourceType": "session",
      "resourceId": "session_nonexistent"
    }
  }
}
```

#### 422 Unprocessable Entity
Request is valid but cannot be processed due to business logic constraints.

```json
{
  "error": {
    "code": "BUSINESS_RULE_VIOLATION",
    "message": "Cannot add item to cart",
    "details": {
      "reason": "Item is restricted in your region",
      "sku": "restricted_item_001",
      "region": "EU"
    }
  }
}
```

#### 429 Too Many Requests
Rate limit exceeded.

```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many requests",
    "details": {
      "limit": 100,
      "window": "1 hour",
      "retryAfter": 3600,
      "resetTime": "2025-09-08T16:30:00Z"
    }
  }
}
```

### 5xx Server Errors

#### 500 Internal Server Error
Unexpected server error.

```json
{
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "An unexpected error occurred",
    "details": {
      "errorId": "error_internal_001",
      "support": "Contact support with this error ID"
    }
  }
}
```

#### 502 Bad Gateway
Upstream service error.

```json
{
  "error": {
    "code": "UPSTREAM_SERVICE_ERROR",
    "message": "Product search service is temporarily unavailable",
    "details": {
      "service": "product_search",
      "retryable": true,
      "estimatedRecovery": "5 minutes"
    }
  }
}
```

#### 503 Service Unavailable
Service temporarily unavailable.

```json
{
  "error": {
    "code": "SERVICE_UNAVAILABLE", 
    "message": "Service is temporarily unavailable due to maintenance",
    "details": {
      "maintenanceWindow": {
        "start": "2025-09-08T16:00:00Z",
        "end": "2025-09-08T17:00:00Z"
      },
      "retryAfter": 3600
    }
  }
}
```

## In-Response Errors

Even with HTTP 200 responses, individual operations can fail. These errors are included in the `errors` array:

```json
{
  "sessionId": "session_123",
  "responseId": "resp_456",
  "status": "complete",
  "assistantMessage": {
    "text": "I found some great dresses, but couldn't add the selected item to your cart."
  },
  "products": [...],
  "actions": [
    {
      "type": "cart",
      "name": "add-to-cart", 
      "payload": {
        "sku": "dress_oos_001",
        "quantity": 1
      },
      "status": "failed",
      "message": "Item is out of stock"
    }
  ],
  "errors": [
    {
      "code": "SKU_OUT_OF_STOCK",
      "message": "The requested item is currently out of stock",
      "target": {
        "type": "sku",
        "id": "dress_oos_001"
      },
      "retryable": false,
      "details": {
        "availableQuantity": 0,
        "restockDate": "2025-09-15",
        "alternatives": ["dress_similar_002", "dress_similar_003"]
      }
    }
  ]
}
```

## Error Codes Reference

### Authentication & Authorization
- `UNAUTHORIZED`: Authentication required
- `INVALID_TOKEN`: Authentication token invalid
- `TOKEN_EXPIRED`: Authentication token expired  
- `FORBIDDEN`: Insufficient permissions
- `RATE_LIMIT_EXCEEDED`: Too many requests

### Request Validation
- `INVALID_REQUEST`: Malformed request
- `MISSING_REQUIRED_FIELD`: Required field missing
- `INVALID_FIELD_VALUE`: Field value invalid
- `MESSAGE_TOO_LONG`: Message exceeds length limit
- `UNSUPPORTED_ATTACHMENT_TYPE`: Attachment type not supported
- `INVALID_SESSION_ID`: Session ID format invalid

### Business Logic
- `SKU_OUT_OF_STOCK`: Item out of stock
- `SKU_NOT_FOUND`: SKU doesn't exist
- `INVALID_QUANTITY`: Invalid quantity specified
- `CART_ITEM_LIMIT_EXCEEDED`: Too many items in cart
- `REGION_RESTRICTED`: Item not available in region
- `AGE_RESTRICTED`: Age verification required
- `PAYMENT_REQUIRED`: Payment method required

### System & Service
- `INTERNAL_SERVER_ERROR`: Unexpected server error
- `UPSTREAM_SERVICE_ERROR`: External service error
- `SERVICE_UNAVAILABLE`: Service temporarily down
- `TIMEOUT`: Request timed out
- `RESOURCE_NOT_FOUND`: Resource doesn't exist

## Error Handling Best Practices

### Client-Side Handling

```javascript
async function handleApiResponse(response) {
    if (!response.ok) {
        // HTTP error
        const errorData = await response.json();
        handleHttpError(response.status, errorData);
        return null;
    }
    
    const data = await response.json();
    
    // Check for in-response errors
    if (data.errors && data.errors.length > 0) {
        handleInResponseErrors(data.errors);
    }
    
    // Check for failed actions
    if (data.actions) {
        data.actions.forEach(action => {
            if (action.status === 'failed') {
                handleFailedAction(action);
            }
        });
    }
    
    return data;
}

function handleHttpError(status, errorData) {
    switch (status) {
        case 400:
            showValidationError(errorData);
            break;
        case 401:
            redirectToLogin();
            break;
        case 429:
            showRateLimitMessage(errorData.error.details.retryAfter);
            break;
        case 500:
        case 502:
        case 503:
            showServerErrorMessage(errorData.error.details.retryable);
            break;
        default:
            showGenericError();
    }
}
```

### Retry Logic

```javascript
class ApiClient {
    async chatWithRetry(requestData, maxRetries = 3) {
        let lastError;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const response = await fetch('/v1/chat', {
                    method: 'POST',
                    headers: this.getHeaders(),
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    return await response.json();
                }
                
                const errorData = await response.json();
                lastError = errorData;
                
                // Don't retry client errors (4xx)
                if (response.status >= 400 && response.status < 500) {
                    throw errorData;
                }
                
                // Wait before retry with exponential backoff
                const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
                await new Promise(resolve => setTimeout(resolve, delay));
                
            } catch (error) {
                lastError = error;
                
                // Don't retry network errors on last attempt
                if (attempt === maxRetries) {
                    throw error;
                }
            }
        }
        
        throw lastError;
    }
}
```

### Error Recovery Strategies

#### 1. Out of Stock Items
```json
{
  "errors": [
    {
      "code": "SKU_OUT_OF_STOCK",
      "target": {"type": "sku", "id": "dress_001_m"},
      "details": {
        "alternatives": ["dress_001_s", "dress_002_m"]
      }
    }
  ]
}
```

**Recovery**: Show alternative sizes/products, offer stock notifications

#### 2. Service Unavailable
```json
{
  "error": {
    "code": "SERVICE_UNAVAILABLE",
    "details": {
      "retryable": true,
      "retryAfter": 300
    }
  }
}
```

**Recovery**: Show maintenance message, implement auto-retry after delay

#### 3. Rate Limiting
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "details": {
      "retryAfter": 3600
    }
  }
}
```

**Recovery**: Show rate limit message, implement request queuing

## Error Monitoring & Debugging

### Error Tracking
- Log all errors with request IDs for correlation
- Track error rates by endpoint and error code
- Monitor retry success rates
- Alert on error rate spikes

### Debug Information
In development/debug mode, responses may include additional error context:

```json
{
  "errors": [
    {
      "code": "SKU_OUT_OF_STOCK",
      "message": "Item out of stock",
      "debug": {
        "stackTrace": "...",
        "upstreamResponse": "...",
        "queryExecutionTime": 150
      }
    }
  ]
}
```

### Request ID Tracking
Always include request IDs in error reports:

```bash
curl -X POST /v1/chat \
  -H "X-Request-ID: req_debug_001" \
  -d '{...}'
```

Response includes the same request ID:
```json
{
  "requestId": "req_debug_001",
  "errors": [...]
}
```

## Graceful Degradation

When core services are unavailable, the API should gracefully degrade:

1. **Search Service Down**: Return cached/popular products with warning
2. **Cart Service Down**: Allow browsing but disable cart operations  
3. **Personalization Down**: Return generic recommendations
4. **Image Processing Down**: Skip image-based search, use text only

Example degraded response:
```json
{
  "assistantMessage": {
    "text": "I'm showing you popular items since our personalization service is temporarily unavailable."
  },
  "products": [...],
  "meta": {
    "serviceStatus": {
      "personalization": "degraded",
      "search": "healthy",
      "cart": "healthy"
    }
  }
}
```