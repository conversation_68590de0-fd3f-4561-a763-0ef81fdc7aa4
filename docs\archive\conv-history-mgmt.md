# Conversation History Management in Quepasa Agentic Flows

## Overview

This document provides a comprehensive analysis of how user conversation history is managed during agentic flows in the Quepasa codebase. It traces the complete execution path from API endpoints through history storage and retrieval operations.

## 1. Agentic Execution Path Analysis

### API Entry Points

The agentic flow starts at the `/retrieve/answer/` and `/retrieve/answer/stream` endpoints when called with `source: "agentic"`.

```python
# quepasa/searcher/api/base.py
class BaseAPIHandler:
    def __init__(self, config: QuepasaConfigurationHub):
        self.config = config
        self.history_manager = HistoryManager(config)
        self.auth_manager = AuthManager(config)
        self.source_factory = SourceFactory(config)
```

### Complete Flow Sequence

```mermaid
sequenceDiagram
    participant Client
    participant API as BaseAPIHandler
    participant HM as HistoryManager
    participant SF as SourceFactory
    participant AS as AgenticSource
    participant LLM as LLM Gateway
    participant Storage as FileHistoryStorage

    Client->>API: POST /retrieve/answer (source: "agentic")
    API->>HM: get_conversation_history(user_id)
    HM->>Storage: get_items(filter)
    Storage-->>HM: List[HistoryItem]
    HM-->>API: conversation_history
    
    API->>SF: get_response(stream=False)
    SF->>AS: _handle_agentic_answer()
    
    loop Tool Calling Loop (max 5 iterations)
        AS->>LLM: get_llm_tools_answer(messages, tools)
        LLM-->>AS: content, tool_calls
        
        alt Has tool_calls
            loop For each tool_call
                AS->>AS: _get_agentic_function_answer()
                AS->>HM: save_message(role="tool", ...)
                HM->>Storage: save_item(user_id, item)
            end
        else No tool_calls
            AS-->>SF: Final response
        end
    end
    
    SF-->>API: QuepasaResponse
    API->>HM: save_message(role="assistant", ...)
    API-->>Client: Response
```

## 2. History Management Mechanisms

### Storage Architecture

The system uses file-based storage with compressed JSON files organized hierarchically:

```python
# quepasa/searcher/history/storage.py
class FileHistoryStorage(HistoryStorage):
    def _get_history_path(self, user_id: str) -> str:
        pathes = [
            'history',
            self.config.get_client_code()
        ]
        
        protocol = self.config.request.protocol
        if protocol in [TELEGRAM_PROTOCOL, WEBHOOK_PROTOCOL]:
            pathes.append(protocol)
            
        pathes.append(str(user_id))
        return os.path.join(*pathes)
```

### History Data Structure

Each history item contains comprehensive conversation context:

```python
# quepasa/searcher/history/models.py
@dataclass
class HistoryItem:
    request: Dict[str, Any]
    ts: datetime
    role: str  # 'user', 'assistant' or 'tool'
    content: str
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    name: Optional[str] = None
    references: Optional[Dict[str, Any]] = None
```

### History Configuration

Default configuration provides reasonable limits for conversation context:

```python
# configuration/main/default.py
def get_history_config(self) -> HistoryConfig:
    return HistoryConfig(
        forget_after_seconds=8 * 3600,  # 8 hours
        max_last_messages=64,
        use_roles=["user", "assistant", "tool"]
    )
```

## 3. Architecture Diagrams

### History Component Architecture

```mermaid
graph TB
    subgraph "API Layer"
        API[BaseAPIHandler]
    end
    
    subgraph "History Management"
        HM[HistoryManager]
        HF[HistoryFilter]
        HI[HistoryItem]
    end
    
    subgraph "Storage Layer"
        FS[FileHistoryStorage]
        Files[(JSON.zlib Files)]
    end
    
    subgraph "Configuration"
        Config[QuepasaConfigurationHub]
        HC[HistoryConfig]
    end
    
    API --> HM
    HM --> FS
    HM --> HF
    HM --> HI
    FS --> Files
    Config --> HC
    HM --> Config
    
    Files --> |"history/{client}/{protocol}/{user_id}/{date}/{timestamp}.json.zlib"| FS
```

### Agentic Flow with History Integration

```mermaid
graph LR
    subgraph "Request Processing"
        REQ[Request] --> AUTH[Authentication]
        AUTH --> HIST_LOAD[Load History]
    end
    
    subgraph "History Integration"
        HIST_LOAD --> HIST_FILTER[Apply Filters]
        HIST_FILTER --> HIST_FORMAT[Format for LLM]
    end
    
    subgraph "Agentic Execution"
        HIST_FORMAT --> LLM_PROMPT[Build LLM Prompt]
        LLM_PROMPT --> TOOL_CALLS[Tool Execution]
        TOOL_CALLS --> HIST_SAVE[Save Tool Results]
    end
    
    subgraph "Response"
        HIST_SAVE --> FINAL_RESP[Final Response]
        FINAL_RESP --> SAVE_ASSIST[Save Assistant Response]
    end
```

## 4. Key History Operations

### History Retrieval and Filtering

The `HistoryManager` applies multiple filters to retrieve relevant conversation context:

```python
# quepasa/searcher/history/manager.py
def get_conversation_history(self, user_id: str) -> List[HistoryItem]:
    config = self.config.get_history_config()
        
    # Calculate time window
    end_time = datetime.now()
    start_time = None
    if config.forget_after_seconds:
        start_time = end_time - timedelta(seconds=config.forget_after_seconds)
        
    # Create filter
    filter = HistoryFilter(
        user_id=user_id,
        start_time=start_time,
        end_time=end_time,
        roles=config.use_roles,
        max_items=config.max_last_messages
    )
```

### History Integration in Agentic Prompts

History is seamlessly integrated into LLM prompts to maintain conversation context:

```python
# quepasa/searcher/sources/mixins.py
# Add history
if request.history:
    # Append before last message
    prompt_messages[-1:-1] = [
        {
            'role': history_item.role,
            'content': history_item.content,
        }
        for history_item in request.history
        if history_item.role in ["user", "assistant"]
    ]
```

### Tool Result Storage

Tool execution results are automatically saved to maintain complete conversation context:

```python
# quepasa/searcher/sources/factory.py
history_manager.save_message(
    user_id=user_id,
    role="tool",
    content=function_answer,
    name=function_name,
    tool_call_id=tool_call['id'],
    references=function_references
)
```

## 5. Data Flow Analysis

### History Data Flow

```mermaid
flowchart TD
    A[User Request] --> B{User ID Available?}
    B -->|Yes| C[Load History from Storage]
    B -->|No| D[Skip History Loading]
    
    C --> E[Apply Time Window Filter]
    E --> F[Apply Role Filter]
    F --> G[Apply Message Limit]
    G --> H[Convert to UserHistoryItem]
    
    H --> I[Add to Request Context]
    I --> J[Build LLM Prompt with History]
    
    J --> K[Execute Agentic Loop]
    K --> L{Tool Calls?}
    L -->|Yes| M[Execute Tools]
    M --> N[Save Tool Results to History]
    N --> K
    L -->|No| O[Generate Final Response]
    
    O --> P[Save Assistant Response]
    P --> Q[Return to User]
    
    D --> I
```

### File Storage Structure

```
history/
├── {client_code}/
│   ├── {protocol}/          # telegram, webhook, etc.
│   │   └── {user_id}/
│   │       ├── 2024-01-15/
│   │       │   ├── 1705123456.789.json.zlib
│   │       │   └── 1705123567.890.json.zlib
│   │       └── 2024-01-16/
│   │           └── 1705209876.543.json.zlib
│   └── {user_id}/           # Direct user ID for other protocols
```

## 6. Integration Points

### History Manager Integration

The `BaseAPIHandler` automatically loads and integrates conversation history:

```python
# quepasa/searcher/api/base.py
if (
    user_id
    and self.config.request.history == None
):
    history_items = self.history_manager.get_conversation_history(user_id)
    
    # Convert history items to list format expected by classification
    history_list = [
        UserHistoryItem(
            request=item.request,
            role=item.role,
            content=item.content,
            tool_calls=item.tool_calls,
            tool_call_id=item.tool_call_id,
            name=item.name,
            references=item.references
        )
        for item in sorted(history_items, key=lambda i: i.ts)
        if item.content and item.content.strip()
    ]

    self.config.request.history = history_list
```

## 7. Key Components

### HistoryManager Class

**Location**: `quepasa/searcher/history/manager.py`

**Key Methods**:
- `save_message()`: Saves new messages to history with full context
- `get_conversation_history()`: Retrieves filtered conversation history
- `clear_user_history()`: Clears all history for a user
- `get_related_history()`: Gets history items marked as related

### FileHistoryStorage Class

**Location**: `quepasa/searcher/history/storage.py`

**Key Methods**:
- `save_item()`: Persists history items as compressed JSON files
- `get_items()`: Retrieves history items based on filters
- `clear_history()`: Removes all history files for a user

### HistoryFilter Class

**Location**: `quepasa/searcher/history/models.py`

**Filter Criteria**:
- Time window (start_time, end_time)
- User ID
- Message roles (user, assistant, tool)
- Maximum message count

## 8. Key Findings

1. **File-based Storage**: History is stored as compressed JSON files organized by client, protocol, user, and date
2. **Multi-role Support**: Tracks `user`, `assistant`, and `tool` messages with full context
3. **Time-based Filtering**: Configurable time windows (default 8 hours) with message limits (default 64)
4. **Tool Integration**: Tool calls and results are fully tracked in conversation history
5. **Streaming Support**: History is maintained consistently across both streaming and non-streaming responses
6. **Protocol Awareness**: Storage paths adapt to different protocols (Telegram, webhook, etc.)
7. **Automatic Integration**: History loading and saving is handled transparently by the API layer
8. **Context Preservation**: Complete conversation context is maintained across multi-turn interactions

## 9. Performance Considerations

- **Compression**: Uses zlib compression for storage efficiency
- **Date-based Organization**: Files are organized by date for efficient retrieval
- **Configurable Limits**: Time windows and message counts prevent unbounded growth
- **Lazy Loading**: History is only loaded when user_id is available
- **Filtering**: Multiple filter criteria reduce memory usage and processing time

## 10. Future Enhancements

Potential areas for improvement:
- Database backend for better querying capabilities
- Distributed storage for scalability
- Advanced filtering and search capabilities
- History summarization for long conversations
- Configurable retention policies per client

---

Document generated: 08-09-2025 