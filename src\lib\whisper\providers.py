from enum import Enum

class WhisperProvider(Enum):
    """Enumeration of available Whisper transcription providers"""
    REPLICATE = "replicate"
    
    # Future providers can be added here
    # OPENAI = "openai"
    # NEBIUS = "nebius"

    @classmethod
    def from_str(cls, value: str) -> 'WhisperProvider':
        """Convert string to WhisperProvider enum.
        
        Args:
            value: String value to convert. For backward compatibility,
                    'openai' is mapped to OPENAI.
            
        Returns:
            Corresponding WhisperProvider enum value
            
        Raises:
            ValueError: If value is not a valid provider
        """
        value = value.lower()
        try:
            return cls(value)

        except ValueError:
            raise ValueError(f"Unknown embedding provider: {value}. "
                            f"Supported providers: {[e.value for e in cls]}") 