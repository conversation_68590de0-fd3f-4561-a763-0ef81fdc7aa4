"""
Unit tests for HTTPHandler response transformation logic
"""
import pytest
from unittest.mock import Mock

from quepasa.searcher.api.http import HTTPHandler
from quepasa.searcher.models.response import (
    QuepasaResponse, QuepasaAnswer, ProductItem, Reference,
    ChatResponse, ResponseStatus, ProductSet, ReferenceItem
)
from quepasa.searcher.models.request import UserInfo
from configuration.main.default import QuepasaConfigurationHub


class TestHTTPResponseTransformation:
    """Test HTTP response transformation from QuepasaResponse to ChatResponse"""
    
    @pytest.fixture
    def mock_config(self):
        """Create mock configuration"""
        config = Mock(spec=QuepasaConfigurationHub)
        config.request = Mock()
        config.request.request_id = "test-request-123"
        config.request.user_info = UserInfo(
            id="session-123",
            session_id="session-123", 
            visitor_id="visitor-456",
            name="Test User"
        )
        return config
    
    @pytest.fixture
    def http_handler(self, mock_config):
        """Create HTTPHandler instance"""
        return HTTPHandler(mock_config)
    
    def test_transform_quepasa_answer_object(self, http_handler):
        """Test transformation with standard QuepasaAnswer object"""
        # Create test data with QuepasaAnswer object
        test_product = ProductItem(
            id="test-product-1",
            title="Test Product",
            url="https://example.com/product/1",
            collection="test_collection",
            allMeta={"test": "data"}
        )
        
        test_reference = Reference(
            url="https://example.com/reference",
            text="Test reference text",
            title="Test Reference"
        )
        
        quepasa_answer = QuepasaAnswer(
            type="strict",
            text="This is the answer text",
            markdown="This is the **markdown** answer",
            products=[test_product],
            references={"ref1": test_reference}
        )
        
        quepasa_response = QuepasaResponse(
            status="ok",
            data=quepasa_answer
        )
        
        # Transform response
        result = http_handler._transform_to_chat_response(
            quepasa_response, "session-123", "markdown"
        )
        
        # Verify result
        assert isinstance(result, ChatResponse)
        assert result.sessionId == "session-123"
        assert result.content == "This is the **markdown** answer"
        assert result.visitorId == "visitor-456"
        assert result.status == ResponseStatus.COMPLETE
        assert len(result.components) == 2  # ProductSet + ReferenceItem
        
        # Verify ProductSet component
        product_component = result.components[0]
        assert isinstance(product_component, ProductSet)
        assert len(product_component.products) == 1
        assert product_component.products[0].id == "test-product-1"
        
        # Verify ReferenceItem component
        reference_component = result.components[1]
        assert isinstance(reference_component, ReferenceItem)
        assert reference_component.url == "https://example.com/reference"
        assert reference_component.text == "Test reference text"
    
    def test_transform_dict_data_agentic_response(self, http_handler):
        """Test transformation with dictionary data (from agentic source)"""
        # Create test data with dictionary format (as from agentic source)
        test_data = {
            'type': 'strict.agentic',
            'text': 'Here is a cute NFL product for babies',
            'markdown': 'Here is a **cute NFL product** for babies',
            'products': [
                {
                    'id': 'gbibaby_5717058',
                    'title': 'NFL Indianapolis Colts Half Time Long Sleeve Coverall',
                    'url': 'https://gb-housewares.groupby.cloud/product/title/gbibaby_5717058',
                    'collection': 'housewares',
                    'allMeta': {'test': 'data'}
                }
            ],
            'references': {
                '1': {
                    'url': 'https://gb-housewares.groupby.cloud/product/title/gbibaby_5717058',
                    'text': 'NFL Indianapolis Colts Half Time Long Sleeve Coverall description',
                    'title': 'NFL Indianapolis Colts Half Time Long Sleeve Coverall'
                }
            }
        }
        
        quepasa_response = QuepasaResponse(
            status="ok",
            data=test_data
        )
        
        # Transform response
        result = http_handler._transform_to_chat_response(
            quepasa_response, "session-123", "markdown"
        )
        
        # Verify result
        assert isinstance(result, ChatResponse)
        assert result.sessionId == "session-123"
        assert result.content == 'Here is a **cute NFL product** for babies'
        assert result.visitorId == "visitor-456"
        assert result.status == ResponseStatus.COMPLETE
        assert len(result.components) == 2  # ProductSet + ReferenceItem
        
        # Verify ProductSet component with dict-based products
        product_component = result.components[0]
        assert isinstance(product_component, ProductSet)
        assert len(product_component.products) == 1
        assert product_component.products[0]['id'] == 'gbibaby_5717058'
        assert product_component.products[0]['title'] == 'NFL Indianapolis Colts Half Time Long Sleeve Coverall'
        
        # Verify ReferenceItem component with dict-based references
        reference_component = result.components[1]
        assert isinstance(reference_component, ReferenceItem)
        assert reference_component.url == 'https://gb-housewares.groupby.cloud/product/title/gbibaby_5717058'
        assert reference_component.text == 'NFL Indianapolis Colts Half Time Long Sleeve Coverall description'
    
    def test_transform_plain_text_format(self, http_handler):
        """Test transformation with plain text format parameter"""
        quepasa_answer = QuepasaAnswer(
            type="strict", 
            text="Plain text content",
            markdown="**Markdown** content"
        )
        
        quepasa_response = QuepasaResponse(
            status="ok",
            data=quepasa_answer
        )
        
        # Transform with plain text format
        result = http_handler._transform_to_chat_response(
            quepasa_response, "session-123", "plain_text"
        )
        
        # Should use text instead of markdown
        assert result.content == "Plain text content"
