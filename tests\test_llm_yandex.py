import os
import json
import pytest
import requests
from unittest.mock import Mock, patch
from src.lib.llm.yandex import YandexLL<PERSON>
from src.lib.llm.providers import LL<PERSON>rovider

@pytest.fixture
def llm():
    """Fixture that provides a Yandex LLM instance."""
    with patch.dict('os.environ', {
        'YANDEX_API_KEY': 'test-key',
        'YANDEX_FOLDER_ID': 'test-folder'
    }):
        return YandexLLM()

def test_provider(llm):
    """Test provider identification."""
    assert llm.provider == LLMProvider.YANDEX

def test_get_answer_success(llm):
    """Test successful answer generation."""
    prompts = [{"role": "user", "content": "test"}]
    
    with patch('requests.post') as mock_post:
        mock_response = Mock()
        mock_response.json.return_value = {
            "result": {
                "alternatives": [{
                    "message": {"text": "Test response"}
                }]
            }
        }
        mock_post.return_value = mock_response
        
        response = llm.get_answer("yandex:latest", prompts, 100)
        assert response == "Test response"

def test_get_answer_failure(llm):
    """Test error handling for failed answer generation."""
    prompts = [{"role": "user", "content": "test"}]
    
    with patch('requests.post', side_effect=Exception("API error")), \
         pytest.raises(Exception):
        llm.get_answer("yandex:latest", prompts, 100)

def test_missing_credentials():
    """Test error when credentials are missing."""
    with patch.dict('os.environ', clear=True), \
         pytest.raises(ValueError) as exc_info:
        YandexLLM()
    assert "YANDEX_API_KEY and YANDEX_FOLDER_ID environment variables must be set" in str(exc_info.value) 