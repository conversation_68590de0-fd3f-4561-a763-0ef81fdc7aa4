{"status": "success", "results": [{"id": "https://t.me/c/**********/621", "url": "https://t.me/c/**********/621", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): https://docs.google.com/presentation/d/1BQTxB50pZwr-52ua87Kq88LiV8f1aZeJ01G7ysJLBkE/edit#slide=id.g3271e60313b_0_11 [<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_<PERSON>) [Любимая](https://t.me/<PERSON>V<PERSON>ov) [Fedor](https://t.me/id1906) посмотрите, пожалуйста. Набросал черновое оглавление презентации ОК / не ОК?", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-20T12:15:02Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}, {"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/624", "url": "https://t.me/c/**********/624", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Какие пункты нуждаются в дитализации, на твой взгляд? Давай \"разошьем\" их", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-20T15:01:51Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/626", "url": "https://t.me/c/**********/626", "title": "AI mindset", "language": "en", "text": "[Юр<PERSON>ов](https://t.me/y<PERSON><PERSON>_<PERSON>): Кстати, вот тут примеры для Лигал: Based on workflows ([Harvey – Workflows: Streamline and automate tasks with end-to-end tools.](https://www.harvey.ai/products/workflows)) and Prompt Libraries: A collection of expertly-curated prompts and examples at your fingertips. (https://www.harvey.ai) I think they collected a set of curated prompts to do some routine tasks: •⁠  ⁠translation •⁠  ⁠⁠critical analysis •⁠  ⁠⁠comparison •⁠  ⁠⁠AI search", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-20T15:21:24Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/628", "url": "https://t.me/c/**********/628", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Мне не очень нравится этот плэйсхолдер. Я не знаю что сказать тут кроме \"понятия не имею\". Это узкоспециализированные инструменты, которые к тому же насколько я знаю забанены в РФ.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-20T20:18:35Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/629", "url": "https://t.me/c/**********/629", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Я бы сказал про разницу между AI Общего назначения и специализированных AI и объяснил бы дальше зачем им нужен AI общего назначения", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-20T20:19:19Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/632", "url": "https://t.me/c/**********/632", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): [<PERSON><PERSON>](https://t.me/id1906) есть важная задача: собрать список конкретных кейсов, о которых расскажем на вебинаре Сможешь ее взять в работу? Если да, уточни срок готовности 🙏", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>", "created_at": "2025-01-21T15:38:51Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/633", "url": "https://t.me/c/**********/633", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_vorontsov): Я немного подготовился, вместе с o1 собрал слайды (см. последнее) [ChatGPT - LLM для бизнеса: Shared via ChatGPT](https://chatgpt.com/share/678fbfad-1698-800a-9f66-2dbfaf44eefe)   Имхо, можно найти примеры документов, которые могут встречаться у них и сделать несколько демонстраций.", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-21T15:41:37Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/635", "url": "https://t.me/c/**********/635", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Я ещё на работе, только что закрыл ноут. Юра, можешь поделиться слайдами", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T16:40:51Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/637", "url": "https://t.me/c/**********/637", "title": "AI mindset", "language": "bg", "text": "[<PERSON><PERSON><PERSON>ов](https://t.me/yuri<PERSON>_voro<PERSON>): [ChatGPT - LLM для бизнеса: Shared via ChatGPT](https://chatgpt.com/share/678fbfad-1698-800a-9f66-2dbfaf44eefe) Последний ответ", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-21T16:41:31Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/638", "url": "https://t.me/c/**********/638", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Я скажу сегодня либо выдам список кейсов, либо скажу когда выдам.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T16:41:56Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/640", "url": "https://t.me/c/**********/640", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Я бы вот тут немного порядок поменял: Слайд 11. Как начать внедрение?  1. Аудит процессов: Определить, какие рутинные задачи можно автоматизировать (документооборот, поиск, переписка).  2. Выбор пилотных проектов: Начать с небольших, но ощутимых кейсов.  3. Тестирование и прототипы: Запустить простые MVP (чат-бот, генератор текстов, помощник в BIM-системе).  4. Обучение команды: Воркшопы, хакатоны, тренинги по prompt engineering.  5. Обратная связь и масштабирование: Оценить эффективность и постепенно расширять сферу применения.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T16:44:30Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/641", "url": "https://t.me/c/**********/641", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON>ов](https://t.me/yuri<PERSON>_voro<PERSON>): Я скаже, что o1 очень хорошо заменеджил бизнес нашу стратегию, могу прислать тебе отдельно", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-21T16:45:25Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/642", "url": "https://t.me/c/**********/642", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Я тут фокус бы именно на ai mindset сделал: мы не знаем что у вас болит лучше чем вам поэтому мы как в фильме армагеддон будем делать из бурильщиков космонавтов", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T17:03:05Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/643", "url": "https://t.me/c/**********/643", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): # Как начать внедрение GenAI? 1. Выявление агентов роста:    - Поиск энтузиастов в разных отделах    - Определение ранних последователей    - Создание сети внутренних амбассадоров 2. Создание культуры экспериментов:    - Поощрение пробных проектов    - Право на ошибку    - Обмен опытом между командами 3. Выбор пилотных проектов:    - Фокус на quick wins с видимым результатом    - Минимальное влияние на существующие процессы    - Максимальная видимость успеха 4. Образовательная экосистема:    - Воркшопы и хакатоны    - Peer-to-peer обучение    - Sharing sessions успешных кейсов 5. Масштабирование через сообщество:    - Развитие внутренней экспертизы    - Документирование лучших практик    - Создание центров компетенций Акцент на развитии людей и культуры, а не только на технической стороне внедрения.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T17:03:08Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/645", "url": "https://t.me/c/**********/645", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Слайд 9 тоже мимо: нужно давить на один месседж: хуман аугментэйш<PERSON>, а этот слайд про автоматизацию и оптимизацию. Мне кажется оптимизация вторична, а автоматизация вообще немного из другой оперы - в такое опасно соваться на данном этапе. Предлагаю такой вариант слайда: # Выгоды внедрения GenAI в проектировании 1. Усиление возможностей сотрудников:    - Помощь в анализе сложных данных    - Поддержка в принятии решений    - Доступ к лучшим практикам 2. Развитие экспертизы:    - Быстрый доступ к накопленному опыту    - Помощь в обучении новых сотрудников    - Сохранение знаний уходящих экспертов 3. Улучшение качества:    - Помощь в проверке соответствия нормам    - Поддержка в выявлении потенциальных проблем    - Consistency check между разделами 4. Оптимизация процессов:    - Ускорение рутинных операций    - Помощь в подготовке документации    - Поддержка в координации между отделами 5. Инновационный потенциал:    - Возможность тестировать новые подходы    - Поиск нестандартных решений    - Развитие культуры постоянного улучшения Акцент на усилении человеческого потенциала, а не на автоматизации.​​​​​​​​​​​​​​​​", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T17:09:46Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/647", "url": "https://t.me/c/**********/647", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Самому захотелось на такую презентацию сходить.  Мне очень понравился слайд про ограничения.  Юра, можешь пожалуйста попросить о1 поменять слайды по моим рекомендациям и прислать весь аутлайн сюда?", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T17:15:54Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/649", "url": "https://t.me/c/**********/649", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Ага, вот я немного поработал над слайдом по ограничениям, по-моему так лучше: # Потенциальные риски и ограничения GenAI 1. Галлюцинации и неточности:    - Примеры:      * Придумывание несуществующих норм и стандартов      * Смешивание требований из разных документов      * Уверенные ответы о несуществующих технологиях    - Как проверить:      * Спросить про \"СНиП 99.99-2023 по проектированию космических лифтов\"      * Попросить описать конкретные пункты из \"обновленного ГОСТ 2025 года\" 2. Ограничения в понимании контекста:    - Примеры:      * Путаница в технических терминах с двойным значением      * Неверная интерпретация чертежей без полного контекста      * Смешивание требований из разных разделов проекта    - Как проверить:      * Дать описание многозначного термина в разных контекстах      * Попросить проанализировать фрагмент чертежа без общего вида 3. Проблемы с конфиденциальностью:    - Примеры:      * Включение в ответы деталей из других проектов      * Сохранение sensitive информации в истории      * Использование данных в обучении моделей    - Как защититься:      * Четкие правила работы с конфиденциальной информацией      * Использование enterprise решений с контролем данных 4. Организационные барьеры:    - Типичные сценарии:      * \"У меня 20 лет опыта, зачем мне AI?\"      * \"Я лучше по старинке, так надежнее\"      * \"Это слишком сложно для меня\"    - Как работать:      * Показывать конкретные примеры пользы      * Начинать с простых сценариев      * Обеспечивать поддержку при освоении Ключевой принцип: знание ограничений - путь к эффективному использованию.​​​​​​​​​​​​​​​​", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T17:22:28Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/650", "url": "https://t.me/c/**********/650", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): Я попробую этой штуке скормить слайды [Napkin AI - The visual AI for business storytelling: Just type, copy-paste or generate your text and <PERSON><PERSON><PERSON> will instantly transform it into insightful visuals. Make your communication more effective with <PERSON><PERSON><PERSON>.](https://www.napkin.ai/)", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T18:10:53Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/651", "url": "https://t.me/c/**********/651", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Я готов съесть свою шляпу. Последний месяц-два я подчеркнуто не любил deepseek из-за китайских корней. Сегодня я потестировал их свежее R1 (аналог О1) и хочу сказать, что это очень впечатляет. Безотносительно этичности и слепых зон китайского llm (не надо спрашивать чей Тибет), результаты прямо ого-го.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-21T18:49:45Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/656", "url": "https://t.me/c/**********/656", "title": "AI mindset", "language": "ru", "text": "[Ю<PERSON><PERSON>ов](https://t.me/yuri<PERSON>_voro<PERSON>): Офигенная метафора, её нужно в начале использовать. Только что на разговоре использовал — она круто работает!", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-21T19:40:39Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/659", "url": "https://t.me/c/**********/659", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voro<PERSON>ov): Кажет<PERSON>я, я могу выйти ещё на парочку росскийских проектировщиков, работающих в рамках застройщиков, чтобы им предложить вебинар", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-21T19:44:36Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/662", "url": "https://t.me/c/**********/662", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON>ов](https://t.me/yuri<PERSON>_v<PERSON>): https://inpad.store Разработка приложений: Разработка приложений для строительства. Плагины для Revit, NanoCAD, Renga", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-21T19:46:15Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/665", "url": "https://t.me/c/**********/665", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> В<PERSON>ов](https://t.me/yuri<PERSON>_voro<PERSON>): Я начал думать про Cursor.app, как про Midjourney. Загнал все хорошие промпты в o1 и попросил напистаь рекоммендации. А потом стал его спрашивать.", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T11:44:27Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/666", "url": "https://t.me/c/**********/666", "title": "AI mindset", "language": "ru", "text": "[Ю<PERSON><PERSON>ов](https://t.me/yuri<PERSON>_v<PERSON>): Для примера мой план для этого был такой - Analyse code at quepasa/api - Write tests to folder tests/ with prefix test_api_ - Use pytest - Run tests and check", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T11:45:04Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/670", "url": "https://t.me/c/**********/670", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): # Искусственный интеллект (ИИ) и большие языковые модели (LLM) в сфере проектирования ## Слайд 1. Титульный слайд Заголовок: «Искусственный интеллект (ИИ) и большие языковые модели (LLM) в сфере проектирования»   Подзаголовок: «Возможности, примеры, перспективы» Информация для отображения: - Ваше имя и должность (или имена и должности выступающих) - Дата проведения вебинара *Демонстрация:*   «Hook»-история: в начале презентации можете рассказать короткую историю о том, как кто-то из коллег (или вы сами) задал LLM специфический вопрос по проектированию и неожиданно получил полезное, но удивительное решение. *Как показать:* буквально на 1 минуту описать личный опыт или кейс, чтобы заинтересовать слушателей. ## Слайд 2. План (Agenda) - Что такое большие языковые модели (LLM) и как они работают - Ключевые области применения ИИ (с фокусом на проектирование) - Примеры реальных бизнес-кейсов и инструментов - Выгоды и риски внедрения - Вопросы и ответы (Q&A) *Пример / Демонстрация:*   Покажите короткий дорожно-тематический план: где мы сейчас, куда двигаемся (шаги). *Как показать:* можно включить небольшой таймлайн в слайд с разбивкой по минутам или разделам. ## Слайд 3. Коротко об ИИ: основные направления Основные направления: - Computer Vision (распознавание изображений и видео) - Natural Language Processing (NLP) — сюда относятся LLM - Speech Recognition and Synthesis (распознавание речи и синтез) - Robotics (автономные системы, манипуляторы) - Generative AI (текст, изображение, музыка, видео) - Predictive Analytics & Machine Learning (прогнозы, анализ, статистика) *Пример / Демонстрация:* Computer Vision:   Распознавание чертежей или 3D-сканированных объектов. *Как показать:* включите скриншот популярного сервиса, распознающего чертёж (например, сервисы, распознающие PDF-чертежи и создающие 3D-модели). Speech Recognition:   Голосовые помощники (Siri, Google Assistant). *Как показать:* можно попросить вашего смартфона «Открыть BIM-модель по ключевому слову» (в демонстрационных целях). ## Слайд 4. Что такое большие языковые модели (LLM)? Определение:   LLM (Large Language Model) — алгоритмы, обученные на огромном массиве текстовых данных, способные генерировать и анализировать тексты. Как это работает (упрощённо): - Модель «читает» миллиарды слов/фраз - Находя статистические закономерности, модель предсказывает следующее слово или фразу Применение: - Автоматизация документооборота - Создание черновиков текстов, инструкций, ТЗ - Анализ документов на противоречия *Пример / Демонстрация:* Короткий чат с моделью:   *Как показать:* - Подготовьте окно браузера с ChatGPT (или другой моделью) - Введите запрос: «Объясни простыми словами, что такое BIM» - Показать участникам ответ на экране ## Слайд 5. Где LLM (NLP) уже используется сегодня? - Бизнес-коммуникации: чат-боты, ассистенты для клиентов - Внутренний документооборот: генерация шаблонов документов, проверка текста - Маркетинг и продажи: анализ отзывов, создание контента - Поддержка пользователей: авто-ответы на частые вопросы - Проектная деятельность: поиск и анализ стандартов, технических условий, BIM-регламентов *Пример / Демонстрация:* Автоответ на почту:   *Как показать:* - На экране в чате с моделью дайте команду: «Напиши вежливый ответ клиенту, который спрашивает о сроках поставки чертежей. Укажи, что срок — 2 недели» - Модель выдаст готовый текст письма ## Слайд 6. Пример кейса: Автоматизация обработки текстов в проектировании Задача: Быстрый поиск и сведение информации из разных документов (нормативы, BIM-стандарты, регламенты). Решение с использованием LLM: - Внутренний чат-бот, обученный на документах компании - Запрос: «Какие требования по пожарной безопасности для такого-то типа здания?» — бот выдаёт компиляцию информации Польза: - Сокращение времени на поиск и проверку - Унификация знаний - Снижение риска пропустить важный пункт *Пример / Демонстрация:* Синтетический пример:   *Как показать:* - Возьмите короткий отрывок из СП (Строительные Правила) или ГОСТ, залейте его в ChatGPT (или в любой инструмент, где можно загружать текст)", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T17:50:09Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/671", "url": "https://t.me/c/**********/671", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): - Задайте вопрос: «Какие ключевые пункты в этих нормах касаются пожарных выходов для зданий такого-то класса?» - Показать ответ модели - Обязательно уточните, что в реальной жизни нужно следить за актуальностью версий документов и дообучать модель ## Слайд 7. Как это может помочь BIM-отделам? - Генерация описаний BIM-моделей: Авто-составление инструкций к модели, выгрузка в документацию - Валидация данных: Быстрый анализ на соответствие BIM-стандартам - BIM-координация: Автоматическая генерация протоколов встреч, распределение задач - Обучение новых сотрудников: Корпоративный чат-бот для ответов на типовые вопросы *Пример / Демонстрация:* Автоматический протокол коллизий:   *Как показать:* - Подготовьте пример списка обнаруженных коллизий (5-6 пунктов) в текстовом виде - Попросите LLM «Напиши формальный протокол совещания о коллизиях и предложи варианты устранения» - На глазах у слушателей получите структурированный, читабельный документ ## Слайд 8. Дополнительно: Генеративные модели для визуализации Почему интересно архитекторам и дизайнерам: - Быстрая генерация концепций, эскизов, mood-boards - Подбор вариантов оформления фасадов, интерьеров - Эксперименты с формой *Пример / Демонстрация:* Концепт здания с помощью Midjourney (или Stable Diffusion):   *Как показать:* - Заранее зарегистрируйтесь в Midjourney (через Discord) или используйте любой веб-интерфейс Stable Diffusion - Введите запрос: «Futuristic office building concept, glass facade, sustainable design» (на английском, если сервис англоязычный) - Покажите, как за несколько секунд/минут сервис выдаст уникальные изображения ## Слайд 9. Выгоды внедрения GenAI в проектировании 1. Усиление возможностей сотрудников:    - Помощь в анализе сложных данных    - Поддержка в принятии решений    - Доступ к лучшим практикам 2. Развитие экспертизы:    - Быстрый доступ к накопленному опыту    - Помощь в обучении новых сотрудников    - Сохранение знаний уходящих экспертов 3. Улучшение качества:    - Помощь в проверке соответствия нормам    - Поддержка в выявлении потенциальных проблем    - Consistency check между разделами 4. Оптимизация процессов:    - Ускорение рутинных операций    - Помощь в подготовке документации    - Поддержка в координации между отделами 5. Инновационный потенциал:    - Возможность тестировать новые подходы    - Поиск нестандартных решений    - Развитие культуры постоянного улучшения *Пример / Демонстрация:* Сравнение «до» и «после»:   *Как показать:* - Опишите вымышленный сценарий: «Раньше на поиск нужных норм уходило 2-3 часа, теперь — 10 минут» - Подкрепите цифрами или диаграммой (даже если она условная) — иллюстрация экономии времени ## Слайд 10. Потенциальные риски и ограничения - Галлюцинации модели (придуманные факты) - Безопасность данных (конфиденциальность) - Неполное понимание контекста - Сопротивление изменениям (культурные и организационные барьеры) *Пример / Демонстрация:* Показать «галлюцинацию»:   *Как показать:* - Задайте модели вопрос о «несуществующем» СНиП или ГОСТ. Например, «Расскажи о требованиях СНиП 99.99-2023» - Модель может начать отвечать, хотя такого СНиП реально нет - Это подчеркнёт важность верификации ответов ## Слайд 11. Как начать внедрение GenAI? 1. Выявление агентов роста:    - Поиск энтузиастов в разных отделах    - Определение ранних последователей    - Создание сети внутренних амбассадоров 2. Создание культуры экспериментов:    - Поощрение пробных проектов    - Право на ошибку    - Обмен опытом между командами 3. Выбор пилотных проектов:    - Фокус на quick wins с видимым результатом    - Минимальное влияние на существующие процессы    - Максимальная видимость успеха 4. Образовательная экосистема:    - Воркшопы и хакатоны    - Peer-to-peer обучение    - Sharing sessions успешных кейсов 5. Масштабирование через сообщество:    - Развитие внутренней экспертизы    - Документирование лучших практик    - Создание центров компетенций *Пример / Демонстрация:* Пример пилотного проекта:   *Как показать:* - Опишите сценарий: «Возьмём задачу составления еженедельного отчёта по статусу BIM-моделей»", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T17:50:10Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/672", "url": "https://t.me/c/**********/672", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): - Покажите, как можно автоматически собирать данные из нескольких источников (Revit, Navisworks и т.д.) и формировать сводку (через LLM или простые макросы) ## Слайд 12. Формат дальнейшего обучения и поддержки - Корпоративные воркшопы: практикум по prompt engineering - Advisory для топ-менеджмента: расчёт ROI, оценка рисков - AI-Hackathon: совместное решение конкретных бизнес-задач - Мастер-классы: работа с инструментами (AutoGPT, ChatGPT, плагины для BIM и т.п.) *Пример / Демонстрация:* Мини «AI-Hackathon» за 15 минут:   *Как показать:* - Разделите участников на группы (даже если чисто виртуально) - Дайте им короткую задачу, где требуется сформулировать prompt для LLM - Пусть каждая группа попробует получить оптимальный ответ - Сравните результаты ## Слайд 13. Вопросы / Обсуждение - Что кажется наиболее актуальным для ваших команд? - Какие задачи требуют автоматизации в первую очеред", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T17:50:10Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/673", "url": "https://t.me/c/**********/673", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voro<PERSON><PERSON>): *Демонстрация:*   «Hook»-история: в начале презентации можете рассказать короткую историю о том, как кто-то из коллег (или вы сами) задал LLM специфический вопрос по проектированию и неожиданно получил полезное, но удивительное решение. Надо поменять, или у нас есть идеи именно по проектированию.", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T17:53:43Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/674", "url": "https://t.me/c/**********/674", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuriy_vorontsov): *Пример / Демонстрация:*   Покажите короткий дорожно-тематический план: где мы сейчас, куда двигаемся (шаги). *Как показать:* можно включить небольшой таймлайн в слайд с разбивкой по минутам или разделам. У тебя есть идеи про план", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T17:53:54Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/675", "url": "https://t.me/c/**********/675", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voronts<PERSON>): Computer Vision:   Распознавание чертежей или 3D-сканированных объектов. *Как показать:* включите скриншот популярного сервиса, распознающего чертёж (например, сервисы, распознающие PDF-чертежи и создающие 3D-модели). [<PERSON><PERSON>](https://t.me/il_37) Давай на сайте онпада поищем что-нибудь", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T17:53:58Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/676", "url": "https://t.me/c/**********/676", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voronts<PERSON>): Speech Recognition:   Голосовые помощники (<PERSON><PERSON>, Google Assistant). *Как показать:* можно попросить вашего смартфона «Открыть BIM-модель по ключевому слову» (в демонстрационных целях). [<PERSON><PERSON>](https://t.me/il_37) Я думаю можно сделать чат с резюме нашей прошлой встречи. Или с каким-то видео с их сайта.", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T17:54:57Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/677", "url": "https://t.me/c/**********/677", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuriy_vorontsov): *Пример / Демонстрация:* Короткий чат с моделью:   *Как показать:* - Подготовьте окно браузера с ChatGPT (или другой моделью) - Введите запрос: «Объясни простыми словами, что такое BIM» - Показать участникам ответ на экране Можно, например, взять какой-то ГОСТ, например,  ГОСТ 21.101-2020 — и поспрашивать по нему", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T17:56:08Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/678", "url": "https://t.me/c/**********/678", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuriy_vorontsov): *Пример / Демонстрация:* Автоответ на почту:   *Как показать:* - На экране в чате с моделью дайте команду: «Напиши вежливый ответ клиенту, который спрашивает о сроках поставки чертежей. Укажи, что срок — 2 недели» - Модель выдаст готовый текст письма Можно показать, как я сделал фоллоу ап по прошлой встрече", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T17:56:38Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/679", "url": "https://t.me/c/**********/679", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuri<PERSON>_voronts<PERSON>): ...Синтетический пример:   *Как показать:* - Возьмите короткий отрывок из СП (Строительные Правила) или ГОСТ, залейте его в ChatGPT (или в любой инструмент, где можно загружать текст) - Задайте вопрос: «Какие ключевые пункты в этих нормах касаются пожарных выходов для зданий такого-то класса?» - Показать ответ модели - Обязательно уточните, что в реальной жизни нужно следить за актуальностью версий документов и дообучать модель ... Вроде — ГОСТ 21.101-2020 — реальный гост", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T17:57:19Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/680", "url": "https://t.me/c/**********/680", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuriy_vorontsov): Концепт здания с помощью Midjourney (или Stable Diffusion):   *Как показать:* - Заранее зарегистрируйтесь в Midjourney (через Discord) или используйте любой веб-интерфейс Stable Diffusion - Введите запрос: «Futuristic office building concept, glass facade, sustainable design» (на английском, если сервис англоязычный) - Покажите, как за несколько секунд/минут сервис выдаст уникальные изображения Можно показать DALL-e фотку здания и попросить сделать благоустройство, например", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T17:58:55Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/681", "url": "https://t.me/c/**********/681", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voronts<PERSON>): Сравнение «до» и «после»:   *Как показать:* - Опишите вымышленный сценарий: «Раньше на поиск нужных норм уходило 2-3 часа, теперь — 10 минут» - Подкрепите цифрами или диаграммой (даже если она условная) — иллюстрация экономии времени Я думаю, это домашнее задание, придумать кейсы и составить желаемое — до и после", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T17:59:46Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/682", "url": "https://t.me/c/**********/682", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuriy_vorontsov): Показать «галлюцинацию»:   *Как показать:* - Задайте модели вопрос о «несуществующем» СНиП или ГОСТ. Например, «Расскажи о требованиях СНиП 99.99-2023» - Модель может начать отвечать, хотя такого СНиП реально нет - Это подчеркнёт важность верификации ответов Надо подобрать. Можно ещё попросить добавить вертолётную площадку, или бассейн для подводн<PERSON>й лодки, или космопорт", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:01:00Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/683", "url": "https://t.me/c/**********/683", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuriy_vorontsov): ...Пример пилотного проекта:   *Как показать:* - Опишите сценарий: «Возьмём задачу составления еженедельного отчёта по статусу BIM-моделей» - Покажите, как можно автоматически собирать данные из нескольких источников (Revit, Navisworks и т.д.) и формировать сводку (через LLM или простые макросы) ... Можно попробовать это тоже сделать, как пример демо — на данных блогов или специальных ютуб каналов", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:01:54Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/684", "url": "https://t.me/c/**********/684", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voro<PERSON><PERSON>): Мини «AI-Hackathon» за 15 минут:   *Как показать:* - Разделите участников на группы (даже если чисто виртуально) - Дайте им короткую задачу, где требуется сформулировать prompt для LLM - Пусть каждая группа попробует получить оптимальный ответ - Сравните результаты Мне кажется, это на вторую серию задел", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:02:38Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/685", "url": "https://t.me/c/**********/685", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voronts<PERSON>): ## Слайд 13. Вопросы / Обсуждение - Что кажется наиболее актуальным для ваших команд? - Какие задачи требуют автоматизации в первую очеред [Fedor](https://t.me/id1906) оборвалось", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:02:56Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/686", "url": "https://t.me/c/**********/686", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON><PERSON>ов](https://t.me/yuri<PERSON>_v<PERSON>): [<PERSON><PERSON>](https://t.me/id1906) Очень хорошо. Нужно на первый слайд поставить аллегорию на Армегеддон, зачем мы вообще всё это делаем!", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:03:42Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/689", "url": "https://t.me/c/**********/689", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): ## Слайд 13. Вопросы / Обсуждение - Что кажется наиболее актуальным для ваших команд? - Какие задачи требуют автоматизации в первую очередь? - Какие риски вы видите в использовании LLM? *Пример / Демонстрация:* Интерактивный опрос:   *Как показать:* - Используйте инструменты Zoom/Teams/Webex (опрос, чат) - Задайте 2-3 вопроса, соберите ответы сразу - Отобразите результаты в виде диаграммы (если платформа позволяет) ## Слайд 14. Контакты - Имя докладчика(ов) - Email, телефон, мессенджеры - Ссылки на ресурсы/сайты (при необходимости)", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T18:23:35Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/690", "url": "https://t.me/c/**********/690", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuriy_vorontsov): Интерактивный опрос:   *Как показать:* - Используйте инструменты Zoom/Teams/Webex (опрос, чат) - Задайте 2-3 вопроса, соберите ответы сразу - Отобразите результаты в виде диаграммы (если платформа позволяет) Кстати, хоть в комменты пусть митса пусть покидают", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:24:31Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/692", "url": "https://t.me/c/**********/692", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON><PERSON>ов](https://t.me/yuri<PERSON>_voro<PERSON>): [<PERSON><PERSON>](https://t.me/id1906) Вы с LLM молодцы! (Новая этика AI Mindset) Осталось допилить примеров и MVP вебинара готов.", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:25:45Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/693", "url": "https://t.me/c/**********/693", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON><PERSON>](https://t.me/y<PERSON><PERSON>_<PERSON>): [<PERSON><PERSON>](https://t.me/il_37) [Любимая](https://t.me/<PERSON>ov)  В каком формате нам лучше делать слайды?", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:27:06Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/695", "url": "https://t.me/c/**********/695", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON><PERSON>](https://t.me/y<PERSON><PERSON>_v<PERSON>): Кстати, если это РФ — может быть им DeepSeek дать? [<PERSON>or](https://t.me/id1906) Рав, [Chat with DeepSeek AI.](https://chat.deepseek.com/sign_in) кошерен?", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:31:48Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/696", "url": "https://t.me/c/**********/696", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON><PERSON>ов](https://t.me/yuri<PERSON>_v<PERSON>): [<PERSON><PERSON>](https://t.me/id1906) в следующий раз лучше кидай шэр на чатгпт или файлом, дополнительно, чтобы проще было копировать", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T18:34:57Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/697", "url": "https://t.me/c/**********/697", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_vorontsov): [<PERSON><PERSON>](https://t.me/il_37) [GenAI: Искусственный интеллект (ИИ) и большие языковые модели (LLM) в сфере проектирования Возможности, примеры, перспективы В фильме «Армагеддон» команда бурильщиков отправилась на астероид спасать Землю. Их за короткий срок обучили базовым навыкам астронавтов. Мы же хотим за короткий срок «прокачать» ...](https://docs.google.com/presentation/d/1ieFl3jK1hHX8GwI9M6MD7sOQgLaAXcQlgQ6OrSrt71c/edit?usp=sharing) Полчаса позора курсора", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T19:02:02Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/698", "url": "https://t.me/c/**********/698", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuriy_voro<PERSON><PERSON>): Нашёл просто картинку из интернета: На изображении представлен архитектурный план первого этажа здания, выполненный в формате, типичном для строительных чертежей. Вот подробное описание: ### Основные элементы чертежа: 1. Этаж и отметка высоты:    - Указано, что это \"План 1-го этажа на отметке 0.000\", что означает базовый уровень пола первого этажа. 2. <PERSON>бозначение помещений:    - Гостиная:       - Площадь: 23.79 м².      - Расположена в нижней левой части чертежа.      - Содержит мебель: диван, телевизор, обеденный стол.    - Кухня-столовая:      - Площадь: 13.95 м².      - Включает мебель для приготовления пищи и обеденный стол.    - Санузел:      - Площадь: 5.56 м².      - Присутствует стандартное сантехническое оборудование.    - Котельная:      - Площадь: 6.89 м².      - Предназначена для отопительного оборудования.    - Прихожая:      - Площадь: 5.56 м².      - Центральная часть плана.    - Гараж:      - Площадь: 17.17 м².      - Расположен справа и имеет отдельный вход. 3. Стены и размеры:    - Несущие стены изображены более толстыми линиями, а внутренние перегородки тоньше.    - Указаны размеры стен, проемов и расстояний между объектами. 4. Двери и окна:    - Дверные проемы обозначены дугами, показывающими направление открывания.    - Оконные проемы показаны в виде линий на внешних стенах с указанием их размеров. 5. Размерные линии:    - Указаны внешние и внутренние размеры помещений.    - Размеры даны в миллиметрах, например, 865, 3000, 4915. 6. Условные обозначения:    - Присутствуют символы и линии для обозначения мебели, сантехники и инженерных систем.    - Каждое помещение подписано с указанием площади в квадратных метрах. 7. Штриховка:    - Стены и перекрытия заштрихованы, чтобы выделить их толщину и материал. 8. Оси и маркировка:    - Чертеж снабжен осями (А, Б, В, Г, Д и 1, 2, 3, 4), которые облегчают понимание расположения помещений. Если нужно дополнительное описание или конкретная информация о чертеже, уточните, пожалуйста!", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T19:08:00Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/702", "url": "https://t.me/c/**********/702", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Аккуратно с таким - я как инженер за такое бы уцепился и просто по пунктам бы развалил; особенно если бы до моего чертежа какой-то ботан бы докопался", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T19:43:16Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/704", "url": "https://t.me/c/**********/704", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): В целом я считаю что лезть в конкретику инженерной профессии особенно с чертежами очень опасная дорожка. Поплывем.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T19:43:49Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/709", "url": "https://t.me/c/**********/709", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): На основе представленного поэтажного плана я подготовлю полезное описание проекта. Одноэтажный жилой дом с рациональной планировкой, общая площадь ~100 м². Ключевые преимущества проекта: 1. Продуманное зонирование: - Просторная гостиная, совмещенная с кухней-столовой (~40 м²) - Отдельная котельная с техническим помещением - Гараж на 1 автомобиль с удобным въездом - Компактная входная группа с тамбуром 2. Технические особенности: - Капитальные несущие стены обеспечивают надежность конструкции - Оптимальные габариты помещений: гостиная 23.79 м², кухня-столовая 13.25 м² - Инженерные коммуникации сгруппированы (кухня + санузел) - Предусмотрено место под индивидуальный котел в котельной 3. Преимущества для строительства: - Простая прямоугольная форма здания 12x9 м - Рациональная конструктивная схема без сложных узлов - Возможность быстрого возведения благодаря типовым решениям - Минимум несущих перегородок внутри дома 4. Экономическая эффективность: - Компактные размеры снижают затраты на фундамент и кровлю - Простая геометрия упрощает отделочные работы - Оптимальное соотношение жилой и общей площади - Возможность поэтапного строительства Проект подойдет для постоянного проживания семьи из 2-4 человек. Планировка позволяет при необходимости выполнить перепланировку или достроить второй этаж в будущем.​​​​​​​​​​​​​​​​", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T19:48:39Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/710", "url": "https://t.me/c/**********/710", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Вот мой тейк. Я конечно космонавт, но ещё и инженер. А у любого текста должна быть цель", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T19:49:15Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/711", "url": "https://t.me/c/**********/711", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Промпт: «Напиши полезный и продающий текст на основании этого чертежа который бы мог помочь инженеру и сэкономить его время»", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T19:50:01Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/712", "url": "https://t.me/c/**********/712", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): У такого примера больше шансов сделать вау эффект и чутка вдохновить. Спеки они сами умеют писать с закрытыми глазами. Более того, даже в мои инженерные времена можно было такие спеки генерить в несколько кликов.  А вот продающий текст средний инженер написать не может :)", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-22T20:00:38Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/714", "url": "https://t.me/c/**********/714", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuriy_vorontsov): Этот чертеж представляет собой детальный план первого этажа современного и функционального дома с продуманной планировкой. Пространство организовано так, чтобы удовлетворить потребности семьи и обеспечить комфортное проживание. ### Описание: - Гостиная (23,79 м²) — просторная зона для отдыха и встреч с друзьями. Здесь можно разместить удобный диван, телевизор и создать уютную атмосферу для семейных вечеров. - Кухня-столовая (13,95 м²) — компактная, но функциональная зона для готовки и совместных обедов. Благодаря близости к гостиной, создается ощущение открытого пространства. - Санузел (5,56 м²) — достаточно просторный для размещения всей необходимой сантехники. - Прихожая (5,56 м²) — удобное пространство для хранения верхней одежды и обуви. - Котельная (6,89 м²) — изолированное помещение для технического оборудования, что повышает энергоэффективность дома. - Гараж (17,17 м²) — вместительный, подходит для хранения автомобиля и дополнительных вещей. - Тамбур (2,61 м²) — функциональное пространство, которое предотвращает попадание холода внутрь дома. ### Преимущества: 1. Эффективное использование пространства. Компактная планировка идеально подходит для небольших участков, сохраняя комфорт. 2. Зонирование. Отдельные помещения позволяют членам семьи заниматься своими делами, не мешая друг другу. 3. Гараж в доме. Удобство хранения автомобиля, особенно в холодное время года. 4. Техническое помещение. Возможность установки системы отопления или дополнительного оборудования в специально выделенном месте. ### Для кого подойдет: - Для семей, которые ценят комфорт и функциональность. - Для тех, кто ищет проект, легко адаптируемый под индивидуальные потребности. Если вы хотите жить в уютном и энергоэффективном доме, который станет вашим настоящим «гнездом», этот проект — идеальное решение для вас!", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T20:27:55Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/716", "url": "https://t.me/c/**********/716", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuriy_vorontsov): Ниже приведён пример, как можно обыграть метафору из фильма «Армагеддон» и сравнить «тренировку бурильщиков в космонавтов» с подготовкой архитекторов и инженеров к работе с ИИ. Такой приём поможет сделать подачу более живой и запоминающейся. --- ## Идея метафоры В фильме «Армагеддон» команда бурильщиков отправилась на астероид спасать Землю. Их за короткий срок обучили базовым навыкам астронавтов. Мы же хотим за короткий срок «прокачать» архитекторов и инженеров, чтобы они стали «космонавтами» в мире ИИ — людьми, способными быстро и эффективно применять AI/LLM-технологии в своей работе. Основная мысль: «Если NASA смогла за несколько недель преобразить группу эксцентричных бурильщиков в космонавтов, то и мы сможем за короткий промежуток времени сделать из архитекторов и инженеров настоящих экспертов в области ИИ (по крайней мере, в тех аспектах, которые им критически нужны на практике)». --- ## Как внедрить эту метафору в презентацию / семинар ### 1. Вступительная шутливая часть - Слайд с кадром из «Армагеддона»: можно взять нейтральный стоп-кадр, где команда героев собирается на обучении в NASA.   - Текст или устный комментарий:     > «Помните, как в «Армагеддоне» NASA решила, что легче научить бурильщиков стать космонавтами, чем космонавтов научить бурить? Вот и мы теперь хотим показать, что легче взять опытных архитекторов и инженеров и быстро ввести их в мир ИИ, чем объяснять ИИ-шникам всю специфику проектирования!» ### 2. Параллели с фильмом - В фильме: ускоренная подготовка команды, нестандартный подход, весёлые конфликты, но в итоге — успешное выполнение миссии.   - У вас на семинаре: интенсивные блоки обучения (короткие, но максимально прикладные), минимум теории — максимум практики с реальными кейсами.     > «Пусть у нас не будет астероида, угрожающего Земле (к счастью!), но у нас есть *дедлайны*, цифровая трансформация и конкуренция на рынке, которые не меньше подгоняют нас к действиям.» ### 3. Структура семинара «по-армегеддоновски» 1. Сжатые сроки, чёткие задачи: «В фильме у них было пару недель до столкновения с астероидом. У нас, конечно, времени чуть больше, но всё равно нужно действовать быстро.»   2. Минимум теории — максимум реальной практики: «Как в тренировочном лагере NASA: даём вам сразу инструменты (ChatGPT, Midjourney, плагины для BIM и др.), учим ими пользоваться на конкретных примерах.»   3. Проверка (или «экзамен») в конце: «В «Армагеддоне» бурильщики сдавали тесты и чуть не взорвали центр подготовки NASA, а у нас вы создадите прототип проекта или автоматизируете задачу, которую раньше делали вручную неделями.» ### 4. Демонстрация «до» и «после» - Покажите, как архитектор/инженер решал бы задачу без ИИ, и как он решит её, имея базовые навыки в LLM.   - Подчеркните, что как героям фильма после тренировок стало проще работать в условиях невесомости, так и вашим участникам станет проще работать в условиях *«цифровой перестройки»*, когда ИИ-инструменты будут на каждом шагу. ### 5. Итоговый вывод (подчёркиваем аналогию) > «В «Армагеддоне» всё закончится хэппи-эндом (спасли планету). У нас с вами тоже всё должно закончиться отлично: спасём компанию от устаревших процессов, оптимизируем проектирование и сделаем нашу команду сверхэффективной. Главное — действовать, учиться новому и не бояться экспериментов!» --- ## Пример, как оформить этот блок в начале семинара (мини-сценарий)", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T20:30:20Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/717", "url": "https://t.me/c/**********/717", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuriy_vorontsov): 1. Показать на экране (или упомянуть устно) короткий отрывок из «Армагеддона» (10-15 секунд, если позволяет авторское право и формат) или просто памятный кадр / постер.   2. Сказать (пример речи):      > «Коллеги, наверняка вы помните старый фильм «Армагеддон», где NASA решило научить бурильщиков быть космонавтами за пару недель. Почему? Потому что это было проще, чем учить космонавтов бурить. Нам с вами предстоит нечто похожее: мы не будем делать из вас программистов, зато научим пользоваться ИИ на вашем профессиональном языке. Чтобы вы, опытные архитекторы и инженеры, смогли с помощью больших языковых моделей экономить массу времени, быстрее принимать решения и оставаться в авангарде технологий. Как те самые ребята из «Армагеддона», мы подготовимся быстро и эффективно!»   3. Переключиться на следующий слайд (примерно: «Окей, шутки в сторону, давайте перейдём к делу!»). --- ## Что даёт такая метафора? 1. Лёгкий и запоминающийся «вход» в тему.   2. Снятие напряжения: участники начинают семинар с улыбкой, а не с опасением «о, это что-то сложное и непонятное».   3. Яркая иллюстрация: люди понимают, что вы хотите дать им интенсив по работе с ИИ, а не сделать их «полноценными дата-сайентистами». Это помогает избежать страха перед «программистскими» терминами. --- ### Итог Используйте «фишку» с «Армагеддоном» в начале или в течение семинара, чтобы зацепить внимание, разрядить обстановку и донести главный смысл: «Если даже бурильщиков можно было обучить космическим технологиям, то и мы — опытные проектировщики, архитекторы, инженеры — вполне в силах быстро освоить ИИ и внедрить его в наши рабочие процессы.»   Таким образом, вы создадите эффект вовлечённости, а ваша аудитория почувствует, что получает поддержку и чёткий план по освоению новых технологий.", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T20:30:20Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/718", "url": "https://t.me/c/**********/718", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voro<PERSON><PERSON>): «Пусть у нас не будет астероида, угрожающего Земле (к счастью!), но у нас есть *дедлайны*, цифровая трансформация и конкуренция на рынке, которые не меньше подгоняют нас к действиям.»", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-22T20:31:37Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/729", "url": "https://t.me/c/**********/729", "title": "AI mindset", "language": "ru", "text": "[Любимая](https://t.me/<PERSON><PERSON><PERSON><PERSON><PERSON>): Класс! Вы вчера мощно продвинулись, пока я отмечала день рождения Элиэзера Бен-Йехуды.  Есть ли тех. уже задание для верстки? Канва? Пожелания по стилю шаблона? Конкретный шаблон в канве, который нравится? Что по поводу иллюстраций к слайдам? Если немного AI-generated картинок были бы к месту, напишите, к каким слайдам и что примерно. У нашей индиго-дизайнерки как раз будет 2 дня выходных. Нагрузите ее.\n  [Fedor](https://t.me/id1906): Полное доверие нашим обожаемым верстальщикам!", "keywords": "<PERSON><PERSON>, Любимая", "created_at": "2025-01-23T08:40:08Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}]}, {"id": "https://t.me/c/**********/731", "url": "https://t.me/c/**********/731", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Может совет попробовать napkin.ai для генерации стиля и картинок\n  [Любимая](https://t.me/<PERSON>): Вечером с Варей вместе попробуем, это интересно!", "keywords": "<PERSON><PERSON>, Любимая", "created_at": "2025-01-23T08:56:26Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}], "updated_at": "2025-01-23T09:04:00Z"}, {"id": "https://t.me/c/**********/732", "url": "https://t.me/c/**********/732", "title": "AI mindset", "language": "ru", "text": "[Любимая](https://t.me/<PERSON><PERSON><PERSON><PERSON><PERSON>): Можно я подокапываюсь немножко?  Я - сферический занудный инженер в вакууме, ничего не знаю про ИИ. Слайд 3. - LLM относятся к пункту Natural Language Processing (NLP).  Слайд 8. - Генеративные модели для визуализации.  Слайд 9. - Внедрение GenAI в проектировании - и дальше опять про LLM. А мне только что рассказали, что LLM - это пункт NLP, а GenAI - это про визуализацию.  Может быть, где-то в начале нужно явно четко написать, что LLM - это одновременно и подмножество GenAI, и подмножество NLP?", "keywords": "Любимая", "created_at": "2025-01-23T09:01:02Z", "entities": [{"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}], "updated_at": "2025-01-23T09:12:53Z"}, {"id": "https://t.me/c/**********/734", "url": "https://t.me/c/**********/734", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuri<PERSON>_voronts<PERSON>): [Любимая](https://t.me/<PERSON><PERSON><PERSON><PERSON><PERSON>) Постепенно надо перебираться из обсуждения слайдов в чате, в обсуждение слайдов в черновике [GenAI: Искусственный интеллект (ИИ) и большие языковые модели (LLM) в сфере проектирования Возможности, примеры, перспективы В фильме «Армагеддон» команда бурильщиков отправилась на астероид спасать Землю. Их за короткий срок обучили базовым навыкам астронавтов. Мы же хотим за короткий срок «прокачать» ...](https://docs.google.com/presentation/d/1ieFl3jK1hHX8GwI9M6MD7sOQgLaAXcQlgQ6OrSrt71c/edit?usp=sharing)", "keywords": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-23T09:11:09Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}], "updated_at": "2025-01-23T09:11:19Z"}, {"id": "https://t.me/c/**********/737", "url": "https://t.me/c/**********/737", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON><PERSON>ов](https://t.me/y<PERSON><PERSON>_v<PERSON>): [<PERSON><PERSON>](https://t.me/il_37) [Любимая](https://t.me/<PERSON>ov) Вам надо сегодня созвониться и добить черновик презентации по слайдам", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-23T10:07:09Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}], "updated_at": "2025-01-23T18:08:16Z"}, {"id": "https://t.me/c/**********/744", "url": "https://t.me/c/**********/744", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Привет, Илья. Я накидал ответы на твои вопросы плюс обсудил с нашим хэдом технологий перспективы использования подобного инструмента. Он очень скептически настроен, что получится сделать что-то рабочее. Думаю, тут без какого-то уже работающего инструмента не получится начать обсуждение. Ответы на вопросы 1. Важен ли функционал разных прав доступа? Разным ролям доступны разные статьи? Да, но он должен как-то наследоваться от прав в Конфлюенс и Слака. То есть человек не должен видеть информацию из статьи в пространстве Конфлюенс, которая для него закрыта. Точно так же, он не должен видеть информацию из приватного канала, куда он не добавлен. 2. Нужен ли поиск по сотрудникам? Тут не совсем понял, что имеется ввиду.  3. Будет ли у нас доступ внутрь контура для отладки? Не готов сейчас ответить. Скорее всего процесс будет выглядеть так: боссы смотрят какой-то MVP, оценивают его полезность и стоимость, принимают решение о подключении (или нет) к нашим системам. 4. Есть ли план внедрения? Сначала Конфлюенс, потом Слак, например. В идеале это должен быть комплексный инструмент. Но, наверное, можно начать со Слака. 5. Есть ли примеры типов вопросов, на которые нужно обязательно настроить поиск в первую очередь? Неа, и, скорее всего, такого не будет. У разных команд, разные вопросы, вряд ли кто-то найдёт ресурс чтобы их собрать. По идее, сервис должен быть универсальным, а не заточен под какие-то конкретные вопросы.\n  [Fedor](https://t.me/id1906): просто нужно понимать, что ни одна существующая система из коробки этого всего не сделает. Делать сейчас свою будет больно и дорого. А еще, существует вероятность, что сейчас 3 месяца будем искать деньги, еще 3 месяца будем планировать, а через месяц когда приступим окажется что коробочное решение того же майкрософта, гугла или амазона наконец отдуплилось и научилось нормально такие штуки из коробки пилить.\n  [Fedor](https://t.me/id1906): А самый наверное главный комментарий - идеологически это напоминает описание майкрософт офиса или гугл ворплейса, до появления майкрософт офиса или гугл воркплейса.  Описанная система выглядит очень логично и естественно. Например, Майкрософт копайлот продает себя как экосистему, выполняющую все вот эти требования скопом. Если в это влезть нам и авиасэйлз, то окажется что мы конкурируем с теми же майкрософт, гугл и всеми сэйлсфорсами на их поляне. Мы конечно очень умные и смелые, но ресурсы у нас весьма ограниченные...", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>", "created_at": "2025-01-24T12:09:12Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}], "updated_at": "2025-01-24T12:30:28Z"}, {"id": "https://t.me/c/**********/746", "url": "https://t.me/c/**********/746", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Я бы посоветовался с Федей, как на такое лучше всего заходить. Потому что самое сложное — для любого решения — как разделять права доступа.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-24T12:09:12Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/748", "url": "https://t.me/c/**********/748", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): [<PERSON><PERSON>](https://t.me/id1906) выше важный контекст и вопрос к тебе. Пожалуйста, внимательно всмотрись и вернись к нам с ценными инсайтами😊", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>", "created_at": "2025-01-24T12:10:29Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/753", "url": "https://t.me/c/**********/753", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): [Enterprise AI Adoption strategy: The Mirror, Not The Magic Wand | <PERSON><PERSON>: My role sits at the intersection of company-wide AI adoption initiatives hands on individual implementation of GenAI projects. This blend of experiences has given me a healthy dose of pragmatism - maybe even skepticism - when it comes to bold proclamations like \"AI automates 80% of tasks!\" or \"Deplo](https://www.linkedin.com/pulse/enterprise-ai-adoption-strategy-mirror-magic-wand-fedor-zomba-9risf/?trackingId=mN2NQrj1Ti%2BQYO9v0w9heQ%3D%3D) Вот пост. Если пост пересказывать двумя предложениями, то коробочные AI решения - дорого, долго и стремно, обучать сотрудников, подход и тренировать AI Mindset'y тоже дорого, долго и стремно, но выхлоп намного конкретнее. А вот этот сложный проект который егор описал я бы взял, прикинул сколько он стоит (в ФМ он бы стоил в районе 800к USD только на разработку и оплату десяти пмов) и спросил, на что еще эти 800к можно потратить. Нампример - мы можем запилить complain generator прямо на авиасэйлс. Задержался рейс? Зааттачь билет и тул выплюнет весь пакет документов, подаст жалобу за вас и вернет деньги за комиссию 10%. Ну или зааттачь билет и сервис просто тебя проконсультирует пошагово как бабло срубить. Шуму такое наведет много, а делать проще в 10 раз.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-24T13:22:55Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}], "updated_at": "2025-01-24T20:44:50Z"}, {"id": "https://t.me/c/**********/755", "url": "https://t.me/c/**********/755", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Кстати, я сегодня познакомился с Ромой Рабиновичем - он фаундер [Цифровая транcформация бизнеса и корпоративного обучения: Цифровая трансформация бизнеса и корпоративного обучения, обучение продуктовых команд](https://neuromap.tech/) и он прямо сам пошел на контакт, приятно. [<PERSON><PERSON>](https://t.me/il_37)  ты его кажется знаешь?\n  [<PERSON><PERSON>](https://t.me/il_37): Да, знакомы с Ромой) он сейчас в Израиле?)", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>", "created_at": "2025-01-24T13:34:01Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/757", "url": "https://t.me/c/**********/757", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuri<PERSON>_voronts<PERSON>): Смотрите, как я научился Good news everyone! Added ICC to ChatGPT as an independent bot. Here is an example: [ChatGPT - Crimes Against Humanity Examples: Shared via ChatGPT](https://chatgpt.com/share/6793bb3d-fed0-800a-8882-4b38cb8c90fb) Here is a bot to try: https://chatgpt.com/g/g-6793b7e45390819184a2b06b68ca156a-icc-gpt\n  [Юрий Воронцов](https://t.me/yuriy_vorontsov): Во-первых, это реальная интеграция агента. И у меня есть несколько задумок: 1. Добавить это в слайды вэбинара, как пример 2. Протестировать работу с таким ботом на своих данных и потом портировать это в Hugging Face Chat\n  [Ilya Briskin](https://t.me/il_37): А поч такая тема жесткая?", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-24T16:13:45Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/764", "url": "https://t.me/c/**********/764", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): [AI in Education: Leveraging ChatGPT for Teaching: Offered by University of Pennsylvania. Working alongside ... Enroll for free.](https://www.coursera.org/learn/wharton-ai-in-education-leveraging-chatgpt-for-teaching)#modules", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-24T21:05:04Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/765", "url": "https://t.me/c/**********/765", "title": "AI mindset", "language": "en", "text": "[<PERSON>or](https://t.me/id1906): [Post-apocalyptic education: What comes after the Homework Apocalypse](https://www.oneusefulthing.org/p/post-apocalyptic-education)", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-24T21:11:50Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/767", "url": "https://t.me/c/**********/767", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): Вот кстати история о которой за пределами моего отдела почти не говорят, а история важная: While making AI features free sounds great, there's a crucial detail being overlooked: Google made it nearly impossible to disable AI features for paying customers. This creates significant challenges for organizations in regulated markets who need granular control over AI usage. Microsoft's Copilot might be more expensive and claims to give enterprises more control, but let's be real - they're also fond of sneaking in AI features by default. As someone in Digital Workplace, playing \"whack-a-mole\" with unexpected AI capabilities popping up in production is becoming our favorite corporate game. Don't get me wrong - I'm a huge advocate for GenAI in enterprise. But I don't appreciate when vendors bypass governance to force adoption. The last thing I want is to wake up to headlines about AI-generated performance reviews causing union protests.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-25T09:54:29Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/768", "url": "https://t.me/c/**********/768", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/id1906): И вот ещё интересный тейк с утра  Today I learned about this area called \"MarTech\" It refers to \"software marketing teams use so they do not have to bug engineers to build stuff like send customized emails, track customers, optimize ads etc\" It's a MASSIVE category, apparently. Thousands of companies and big $$ [<PERSON><PERSON><PERSON><PERSON> Orosz (@GergelyOrosz) on X: Today I learned about this area called \"MarTech\" It refers to \"software marketing teams use so they do not have to bug engineers to build stuff like send customized emails, track customers, optimize ads etc\" It's a MASSIVE category, apparently. Thousands of companies and big $$](https://x.com/GergelyOrosz/status/1882771371715125290)", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-25T10:00:55Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/769", "url": "https://t.me/c/**********/769", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): [<PERSON><PERSON> on LinkedIn: Comprehensive Review of AI Workplace Law and Litigation as We Enter 2025: Ever tried turning off AI features in your enterprise tools lately? Good luck with that. Vendors are increasingly making AI mandatory, and it's creating more…](https://www.linkedin.com/posts/fzomba_comprehensive-review-of-ai-workplace-law-activity-7288917541228351488-6BRx?utm_source=share&utm_medium=member_desktop) утром в куплете, вечером в газете", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-25T13:57:03Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/771", "url": "https://t.me/c/**********/771", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Вот такие истории мне просто мозг взрывают [<PERSON> (@levie) on X: AI Agents have unlimited attention span and you can run them in parallel. Here's an example of using Operator to watch a live video feed and noting every time it sees a black vehicle in the stream. The generalizability means you can bring automation to anything.](https://x.com/levie/status/1883732583068127480) Я фантазировал лет 7 назад что уйду в отпуск и погружусь в computer vision на пару недель чтобы похожий pox запилить для общего развития, но заленился в итоге. А этот черт в 5 кликов в браузере сделал.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-27T06:39:42Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/772", "url": "https://t.me/c/**********/772", "title": "AI mindset", "language": "ru", "text": "[Любимая](https://t.me/<PERSON><PERSON>ov): Вон какая мне реклама прилетела. Кто хочет классификацировать отзывов?", "keywords": "Любимая", "created_at": "2025-01-27T06:47:40Z", "entities": [{"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}], "updated_at": "2025-01-27T07:17:29Z"}, {"id": "https://t.me/c/**********/774", "url": "https://t.me/c/**********/774", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/id1906): [S4E3 Grit & Growth | Co-Intelligence; An AI Masterclass with <PERSON>: Welcome to Grit & Growth’s masterclass on AI — a practical guide for experimenting and engaging with artificial intelligence. <PERSON>, Wharton School associate professor of innovation and entrepreneurship, AI visionary, and best-selling author walks us through the hype, fears, and potential of this transformative and complex technology. AI is reshaping business, society, and education with unprecedented speed. <PERSON> urges business leaders and educators to get in there and figure it out for themselves — to experiment and discover, rather than sitting on the sidelines waiting for AI to come to them. His latest book, Co-Intelligence: Living and Working with AI, is a practical guide for thinking and working with AI so you can determine how and where it can be utilized most effectively. <PERSON><PERSON><PERSON> believes that AI can help entrepreneurs at every stage of business, including coming up with the very idea for the business itself. “AI out-innovates people in most cases,” he says, “so you should probably be using it to help you generate ideas.” In fact, he encourages us to think about AI as a cofounder to bounce ideas off. <PERSON><PERSON><PERSON> also acknowledges that people need to push through those initial couple hours of resistance when exploring AI. “There’s a lot of reasons people stop using AI. It’s weird. It freaks them out. It gives them bad answers — initially. You need to push through, like there is a point of expertise with this, where you start to get what it does and what it doesn’t. Ten hours is my loose rule of thumb for how much time you have to spend using these systems to kind of get it.” <PERSON><PERSON>ck’s Four Essential Rules for Integrating AI into Work and Life 1. Always invite AI to the table. “You don’t know what AI is good for or bad for inside your job or your industry. Nobody knows. The only way to figure it out is disciplined experimentation. Just use it a lot for everything you possibly can.” 2. Be the human in the loop. “The AI is better than a lot of people in a lot of jobs, but not at their whole job, right? And so, whatever you’re best at, you’re almost certainly better than the AI is.” 3. Treat AI like a human. AI models are “trained on human language, and they’re refined on human language. And it just turns out that they respond best to human speech. Telling it and giving tasks like a person often gets you where you need to go.” … (but tell it what kind of human to be) “AI models often need context to operate. Otherwise, they produce very generic results. So, a persona is an easy way to give context. ‘You are an expert marketing manager in India, focusing on technology ventures that work with the US’ will put it in a different headspace than if you say you’re a marketer or if you don’t give it any instructions at all.” 4. Assume this is the worst AI you will ever use.“We’re early, early days still. I mean, there’s a lot of stuff still being built.” Listen to Ethan Mollick’s insights on how AI can level the playing field for startups and how entrepreneurs and teams can use it to enhance creativity, efficiency, and innovation.](https://youtu.be/sOeNWib_nvo?si=KLUit7naKik6hIds) очень советую подкаст- супер толковый.", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-27T07:17:47Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/791", "url": "https://t.me/c/**********/791", "title": "AI mindset", "language": "ru", "text": "[Любимая](https://t.me/<PERSON>ov): Napkin.ai сложно структурировать, когда информаци много - он начинает сокращать текст, отбрасывать часть иерархии", "keywords": "Любимая", "created_at": "2025-01-27T09:44:01Z", "entities": [{"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}], "updated_at": "2025-01-27T18:24:31Z"}, {"id": "https://t.me/c/**********/800", "url": "https://t.me/c/**********/800", "title": "AI mindset", "language": "ru", "text": "[Ю<PERSON><PERSON>](https://t.me/yuri<PERSON>_v<PERSON>): Загнал чатик в GPT, но т.к. он не умеет читать файлы — пока протупил", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-01-28T12:04:41Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/803", "url": "https://t.me/c/**********/803", "title": "AI mindset", "language": "en", "text": "[<PERSON>or](https://t.me/id1906): [<PERSON><PERSON> on LinkedIn: Wiz Research Uncovers Exposed DeepSeek Database Leaking Sensitive…: Remember my post about not sharing sensitive data with DeepSeek? Well, Wiz Research just found their database completely exposed - chat histories, API secrets, operational details, all accessible without authentication. This isn't just another security breach. When you combine basic security holes with frontier AI capabilities, the risks multiply exponentially. Imagine what malicious actors could learn from exposed chat histories and operational data of an AI system. The narrative of \"better, faster, cheaper\" AI development is appealing, but there's no magic here. Trade-offs aren't just about cost and performance - they show up in security, reliability, and trust. I'm impressed by what DeepSeek achieved technically for $6M. But this incident reminds us that \"cheap and fast\" often means cutting corners somewhere. Usually in the boring but crucial parts. Quick look at their LinkedIn job openings tells a story: engineers, data scientists, computer technicians (???), business development managers... but no AppSec…](https://www.linkedin.com/posts/fzomba_wiz-research-uncovers-exposed-deepseek-database-activity-7290616073136054273-G3eY?utm_source=share&utm_medium=member_ios)", "keywords": "<PERSON><PERSON>", "created_at": "2025-01-30T06:40:54Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/805", "url": "https://t.me/c/**********/805", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): У меня сейчас очень интересный митинг был с презенташкой от команды edisonda.com Делали глубокий анализ готовности сотрудников к ai прямо хорошее исследование с интервью и всеми делами\n  [Юрий Воронцов](https://t.me/yuriy_vorontsov): В паблике есть материалы?\n  [<PERSON><PERSON>](https://t.me/il_37): А можно исследование посмотреть?", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-30T13:42:52Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}], "updated_at": "2025-01-31T22:21:17Z"}, {"id": "https://t.me/c/**********/817", "url": "https://t.me/c/**********/817", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): [MasterClass | Achieve More With GenAI: Discover how AI can supercharge your productivity and creativity. Learn from AI experts how to automate tasks and make AI work for you.](https://www.masterclass.com/series/achieve-more-with-gen-ai?utm_source=facebook&utm_medium=cpc&utm_campaign=PP_%7C_INS-GenAi_%7C_T2&ad_id=120216792229600079&adset_id=120216781934830079&campaign_id=120216781934850079&placement=Facebook_Mobile_Reels&site_source_name=%7B%7Bsite.source.name%7D%7D&fbclid=IwY2xjawIKyjRleHRuA2FlbQEwAGFkaWQBqxiL57x7LwEdZ3fw30HSU-y1-efvSpRj0kEW6AMsqasr0MBXw9YFdOaK1QpbbmOou86P_aem_zQ6Zv4nwAbky9MIjUDI7Sw) Cамый крутой чувак про эйай курс сделал на мастерклассе.", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-01T09:37:27Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/819", "url": "https://t.me/c/**********/819", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/il_37): [GitHub - logancyang/obsidian-copilot: THE Copilot in Obsidian: THE Copilot in Obsidian. Contribute to logancyang/obsidian-copilot development by creating an account on GitHub.](https://github.com/logancyang/obsidian-copilot)", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-01T12:47:24Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/822", "url": "https://t.me/c/**********/822", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/il_37): [The Ultimate AI Assistant for Your Second Brain](https://www.obsidiancopilot.com/en)", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-01T12:48:57Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/823", "url": "https://t.me/c/**********/823", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/il_37): И вот такие ребятки [TextCortex: The most advanced enterprise AI platform: An AI platform for enterprise knowledge management––transforming scattered data into actionable insights for smarter decisions.](https://textcortex.com/)\n  [Юр<PERSON>о<PERSON>цов](https://t.me/yuri<PERSON>_voro<PERSON>): По видосам это похоже на то, направление, про которое я говорил.", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-02-01T12:53:53Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/826", "url": "https://t.me/c/**********/826", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Задача: организовать проектный менеджмент съемок комедийного скетча", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-01T22:05:38Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/830", "url": "https://t.me/c/**********/830", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Так выглядит список задач, сгруппированный чатом ГПТ по ответственному", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-01T22:06:45Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}], "updated_at": "2025-02-01T22:07:57Z"}, {"id": "https://t.me/c/**********/833", "url": "https://t.me/c/**********/833", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): А потом очень удобно из чата копировать список и в ЛС его присылать ответственному!", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-01T22:10:15Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/834", "url": "https://t.me/c/**********/834", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/il_37): [Magic Patterns: AI Design Tool: Build beautiful, functional components and frontend applications with generative AI.](https://www.magicpatterns.com/d/mE536SQ1d1dAFTcNsJZF2z)", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-01T22:37:25Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/835", "url": "https://t.me/c/**********/835", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Можно начать собирать кейсы реального применения ИИ для работы. Не видел еще такого.", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-01T22:37:55Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/837", "url": "https://t.me/c/**********/837", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Вот еще кейс: не могу вспомнить ник девочки, которая год назад помогала на съемках. а если бы все-все мои чаты и переписки были бы скормлены (в идеале по АПИ) ЛЛМке, я бы просто спросил \"Пришли ники всех, с кем я общался в октябре 2023 с комментарием, о чем шла речь в переписке с этим ником\"", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-01T23:33:52Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/838", "url": "https://t.me/c/**********/838", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Спросил у чатаГПТ: Приведи примеры конкретных кейсов, когда доступ ИИ к чатам, базам знаний и файловым хранилищам команды из 10 человек повышает эффективность взаимодействия команды. При условии, что у каждого из членов команды есть личная ЛЛМ с доступом ко всем его чатам, базам знаний, файловым хранилищам.", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-02T02:59:38Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}], "updated_at": "2025-02-04T14:15:45Z"}, {"id": "https://t.me/c/**********/839", "url": "https://t.me/c/**********/839", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Чат ответил: 📌 Кейсы, когда каждый член команды имеет личную ЛЛМ с доступом к своим чатам, базам знаний и файловым хранилищам В такой системе личные ИИ-ассистенты действуют как персональные помощники, которые знают контекст работы каждого сотрудника и помогают ему эффективнее взаимодействовать с коллегами. 🔍 1. Мгновенные ответы на вопросы внутри команды 📌 Кейс: Менеджер спрашивает в командном Slack: «Кто последний обновлял техническую документацию по API?» 🤖 Решение: Личные ЛЛМ анализируют чаты и файлы, находят последний апдейт и мгновенно дают ответ. ✅ Результат: Вместо того, чтобы ждать ответа коллег, нужную информацию получают за секунды. 📂 2. Быстрый поиск нужных файлов и обсуждений 📌 Кейс: Дизайнеру нужно найти последние правки UI, но искать в Figma, Slack и Google Drive – долго. 🤖 Решение: Личная ЛЛМ анализирует все источники (чаты, файлы, таск-менеджеры) и выдает ссылку на актуальный макет. ✅ Результат: Экономия 10-15 минут на каждый поиск файлов. 📊 3. Автоматическая подготовка к встречам 📌 Кейс: Перед митингом продукт-менеджеру нужно собрать данные по задачам, обсуждаемым в Slack и Jira. 🤖 Решение: Личная ЛЛМ собирает апдейты из всех источников, формирует краткое резюме и список вопросов. ✅ Результат: Участники приходят на встречу подготовленными, митинги короче и продуктивнее. ✍️ 4. Автоматическое резюмирование чатов и переписки 📌 Кейс: PM пропустил важное обсуждение в Slack и теперь тратит время на чтение сотни сообщений. 🤖 Решение: Личная ЛЛМ анализирует чат и выдает краткое резюме с ключевыми решениями и задачами. ✅ Результат: Время на вникание в контекст сокращается в 3-5 раз. 🗂 5. Автоматическое обновление базы знаний 📌 Кейс: Разработчик вносит изменения в код, но документацию обновлять забывает. 🤖 Решение: Личная ЛЛМ фиксирует изменения, анализирует чаты и файлы, обновляет базу знаний в Notion. ✅ Результат: Документация всегда актуальна, новые сотрудники быстрее вникают в код. ⏳ 6. Автоматическое напоминание о дедлайнах и задачах 📌 Кейс: У разработчика несколько тасков в Jira, но он забыл про срочную задачу. 🤖 Решение: Личная ЛЛМ анализирует таск-менеджер и Slack, напоминает о ближайших дедлайнах. ✅ Результат: Снижается количество просроченных задач. 🎤 7. Транскрибация и анализ голосовых сообщений 📌 Кейс: Внутри команды часто отправляют голосовые сообщения, но текстовый формат удобнее. 🤖 Решение: Личная ЛЛМ автоматически расшифровывает голосовые и выдает текстовые версии с ключевыми моментами. ✅ Результат: Все участники быстрее обрабатывают информацию и не тратят время на прослушивание. 🎯 8. Персонализированные рекомендации по работе 📌 Кейс: PM хочет понять, какие задачи в бэклоге самые важные, но приоритизация занимает много времени. 🤖 Решение: Личная ЛЛМ анализирует прошлые обсуждения, бизнес-цели и предлагает приоритетный список задач. ✅ Результат: Оптимизация работы, меньше перегрузки. 📅 9. AI-помощник по календарю 📌 Кейс: Член команды хочет поставить встречу, но не знает, когда у коллег свободное время. 🤖 Решение: Личная ЛЛМ анализирует календари всех участников и предлагает оптимальное время. ✅ Результат: Быстрое согласование встреч без долгих переписок. 🛠 10. Автоматическое формирование тасков из чатов 📌 Кейс: В обсуждении в Slack возникла идея, но никто не зафиксировал ее в Jira. 🤖 Решение: Личная ЛЛМ распознает важные моменты в переписке и предлагает создать задачу в таск-менеджере. ✅ Результат: Идеи не теряются, команда быстрее внедряет улучшения. 🚀 Как это меняет работу команды? 🔹 Больше автоматизации – меньше ручной работы с файлами, чатами и базами знаний. 🔹 Быстрое принятие решений – нужную информацию получают за секунды. 🔹 Снижение хаоса – всё структурируется и обновляется автоматически. 🔹 Экономия времени – каждую неделю экономится 5-10 часов на поиск информации и рутину. Если нужен разбор кейса под твои задачи – пиши! 🔥", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-02T03:00:37Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}], "updated_at": "2025-02-02T19:22:52Z"}, {"id": "https://t.me/c/**********/840", "url": "https://t.me/c/**********/840", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Реально классные кейсы, по-моему) Особенно: 1, 3, 4, 5 (но не с разработчиком, а с проджектом, продактом, сейлзом), 6, 8, 9", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-02T03:02:22Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/842", "url": "https://t.me/c/**********/842", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/il_37): [Google NotebookLM | Note Taking & Research Assistant Powered by AI: Use the power of AI for quick summarization and note taking, Notebook<PERSON> is your powerful virtual research assistant rooted in information you can trust.](https://notebooklm.google/)", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-02T15:57:55Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/843", "url": "https://t.me/c/**********/843", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): [<PERSON><PERSON>](https://t.me/id1906) вопрос по вебинару по BIM Я пробежался по интернетам и не нашел нормально описанных кейсов, чтобы показать Но я не супер-внимательно искал. Можно тебя попросить порисерчить? У тебя глаз наметан!", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>", "created_at": "2025-02-04T16:58:20Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/845", "url": "https://t.me/c/**********/845", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): [The World's Leading AI Platform for Enterprise | Cohere: Cohere is the leading AI platform for enterprise. Augment your workforce, automate workflows, and enrich customer experiences with secure and scalable AI.](https://cohere.com/) смотрите какие интересные ребята", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-06T05:22:04Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/846", "url": "https://t.me/c/**********/846", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Это прям эй ай майндсет. Мне кажется с их сайта можно надергать идей", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-06T05:23:48Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/847", "url": "https://t.me/c/**********/847", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/il_37): [How AI Will Transform Construction (2025): Free training course on ChatGPT for Project Managers: https://courses.construct-iq.com/courses/ChatGP-for-Project-Managers Discover how artificial intelligence is revolutionizing the construction industry in 2024. From digital construction and construction automation to advanced construction management techniques, AI is reshaping the way projects are planned, executed, and monitored. Learn how cutting-edge technologies like ChatGPT are being integrated into construction processes, paving the way for increased efficiency and productivity. Stay ahead of the curve and explore the future of construction with AI.](https://www.youtube.com/watch?v=-kS2kuprIzI) Из этой презентации нам есть чего утащить, как думаешь? [Fedor](https://t.me/id1906)\n  [Fedor](https://t.me/id1906): Я пробежался по первой половине видео, пока ничего принципиально нового. Анализ данных, business writing, project management, learning.", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>", "created_at": "2025-02-06T16:49:42Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/850", "url": "https://t.me/c/**********/850", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Там в общем чувак говорит, что эйай особо не поможет, но в целом клиенты смотрят на construction costs в первую очередь и если благодаря ai удастся срезать косты, то зашибись.", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-06T18:05:28Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/853", "url": "https://t.me/c/**********/853", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Ну я бы сказал в общем что этот чувак может и шарит в констракшн но вообще не рубит в genai", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-06T18:13:05Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/854", "url": "https://t.me/c/**********/854", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): [ИИ и машиночитаемость в стройке | Поговорим за BIM | Ольга Кутузова: Версия в VK в Telegram канале InfraBIM.Pro: https://t.me/InfraBIM/183 Аудио версия: https://t.me/InfraBIM/184 Онлайн-курсы Робур, Civil 3D, Infraworks, Revit ИССО и Autodesk Subassembly Composer https://infrabim.pro/online-course 00:00 Вводное слово 01:23 Вступление про ИИ 02:45 Чем занимается Ольга 04:45 Машиночитаемость\\Машинопонимаемость\\Машиноинтерпретиумость в чем разница 12:30 Перевод нормативных документов в машиночитаемость  20:55 Связь базы требований с САПР 23:30 Машинопонимаемые требования для автоматической проверки ЦИМ 39:18 Проверка на коллизии по нормативным документами в Нанокаде 41:30 Зачем это всё в BIM (ЦИМ) 46:44 Государственное направление в машинопонимании. Реестр требований 51:05 Насколько применим ИИ в строительстве 58:05 Чат бот по нормативным требованиям 1:00:53 Большие языковые модели для строительства и нормативки 1:06:21  Анализ текстовой части проектной документации 1:17:15 К чему это всё идёт?](https://youtu.be/c3Hgb3AbZuE?si=Q6R5R6AyNC92wFVz)", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-06T19:52:17Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/855", "url": "https://t.me/c/**********/855", "title": "AI mindset", "language": "en", "text": "[<PERSON>or](https://t.me/id1906): https://x.com/mindbranches/status/1887606988831662233?s=52", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-08T07:33:19Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/857", "url": "https://t.me/c/**********/857", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): [How real-world businesses are transforming with AI – with 50 new stories: Updated February 5, 2025: The post contains 50 new customer stories, which appear at the beginning of each section of customer lists. The post will be updated regularly with new stories. One of the highlights of my career has always been connecting with customers and partners across industries to learn how they are using technology...](https://blogs.microsoft.com/blog/2025/02/05/https-blogs-microsoft-com-blog-2024-11-12-how-real-world-businesses-are-transforming-with-ai/)", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-08T07:35:00Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/858", "url": "https://t.me/c/**********/858", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): When foundational models become commoditized through open source, the real profit will come from industry-specific applications. Интересная точка зрения [From the legaltech community on Reddit: Explore this post and more from the legaltech community](https://www.reddit.com/r/legaltech/comments/1ijicb4/open_source_ai_wont_be_enough_distribution_and/) [Fedor](https://t.me/id1906) что думаешь?\n  [Fedor](https://t.me/id1906): Отличный тейк, согласен почти полностью\n  [Felix T](https://t.me/Felixxx89): Да оч интересно примерно тоже самое сегодня от родственника из Deloitte слышал\n  [Fedor](https://t.me/id1906): Мне кажется что чувак упускает главный затык: specialized tool все равно кто-то должен будет внедрять, а людей с релевантными скиллами очень мало.", "keywords": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>", "created_at": "2025-02-08T16:09:14Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON> T](https://t.me/Felixxx89)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/862", "url": "https://t.me/c/**********/862", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Главный боттонек в этой революции - мало business oriented ai engineers", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-08T17:21:38Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/864", "url": "https://t.me/c/**********/864", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): EU наш: в смысле что со второго февраля действует обязательство всех сотрудник обучать ai грамотности", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-09T08:05:13Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/865", "url": "https://t.me/c/**********/865", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): The EU AI Act's AI literacy requirements (Article 4) and prohibited AI practices (Article 5) became legally binding on February 2, 2025, marking the first phase of the regulation's implementation. Here’s a detailed breakdown: --- ### 1. AI Literacy Requirements Under Article 4  - Who is affected? Providers and deployers of AI systems (e.g., developers, companies using AI). - Key obligations:   - Ensure staff and third parties involved in operating AI systems have sufficient AI literacy, tailored to their roles, technical knowledge, and context of use.   - Promote understanding of AI’s opportunities, risks, and potential harms, including compliance with ethical and legal standards.   - Training programs must address technical skills, data governance, and intersectional risks (e.g., privacy, cybersecurity). - Scope: Applies to all AI systems, regardless of risk level (e.g., high-risk or minimal-risk systems). --- ### 2. EU Support Mechanisms for Compliance - Living Repository: The EU AI Office created a dynamic database of AI literacy practices (e.g., training modules, workshops) from organizations like AI Pact signatories. While not a compliance guarantee, it fosters knowledge-sharing. - Guidelines: The Commission published non-binding guidelines to clarify the AI system definition and prohibited practices, with updates planned based on stakeholder feedback. - Webinars and Consultations: Events like the AI Pact webinar on February 20, 2025, aim to guide organizations in implementing Article 4. --- ### 3. Penalties and Enforcement - AI Literacy (Article 4): No direct fines, but non-compliance may influence penalties for other breaches (e.g., high-risk system violations). - Prohibited Practices (Article 5): Fines up to €35 million or 7% of global turnover for banned AI uses (effective August 2025). --- ### 4. Prohibited AI Practices Under Article 5 The AI Act bans AI systems that: 1. Manipulate behavior using subliminal/deceptive techniques (e.g., targeting vulnerable groups). 2. Social scoring for public/private purposes (e.g., evaluating individuals based on social behavior). 3. Predict criminal behavior via profiling or personality traits. 4. Untargeted facial scraping from the internet/CCTV to build databases. 5. Real-time biometric identification in public spaces by law enforcement (with narrow exceptions). 6. Emotion recognition in workplaces/education (exceptions for medical/safety uses). --- ### 5. Implementation Timeline - February 2, 2025: AI literacy (Article 4) and prohibited practices (Article 5) take effect. - August 2, 2025: Governance rules for general-purpose AI models apply. - August 2026: Most obligations (e.g., high-risk systems) become enforceable. - August 2027: Extended deadlines for high-risk systems in regulated products. --- ### Recommendations for Organizations - Audit AI systems to identify prohibited practices and align with exceptions (e.g., medical use for emotion recognition). - Develop training programs integrating AI literacy with existing frameworks (e.g., GDPR compliance). - Engage with EU guidelines and the living repository to adopt best practices. For further details, refer to the [EU AI Act guidelines]([The Commission publishes guidelines on AI system definition to facilitate the first AI Act’s rules application: The guidelines on the AI system definition explain the practical application of the legal concept, as anchored in the AI Act.](https://digital-strategy.ec.europa.eu/en/library/commission-publishes-guidelines-ai-system-definition-facilitate-first-ai-acts-rules-application)) and the [AI literacy repository](https://digital-strategy.ec.europa.eu/en/library/living-repository-foster-learning-and-exchange-ai-literacy).", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-09T08:05:13Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/866", "url": "https://t.me/c/**********/866", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): [This 20+ AI Agent Team Automates ALL Your Work (GPT-01) (Relevance AI): 👇 Join my community and get the template of this video, all my other templates, tech help & more: https://bit.ly/bensaicommunity 👇 Get the Agent System Overview (Figjam): https://benvansprundel.gumroad.com/l/xdgpur 💼 AI implementation or consulting? 📆 Book me in here: https://www.benvansprundel.com/ 🔗 My links: Linkedin: https://www.linkedin.com/in/benvansprundel/ X: https://x.com/ben_vs92 💻 Softwares I use (some of these are affiliate-links, thanks!): Relevance AI: https://relevanceai.com/?via=ben ElevenLabs: https://try.elevenlabs.io/fps0xgonqagd Sendspark: https://sendspark.com/?via=ben-ai Make.com: https://www.make.com/en/register?pc=benai Agentive: https://agentivehub.com/ Dumpling AI: https://www.dumplingai.com/?via=ben-ai Chapters: 00:00 - Intro 01:42 - Live Demo 10:22 - Agent System Overview 21:30 - Director Agent Setup 37:36 - Research Manager Agent + Subagents Setup 52:26 - Comm Manager Agent + Subagents Setup 1:02:02 - Project Manager Agent + Subagents Setup 1:07:05 - Content Manager Agent + Subagents Setup 1:09:50 - How to Schedule Automations 1:14:23 - Whatsapp Trigger Setup 👋🏼 About me: I'm Ben, a 3-time founder & Dutch AI entrepreneur. In 2023, I started my AI automation agency https://www.benvansprundel.com/. In this channel, I help businesses, professionals & aspiring AI entrepreneurs to build powerful no-code AI Agents & Automations for 10x efficiency. I post new useful AI use cases for businesses weekly.](https://youtu.be/Lj5fyDX01v8?si=xLkmIgCgV7YnvH_t) for inspiration", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-10T09:59:37Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/867", "url": "https://t.me/c/**********/867", "title": "AI mindset", "language": "uk", "text": "[<PERSON><PERSON>](https://t.me/id1906): Просто бомба\n  [<PERSON><PERSON>](https://t.me/il_37): Просто Зомба!", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>", "created_at": "2025-02-10T10:06:49Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/869", "url": "https://t.me/c/**********/869", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): https://n8n.io Юра, а ты видел эту штуковину? n8n.io - a powerful workflow automation tool: n8n is a free and source-available workflow automation tool\n  [Юр<PERSON> Воронцов](https://t.me/yuriy_vorontsov): Про эту слышал, видел аналоги", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-02-10T13:58:18Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/870", "url": "https://t.me/c/**********/870", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Сергей, привет! Программа буткемпа есть здесь. Следующий поток стартует через недели две примерно SalesAI Bootcamp - практическое использование Ai для задач продаж | Notion: Привет! Мы, ведущие специалисты по практическому применению ИИ в бизнесе — Дмитрий Коробовцев и Павел Журавлев запустили SalesAi Bootcamp — программу, посвящённую практическому использованию ИИ для решения актуальных задач в продажах.", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-10T14:11:03Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}]}, {"id": "https://t.me/c/**********/872", "url": "https://t.me/c/**********/872", "title": "AI mindset", "language": "ru", "text": "[Юрий Воронцов](https://t.me/yuriy_vorontsov): Ого какие темы постит Anthropic. Не могу не поделиться.  Ребята выпустили исследование как же используется AI сейчас и каков его экономический вклад (хотя тут я не очень понял). Цифры крайне занимательные. Картиночки приложу.  Основные выводы: - большая часть использования(36%) AI приходится на задачи связанные с technical documentation etc и software development (кто бы мог подумать, да?) - небольшой скос в сторону именно не полной автоматизации, а так называемой аугументации задач, это когда вы проверяете или дополняете задачу с помощью AI (я думаю разрыв в сторону аугументации будет расти очень сильно) - большая часть задач которые решаются категоризированы к профессиям среднего/высокого достатка. И практически ничего с самыми низкими и высоко оплачиваемыми ролями. Тут я считаю просто скос из-за аудитории, которая использует их Claude. Вряд ли дворники или CEO доверяют свои задачи AI. Пока что.  Крайне советую прочесть, дает много инсайтов как и по областям применения и того, что действительно может работать.  Я все так же считаю: - полная автоматизация с помощью агентов будет очень нескоро даже простых задач (см как же быстро вошли в жизнь self driving cars) - все больше применения будет в аугументации задач и улучшении результатов (а-ля инструмент помощи) - агенты == зло, базовые автоматизации круть (отдельные блоки в процессах, latency, качество определенных решений - условно замена старой технологии на новую)\n  [Fedor](https://t.me/id1906): Офигенно!", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-02-10T17:20:56Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}], "updated_at": "2025-02-12T15:26:04Z"}, {"id": "https://t.me/c/**********/880", "url": "https://t.me/c/**********/880", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/id1906): [<PERSON><PERSON> on LinkedIn: Two founders. $3.6M in funding. One \"tedious\" task.  That's how… | 30 comments: Two founders. $3.6M in funding. One \"tedious\" task.  That's how disruption always starts.  Why This Matters:  → Perceptis AI automated proposal writing  →… | 30 comments on LinkedIn](https://www.linkedin.com/posts/usmans_two-founders-36m-in-funding-one-tedious-activity-7295728429897818116-ZxCx?utm_source=share&utm_medium=member_ios&rcm=ACoAAAhZ5JEBT9b0Pp349B_7noUrVrbpOkA7FDs)", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-13T12:13:04Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/882", "url": "https://t.me/c/**********/882", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Заб<PERSON>вный, кстати, факт: я на эту тему не раз слышал высказывания в частном порядке, и почему-то мало пишут любители пообсуждать ИИ и его текущий экономический эффект. Речь о техническом долге. У любой компании с большими ИТ-ресурсами это серьезная (хотя не наблюдаемая снаружи) проблема: специалистов вечно не хватает на решение новых задач, на развитие и подержание софта. И всегда в какой-то момент возникает ситуация, при которой оказывается, что старый код тормозит развитие, не позволяет внедрять новшества и должен быть переписан.  Только вот его оказывается неприлично много: With over 2.41 trillion of costs in the United States alone, tech debt isn’t just an IT problem; it’s a business liability that requires CEOs’ attention. И вот тут-то ИИ оказывается фантастически эффективен. Рефакторинг софта с помощью ИИ позволяет относительно легко решать проблему: мне известен случай одной отечественной компании (не уверен, что могу называть ее), где “погашение долга” оценивалось буквально под сотню человеко-лет и вызывало панику у СТО. Однако группа меньше чем из 10 человек с помощью ИИ полностью разобралась с долгом за несколько месяцев. Так что есть может и не самые романтические и крутые задачи — но огорчительно дорогие — и там ИИ себя оправдывает сторицей. Самое смешное, что в статье по ссылке говорят не про ИИ как средство решения, а про то,  как надо техдолгом грамотно управлять в преддверии перехода но новое поколения софта с ИИ-фичами:)  https://sloanreview.mit.edu/article/how-to-manage-tech-debt-in-the-ai-era/", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-18T07:24:21Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/883", "url": "https://t.me/c/**********/883", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): мы же встретимся живьем за ai mindset поговорить? Можно в четверг, или завтра?\n  [Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voro<PERSON>ov): Я за!", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-02-18T07:29:45Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/886", "url": "https://t.me/c/**********/886", "title": "AI mindset", "language": "en", "text": "[<PERSON>or](https://t.me/id1906): Я в 9 свободен! Ялла!\n  [<PERSON> T](https://t.me/Felixxx89): Мне еще в Беер Шева возвращаться поздно 9 поздновато", "keywords": "<PERSON><PERSON>, <PERSON>", "created_at": "2025-02-18T07:54:03Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON> T](https://t.me/Felixxx89)"}], "updated_at": "2025-02-18T07:54:11Z"}, {"id": "https://t.me/c/**********/889", "url": "https://t.me/c/**********/889", "title": "AI mindset", "language": "ru", "text": "[<PERSON>or](https://t.me/id1906): Я к вам приду чуть попозже\n  [Юрий Воронцов](https://t.me/yuri<PERSON>_voro<PERSON>): Тебя ждать?", "keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-02-18T09:40:03Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/895", "url": "https://t.me/c/**********/895", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Как я пошла учиться в ИИ лабу. Я как-то еще пару месяцев назад рекламировала вот этих ребят - AI Mindset. Они организовывали уже шестую лабу по системной работе с ИИ - ну типа не просто \"научись применять ИИ\" или \"сделай бота\", а выстрой систему из разных инструментов.  Это меня зацепило и я решила сама пойти к ним учиться. Потому что по чесноку, я ппц какой хаотик. У меня заметки валяются в 20 разных местах, и я до сих пор не знаю, сколько из поставленных на 2024 год целей я выполнила, потому что я не могу найти, куда я их записала 😂 Ну и с нейронками у меня так же. Чуваки обещали научить именно ИИ майндсету.  Когда ты, глядя на любую новую задачу, в первую очередь думаешь, как нейронки могут облегчить ее выполнение. (желательно чтобы они вообще сделали ее сами, пока ты идешь бахнуть пивка с другими нейроработягами) Ну и в общем, 25го января мы погнали учиться 4 недели. Структура примерно такова: Есть общий чат, есть несколько дополнительных чатов по отдельным направлениям, которые интересны. Я выбрала автоматизацию и чат-боты. Каждым направлением заведует отдельный эксперт. Всего во время обучения было 8 созвонов (4 воркшопа, 4 коворкинга, где можно онлайн поработать вместе с другими участниками, и 4 office hours, где разбираются вопросы участников.) А еще у лабы - гигантская и очень тщательно структурированная база знаний, сделанная в Obsidian (черт, пора уже в него погрузиться, адепты обсидиана меня туда тащат второй год) Там хранятся записи созвонов, их транскрипции и саммари, список инструментов и инструкции и тд Я не успевала участвовать в созвонах реалтайм (потому что у меня всратый часовой пояс сейчас и переезды). Но смотрела записи и общалась в чатах. На примере одной маленькой автоматизации, которую я сделала, опишу процесс: - закинула в чатик свою задачу (когда на почту падает письмо с ключевым словом в заголовке, кидать мне пуш в телегу) - эксперт быстренько предложил несколько вариантов автоматизации готовыми сервисами - покопалась, поняла что привязать к ним gmail не получится из-за того что от сервиса автоматизации нужно согласие получать пересылку - обсудили еще раз - пришли к выводу, что можно сделать руками через сервис гугла Apps Script и javascript кода, который мне напишет чат джипити. Я пошла делать. Процесс занял 20 минут и всего 3 ошибки от Apps Script, с которыми мне, опять же, помог чат джипити. Все работает! Дааааа, и я кстати вообще не умею погромировать, тем более - на javascript. 🌚 Ну и некоторые мои выводы по итогам лабы (меня попросили написать честные впечатления): - формат мощный и реально интенсивный. Чтобы получить максимум, надо на 4 недели вгрузиться максимально. Такой ИИ-выживач. - это сильно проще, чем самому ковыряться. Есть об кого подумать. И есть мотивация не бросить на полпути. - на мой взгляд, эта лаба не подойдет полным гуманитариям. Ну, скажем если вы впервые слышите слова типа \"токен\", \"API\", \"гитхаб\", вам будет очень сложно. Надо быть хоть немножко технарями или готовыми погрузиться. Если один вид джаваскрипта вызывает у вас желание беспомощно заплакать, вам будет тяжело. 😭 - Если у вас аллергия на английский язык - будет тяжело тоже. В ИИ сфере очень много всего пишется на английском, во время лабы презы тоже были на английском (спикеры говорили на русском).  Короче, лет ми спик фром май харт. Если вы из тех кто \"рррряяяя хватит англицизмов\" (у меня есть такие подписчики), то лаба не для вас. Да, собсно 25 марта у них следующий поток, если то, что я написала, вам отзывается, можете вписаться на борт вот тут.", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-20T07:22:50Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/896", "url": "https://t.me/c/**********/896", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): <PERSON><PERSON><PERSON>я<PERSON><PERSON>, я тут играюсь с Claude 3.7 и это полный пипец!  Такого я ещё не видел.  [A new generation of AIs: <PERSON> 3.7 and <PERSON><PERSON> 3: Yes, AI suddenly got better... again](https://open.substack.com/pub/oneusefulthing/p/a-new-generation-of-ais-claude-37?r=ltq7b&utm_medium=ios) Он реально умнее на голову Claude 3.5 и такой же милый (кажется)", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-24T21:16:23Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/900", "url": "https://t.me/c/**********/900", "title": "AI mindset", "language": "en", "text": "[<PERSON>or](https://t.me/id1906): https://x.com/deedydas/status/1894110678027571412 твитер кишит ими", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-24T21:36:13Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/901", "url": "https://t.me/c/**********/901", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/id1906): Мне понравился такой: Make me an interactive time machine artifact, let me  travel back in time and interesting things happen. pick unusual times I  can go back to… и Add graphics to it", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-24T21:37:53Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/902", "url": "https://t.me/c/**********/902", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): [<PERSON><PERSON><PERSON>’s Claude 3.7 Sonnet is available on Vertex AI | Google Cloud Blog: Claude 3.7 Sonnet, Anthropic’s most intelligent model to date and the first hybrid reasoning model on the market, is available on Vertex AI. Get started today.](https://cloud.google.com/blog/products/ai-machine-learning/anthropics-claude-3-7-sonnet-is-available-on-vertex-ai?linkId=13111326) Они еще и в мультиклауд пошли к гуглу. Вау вау. Запомните этот твит: антропик к концу года по капитализации догонит OpenAI.", "keywords": "<PERSON><PERSON>", "created_at": "2025-02-24T23:03:22Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/907", "url": "https://t.me/c/**********/907", "title": "AI mindset", "language": "ru", "text": "[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voro<PERSON>): я то сам в настройках кубера пока не всё знаю, например, не очевидно, что есть специальный флаг, который обязывает ставить самые свежие версии докер контейнеров", "keywords": "<PERSON><PERSON><PERSON>", "created_at": "2025-02-25T09:27:40Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********/914", "url": "https://t.me/c/**********/914", "title": "AI mindset", "language": "en", "text": "[<PERSON><PERSON>](https://t.me/id1906): [Crossing the uncanny valley of conversational voice: At Sesame, our goal is to achieve “voice presence”—the magical quality that makes spoken interactions feel real, understood, and valued.](https://www.sesame.com/research/crossing_the_uncanny_valley_of_voice)#demo", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-04T12:30:21Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}], "updated_at": "2025-03-04T13:11:42Z"}, {"id": "https://t.me/c/**********/916", "url": "https://t.me/c/**********/916", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Друзья, похвастаюсь - я сейчас провожу тестовый четырехчасовой воркшоп с нашими head of procurement и head of supply chain. Оба пищат от восторга. Сейчас переходим а практической части, не знаю, как оно обернется, но это можно как есть заворачивать и продавать.\n  [<PERSON><PERSON>](https://t.me/il_37): Можно будет посмотреть запись?", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>", "created_at": "2025-03-09T13:15:14Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}], "updated_at": "2025-03-09T13:31:37Z"}, {"id": "https://t.me/c/**********/920", "url": "https://t.me/c/**********/920", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Если вкратце, я только что провел четырехчасовой тренинг, который затянулся на лишние полтора часа - те, кого я обучал хотели еще и еще. Я объяснял нашим head of procurement и head of supply chain про helpful questions и problem questions, ограничение языковых моделей и используя знания во второй части мы придумывали об AI как заработать больше денег для компании. Я очень волновался: делал эту презентацию вчера до трех часов ночи и все утро. Но получилось прекрасно: оба head’a пищат от восторга, им супер полезно и идеи упали на благодатную почву. Очень уставший и очень довольный.", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-09T17:07:53Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}], "updated_at": "2025-03-10T03:24:46Z"}, {"id": "https://t.me/c/**********/922", "url": "https://t.me/c/**********/922", "title": "AI mindset", "language": "en", "text": "[Fedor](https://t.me/id1906): Our friend <PERSON><PERSON>, jointly with his partners from the U.S., is organizing The Venture Award Israel in early April in Tel Aviv.  We encourage investment-ready early-stage startups to apply!  The top 12 startups will be invited to the award ceremony to pitch to 10+ Israeli VCs and syndicates. More details can be found here:\" #theventures #futuround #startupaward #startupnation #meetthejudges | Oleg <PERSON>: 🚀 𝗠𝗲𝗲𝘁 𝘁𝗵𝗲 𝗝𝘂𝗱𝗴𝗲𝘀 𝗼𝗳 𝗧𝗵𝗲 𝗩𝗲𝗻𝘁𝘂𝗿𝗲𝘀 𝗔𝘄𝗮𝗿𝗱 𝗜𝘀𝗿𝗮𝗲𝗹! 🚀 As we gear up for 𝗧𝗵𝗲 𝗩𝗲𝗻𝘁𝘂𝗿𝗲𝘀 𝗔𝘄𝗮𝗿𝗱 𝗜𝘀𝗿𝗮𝗲𝗹, we’re thrilled to introduce the powerhouse panel of judges who will evaluate and support the next wave of groundbreaking startups 𝗼𝗻 𝗔𝗽𝗿𝗶𝗹 𝟯𝗿𝗱 𝗶𝗻 𝗧𝗲𝗹 𝗔𝘃𝗶𝘃. These industry leaders bring extensive expertise in 𝘃𝗲𝗻𝘁𝘂𝗿𝗲 𝗰𝗮𝗽𝗶𝘁𝗮𝗹, 𝗲𝗻𝘁𝗿𝗲𝗽𝗿𝗲𝗻𝗲𝘂𝗿𝘀𝗵𝗶𝗽, 𝗶𝗻𝗻𝗼𝘃𝗮𝘁𝗶𝗼𝗻, and 𝘀𝗰𝗮𝗹𝗶𝗻𝗴 𝗯𝘂𝘀𝗶𝗻𝗲𝘀𝘀𝗲𝘀—ensuring that every award finalist receives the attention it deserves. 🌟 𝗠𝗲𝗲𝘁 𝘁𝗵𝗲 𝗝𝘂𝗱𝗴𝗲𝘀: 🔹 <PERSON><PERSON>-Brenner – Partner, Showcase IL, and Founder, <PERSON><PERSON><PERSON><PERSON><PERSON> 🔹 <PERSON> (ADV) – Managing Partner, Horizon Capital 🔹 Anat Tila Cherni – Managing Partner, Aristagora VC 🔹 Ofer SHOSHAN - Venture Partner, OurCrowd 🔹 <PERSON> – Co-founder and Managing Partner, SeedIL Ventures 🔹 <PERSON> – Partner, 5 Eyes, Angel Investor 🔹 <PERSON><PERSON> – CEO, TEClub VC 🔹 <PERSON> – Co-founder and CEO, Veligera Investment Group 🔹 <PERSON><PERSON><PERSON> – Head of Programming, MassChallenge If you're an early-stage startup 𝗽𝘂𝘀𝗵𝗶𝗻𝗴 𝘁𝗵𝗲 𝗯𝗼𝘂𝗻𝗱𝗮𝗿𝗶𝗲𝘀 𝗼𝗳…", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-09T18:23:32Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/923", "url": "https://t.me/c/**********/923", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): сделал себе пессимистикбота, который на любую реплику втирает умно про бренность всего сущего с легким британским акцентом. https://elevenlabs.io/app/talk-to?agent_id=IqZZ8zqQ9aq5eUv1JnQH В удивительное время живем.  Не пересылайте пожалуйста ссылку - я за каждый разговор плачу чуть-чуть кредитами, которых у меня ограниченное число.", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-11T00:01:30Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/924", "url": "https://t.me/c/**********/924", "title": "AI mindset", "language": "ru", "text": "[<PERSON><PERSON>](https://t.me/il_37): Хаверим, шалом. Планируем в Тель Авиве делать серию митапов. Тематика: - Project Management  - AI - Стартапы  - GameDev Ищем интересных спикеров. Кому интересно выступить спикером - напишите, пожалуйста, в личные сообщения.\n  [<PERSON><PERSON>](https://t.me/il_37): [Fedor](https://t.me/id1906) fyi", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>", "created_at": "2025-03-11T14:36:12Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/926", "url": "https://t.me/c/**********/926", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Мне страшно, помогите написать питч что я хороший спикер на тему пожалуйста", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-12T23:26:23Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/929", "url": "https://t.me/c/**********/929", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Так норм: Привет! Я внедряю GenAI в компании из Fortune 500 и хотел бы поделиться реальным опытом - не теорией, а практикой с граблями и успехами. Интересно обсудить, как AI меняет корпоративные процессы, и какие подходы реально работают. В последнее время много про этом пишу у себя в линкедине.", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-12T23:57:36Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}], "updated_at": "2025-03-13T10:27:18Z"}, {"id": "https://t.me/c/**********/931", "url": "https://t.me/c/**********/931", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Ребята, важный корпоинсайт: сейчас примерно до 15 марта во всех больших корпо паника - нужно верстать бюджеты на конференции и обучения на 25-й иначе в следующем году денек не дадут. Следующие пару дней желающих дать денег сильно больше чем обычно.", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-13T06:46:35Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/932", "url": "https://t.me/c/**********/932", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Ну там тоже, наверное, есть цикличность, от раздачи венчурного капитала или чего там. Но в тех, кто сам деньги зарабатывает, это стандарт: в 4м квартале экономим на всём, даже на ручках, и уж точно на хайринге, в 1м начинаем жить по новому бюджету, где всё это есть, а дальше уже как пойдёт, но в последние годы идёт одинаково и к 3-4му кварталу опять режим экономии.", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-13T06:46:36Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/933", "url": "https://t.me/c/**********/933", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Надо ускоряться с публичными демками друзья, чтобы не продолбать хайп. Мы с Юрой уже 2 недели такие штуки тестируем", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-13T07:20:34Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/934", "url": "https://t.me/c/**********/934", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): 🔥ИИ-нетворкер Вау, тут прямо отличный нетворкинг сервис запустили. Звонишь ИИ, он тебе задает несколько вопросов про твою историю (работа, учеба, задачи, которые сейчас решаешь) и потом он тебя “мэтчит” с человеком, который может быть тебе полезен. Знаете, как бывает - говоришь с кем-то, а он тебе потом “ой, тебе надо познакомиться с Х” - и соединяет вас. Тут тоже самое, только ИИ! Почему это круто? Борди (название сервиса) проинтервьюировал уже тысячи человек и у него огромная база контактов, а, поскольку технология еще довольно новая, у людей есть ощущение “теплого” интро и люди охотно идут на контакт.  Сразу идея использования - если вы сейчас ищите работу и не очень уверены, кем стать - расскажите ему свою историю и он предложит соединить вас с людьми, которые прошли похожий путь и смогли себя найти. Или же, если хотите на работу в определенную компанию - узнайте, вдруг у него есть там контакты - но не с целью получить таким образом реферрала, а узнать у человека, как он туда попал, какая там культура. Может быть не конкретная компания, а похожая - все будет полезно и хорошо. В качестве дополнительного бонуса использования Броди - интервью проходит на английском и он повторяет за вами то, что вы ему рассказали, но своими словами - такая техника отлично работает, начинаешь сам лучше понимать, чем ты занимаешься и какие у тебя сейчас проблемы. Если вы зависли - не стесняйтесь спрашивать у него самого, чем он может помочь и что он бы предложил в вашей ситуации, может быть, какие-то идеи у него возникнут. Чтобы начать общение откройте страничку Борди в Линкедине, и напишите ему сообщение с вашим имейлом и номером телефона. Он сразу же перезвонит. Потом можно забронировать такой звонок на раз в неделю. Нонтек работа в хайтеке @nontechitech", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-13T07:20:34Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/936", "url": "https://t.me/c/**********/936", "title": "AI mindset", "language": "en", "text": "[<PERSON>or](https://t.me/id1906): https://elevenlabs.io/app/talk-to?agent_id=KRIcTJBrLXVZrMSlkEhu\n  [<PERSON> T](https://t.me/Felixxx89): Bag словил :-(", "keywords": "<PERSON><PERSON>, <PERSON>", "created_at": "2025-03-13T07:21:24Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON> T](https://t.me/Felixxx89)"}]}, {"id": "https://t.me/c/**********/942", "url": "https://t.me/c/**********/942", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Так, ребята, у меня получилось. Это просто пушка. Послушайте аудио или просто попробуйте сами.", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-13T14:51:32Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}], "updated_at": "2025-03-13T14:54:04Z"}, {"id": "https://t.me/c/**********/943", "url": "https://t.me/c/**********/943", "title": "AI mindset", "language": "mt", "text": "[Fedor](https://t.me/id1906): https://elevenlabs.io/app/talk-to?agent_id=KRIcTJBrLXVZrMSlkEhu попробуйте тоже", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-13T14:51:58Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********/945", "url": "https://t.me/c/**********/945", "title": "AI mindset", "language": "ru", "text": "[Fedor](https://t.me/id1906): Помните, как я говорил, что нужно на детях тестировать, чтобы понять хороший продукт, или не очень. Так вот, я ни разу не врал и не шутил. У нас есть продукт, друзья, который охуенный.", "keywords": "<PERSON><PERSON>", "created_at": "2025-03-13T16:33:56Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********#groupped:part1", "url": "https://t.me/c/**********/620", "title": "AI mindset", "language": "sr", "text": "https://t.me/c/**********/620:\n2025-01-20T10:34:30Z\n[<PERSON><PERSON>](https://t.me/il_37): А я не смогу( А завтра когда можешь?\n\n\nhttps://t.me/c/**********/621:\n2025-01-20T12:15:02Z\n[<PERSON><PERSON>](https://t.me/il_37): https://docs.google.com/presentation/d/1BQTxB50pZwr-52ua87Kq88LiV8f1aZeJ01G7ysJLBkE/edit#slide=id.g3271e60313b_0_11 [Юр<PERSON> Воронцов](https://t.me/yuriy_vorontsov) [Любимая](https://t.me/AnastasiaVorontsov) [Fedor](https://t.me/id1906) посмотрите, пожалуйста. Набросал черновое оглавление презентации ОК / не ОК?\n\n\nhttps://t.me/c/**********/622:\n2025-01-20T12:15:47Z\n[Ilya <PERSON>riskin](https://t.me/il_37): Давайте быренько соберем, и я покажу в чатике с заказчиком)\n\n\nhttps://t.me/c/**********/623:\n2025-01-20T14:50:42Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): по огравлению ещё мало что понятно\n\n\nhttps://t.me/c/**********/624:\n2025-01-20T15:01:51Z\n[Ilya Briskin](https://t.me/il_37): Какие пункты нуждаются в дитализации, на твой взгляд? Давай \"разошьем\" их\n\n\nhttps://t.me/c/**********/625:\n2025-01-20T15:20:21Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Без содержания слайдов — я не понимаю\n\n\nhttps://t.me/c/**********/626:\n2025-01-20T15:21:24Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Кстати, вот тут примеры для Лигал: Based on workflows ([Harvey – Workflows: Streamline and automate tasks with end-to-end tools.](https://www.harvey.ai/products/workflows)) and Prompt Libraries: A collection of expertly-curated prompts and examples at your fingertips. (https://www.harvey.ai) I think they collected a set of curated prompts to do some routine tasks: •⁠  ⁠translation •⁠  ⁠⁠critical analysis •⁠  ⁠⁠comparison •⁠  ⁠⁠AI search\n\n\nhttps://t.me/c/**********/627:\n2025-01-20T16:55:50Z\n[Fedor](https://t.me/id1906): Вечером гляну\n\n\nhttps://t.me/c/**********/628:\n2025-01-20T20:18:35Z\n[Fedor](https://t.me/id1906): Мне не очень нравится этот плэйсхолдер. Я не знаю что сказать тут кроме \"понятия не имею\". Это узкоспециализированные инструменты, которые к тому же насколько я знаю забанены в РФ.\n\n\nhttps://t.me/c/**********/629:\n2025-01-20T20:19:19Z\n[Fedor](https://t.me/id1906): Я бы сказал про разницу между AI Общего назначения и специализированных AI и объяснил бы дальше зачем им нужен AI общего назначения\n\n\nhttps://t.me/c/**********/630:\n2025-01-21T15:37:26Z\n[Ilya Briskin](https://t.me/il_37): Ребят, привет!\n\n\nhttps://t.me/c/**********/631:\n2025-01-21T15:37:44Z\n[Ilya Briskin](https://t.me/il_37): Мы затягиваем сдачу драфта содержания. Давайте соберемся)\n\n\nhttps://t.me/c/**********/632:\n2025-01-21T15:38:51Z\n[Ilya Briskin](https://t.me/il_37): [Fedor](https://t.me/id1906) есть важная задача: собрать список конкретных кейсов, о которых расскажем на вебинаре Сможешь ее взять в работу? Если да, уточни срок готовности 🙏\n\n\nhttps://t.me/c/**********/633:\n2025-01-21T15:41:37Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Я немного подготовился, вместе с o1 собрал слайды (см. последнее) [ChatGPT - LLM для бизнеса: Shared via ChatGPT](https://chatgpt.com/share/678fbfad-1698-800a-9f66-2dbfaf44eefe)   Имхо, можно найти примеры документов, которые могут встречаться у них и сделать несколько демонстраций.\n\n\nhttps://t.me/c/**********/634:\n2025-01-21T15:42:08Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): ЭТО AI MINDSET в действии =)\n\n\nhttps://t.me/c/**********/635:\n2025-01-21T16:40:51Z\n[Fedor](https://t.me/id1906): Я ещё на работе, только что закрыл ноут. Юра, можешь поделиться слайдами\n\n\nhttps://t.me/c/**********/636:\n2025-01-21T16:41:01Z\n[Fedor](https://t.me/id1906): Сюда, по дороге почитаю\n\n\nhttps://t.me/c/**********/637:\n2025-01-21T16:41:31Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): [ChatGPT - LLM для бизнеса: Shared via ChatGPT](https://chatgpt.com/share/678fbfad-1698-800a-9f66-2dbfaf44eefe) Последний ответ\n\n\nhttps://t.me/c/**********/638:\n2025-01-21T16:41:56Z\n[Fedor](https://t.me/id1906): Я скажу сегодня либо выдам список кейсов, либо скажу когда выдам.\n\n\nhttps://t.me/c/**********/639:\n2025-01-21T16:43:40Z\n[Fedor](https://t.me/id1906): Слушай, прямо хорошие слайды, респект\n\n\nhttps://t.me/c/**********/640:\n2025-01-21T16:44:30Z\n[Fedor](https://t.me/id1906): Я бы вот тут немного порядок поменял: Слайд 11. Как начать внедрение?  1. Аудит процессов: Определить, какие рутинные задачи можно автоматизировать (документооборот, поиск, переписка).  2. Выбор пилотных проектов: Начать с небольших, но ощутимых кейсов.  3. Тестирование и прототипы: Запустить простые MVP (чат-бот, генератор текстов, помощник в BIM-системе).  4. Обучение команды: Воркшопы, хакатоны, тренинги по prompt engineering.  5. Обратная связь и масштабирование: Оценить эффективность и постепенно расширять сферу применения.\n\n\nhttps://t.me/c/**********/641:\n2025-01-21T16:45:25Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Я скаже, что o1 очень хорошо заменеджил бизнес нашу стратегию, могу прислать тебе отдельно\n\n\nhttps://t.me/c/**********/642:\n2025-01-21T17:03:05Z\n[Fedor](https://t.me/id1906): Я тут фокус бы именно на ai mindset сделал: мы не знаем что у вас болит лучше чем вам поэтому мы как в фильме армагеддон будем делать из бурильщиков космонавтов\n\n\nhttps://t.me/c/**********/643:\n2025-01-21T17:03:08Z\n[Fedor](https://t.me/id1906): # Как начать внедрение GenAI? 1. Выявление агентов роста:    - Поиск энтузиастов в разных отделах    - Определение ранних последователей    - Создание сети внутренних амбассадоров 2. Создание культуры экспериментов:    - Поощрение пробных проектов    - Право на ошибку    - Обмен опытом между командами 3. Выбор пилотных проектов:    - Фокус на quick wins с видимым результатом    - Минимальное влияние на существующие процессы    - Максимальная видимость успеха 4. Образовательная экосистема:    - Воркшопы и хакатоны    - Peer-to-peer обучение    - Sharing sessions успешных кейсов 5. Масштабирование через сообщество:    - Развитие внутренней экспертизы    - Документирование лучших практик    - Создание центров компетенций Акцент на развитии людей и культуры, а не только на технической стороне внедрения.\n\n\nhttps://t.me/c/**********/644:\n2025-01-21T17:03:50Z\n[Fedor](https://t.me/id1906): И мы предложим фасилитацию и тулсет для всего этого\n\n\nhttps://t.me/c/**********/645:\n2025-01-21T17:09:46Z\n[Fedor](https://t.me/id1906): Слайд 9 тоже мимо: нужно давить на один месседж: хуман аугментэйшн, а этот слайд про автоматизацию и оптимизацию. Мне кажется оптимизация вторична, а автоматизация вообще немного из другой оперы - в такое опасно соваться на данном этапе. Предлагаю такой вариант слайда: # Выгоды внедрения GenAI в проектировании 1. Усиление возможностей сотрудников:    - Помощь в анализе сложных данных    - Поддержка в принятии решений    - Доступ к лучшим практикам 2. Развитие экспертизы:    - Быстрый доступ к накопленному опыту    - Помощь в обучении новых сотрудников    - Сохранение знаний уходящих экспертов 3. Улучшение качества:    - Помощь в проверке соответствия нормам    - Поддержка в выявлении потенциальных проблем    - Consistency check между разделами 4. Оптимизация процессов:    - Ускорение рутинных операций    - Помощь в подготовке документации    - Поддержка в координации между отделами 5. Инновационный потенциал:    - Возможность тестировать новые подходы    - Поиск нестандартных решений    - Развитие культуры постоянного улучшения Акцент на усилении человеческого потенциала, а не на автоматизации.​​​​​​​​​​​​​​​​\n\n\nhttps://t.me/c/**********/646:\n2025-01-21T17:12:58Z\n[Fedor](https://t.me/id1906): В остальном прямо отлично\n\n\nhttps://t.me/c/**********/647:\n2025-01-21T17:15:54Z\n[Fedor](https://t.me/id1906): Самому захотелось на такую презентацию сходить.  Мне очень понравился слайд про ограничения.  Юра, можешь пожалуйста попросить о1 поменять слайды по моим рекомендациям и прислать весь аутлайн сюда?\n\n\nhttps://t.me/c/**********/648:\n2025-01-21T17:17:02Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Через полчаса\n\n\nhttps://t.me/c/**********/649:\n2025-01-21T17:22:28Z\n[Fedor](https://t.me/id1906): Ага, вот я немного поработал над слайдом по ограничениям, по-моему так лучше: # Потенциальные риски и ограничения GenAI 1. Галлюцинации и неточности:    - Примеры:      * Придумывание несуществующих норм и стандартов      * Смешивание требований из разных документов      * Уверенные ответы о несуществующих технологиях    - Как проверить:      * Спросить про \"СНиП 99.99-2023 по проектированию космических лифтов\"      * Попросить описать конкретные пункты из \"обновленного ГОСТ 2025 года\" 2. Ограничения в понимании контекста:    - Примеры:      * Путаница в технических терминах с двойным значением      * Неверная интерпретация чертежей без полного контекста      * Смешивание требований из разных разделов проекта    - Как проверить:      * Дать описание многозначного термина в разных контекстах      * Попросить проанализировать фрагмент чертежа без общего вида 3. Проблемы с конфиденциальностью:    - Примеры:      * Включение в ответы деталей из других проектов      * Сохранение sensitive информации в истории      * Использование данных в обучении моделей    - Как защититься:      * Четкие правила работы с конфиденциальной информацией      * Использование enterprise решений с контролем данных 4. Организационные барьеры:    - Типичные сценарии:      * \"У меня 20 лет опыта, зачем мне AI?\"      * \"Я лучше по старинке, так надежнее\"      * \"Это слишком сложно для меня\"    - Как работать:      * Показывать конкретные примеры пользы      * Начинать с простых сценариев      * Обеспечивать поддержку при освоении Ключевой принцип: знание ограничений - путь к эффективному использованию.​​​​​​​​​​​​​​​​\n\n\nhttps://t.me/c/**********/650:\n2025-01-21T18:10:53Z\n[Fedor](https://t.me/id1906): Я попробую этой штуке скормить слайды [Napkin AI - The visual AI for business storytelling: Just type, copy-paste or generate your text and Napkin will instantly transform it into insightful visuals. Make your communication more effective with Napkin.](https://www.napkin.ai/)\n\n\nhttps://t.me/c/**********/651:\n2025-01-21T18:49:45Z\n[Fedor](https://t.me/id1906): Я готов съесть свою шляпу. Последний месяц-два я подчеркнуто не любил deepseek из-за китайских корней. Сегодня я потестировал их свежее R1 (аналог О1) и хочу сказать, что это очень впечатляет. Безотносительно этичности и слепых зон китайского llm (не надо спрашивать чей Тибет), результаты прямо ого-го.\n\n\nhttps://t.me/c/**********/652:\n2025-01-21T19:39:27Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): А меня он не понял =(\n\n\nhttps://t.me/c/**********/653:\n2025-01-21T19:40:10Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Освободился, ещё актуально\n\n\nhttps://t.me/c/**********/654:\n2025-01-21T19:40:19Z\n[Fedor](https://t.me/id1906): Засылай\n\n\nhttps://t.me/c/**********/655:\n2025-01-21T19:40:38Z\n[Fedor](https://t.me/id1906): я пойду чуть потусуюсь с детьми, а потом пофигачу\n\n\nhttps://t.me/c/**********/656:\n2025-01-21T19:40:39Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Офигенная метафора, её нужно в начале использовать. Только что на разговоре использовал — она круто работает!\n\n\nhttps://t.me/c/**********/657:\n2025-01-21T19:41:10Z\n[Fedor](https://t.me/id1906): у меня бывают проблески вдохновения :)\n\n\nhttps://t.me/c/**********/658:\n2025-01-21T19:41:14Z\n[Fedor](https://t.me/id1906): спасибо :)\n\n\nhttps://t.me/c/**********/659:\n2025-01-21T19:44:36Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Кажется, я могу выйти ещё на парочку росскийских проектировщиков, работающих в рамках застройщиков, чтобы им предложить вебинар\n\n\nhttps://t.me/c/**********/661:\n2025-01-21T19:45:12Z\n[Fedor](https://t.me/id1906): а расскажи про компанию которой мы будем это все втирать\n\n\nhttps://t.me/c/**********/662:\n2025-01-21T19:46:15Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): https://inpad.store Разработка приложений: Разработка приложений для строительства. Плагины для Revit, NanoCAD, Renga\n\n\nhttps://t.me/c/**********/663:\n2025-01-21T19:46:35Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Это гендир, наш с вашим дважды соотечественник\n\n\nhttps://t.me/c/**********/664:\n2025-01-21T23:06:28Z\n[Fedor](https://t.me/id1906): Так, я уже кажется собрал аутлайн\n\n\nhttps://t.me/c/**********/665:\n2025-01-22T11:44:27Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Я начал думать про Cursor.app, как про Midjourney. Загнал все хорошие промпты в o1 и попросил напистаь рекоммендации. А потом стал его спрашивать.\n\n\nhttps://t.me/c/**********/666:\n2025-01-22T11:45:04Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Для примера мой план для этого был такой - Analyse code at quepasa/api - Write tests to folder tests/ with prefix test_api_ - Use pytest - Run tests and check\n\n\nhttps://t.me/c/**********/667:\n2025-01-22T11:46:19Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): AI Mindset по всём\n\n\nhttps://t.me/c/**********/668:\n2025-01-22T16:13:22Z\n[Fedor](https://t.me/id1906): Охуенно! Можно я пост напишу про тебя и тебя затегаю?\n\n\nhttps://t.me/c/**********/669:\n2025-01-22T16:42:39Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Можно\n\n\nhttps://t.me/c/**********/670:\n2025-01-22T17:50:09Z\n[Fedor](https://t.me/id1906): # Искусственный интеллект (ИИ) и большие языковые модели (LLM) в сфере проектирования ## Слайд 1. Титульный слайд Заголовок: «Искусственный интеллект (ИИ) и большие языковые модели (LLM) в сфере проектирования»   Подзаголовок: «Возможности, примеры, перспективы» Информация для отображения: - Ваше имя и должность (или имена и должности выступающих) - Дата проведения вебинара *Демонстрация:*   «Hook»-история: в начале презентации можете рассказать короткую историю о том, как кто-то из коллег (или вы сами) задал LLM специфический вопрос по проектированию и неожиданно получил полезное, но удивительное решение. *Как показать:* буквально на 1 минуту описать личный опыт или кейс, чтобы заинтересовать слушателей. ## Слайд 2. План (Agenda) - Что такое большие языковые модели (LLM) и как они работают - Ключевые области применения ИИ (с фокусом на проектирование) - Примеры реальных бизнес-кейсов и инструментов - Выгоды и риски внедрения - Вопросы и ответы (Q&A) *Пример / Демонстрация:*   Покажите короткий дорожно-тематический план: где мы сейчас, куда двигаемся (шаги). *Как показать:* можно включить небольшой таймлайн в слайд с разбивкой по минутам или разделам. ## Слайд 3. Коротко об ИИ: основные направления Основные направления: - Computer Vision (распознавание изображений и видео) - Natural Language Processing (NLP) — сюда относятся LLM - Speech Recognition and Synthesis (распознавание речи и синтез) - Robotics (автономные системы, манипуляторы) - Generative AI (текст, изображение, музыка, видео) - Predictive Analytics & Machine Learning (прогнозы, анализ, статистика) *Пример / Демонстрация:* Computer Vision:   Распознавание чертежей или 3D-сканированных объектов. *Как показать:* включите скриншот популярного сервиса, распознающего чертёж (например, сервисы, распознающие PDF-чертежи и создающие 3D-модели). Speech Recognition:   Голосовые помощники (Siri, Google Assistant). *Как показать:* можно попросить вашего смартфона «Открыть BIM-модель по ключевому слову» (в демонстрационных целях). ## Слайд 4. Что такое большие языковые модели (LLM)? Определение:   LLM (Large Language Model) — алгоритмы, обученные на огромном массиве текстовых данных, способные генерировать и анализировать тексты. Как это работает (упрощённо): - Модель «читает» миллиарды слов/фраз - Находя статистические закономерности, модель предсказывает следующее слово или фразу Применение: - Автоматизация документооборота - Создание черновиков текстов, инструкций, ТЗ - Анализ документов на противоречия *Пример / Демонстрация:* Короткий чат с моделью:   *Как показать:* - Подготовьте окно браузера с ChatGPT (или другой моделью) - Введите запрос: «Объясни простыми словами, что такое BIM» - Показать участникам ответ на экране ## Слайд 5. Где LLM (NLP) уже используется сегодня? - Бизнес-коммуникации: чат-боты, ассистенты для клиентов - Внутренний документооборот: генерация шаблонов документов, проверка текста - Маркетинг и продажи: анализ отзывов, создание контента - Поддержка пользователей: авто-ответы на частые вопросы - Проектная деятельность: поиск и анализ стандартов, технических условий, BIM-регламентов *Пример / Демонстрация:* Автоответ на почту:   *Как показать:* - На экране в чате с моделью дайте команду: «Напиши вежливый ответ клиенту, который спрашивает о сроках поставки чертежей. Укажи, что срок — 2 недели» - Модель выдаст готовый текст письма ## Слайд 6. Пример кейса: Автоматизация обработки текстов в проектировании Задача: Быстрый поиск и сведение информации из разных документов (нормативы, BIM-стандарты, регламенты). Решение с использованием LLM: - Внутренний чат-бот, обученный на документах компании - Запрос: «Какие требования по пожарной безопасности для такого-то типа здания?» — бот выдаёт компиляцию информации Польза: - Сокращение времени на поиск и проверку - Унификация знаний - Снижение риска пропустить важный пункт *Пример / Демонстрация:* Синтетический пример:   *Как показать:* - Возьмите короткий отрывок из СП (Строительные Правила) или ГОСТ, залейте его в ChatGPT (или в любой инструмент, где можно загружать текст)\n\n\nhttps://t.me/c/**********/671:\n2025-01-22T17:50:10Z\n[Fedor](https://t.me/id1906): - Задайте вопрос: «Какие ключевые пункты в этих нормах касаются пожарных выходов для зданий такого-то класса?» - Показать ответ модели - Обязательно уточните, что в реальной жизни нужно следить за актуальностью версий документов и дообучать модель ## Слайд 7. Как это может помочь BIM-отделам? - Генерация описаний BIM-моделей: Авто-составление инструкций к модели, выгрузка в документацию - Валидация данных: Быстрый анализ на соответствие BIM-стандартам - BIM-координация: Автоматическая генерация протоколов встреч, распределение задач - Обучение новых сотрудников: Корпоративный чат-бот для ответов на типовые вопросы *Пример / Демонстрация:* Автоматический протокол коллизий:   *Как показать:* - Подготовьте пример списка обнаруженных коллизий (5-6 пунктов) в текстовом виде - Попросите LLM «Напиши формальный протокол совещания о коллизиях и предложи варианты устранения» - На глазах у слушателей получите структурированный, читабельный документ ## Слайд 8. Дополнительно: Генеративные модели для визуализации Почему интересно архитекторам и дизайнерам: - Быстрая генерация концепций, эскизов, mood-boards - Подбор вариантов оформления фасадов, интерьеров - Эксперименты с формой *Пример / Демонстрация:* Концепт здания с помощью Midjourney (или Stable Diffusion):   *Как показать:* - Заранее зарегистрируйтесь в Midjourney (через Discord) или используйте любой веб-интерфейс Stable Diffusion - Введите запрос: «Futuristic office building concept, glass facade, sustainable design» (на английском, если сервис англоязычный) - Покажите, как за несколько секунд/минут сервис выдаст уникальные изображения ## Слайд 9. Выгоды внедрения GenAI в проектировании 1. Усиление возможностей сотрудников:    - Помощь в анализе сложных данных    - Поддержка в принятии решений    - Доступ к лучшим практикам 2. Развитие экспертизы:    - Быстрый доступ к накопленному опыту    - Помощь в обучении новых сотрудников    - Сохранение знаний уходящих экспертов 3. Улучшение качества:    - Помощь в проверке соответствия нормам    - Поддержка в выявлении потенциальных проблем    - Consistency check между разделами 4. Оптимизация процессов:    - Ускорение рутинных операций    - Помощь в подготовке документации    - Поддержка в координации между отделами 5. Инновационный потенциал:    - Возможность тестировать новые подходы    - Поиск нестандартных решений    - Развитие культуры постоянного улучшения *Пример / Демонстрация:* Сравнение «до» и «после»:   *Как показать:* - Опишите вымышленный сценарий: «Раньше на поиск нужных норм уходило 2-3 часа, теперь — 10 минут» - Подкрепите цифрами или диаграммой (даже если она условная) — иллюстрация экономии времени ## Слайд 10. Потенциальные риски и ограничения - Галлюцинации модели (придуманные факты) - Безопасность данных (конфиденциальность) - Неполное понимание контекста - Сопротивление изменениям (культурные и организационные барьеры) *Пример / Демонстрация:* Показать «галлюцинацию»:   *Как показать:* - Задайте модели вопрос о «несуществующем» СНиП или ГОСТ. Например, «Расскажи о требованиях СНиП 99.99-2023» - Модель может начать отвечать, хотя такого СНиП реально нет - Это подчеркнёт важность верификации ответов ## Слайд 11. Как начать внедрение GenAI? 1. Выявление агентов роста:    - Поиск энтузиастов в разных отделах    - Определение ранних последователей    - Создание сети внутренних амбассадоров 2. Создание культуры экспериментов:    - Поощрение пробных проектов    - Право на ошибку    - Обмен опытом между командами 3. Выбор пилотных проектов:    - Фокус на quick wins с видимым результатом    - Минимальное влияние на существующие процессы    - Максимальная видимость успеха 4. Образовательная экосистема:    - Воркшопы и хакатоны    - Peer-to-peer обучение    - Sharing sessions успешных кейсов 5. Масштабирование через сообщество:    - Развитие внутренней экспертизы    - Документирование лучших практик    - Создание центров компетенций *Пример / Демонстрация:* Пример пилотного проекта:   *Как показать:* - Опишите сценарий: «Возьмём задачу составления еженедельного отчёта по статусу BIM-моделей»\n\n\nhttps://t.me/c/**********/672:\n2025-01-22T17:50:10Z\n[Fedor](https://t.me/id1906): - Покажите, как можно автоматически собирать данные из нескольких источников (Revit, Navisworks и т.д.) и формировать сводку (через LLM или простые макросы) ## Слайд 12. Формат дальнейшего обучения и поддержки - Корпоративные воркшопы: практикум по prompt engineering - Advisory для топ-менеджмента: расчёт ROI, оценка рисков - AI-Hackathon: совместное решение конкретных бизнес-задач - Мастер-классы: работа с инструментами (AutoGPT, ChatGPT, плагины для BIM и т.п.) *Пример / Демонстрация:* Мини «AI-Hackathon» за 15 минут:   *Как показать:* - Разделите участников на группы (даже если чисто виртуально) - Дайте им короткую задачу, где требуется сформулировать prompt для LLM - Пусть каждая группа попробует получить оптимальный ответ - Сравните результаты ## Слайд 13. Вопросы / Обсуждение - Что кажется наиболее актуальным для ваших команд? - Какие задачи требуют автоматизации в первую очеред\n\n\nhttps://t.me/c/**********/673:\n2025-01-22T17:53:43Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): *Демонстрация:*   «Hook»-история: в начале презентации можете рассказать короткую историю о том, как кто-то из коллег (или вы сами) задал LLM специфический вопрос по проектированию и неожиданно получил полезное, но удивительное решение. Надо поменять, или у нас есть идеи именно по проектированию.\n\n\nhttps://t.me/c/**********/674:\n2025-01-22T17:53:54Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): *Пример / Демонстрация:*   Покажите короткий дорожно-тематический план: где мы сейчас, куда двигаемся (шаги). *Как показать:* можно включить небольшой таймлайн в слайд с разбивкой по минутам или разделам. У тебя есть идеи про план\n\n\nhttps://t.me/c/**********/675:\n2025-01-22T17:53:58Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Computer Vision:   Распознавание чертежей или 3D-сканированных объектов. *Как показать:* включите скриншот популярного сервиса, распознающего чертёж (например, сервисы, распознающие PDF-чертежи и создающие 3D-модели). [Ilya Briskin](https://t.me/il_37) Давай на сайте онпада поищем что-нибудь\n\n\nhttps://t.me/c/**********/676:\n2025-01-22T17:54:57Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Speech Recognition:   Голосовые помощники (Siri, Google Assistant). *Как показать:* можно попросить вашего смартфона «Открыть BIM-модель по ключевому слову» (в демонстрационных целях). [Ilya Briskin](https://t.me/il_37) Я думаю можно сделать чат с резюме нашей прошлой встречи. Или с каким-то видео с их сайта.\n\n\nhttps://t.me/c/**********/677:\n2025-01-22T17:56:08Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): *Пример / Демонстрация:* Короткий чат с моделью:   *Как показать:* - Подготовьте окно браузера с ChatGPT (или другой моделью) - Введите запрос: «Объясни простыми словами, что такое BIM» - Показать участникам ответ на экране Можно, например, взять какой-то ГОСТ, например,  ГОСТ 21.101-2020 — и поспрашивать по нему\n\n\nhttps://t.me/c/**********/678:\n2025-01-22T17:56:38Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): *Пример / Демонстрация:* Автоответ на почту:   *Как показать:* - На экране в чате с моделью дайте команду: «Напиши вежливый ответ клиенту, который спрашивает о сроках поставки чертежей. Укажи, что срок — 2 недели» - Модель выдаст готовый текст письма Можно показать, как я сделал фоллоу ап по прошлой встрече\n\n\nhttps://t.me/c/**********/679:\n2025-01-22T17:57:19Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): ...Синтетический пример:   *Как показать:* - Возьмите короткий отрывок из СП (Строительные Правила) или ГОСТ, залейте его в ChatGPT (или в любой инструмент, где можно загружать текст) - Задайте вопрос: «Какие ключевые пункты в этих нормах касаются пожарных выходов для зданий такого-то класса?» - Показать ответ модели - Обязательно уточните, что в реальной жизни нужно следить за актуальностью версий документов и дообучать модель ... Вроде — ГОСТ 21.101-2020 — реальный гост\n\n\nhttps://t.me/c/**********/680:\n2025-01-22T17:58:55Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Концепт здания с помощью Midjourney (или Stable Diffusion):   *Как показать:* - Заранее зарегистрируйтесь в Midjourney (через Discord) или используйте любой веб-интерфейс Stable Diffusion - Введите запрос: «Futuristic office building concept, glass facade, sustainable design» (на английском, если сервис англоязычный) - Покажите, как за несколько секунд/минут сервис выдаст уникальные изображения Можно показать DALL-e фотку здания и попросить сделать благоустройство, например\n\n\nhttps://t.me/c/**********/681:\n2025-01-22T17:59:46Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Сравнение «до» и «после»:   *Как показать:* - Опишите вымышленный сценарий: «Раньше на поиск нужных норм уходило 2-3 часа, теперь — 10 минут» - Подкрепите цифрами или диаграммой (даже если она условная) — иллюстрация экономии времени Я думаю, это домашнее задание, придумать кейсы и составить желаемое — до и после\n\n\nhttps://t.me/c/**********/682:\n2025-01-22T18:01:00Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Показать «галлюцинацию»:   *Как показать:* - Задайте модели вопрос о «несуществующем» СНиП или ГОСТ. Например, «Расскажи о требованиях СНиП 99.99-2023» - Модель может начать отвечать, хотя такого СНиП реально нет - Это подчеркнёт важность верификации ответов Надо подобрать. Можно ещё попросить добавить вертолётную площадку, или бассейн для подводной лодки, или космопорт\n\n\nhttps://t.me/c/**********/683:\n2025-01-22T18:01:54Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): ...Пример пилотного проекта:   *Как показать:* - Опишите сценарий: «Возьмём задачу составления еженедельного отчёта по статусу BIM-моделей» - Покажите, как можно автоматически собирать данные из нескольких источников (Revit, Navisworks и т.д.) и формировать сводку (через LLM или простые макросы) ... Можно попробовать это тоже сделать, как пример демо — на данных блогов или специальных ютуб каналов\n\n\nhttps://t.me/c/**********/684:\n2025-01-22T18:02:38Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Мини «AI-Hackathon» за 15 минут:   *Как показать:* - Разделите участников на группы (даже если чисто виртуально) - Дайте им короткую задачу, где требуется сформулировать prompt для LLM - Пусть каждая группа попробует получить оптимальный ответ - Сравните результаты Мне кажется, это на вторую серию задел\n\n\nhttps://t.me/c/**********/685:\n2025-01-22T18:02:56Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): ## Слайд 13. Вопросы / Обсуждение - Что кажется наиболее актуальным для ваших команд? - Какие задачи требуют автоматизации в первую очеред [Fedor](https://t.me/id1906) оборвалось\n\n\nhttps://t.me/c/**********/686:\n2025-01-22T18:03:42Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): [Fedor](https://t.me/id1906) Очень хорошо. Нужно на первый слайд поставить аллегорию на Армегеддон, зачем мы вообще всё это делаем!\n\n\nhttps://t.me/c/**********/687:\n2025-01-22T18:06:08Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): up!\n\n\nhttps://t.me/c/**********/688:\n2025-01-22T18:19:27Z\n[Fedor](https://t.me/id1906): Ща\n\n\nhttps://t.me/c/**********/689:\n2025-01-22T18:23:35Z\n[Fedor](https://t.me/id1906): ## Слайд 13. Вопросы / Обсуждение - Что кажется наиболее актуальным для ваших команд? - Какие задачи требуют автоматизации в первую очередь? - Какие риски вы видите в использовании LLM? *Пример / Демонстрация:* Интерактивный опрос:   *Как показать:* - Используйте инструменты Zoom/Teams/Webex (опрос, чат) - Задайте 2-3 вопроса, соберите ответы сразу - Отобразите результаты в виде диаграммы (если платформа позволяет) ## Слайд 14. Контакты - Имя докладчика(ов) - Email, телефон, мессенджеры - Ссылки на ресурсы/сайты (при необходимости)\n\n\nhttps://t.me/c/**********/690:\n2025-01-22T18:24:31Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Интерактивный опрос:   *Как показать:* - Используйте инструменты Zoom/Teams/Webex (опрос, чат) - Задайте 2-3 вопроса, соберите ответы сразу - Отобразите результаты в виде диаграммы (если платформа позволяет) Кстати, хоть в комменты пусть митса пусть покидают\n\n\nhttps://t.me/c/**********/691:\n2025-01-22T18:24:41Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): или можно сразу чат в телеге завести и там голосование\n\n\nhttps://t.me/c/**********/692:\n2025-01-22T18:25:45Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): [Fedor](https://t.me/id1906) Вы с LLM молодцы! (Новая этика AI Mindset) Осталось допилить примеров и MVP вебинара готов.\n\n\nhttps://t.me/c/**********/693:\n2025-01-22T18:27:06Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): [Ilya Briskin](https://t.me/il_37) [Любимая](https://t.me/AnastasiaVorontsov)  В каком формате нам лучше делать слайды?\n\n\nhttps://t.me/c/**********/694:\n2025-01-22T18:30:40Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Ща попробуйю замутить через Cursor.app\n\n\nhttps://t.me/c/**********/695:\n2025-01-22T18:31:48Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Кстати, если это РФ — может быть им DeepSeek дать? [Fedor](https://t.me/id1906) Рав, [Chat with DeepSeek AI.](https://chat.deepseek.com/sign_in) кошерен?\n\n\nhttps://t.me/c/**********/696:\n2025-01-22T18:34:57Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): [Fedor](https://t.me/id1906) в следующий раз лучше кидай шэр на чатгпт или файлом, дополнительно, чтобы проще было копировать\n\n\nhttps://t.me/c/**********/697:\n2025-01-22T19:02:02Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): [Ilya Briskin](https://t.me/il_37) [GenAI: Искусственный интеллект (ИИ) и большие языковые модели (LLM) в сфере проектирования Возможности, примеры, перспективы В фильме «Армагеддон» команда бурильщиков отправилась на астероид спасать Землю. Их за короткий срок обучили базовым навыкам астронавтов. Мы же хотим за короткий срок «прокачать» ...](https://docs.google.com/presentation/d/1ieFl3jK1hHX8GwI9M6MD7sOQgLaAXcQlgQ6OrSrt71c/edit?usp=sharing) Полчаса позора курсора\n\n\nhttps://t.me/c/**********/698:\n2025-01-22T19:08:00Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Нашёл просто картинку из интернета: На изображении представлен архитектурный план первого этажа здания, выполненный в формате, типичном для строительных чертежей. Вот подробное описание: ### Основные элементы чертежа: 1. Этаж и отметка высоты:    - Указано, что это \"План 1-го этажа на отметке 0.000\", что означает базовый уровень пола первого этажа. 2. Обозначение помещений:    - Гостиная:       - Площадь: 23.79 м².      - Расположена в нижней левой части чертежа.      - Содержит мебель: диван, телевизор, обеденный стол.    - Кухня-столовая:      - Площадь: 13.95 м².      - Включает мебель для приготовления пищи и обеденный стол.    - Санузел:      - Площадь: 5.56 м².      - Присутствует стандартное сантехническое оборудование.    - Котельная:      - Площадь: 6.89 м².      - Предназначена для отопительного оборудования.    - Прихожая:      - Площадь: 5.56 м².      - Центральная часть плана.    - Гараж:      - Площадь: 17.17 м².      - Расположен справа и имеет отдельный вход. 3. Стены и размеры:    - Несущие стены изображены более толстыми линиями, а внутренние перегородки тоньше.    - Указаны размеры стен, проемов и расстояний между объектами. 4. Двери и окна:    - Дверные проемы обозначены дугами, показывающими направление открывания.    - Оконные проемы показаны в виде линий на внешних стенах с указанием их размеров. 5. Размерные линии:    - Указаны внешние и внутренние размеры помещений.    - Размеры даны в миллиметрах, например, 865, 3000, 4915. 6. Условные обозначения:    - Присутствуют символы и линии для обозначения мебели, сантехники и инженерных систем.    - Каждое помещение подписано с указанием площади в квадратных метрах. 7. Штриховка:    - Стены и перекрытия заштрихованы, чтобы выделить их толщину и материал. 8. Оси и маркировка:    - Чертеж снабжен осями (А, Б, В, Г, Д и 1, 2, 3, 4), которые облегчают понимание расположения помещений. Если нужно дополнительное описание или конкретная информация о чертеже, уточните, пожалуйста!\n\n\nhttps://t.me/c/**********/699:\n2025-01-22T19:09:13Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): короче, всё реально, надо садиться и верстать слайды\n\n\nhttps://t.me/c/**********/700:\n2025-01-22T19:41:17Z\n[Fedor](https://t.me/id1906): РФ некошерен, хороший вариант.\n\n\nhttps://t.me/c/**********/701:\n2025-01-22T19:41:47Z\n[Fedor](https://t.me/id1906): Я с Клаудом фигачу\n\n\nhttps://t.me/c/**********/702:\n2025-01-22T19:43:16Z\n[Fedor](https://t.me/id1906): Аккуратно с таким - я как инженер за такое бы уцепился и просто по пунктам бы развалил; особенно если бы до моего чертежа какой-то ботан бы докопался\n\n\nhttps://t.me/c/**********/703:\n2025-01-22T19:43:48Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): А тут разве плохо получилось?\n\n\nhttps://t.me/c/**********/704:\n2025-01-22T19:43:49Z\n[Fedor](https://t.me/id1906): В целом я считаю что лезть в конкретику инженерной профессии особенно с чертежами очень опасная дорожка. Поплывем.\n\n\nhttps://t.me/c/**********/705:\n2025-01-22T19:44:33Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Мы вроде должны показать его возможности\n\n\nhttps://t.me/c/**********/706:\n2025-01-22T19:44:48Z\n[Fedor](https://t.me/id1906): Перечитал, в целом ок, но я бы переписал промпт\n\n\nhttps://t.me/c/**********/707:\n2025-01-22T19:44:58Z\n[Fedor](https://t.me/id1906): Дай мне пару минут чтобы получше пример сделать\n\n\nhttps://t.me/c/**********/708:\n2025-01-22T19:45:01Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Они и увидят проблемы — мы же космонавты, а они бурильщики\n\n\nhttps://t.me/c/**********/709:\n2025-01-22T19:48:39Z\n[Fedor](https://t.me/id1906): На основе представленного поэтажного плана я подготовлю полезное описание проекта. Одноэтажный жилой дом с рациональной планировкой, общая площадь ~100 м². Ключевые преимущества проекта: 1. Продуманное зонирование: - Просторная гостиная, совмещенная с кухней-столовой (~40 м²) - Отдельная котельная с техническим помещением - Гараж на 1 автомобиль с удобным въездом - Компактная входная группа с тамбуром 2. Технические особенности: - Капитальные несущие стены обеспечивают надежность конструкции - Оптимальные габариты помещений: гостиная 23.79 м², кухня-столовая 13.25 м² - Инженерные коммуникации сгруппированы (кухня + санузел) - Предусмотрено место под индивидуальный котел в котельной 3. Преимущества для строительства: - Простая прямоугольная форма здания 12x9 м - Рациональная конструктивная схема без сложных узлов - Возможность быстрого возведения благодаря типовым решениям - Минимум несущих перегородок внутри дома 4. Экономическая эффективность: - Компактные размеры снижают затраты на фундамент и кровлю - Простая геометрия упрощает отделочные работы - Оптимальное соотношение жилой и общей площади - Возможность поэтапного строительства Проект подойдет для постоянного проживания семьи из 2-4 человек. Планировка позволяет при необходимости выполнить перепланировку или достроить второй этаж в будущем.​​​​​​​​​​​​​​​​\n\n\nhttps://t.me/c/**********/710:\n2025-01-22T19:49:15Z\n[Fedor](https://t.me/id1906): Вот мой тейк. Я конечно космонавт, но ещё и инженер. А у любого текста должна быть цель\n\n\nhttps://t.me/c/**********/711:\n2025-01-22T19:50:01Z\n[Fedor](https://t.me/id1906): Промпт: «Напиши полезный и продающий текст на основании этого чертежа который бы мог помочь инженеру и сэкономить его время»\n\n\nhttps://t.me/c/**********/712:\n2025-01-22T20:00:38Z\n[Fedor](https://t.me/id1906): У такого примера больше шансов сделать вау эффект и чутка вдохновить. Спеки они сами умеют писать с закрытыми глазами. Более того, даже в мои инженерные времена можно было такие спеки генерить в несколько кликов.  А вот продающий текст средний инженер написать не может :)\n\n\nhttps://t.me/c/**********/713:\n2025-01-22T20:24:35Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Продающий?\n\n\nhttps://t.me/c/**********/714:\n2025-01-22T20:27:55Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Этот чертеж представляет собой детальный план первого этажа современного и функционального дома с продуманной планировкой. Пространство организовано так, чтобы удовлетворить потребности семьи и обеспечить комфортное проживание. ### Описание: - Гостиная (23,79 м²) — просторная зона для отдыха и встреч с друзьями. Здесь можно разместить удобный диван, телевизор и создать уютную атмосферу для семейных вечеров. - Кухня-столовая (13,95 м²) — компактная, но функциональная зона для готовки и совместных обедов. Благодаря близости к гостиной, создается ощущение открытого пространства. - Санузел (5,56 м²) — достаточно просторный для размещения всей необходимой сантехники. - Прихожая (5,56 м²) — удобное пространство для хранения верхней одежды и обуви. - Котельная (6,89 м²) — изолированное помещение для технического оборудования, что повышает энергоэффективность дома. - Гараж (17,17 м²) — вместительный, подходит для хранения автомобиля и дополнительных вещей. - Тамбур (2,61 м²) — функциональное пространство, которое предотвращает попадание холода внутрь дома. ### Преимущества: 1. Эффективное использование пространства. Компактная планировка идеально подходит для небольших участков, сохраняя комфорт. 2. Зонирование. Отдельные помещения позволяют членам семьи заниматься своими делами, не мешая друг другу. 3. Гараж в доме. Удобство хранения автомобиля, особенно в холодное время года. 4. Техническое помещение. Возможность установки системы отопления или дополнительного оборудования в специально выделенном месте. ### Для кого подойдет: - Для семей, которые ценят комфорт и функциональность. - Для тех, кто ищет проект, легко адаптируемый под индивидуальные потребности. Если вы хотите жить в уютном и энергоэффективном доме, который станет вашим настоящим «гнездом», этот проект — идеальное решение для вас!\n\n\nhttps://t.me/c/**********/715:\n2025-01-22T20:28:29Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Да, так не докопаешься, ты прав\n", "keywords": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-01-22T20:28:29Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}, {"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}]}, {"id": "https://t.me/c/**********#groupped:part2", "url": "https://t.me/c/**********/715", "title": "AI mindset", "language": "ja", "text": "https://t.me/c/**********/715:\n2025-01-22T20:28:29Z\n[Юр<PERSON> Воронцов](https://t.me/yuri<PERSON>_voronts<PERSON>): Да, так не докопаешься, ты прав\n\n\nhttps://t.me/c/**********/717:\n2025-01-22T20:30:20Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): 1. Показать на экране (или упомянуть устно) короткий отрывок из «Армагеддона» (10-15 секунд, если позволяет авторское право и формат) или просто памятный кадр / постер.   2. Сказать (пример речи):      > «Коллеги, наверняка вы помните старый фильм «Армагеддон», где NASA решило научить бурильщиков быть космонавтами за пару недель. Почему? Потому что это было проще, чем учить космонавтов бурить. Нам с вами предстоит нечто похожее: мы не будем делать из вас программистов, зато научим пользоваться ИИ на вашем профессиональном языке. Чтобы вы, опытные архитекторы и инженеры, смогли с помощью больших языковых моделей экономить массу времени, быстрее принимать решения и оставаться в авангарде технологий. Как те самые ребята из «Армагеддона», мы подготовимся быстро и эффективно!»   3. Переключиться на следующий слайд (примерно: «Окей, шутки в сторону, давайте перейдём к делу!»). --- ## Что даёт такая метафора? 1. Лёгкий и запоминающийся «вход» в тему.   2. Снятие напряжения: участники начинают семинар с улыбкой, а не с опасением «о, это что-то сложное и непонятное».   3. Яркая иллюстрация: люди понимают, что вы хотите дать им интенсив по работе с ИИ, а не сделать их «полноценными дата-сайентистами». Это помогает избежать страха перед «программистскими» терминами. --- ### Итог Используйте «фишку» с «Армагеддоном» в начале или в течение семинара, чтобы зацепить внимание, разрядить обстановку и донести главный смысл: «Если даже бурильщиков можно было обучить космическим технологиям, то и мы — опытные проектировщики, архитекторы, инженеры — вполне в силах быстро освоить ИИ и внедрить его в наши рабочие процессы.»   Таким образом, вы создадите эффект вовлечённости, а ваша аудитория почувствует, что получает поддержку и чёткий план по освоению новых технологий.\n\n\nhttps://t.me/c/**********/718:\n2025-01-22T20:31:37Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): «Пусть у нас не будет астероида, угрожающего Земле (к счастью!), но у нас есть *дедлайны*, цифровая трансформация и конкуренция на рынке, которые не меньше подгоняют нас к действиям.»\n\n\nhttps://t.me/c/**********/719:\n2025-01-22T20:37:30Z\n[Fedor](https://t.me/id1906): Пора верстать\n\n\nhttps://t.me/c/**********/722:\n2025-01-22T20:53:07Z\n[Fedor](https://t.me/id1906): AI Yuri\n\n\nhttps://t.me/c/**********/723:\n2025-01-22T20:53:24Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): AI Alter Ego — @uri_aaranson\n\n\nhttps://t.me/c/**********/729:\n2025-01-23T08:40:08Z\n[Любимая](https://t.me/AnastasiaVorontsov): Класс! Вы вчера мощно продвинулись, пока я отмечала день рождения Элиэзера Бен-Йехуды.  Есть ли тех. уже задание для верстки? Канва? Пожелания по стилю шаблона? Конкретный шаблон в канве, который нравится? Что по поводу иллюстраций к слайдам? Если немного AI-generated картинок были бы к месту, напишите, к каким слайдам и что примерно. У нашей индиго-дизайнерки как раз будет 2 дня выходных. Нагрузите ее.\n  [Fedor](https://t.me/id1906): Полное доверие нашим обожаемым верстальщикам!\n\n\nhttps://t.me/c/**********/731:\n2025-01-23T08:56:26Z\n[Fedor](https://t.me/id1906): Может совет попробовать napkin.ai для генерации стиля и картинок\n  [Любимая](https://t.me/AnastasiaVorontsov): Вечером с Варей вместе попробуем, это интересно!\n\n\nhttps://t.me/c/**********/732:\n2025-01-23T09:01:02Z\n[Любимая](https://t.me/AnastasiaVorontsov): Можно я подокапываюсь немножко?  Я - сферический занудный инженер в вакууме, ничего не знаю про ИИ. Слайд 3. - LLM относятся к пункту Natural Language Processing (NLP).  Слайд 8. - Генеративные модели для визуализации.  Слайд 9. - Внедрение GenAI в проектировании - и дальше опять про LLM. А мне только что рассказали, что LLM - это пункт NLP, а GenAI - это про визуализацию.  Может быть, где-то в начале нужно явно четко написать, что LLM - это одновременно и подмножество GenAI, и подмножество NLP?\n\n\nhttps://t.me/c/**********/734:\n2025-01-23T09:11:09Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): [Любимая](https://t.me/AnastasiaVorontsov) Постепенно надо перебираться из обсуждения слайдов в чате, в обсуждение слайдов в черновике [GenAI: Искусственный интеллект (ИИ) и большие языковые модели (LLM) в сфере проектирования Возможности, примеры, перспективы В фильме «Армагеддон» команда бурильщиков отправилась на астероид спасать Землю. Их за короткий срок обучили базовым навыкам астронавтов. Мы же хотим за короткий срок «прокачать» ...](https://docs.google.com/presentation/d/1ieFl3jK1hHX8GwI9M6MD7sOQgLaAXcQlgQ6OrSrt71c/edit?usp=sharing)\n\n\nhttps://t.me/c/**********/735:\n2025-01-23T09:11:34Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): [Ilya Briskin](https://t.me/il_37) надо доработать черновик, по обсуждениям в чате\n\n\nhttps://t.me/c/**********/736:\n2025-01-23T09:13:12Z\n[Fedor](https://t.me/id1906): Отлично\n\n\nhttps://t.me/c/**********/737:\n2025-01-23T10:07:09Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): [Ilya Briskin](https://t.me/il_37) [Любимая](https://t.me/AnastasiaVorontsov) Вам надо сегодня созвониться и добить черновик презентации по слайдам\n\n\nhttps://t.me/c/**********/739:\n2025-01-24T08:35:39Z\n[Fedor](https://t.me/id1906): Фелииииикс!\n\n\nhttps://t.me/c/**********/744:\n2025-01-24T12:09:12Z\n[Ilya Briskin](https://t.me/il_37): Привет, Илья. Я накидал ответы на твои вопросы плюс обсудил с нашим хэдом технологий перспективы использования подобного инструмента. Он очень скептически настроен, что получится сделать что-то рабочее. Думаю, тут без какого-то уже работающего инструмента не получится начать обсуждение. Ответы на вопросы 1. Важен ли функционал разных прав доступа? Разным ролям доступны разные статьи? Да, но он должен как-то наследоваться от прав в Конфлюенс и Слака. То есть человек не должен видеть информацию из статьи в пространстве Конфлюенс, которая для него закрыта. Точно так же, он не должен видеть информацию из приватного канала, куда он не добавлен. 2. Нужен ли поиск по сотрудникам? Тут не совсем понял, что имеется ввиду.  3. Будет ли у нас доступ внутрь контура для отладки? Не готов сейчас ответить. Скорее всего процесс будет выглядеть так: боссы смотрят какой-то MVP, оценивают его полезность и стоимость, принимают решение о подключении (или нет) к нашим системам. 4. Есть ли план внедрения? Сначала Конфлюенс, потом Слак, например. В идеале это должен быть комплексный инструмент. Но, наверное, можно начать со Слака. 5. Есть ли примеры типов вопросов, на которые нужно обязательно настроить поиск в первую очередь? Неа, и, скорее всего, такого не будет. У разных команд, разные вопросы, вряд ли кто-то найдёт ресурс чтобы их собрать. По идее, сервис должен быть универсальным, а не заточен под какие-то конкретные вопросы.\n  [Fedor](https://t.me/id1906): просто нужно понимать, что ни одна существующая система из коробки этого всего не сделает. Делать сейчас свою будет больно и дорого. А еще, существует вероятность, что сейчас 3 месяца будем искать деньги, еще 3 месяца будем планировать, а через месяц когда приступим окажется что коробочное решение того же майкрософта, гугла или амазона наконец отдуплилось и научилось нормально такие штуки из коробки пилить.\n  [Fedor](https://t.me/id1906): А самый наверное главный комментарий - идеологически это напоминает описание майкрософт офиса или гугл ворплейса, до появления майкрософт офиса или гугл воркплейса.  Описанная система выглядит очень логично и естественно. Например, Майкрософт копайлот продает себя как экосистему, выполняющую все вот эти требования скопом. Если в это влезть нам и авиасэйлз, то окажется что мы конкурируем с теми же майкрософт, гугл и всеми сэйлсфорсами на их поляне. Мы конечно очень умные и смелые, но ресурсы у нас весьма ограниченные...\n\n\nhttps://t.me/c/**********/746:\n2025-01-24T12:09:12Z\n[Ilya Briskin](https://t.me/il_37): Я бы посоветовался с Федей, как на такое лучше всего заходить. Потому что самое сложное — для любого решения — как разделять права доступа.\n\n\nhttps://t.me/c/**********/748:\n2025-01-24T12:10:29Z\n[Ilya Briskin](https://t.me/il_37): [Fedor](https://t.me/id1906) выше важный контекст и вопрос к тебе. Пожалуйста, внимательно всмотрись и вернись к нам с ценными инсайтами😊\n\n\nhttps://t.me/c/**********/749:\n2025-01-24T13:08:20Z\n[Fedor](https://t.me/id1906): Я сегодня про это пост писал как раз.\n\n\nhttps://t.me/c/**********/753:\n2025-01-24T13:22:55Z\n[Fedor](https://t.me/id1906): [Enterprise AI Adoption strategy: The Mirror, Not The Magic Wand | Fedor Zomba: My role sits at the intersection of company-wide AI adoption initiatives hands on individual implementation of GenAI projects. This blend of experiences has given me a healthy dose of pragmatism - maybe even skepticism - when it comes to bold proclamations like \"AI automates 80% of tasks!\" or \"Deplo](https://www.linkedin.com/pulse/enterprise-ai-adoption-strategy-mirror-magic-wand-fedor-zomba-9risf/?trackingId=mN2NQrj1Ti%2BQYO9v0w9heQ%3D%3D) Вот пост. Если пост пересказывать двумя предложениями, то коробочные AI решения - дорого, долго и стремно, обучать сотрудников, подход и тренировать AI Mindset'y тоже дорого, долго и стремно, но выхлоп намного конкретнее. А вот этот сложный проект который егор описал я бы взял, прикинул сколько он стоит (в ФМ он бы стоил в районе 800к USD только на разработку и оплату десяти пмов) и спросил, на что еще эти 800к можно потратить. Нампример - мы можем запилить complain generator прямо на авиасэйлс. Задержался рейс? Зааттачь билет и тул выплюнет весь пакет документов, подаст жалобу за вас и вернет деньги за комиссию 10%. Ну или зааттачь билет и сервис просто тебя проконсультирует пошагово как бабло срубить. Шуму такое наведет много, а делать проще в 10 раз.\n\n\nhttps://t.me/c/**********/755:\n2025-01-24T13:34:01Z\n[Fedor](https://t.me/id1906): Кстати, я сегодня познакомился с Ромой Рабиновичем - он фаундер [Цифровая транcформация бизнеса и корпоративного обучения: Цифровая трансформация бизнеса и корпоративного обучения, обучение продуктовых команд](https://neuromap.tech/) и он прямо сам пошел на контакт, приятно. [Ilya Briskin](https://t.me/il_37)  ты его кажется знаешь?\n  [Ilya Briskin](https://t.me/il_37): Да, знакомы с Ромой) он сейчас в Израиле?)\n\n\nhttps://t.me/c/**********/757:\n2025-01-24T16:13:45Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Смотрите, как я научился Good news everyone! Added ICC to ChatGPT as an independent bot. Here is an example: [ChatGPT - Crimes Against Humanity Examples: Shared via ChatGPT](https://chatgpt.com/share/6793bb3d-fed0-800a-8882-4b38cb8c90fb) Here is a bot to try: https://chatgpt.com/g/g-6793b7e45390819184a2b06b68ca156a-icc-gpt\n  [Юрий Воронцов](https://t.me/yuriy_vorontsov): Во-первых, это реальная интеграция агента. И у меня есть несколько задумок: 1. Добавить это в слайды вэбинара, как пример 2. Протестировать работу с таким ботом на своих данных и потом портировать это в Hugging Face Chat\n  [Ilya Briskin](https://t.me/il_37): А поч такая тема жесткая?\n\n\nhttps://t.me/c/**********/764:\n2025-01-24T21:05:04Z\n[Fedor](https://t.me/id1906): [AI in Education: Leveraging ChatGPT for Teaching: Offered by University of Pennsylvania. Working alongside ... Enroll for free.](https://www.coursera.org/learn/wharton-ai-in-education-leveraging-chatgpt-for-teaching)#modules\n\n\nhttps://t.me/c/**********/765:\n2025-01-24T21:11:50Z\n[Fedor](https://t.me/id1906): [Post-apocalyptic education: What comes after the Homework Apocalypse](https://www.oneusefulthing.org/p/post-apocalyptic-education)\n\n\nhttps://t.me/c/**********/767:\n2025-01-25T09:54:29Z\n[Fedor](https://t.me/id1906): Вот кстати история о которой за пределами моего отдела почти не говорят, а история важная: While making AI features free sounds great, there's a crucial detail being overlooked: Google made it nearly impossible to disable AI features for paying customers. This creates significant challenges for organizations in regulated markets who need granular control over AI usage. Microsoft's Copilot might be more expensive and claims to give enterprises more control, but let's be real - they're also fond of sneaking in AI features by default. As someone in Digital Workplace, playing \"whack-a-mole\" with unexpected AI capabilities popping up in production is becoming our favorite corporate game. Don't get me wrong - I'm a huge advocate for GenAI in enterprise. But I don't appreciate when vendors bypass governance to force adoption. The last thing I want is to wake up to headlines about AI-generated performance reviews causing union protests.\n\n\nhttps://t.me/c/**********/768:\n2025-01-25T10:00:55Z\n[Fedor](https://t.me/id1906): И вот ещё интересный тейк с утра  Today I learned about this area called \"MarTech\" It refers to \"software marketing teams use so they do not have to bug engineers to build stuff like send customized emails, track customers, optimize ads etc\" It's a MASSIVE category, apparently. Thousands of companies and big $$ [Gergely Orosz (@GergelyOrosz) on X: Today I learned about this area called \"MarTech\" It refers to \"software marketing teams use so they do not have to bug engineers to build stuff like send customized emails, track customers, optimize ads etc\" It's a MASSIVE category, apparently. Thousands of companies and big $$](https://x.com/GergelyOrosz/status/1882771371715125290)\n\n\nhttps://t.me/c/**********/769:\n2025-01-25T13:57:03Z\n[Fedor](https://t.me/id1906): [Fedor Zomba on LinkedIn: Comprehensive Review of AI Workplace Law and Litigation as We Enter 2025: Ever tried turning off AI features in your enterprise tools lately? Good luck with that. Vendors are increasingly making AI mandatory, and it's creating more…](https://www.linkedin.com/posts/fzomba_comprehensive-review-of-ai-workplace-law-activity-7288917541228351488-6BRx?utm_source=share&utm_medium=member_desktop) утром в куплете, вечером в газете\n\n\nhttps://t.me/c/**********/771:\n2025-01-27T06:39:42Z\n[Fedor](https://t.me/id1906): Вот такие истории мне просто мозг взрывают [Aaron Levie (@levie) on X: AI Agents have unlimited attention span and you can run them in parallel. Here's an example of using Operator to watch a live video feed and noting every time it sees a black vehicle in the stream. The generalizability means you can bring automation to anything.](https://x.com/levie/status/1883732583068127480) Я фантазировал лет 7 назад что уйду в отпуск и погружусь в computer vision на пару недель чтобы похожий pox запилить для общего развития, но заленился в итоге. А этот черт в 5 кликов в браузере сделал.\n\n\nhttps://t.me/c/**********/772:\n2025-01-27T06:47:40Z\n[Любимая](https://t.me/AnastasiaVorontsov): Вон какая мне реклама прилетела. Кто хочет классификацировать отзывов?\n\n\nhttps://t.me/c/**********/774:\n2025-01-27T07:17:47Z\n[Fedor](https://t.me/id1906): [S4E3 Grit & Growth | Co-Intelligence; An AI Masterclass with Ethan Mollick: Welcome to Grit & Growth’s masterclass on AI — a practical guide for experimenting and engaging with artificial intelligence. Ethan Mollick, Wharton School associate professor of innovation and entrepreneurship, AI visionary, and best-selling author walks us through the hype, fears, and potential of this transformative and complex technology. AI is reshaping business, society, and education with unprecedented speed. Ethan Mollick urges business leaders and educators to get in there and figure it out for themselves — to experiment and discover, rather than sitting on the sidelines waiting for AI to come to them. His latest book, Co-Intelligence: Living and Working with AI, is a practical guide for thinking and working with AI so you can determine how and where it can be utilized most effectively. Mollick believes that AI can help entrepreneurs at every stage of business, including coming up with the very idea for the business itself. “AI out-innovates people in most cases,” he says, “so you should probably be using it to help you generate ideas.” In fact, he encourages us to think about AI as a cofounder to bounce ideas off. Mollick also acknowledges that people need to push through those initial couple hours of resistance when exploring AI. “There’s a lot of reasons people stop using AI. It’s weird. It freaks them out. It gives them bad answers — initially. You need to push through, like there is a point of expertise with this, where you start to get what it does and what it doesn’t. Ten hours is my loose rule of thumb for how much time you have to spend using these systems to kind of get it.” Mollick’s Four Essential Rules for Integrating AI into Work and Life 1. Always invite AI to the table. “You don’t know what AI is good for or bad for inside your job or your industry. Nobody knows. The only way to figure it out is disciplined experimentation. Just use it a lot for everything you possibly can.” 2. Be the human in the loop. “The AI is better than a lot of people in a lot of jobs, but not at their whole job, right? And so, whatever you’re best at, you’re almost certainly better than the AI is.” 3. Treat AI like a human. AI models are “trained on human language, and they’re refined on human language. And it just turns out that they respond best to human speech. Telling it and giving tasks like a person often gets you where you need to go.” … (but tell it what kind of human to be) “AI models often need context to operate. Otherwise, they produce very generic results. So, a persona is an easy way to give context. ‘You are an expert marketing manager in India, focusing on technology ventures that work with the US’ will put it in a different headspace than if you say you’re a marketer or if you don’t give it any instructions at all.” 4. Assume this is the worst AI you will ever use.“We’re early, early days still. I mean, there’s a lot of stuff still being built.” Listen to Ethan Mollick’s insights on how AI can level the playing field for startups and how entrepreneurs and teams can use it to enhance creativity, efficiency, and innovation.](https://youtu.be/sOeNWib_nvo?si=KLUit7naKik6hIds) очень советую подкаст- супер толковый.\n\n\nhttps://t.me/c/**********/775:\n2025-01-27T09:19:45Z\n[Любимая](https://t.me/AnastasiaVorontsov): Посмотрим немножко дизайн слайдов?\n\n\nhttps://t.me/c/**********/783:\n2025-01-27T09:29:48Z\n[Любимая](https://t.me/AnastasiaVorontsov): Напишите, что нравится, что не нравится\n\n\nhttps://t.me/c/**********/784:\n2025-01-27T09:40:52Z\n[Любимая](https://t.me/AnastasiaVorontsov): Мы с Варей попробовали napkin.ai.\n\n\nhttps://t.me/c/**********/787:\n2025-01-27T09:41:53Z\n[Ilya Briskin](https://t.me/il_37): КРа\n\n\nhttps://t.me/c/**********/788:\n2025-01-27T09:41:54Z\n[Ilya Briskin](https://t.me/il_37): со\n\n\nhttps://t.me/c/**********/789:\n2025-01-27T09:41:54Z\n[Ilya Briskin](https://t.me/il_37): та\n\n\nhttps://t.me/c/**********/791:\n2025-01-27T09:44:01Z\n[Любимая](https://t.me/AnastasiaVorontsov): Napkin.ai сложно структурировать, когда информаци много - он начинает сокращать текст, отбрасывать часть иерархии\n\n\nhttps://t.me/c/**********/792:\n2025-01-27T17:47:56Z\n[Fedor](https://t.me/id1906): Это офигенно, выглядит дорого. Супер.\n\n\nhttps://t.me/c/**********/799:\n2025-01-27T18:34:44Z\n[Любимая](https://t.me/AnastasiaVorontsov): На остальных слайдах надо еще доработать текст.\n\n\nhttps://t.me/c/**********/800:\n2025-01-28T12:04:41Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Загнал чатик в GPT, но т.к. он не умеет читать файлы — пока протупил\n\n\nhttps://t.me/c/**********/802:\n2025-01-28T13:37:35Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): DeepSeek шарит За фото спс genza\n\n\nhttps://t.me/c/**********/803:\n2025-01-30T06:40:54Z\n[Fedor](https://t.me/id1906): [Fedor Zomba on LinkedIn: Wiz Research Uncovers Exposed DeepSeek Database Leaking Sensitive…: Remember my post about not sharing sensitive data with DeepSeek? Well, Wiz Research just found their database completely exposed - chat histories, API secrets, operational details, all accessible without authentication. This isn't just another security breach. When you combine basic security holes with frontier AI capabilities, the risks multiply exponentially. Imagine what malicious actors could learn from exposed chat histories and operational data of an AI system. The narrative of \"better, faster, cheaper\" AI development is appealing, but there's no magic here. Trade-offs aren't just about cost and performance - they show up in security, reliability, and trust. I'm impressed by what DeepSeek achieved technically for $6M. But this incident reminds us that \"cheap and fast\" often means cutting corners somewhere. Usually in the boring but crucial parts. Quick look at their LinkedIn job openings tells a story: engineers, data scientists, computer technicians (???), business development managers... but no AppSec…](https://www.linkedin.com/posts/fzomba_wiz-research-uncovers-exposed-deepseek-database-activity-7290616073136054273-G3eY?utm_source=share&utm_medium=member_ios)\n\n\nhttps://t.me/c/**********/804:\n2025-01-30T06:41:00Z\n[Fedor](https://t.me/id1906): Ooops :)\n\n\nhttps://t.me/c/**********/805:\n2025-01-30T13:42:52Z\n[Fedor](https://t.me/id1906): У меня сейчас очень интересный митинг был с презенташкой от команды edisonda.com Делали глубокий анализ готовности сотрудников к ai прямо хорошее исследование с интервью и всеми делами\n  [Юрий Воронцов](https://t.me/yuriy_vorontsov): В паблике есть материалы?\n  [Ilya Briskin](https://t.me/il_37): А можно исследование посмотреть?\n\n\nhttps://t.me/c/**********/807:\n2025-01-30T15:32:44Z\n[Fedor](https://t.me/id1906): Не\n\n\nhttps://t.me/c/**********/808:\n2025-01-30T15:32:59Z\n[Fedor](https://t.me/id1906): Но прикольно что такое бывает\n\n\nhttps://t.me/c/**********/809:\n2025-01-30T15:33:30Z\n[Fedor](https://t.me/id1906): UX reseach который оценивал ровно ai readiness\n\n\nhttps://t.me/c/**********/810:\n2025-01-30T21:00:25Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Я начал кормить мои записи в LiveWiki, очень прикольно\n\n\nhttps://t.me/c/**********/816:\n2025-01-31T23:04:42Z\n[Fedor](https://t.me/id1906): (Шепотом) Можно при личной встрече у меня через плечо\n\n\nhttps://t.me/c/**********/817:\n2025-02-01T09:37:27Z\n[Fedor](https://t.me/id1906): [MasterClass | Achieve More With GenAI: Discover how AI can supercharge your productivity and creativity. Learn from AI experts how to automate tasks and make AI work for you.](https://www.masterclass.com/series/achieve-more-with-gen-ai?utm_source=facebook&utm_medium=cpc&utm_campaign=PP_%7C_INS-GenAi_%7C_T2&ad_id=120216792229600079&adset_id=120216781934830079&campaign_id=120216781934850079&placement=Facebook_Mobile_Reels&site_source_name=%7B%7Bsite.source.name%7D%7D&fbclid=IwY2xjawIKyjRleHRuA2FlbQEwAGFkaWQBqxiL57x7LwEdZ3fw30HSU-y1-efvSpRj0kEW6AMsqasr0MBXw9YFdOaK1QpbbmOou86P_aem_zQ6Zv4nwAbky9MIjUDI7Sw) Cамый крутой чувак про эйай курс сделал на мастерклассе.\n\n\nhttps://t.me/c/**********/818:\n2025-02-01T09:37:37Z\n[Fedor](https://t.me/id1906): Ethan Mollick\n\n\nhttps://t.me/c/**********/819:\n2025-02-01T12:47:24Z\n[Ilya Briskin](https://t.me/il_37): [GitHub - logancyang/obsidian-copilot: THE Copilot in Obsidian: THE Copilot in Obsidian. Contribute to logancyang/obsidian-copilot development by creating an account on GitHub.](https://github.com/logancyang/obsidian-copilot)\n\n\nhttps://t.me/c/**********/820:\n2025-02-01T12:48:15Z\n[Ilya Briskin](https://t.me/il_37): опен-сурс ИИ плагин для популярной knowledge management тулзы\n\n\nhttps://t.me/c/**********/821:\n2025-02-01T12:48:20Z\n[Ilya Briskin](https://t.me/il_37): [Юрий Воронцов](https://t.me/yuriy_vorontsov) fyi\n\n\nhttps://t.me/c/**********/822:\n2025-02-01T12:48:57Z\n[Ilya Briskin](https://t.me/il_37): [The Ultimate AI Assistant for Your Second Brain](https://www.obsidiancopilot.com/en)\n\n\nhttps://t.me/c/**********/823:\n2025-02-01T12:53:53Z\n[Ilya Briskin](https://t.me/il_37): И вот такие ребятки [TextCortex: The most advanced enterprise AI platform: An AI platform for enterprise knowledge management––transforming scattered data into actionable insights for smarter decisions.](https://textcortex.com/)\n  [Юрий Воронцов](https://t.me/yuriy_vorontsov): По видосам это похоже на то, направление, про которое я говорил.\n\n\nhttps://t.me/c/**********/824:\n2025-02-01T12:54:02Z\n[Ilya Briskin](https://t.me/il_37): 1 млн визитов в месяц\n\n\nhttps://t.me/c/**********/826:\n2025-02-01T22:05:38Z\n[Ilya Briskin](https://t.me/il_37): Задача: организовать проектный менеджмент съемок комедийного скетча\n\n\nhttps://t.me/c/**********/828:\n2025-02-01T22:06:07Z\n[Ilya Briskin](https://t.me/il_37): Так выглядит список задач в гугл-таблице\n\n\nhttps://t.me/c/**********/830:\n2025-02-01T22:06:45Z\n[Ilya Briskin](https://t.me/il_37): Так выглядит список задач, сгруппированный чатом ГПТ по ответственному\n\n\nhttps://t.me/c/**********/831:\n2025-02-01T22:06:50Z\n[Ilya Briskin](https://t.me/il_37): Удобно — ппц!!\n\n\nhttps://t.me/c/**********/832:\n2025-02-01T22:08:19Z\n[Fedor](https://t.me/id1906): кайф\n\n\nhttps://t.me/c/**********/833:\n2025-02-01T22:10:15Z\n[Ilya Briskin](https://t.me/il_37): А потом очень удобно из чата копировать список и в ЛС его присылать ответственному!\n\n\nhttps://t.me/c/**********/834:\n2025-02-01T22:37:25Z\n[Ilya Briskin](https://t.me/il_37): [Magic Patterns: AI Design Tool: Build beautiful, functional components and frontend applications with generative AI.](https://www.magicpatterns.com/d/mE536SQ1d1dAFTcNsJZF2z)\n\n\nhttps://t.me/c/**********/835:\n2025-02-01T22:37:55Z\n[Ilya Briskin](https://t.me/il_37): Можно начать собирать кейсы реального применения ИИ для работы. Не видел еще такого.\n\n\nhttps://t.me/c/**********/836:\n2025-02-01T22:38:12Z\n[Ilya Briskin](https://t.me/il_37): Хотя полюбому такая UGC-площадка — полезное дело\n\n\nhttps://t.me/c/**********/837:\n2025-02-01T23:33:52Z\n[Ilya Briskin](https://t.me/il_37): Вот еще кейс: не могу вспомнить ник девочки, которая год назад помогала на съемках. а если бы все-все мои чаты и переписки были бы скормлены (в идеале по АПИ) ЛЛМке, я бы просто спросил \"Пришли ники всех, с кем я общался в октябре 2023 с комментарием, о чем шла речь в переписке с этим ником\"\n\n\nhttps://t.me/c/**********/838:\n2025-02-02T02:59:38Z\n[Ilya Briskin](https://t.me/il_37): Спросил у чатаГПТ: Приведи примеры конкретных кейсов, когда доступ ИИ к чатам, базам знаний и файловым хранилищам команды из 10 человек повышает эффективность взаимодействия команды. При условии, что у каждого из членов команды есть личная ЛЛМ с доступом ко всем его чатам, базам знаний, файловым хранилищам.\n\n\nhttps://t.me/c/**********/839:\n2025-02-02T03:00:37Z\n[Ilya Briskin](https://t.me/il_37): Чат ответил: 📌 Кейсы, когда каждый член команды имеет личную ЛЛМ с доступом к своим чатам, базам знаний и файловым хранилищам В такой системе личные ИИ-ассистенты действуют как персональные помощники, которые знают контекст работы каждого сотрудника и помогают ему эффективнее взаимодействовать с коллегами. 🔍 1. Мгновенные ответы на вопросы внутри команды 📌 Кейс: Менеджер спрашивает в командном Slack: «Кто последний обновлял техническую документацию по API?» 🤖 Решение: Личные ЛЛМ анализируют чаты и файлы, находят последний апдейт и мгновенно дают ответ. ✅ Результат: Вместо того, чтобы ждать ответа коллег, нужную информацию получают за секунды. 📂 2. Быстрый поиск нужных файлов и обсуждений 📌 Кейс: Дизайнеру нужно найти последние правки UI, но искать в Figma, Slack и Google Drive – долго. 🤖 Решение: Личная ЛЛМ анализирует все источники (чаты, файлы, таск-менеджеры) и выдает ссылку на актуальный макет. ✅ Результат: Экономия 10-15 минут на каждый поиск файлов. 📊 3. Автоматическая подготовка к встречам 📌 Кейс: Перед митингом продукт-менеджеру нужно собрать данные по задачам, обсуждаемым в Slack и Jira. 🤖 Решение: Личная ЛЛМ собирает апдейты из всех источников, формирует краткое резюме и список вопросов. ✅ Результат: Участники приходят на встречу подготовленными, митинги короче и продуктивнее. ✍️ 4. Автоматическое резюмирование чатов и переписки 📌 Кейс: PM пропустил важное обсуждение в Slack и теперь тратит время на чтение сотни сообщений. 🤖 Решение: Личная ЛЛМ анализирует чат и выдает краткое резюме с ключевыми решениями и задачами. ✅ Результат: Время на вникание в контекст сокращается в 3-5 раз. 🗂 5. Автоматическое обновление базы знаний 📌 Кейс: Разработчик вносит изменения в код, но документацию обновлять забывает. 🤖 Решение: Личная ЛЛМ фиксирует изменения, анализирует чаты и файлы, обновляет базу знаний в Notion. ✅ Результат: Документация всегда актуальна, новые сотрудники быстрее вникают в код. ⏳ 6. Автоматическое напоминание о дедлайнах и задачах 📌 Кейс: У разработчика несколько тасков в Jira, но он забыл про срочную задачу. 🤖 Решение: Личная ЛЛМ анализирует таск-менеджер и Slack, напоминает о ближайших дедлайнах. ✅ Результат: Снижается количество просроченных задач. 🎤 7. Транскрибация и анализ голосовых сообщений 📌 Кейс: Внутри команды часто отправляют голосовые сообщения, но текстовый формат удобнее. 🤖 Решение: Личная ЛЛМ автоматически расшифровывает голосовые и выдает текстовые версии с ключевыми моментами. ✅ Результат: Все участники быстрее обрабатывают информацию и не тратят время на прослушивание. 🎯 8. Персонализированные рекомендации по работе 📌 Кейс: PM хочет понять, какие задачи в бэклоге самые важные, но приоритизация занимает много времени. 🤖 Решение: Личная ЛЛМ анализирует прошлые обсуждения, бизнес-цели и предлагает приоритетный список задач. ✅ Результат: Оптимизация работы, меньше перегрузки. 📅 9. AI-помощник по календарю 📌 Кейс: Член команды хочет поставить встречу, но не знает, когда у коллег свободное время. 🤖 Решение: Личная ЛЛМ анализирует календари всех участников и предлагает оптимальное время. ✅ Результат: Быстрое согласование встреч без долгих переписок. 🛠 10. Автоматическое формирование тасков из чатов 📌 Кейс: В обсуждении в Slack возникла идея, но никто не зафиксировал ее в Jira. 🤖 Решение: Личная ЛЛМ распознает важные моменты в переписке и предлагает создать задачу в таск-менеджере. ✅ Результат: Идеи не теряются, команда быстрее внедряет улучшения. 🚀 Как это меняет работу команды? 🔹 Больше автоматизации – меньше ручной работы с файлами, чатами и базами знаний. 🔹 Быстрое принятие решений – нужную информацию получают за секунды. 🔹 Снижение хаоса – всё структурируется и обновляется автоматически. 🔹 Экономия времени – каждую неделю экономится 5-10 часов на поиск информации и рутину. Если нужен разбор кейса под твои задачи – пиши! 🔥\n\n\nhttps://t.me/c/**********/840:\n2025-02-02T03:02:22Z\n[Ilya Briskin](https://t.me/il_37): Реально классные кейсы, по-моему) Особенно: 1, 3, 4, 5 (но не с разработчиком, а с проджектом, продактом, сейлзом), 6, 8, 9\n\n\nhttps://t.me/c/**********/842:\n2025-02-02T15:57:55Z\n[Ilya Briskin](https://t.me/il_37): [Google NotebookLM | Note Taking & Research Assistant Powered by AI: Use the power of AI for quick summarization and note taking, NotebookLM is your powerful virtual research assistant rooted in information you can trust.](https://notebooklm.google/)\n\n\nhttps://t.me/c/**********/843:\n2025-02-04T16:58:20Z\n[Ilya Briskin](https://t.me/il_37): [Fedor](https://t.me/id1906) вопрос по вебинару по BIM Я пробежался по интернетам и не нашел нормально описанных кейсов, чтобы показать Но я не супер-внимательно искал. Можно тебя попросить порисерчить? У тебя глаз наметан!\n\n\nhttps://t.me/c/**********/844:\n2025-02-04T17:03:45Z\n[Fedor](https://t.me/id1906): Я попробую.\n\n\nhttps://t.me/c/**********/845:\n2025-02-06T05:22:04Z\n[Fedor](https://t.me/id1906): [The World's Leading AI Platform for Enterprise | Cohere: Cohere is the leading AI platform for enterprise. Augment your workforce, automate workflows, and enrich customer experiences with secure and scalable AI.](https://cohere.com/) смотрите какие интересные ребята\n\n\nhttps://t.me/c/**********/846:\n2025-02-06T05:23:48Z\n[Fedor](https://t.me/id1906): Это прям эй ай майндсет. Мне кажется с их сайта можно надергать идей\n\n\nhttps://t.me/c/**********/847:\n2025-02-06T16:49:42Z\n[Ilya Briskin](https://t.me/il_37): [How AI Will Transform Construction (2025): Free training course on ChatGPT for Project Managers: https://courses.construct-iq.com/courses/ChatGP-for-Project-Managers Discover how artificial intelligence is revolutionizing the construction industry in 2024. From digital construction and construction automation to advanced construction management techniques, AI is reshaping the way projects are planned, executed, and monitored. Learn how cutting-edge technologies like ChatGPT are being integrated into construction processes, paving the way for increased efficiency and productivity. Stay ahead of the curve and explore the future of construction with AI.](https://www.youtube.com/watch?v=-kS2kuprIzI) Из этой презентации нам есть чего утащить, как думаешь? [Fedor](https://t.me/id1906)\n  [Fedor](https://t.me/id1906): Я пробежался по первой половине видео, пока ничего принципиально нового. Анализ данных, business writing, project management, learning.\n\n\nhttps://t.me/c/**********/849:\n2025-02-06T17:47:35Z\n[Fedor](https://t.me/id1906): Хороший слайд про то что нельзя отдавать ai -ограничения\n\n\nhttps://t.me/c/**********/850:\n2025-02-06T18:05:28Z\n[Fedor](https://t.me/id1906): Там в общем чувак говорит, что эйай особо не поможет, но в целом клиенты смотрят на construction costs в первую очередь и если благодаря ai удастся срезать косты, то зашибись.\n\n\nhttps://t.me/c/**********/851:\n2025-02-06T18:07:41Z\n[Fedor](https://t.me/id1906): Короче его вывод - хз как повлияет\n\n\nhttps://t.me/c/**********/852:\n2025-02-06T18:09:29Z\n[Fedor](https://t.me/id1906): Лажа\n\n\nhttps://t.me/c/**********/853:\n2025-02-06T18:13:05Z\n[Fedor](https://t.me/id1906): Ну я бы сказал в общем что этот чувак может и шарит в констракшн но вообще не рубит в genai\n\n\nhttps://t.me/c/**********/854:\n2025-02-06T19:52:17Z\n[Ilya Briskin](https://t.me/il_37): [ИИ и машиночитаемость в стройке | Поговорим за BIM | Ольга Кутузова: Версия в VK в Telegram канале InfraBIM.Pro: https://t.me/InfraBIM/183 Аудио версия: https://t.me/InfraBIM/184 Онлайн-курсы Робур, Civil 3D, Infraworks, Revit ИССО и Autodesk Subassembly Composer https://infrabim.pro/online-course 00:00 Вводное слово 01:23 Вступление про ИИ 02:45 Чем занимается Ольга 04:45 Машиночитаемость\\Машинопонимаемость\\Машиноинтерпретиумость в чем разница 12:30 Перевод нормативных документов в машиночитаемость  20:55 Связь базы требований с САПР 23:30 Машинопонимаемые требования для автоматической проверки ЦИМ 39:18 Проверка на коллизии по нормативным документами в Нанокаде 41:30 Зачем это всё в BIM (ЦИМ) 46:44 Государственное направление в машинопонимании. Реестр требований 51:05 Насколько применим ИИ в строительстве 58:05 Чат бот по нормативным требованиям 1:00:53 Большие языковые модели для строительства и нормативки 1:06:21  Анализ текстовой части проектной документации 1:17:15 К чему это всё идёт?](https://youtu.be/c3Hgb3AbZuE?si=Q6R5R6AyNC92wFVz)\n\n\nhttps://t.me/c/**********/855:\n2025-02-08T07:33:19Z\n[Fedor](https://t.me/id1906): https://x.com/mindbranches/status/1887606988831662233?s=52\n\n\nhttps://t.me/c/**********/857:\n2025-02-08T07:35:00Z\n[Fedor](https://t.me/id1906): [How real-world businesses are transforming with AI – with 50 new stories: Updated February 5, 2025: The post contains 50 new customer stories, which appear at the beginning of each section of customer lists. The post will be updated regularly with new stories. One of the highlights of my career has always been connecting with customers and partners across industries to learn how they are using technology...](https://blogs.microsoft.com/blog/2025/02/05/https-blogs-microsoft-com-blog-2024-11-12-how-real-world-businesses-are-transforming-with-ai/)\n\n\nhttps://t.me/c/**********/858:\n2025-02-08T16:09:14Z\n[Ilya Briskin](https://t.me/il_37): When foundational models become commoditized through open source, the real profit will come from industry-specific applications. Интересная точка зрения [From the legaltech community on Reddit: Explore this post and more from the legaltech community](https://www.reddit.com/r/legaltech/comments/1ijicb4/open_source_ai_wont_be_enough_distribution_and/) [Fedor](https://t.me/id1906) что думаешь?\n  [Fedor](https://t.me/id1906): Отличный тейк, согласен почти полностью\n  [Felix T](https://t.me/Felixxx89): Да оч интересно примерно тоже самое сегодня от родственника из Deloitte слышал\n  [Fedor](https://t.me/id1906): Мне кажется что чувак упускает главный затык: specialized tool все равно кто-то должен будет внедрять, а людей с релевантными скиллами очень мало.\n\n\nhttps://t.me/c/**********/862:\n2025-02-08T17:21:38Z\n[Fedor](https://t.me/id1906): Главный боттонек в этой революции - мало business oriented ai engineers\n\n\nhttps://t.me/c/**********/863:\n2025-02-08T17:22:06Z\n[Fedor](https://t.me/id1906): Мне очень понравилось про AWS; прям четко в тему.\n\n\nhttps://t.me/c/**********/864:\n2025-02-09T08:05:13Z\n[Fedor](https://t.me/id1906): EU наш: в смысле что со второго февраля действует обязательство всех сотрудник обучать ai грамотности\n\n\nhttps://t.me/c/**********/865:\n2025-02-09T08:05:13Z\n[Fedor](https://t.me/id1906): The EU AI Act's AI literacy requirements (Article 4) and prohibited AI practices (Article 5) became legally binding on February 2, 2025, marking the first phase of the regulation's implementation. Here’s a detailed breakdown: --- ### 1. AI Literacy Requirements Under Article 4  - Who is affected? Providers and deployers of AI systems (e.g., developers, companies using AI). - Key obligations:   - Ensure staff and third parties involved in operating AI systems have sufficient AI literacy, tailored to their roles, technical knowledge, and context of use.   - Promote understanding of AI’s opportunities, risks, and potential harms, including compliance with ethical and legal standards.   - Training programs must address technical skills, data governance, and intersectional risks (e.g., privacy, cybersecurity). - Scope: Applies to all AI systems, regardless of risk level (e.g., high-risk or minimal-risk systems). --- ### 2. EU Support Mechanisms for Compliance - Living Repository: The EU AI Office created a dynamic database of AI literacy practices (e.g., training modules, workshops) from organizations like AI Pact signatories. While not a compliance guarantee, it fosters knowledge-sharing. - Guidelines: The Commission published non-binding guidelines to clarify the AI system definition and prohibited practices, with updates planned based on stakeholder feedback. - Webinars and Consultations: Events like the AI Pact webinar on February 20, 2025, aim to guide organizations in implementing Article 4. --- ### 3. Penalties and Enforcement - AI Literacy (Article 4): No direct fines, but non-compliance may influence penalties for other breaches (e.g., high-risk system violations). - Prohibited Practices (Article 5): Fines up to €35 million or 7% of global turnover for banned AI uses (effective August 2025). --- ### 4. Prohibited AI Practices Under Article 5 The AI Act bans AI systems that: 1. Manipulate behavior using subliminal/deceptive techniques (e.g., targeting vulnerable groups). 2. Social scoring for public/private purposes (e.g., evaluating individuals based on social behavior). 3. Predict criminal behavior via profiling or personality traits. 4. Untargeted facial scraping from the internet/CCTV to build databases. 5. Real-time biometric identification in public spaces by law enforcement (with narrow exceptions). 6. Emotion recognition in workplaces/education (exceptions for medical/safety uses). --- ### 5. Implementation Timeline - February 2, 2025: AI literacy (Article 4) and prohibited practices (Article 5) take effect. - August 2, 2025: Governance rules for general-purpose AI models apply. - August 2026: Most obligations (e.g., high-risk systems) become enforceable. - August 2027: Extended deadlines for high-risk systems in regulated products. --- ### Recommendations for Organizations - Audit AI systems to identify prohibited practices and align with exceptions (e.g., medical use for emotion recognition). - Develop training programs integrating AI literacy with existing frameworks (e.g., GDPR compliance). - Engage with EU guidelines and the living repository to adopt best practices. For further details, refer to the [EU AI Act guidelines]([The Commission publishes guidelines on AI system definition to facilitate the first AI Act’s rules application: The guidelines on the AI system definition explain the practical application of the legal concept, as anchored in the AI Act.](https://digital-strategy.ec.europa.eu/en/library/commission-publishes-guidelines-ai-system-definition-facilitate-first-ai-acts-rules-application)) and the [AI literacy repository](https://digital-strategy.ec.europa.eu/en/library/living-repository-foster-learning-and-exchange-ai-literacy).\n\n\nhttps://t.me/c/**********/866:\n2025-02-10T09:59:37Z\n[Fedor](https://t.me/id1906): [This 20+ AI Agent Team Automates ALL Your Work (GPT-01) (Relevance AI): 👇 Join my community and get the template of this video, all my other templates, tech help & more: https://bit.ly/bensaicommunity 👇 Get the Agent System Overview (Figjam): https://benvansprundel.gumroad.com/l/xdgpur 💼 AI implementation or consulting? 📆 Book me in here: https://www.benvansprundel.com/ 🔗 My links: Linkedin: https://www.linkedin.com/in/benvansprundel/ X: https://x.com/ben_vs92 💻 Softwares I use (some of these are affiliate-links, thanks!): Relevance AI: https://relevanceai.com/?via=ben ElevenLabs: https://try.elevenlabs.io/fps0xgonqagd Sendspark: https://sendspark.com/?via=ben-ai Make.com: https://www.make.com/en/register?pc=benai Agentive: https://agentivehub.com/ Dumpling AI: https://www.dumplingai.com/?via=ben-ai Chapters: 00:00 - Intro 01:42 - Live Demo 10:22 - Agent System Overview 21:30 - Director Agent Setup 37:36 - Research Manager Agent + Subagents Setup 52:26 - Comm Manager Agent + Subagents Setup 1:02:02 - Project Manager Agent + Subagents Setup 1:07:05 - Content Manager Agent + Subagents Setup 1:09:50 - How to Schedule Automations 1:14:23 - Whatsapp Trigger Setup 👋🏼 About me: I'm Ben, a 3-time founder & Dutch AI entrepreneur. In 2023, I started my AI automation agency https://www.benvansprundel.com/. In this channel, I help businesses, professionals & aspiring AI entrepreneurs to build powerful no-code AI Agents & Automations for 10x efficiency. I post new useful AI use cases for businesses weekly.](https://youtu.be/Lj5fyDX01v8?si=xLkmIgCgV7YnvH_t) for inspiration\n\n\nhttps://t.me/c/**********/867:\n2025-02-10T10:06:49Z\n[Fedor](https://t.me/id1906): Просто бомба\n  [Ilya Briskin](https://t.me/il_37): Просто Зомба!\n\n\nhttps://t.me/c/**********/869:\n2025-02-10T13:58:18Z\n[Fedor](https://t.me/id1906): https://n8n.io Юра, а ты видел эту штуковину? n8n.io - a powerful workflow automation tool: n8n is a free and source-available workflow automation tool\n  [Юрий Воронцов](https://t.me/yuriy_vorontsov): Про эту слышал, видел аналоги\n\n\nhttps://t.me/c/**********/870:\n2025-02-10T14:11:03Z\n[Ilya Briskin](https://t.me/il_37): Сергей, привет! Программа буткемпа есть здесь. Следующий поток стартует через недели две примерно SalesAI Bootcamp - практическое использование Ai для задач продаж | Notion: Привет! Мы, ведущие специалисты по практическому применению ИИ в бизнесе — Дмитрий Коробовцев и Павел Журавлев запустили SalesAi Bootcamp — программу, посвящённую практическому использованию ИИ для решения актуальных задач в продажах.\n\n\nhttps://t.me/c/**********/872:\n2025-02-10T17:20:56Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Ого какие темы постит Anthropic. Не могу не поделиться.  Ребята выпустили исследование как же используется AI сейчас и каков его экономический вклад (хотя тут я не очень понял). Цифры крайне занимательные. Картиночки приложу.  Основные выводы: - большая часть использования(36%) AI приходится на задачи связанные с technical documentation etc и software development (кто бы мог подумать, да?) - небольшой скос в сторону именно не полной автоматизации, а так называемой аугументации задач, это когда вы проверяете или дополняете задачу с помощью AI (я думаю разрыв в сторону аугументации будет расти очень сильно) - большая часть задач которые решаются категоризированы к профессиям среднего/высокого достатка. И практически ничего с самыми низкими и высоко оплачиваемыми ролями. Тут я считаю просто скос из-за аудитории, которая использует их Claude. Вряд ли дворники или CEO доверяют свои задачи AI. Пока что.  Крайне советую прочесть, дает много инсайтов как и по областям применения и того, что действительно может работать.  Я все так же считаю: - полная автоматизация с помощью агентов будет очень нескоро даже простых задач (см как же быстро вошли в жизнь self driving cars) - все больше применения будет в аугументации задач и улучшении результатов (а-ля инструмент помощи) - агенты == зло, базовые автоматизации круть (отдельные блоки в процессах, latency, качество определенных решений - условно замена старой технологии на новую)\n  [Fedor](https://t.me/id1906): Офигенно!\n\n\nhttps://t.me/c/**********/875:\n2025-02-10T18:50:15Z\n[Fedor](https://t.me/id1906): Находка просто! Спасибо!\n\n\nhttps://t.me/c/**********/878:\n2025-02-13T01:26:12Z\n[Fedor](https://t.me/id1906): Интересная дискуссия и позиционирование RAGa\n\n\nhttps://t.me/c/**********/879:\n2025-02-13T12:13:04Z\n[Fedor](https://t.me/id1906): Хороший пример\n\n\nhttps://t.me/c/**********/880:\n2025-02-13T12:13:04Z\n[Fedor](https://t.me/id1906): [Usman Sheikh on LinkedIn: Two founders. $3.6M in funding. One \"tedious\" task.  That's how… | 30 comments: Two founders. $3.6M in funding. One \"tedious\" task.  That's how disruption always starts.  Why This Matters:  → Perceptis AI automated proposal writing  →… | 30 comments on LinkedIn](https://www.linkedin.com/posts/usmans_two-founders-36m-in-funding-one-tedious-activity-7295728429897818116-ZxCx?utm_source=share&utm_medium=member_ios&rcm=ACoAAAhZ5JEBT9b0Pp349B_7noUrVrbpOkA7FDs)\n", "keywords": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-02-13T12:13:04Z", "entities": [{"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[<PERSON> T](https://t.me/Felixxx89)"}, {"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}, {"id": "https://t.me/c/**********#groupped:part3", "url": "https://t.me/c/**********/883", "title": "AI mindset", "language": "ja", "text": "https://t.me/c/**********/883:\n2025-02-18T07:29:45Z\n[<PERSON><PERSON>](https://t.me/id1906): мы же встретимся живьем за ai mindset поговорить? Можно в четверг, или завтра?\n  [Юрий Воронцов](https://t.me/yuri<PERSON>_voro<PERSON><PERSON>): Я за!\n\n\nhttps://t.me/c/**********/884:\n2025-02-18T07:42:43Z\n[<PERSON> T](https://t.me/Felixxx89): Мы сегодня в тельчике встречаемся\n\n\nhttps://t.me/c/**********/886:\n2025-02-18T07:54:03Z\n[Fedor](https://t.me/id1906): Я в 9 свободен! Ялла!\n  [<PERSON> T](https://t.me/Felixxx89): Мне еще в Беер Шева возвращаться поздно 9 поздновато\n\n\nhttps://t.me/c/**********/887:\n2025-02-18T07:56:29Z\n[Fedor](https://t.me/id1906): חיים שלי! יאללה!!\n\n\nhttps://t.me/c/**********/889:\n2025-02-18T09:40:03Z\n[Fedor](https://t.me/id1906): Я к вам приду чуть попозже\n  [Юрий Воронцов](https://t.me/yuriy_vorontsov): Тебя ждать?\n\n\nhttps://t.me/c/**********/892:\n2025-02-18T19:14:35Z\n[Fedor](https://t.me/id1906): Лечу на встречу к вам друзья.\n\n\nhttps://t.me/c/**********/893:\n2025-02-18T19:17:32Z\n[Felix T](https://t.me/Felixxx89): какой eta\n\n\nhttps://t.me/c/**********/894:\n2025-02-20T07:22:49Z\n[Fedor](https://t.me/id1906): Вот чуваки пилят курсы и активно их форсят\n\n\nhttps://t.me/c/**********/895:\n2025-02-20T07:22:50Z\n[Fedor](https://t.me/id1906): Как я пошла учиться в ИИ лабу. Я как-то еще пару месяцев назад рекламировала вот этих ребят - AI Mindset. Они организовывали уже шестую лабу по системной работе с ИИ - ну типа не просто \"научись применять ИИ\" или \"сделай бота\", а выстрой систему из разных инструментов.  Это меня зацепило и я решила сама пойти к ним учиться. Потому что по чесноку, я ппц какой хаотик. У меня заметки валяются в 20 разных местах, и я до сих пор не знаю, сколько из поставленных на 2024 год целей я выполнила, потому что я не могу найти, куда я их записала 😂 Ну и с нейронками у меня так же. Чуваки обещали научить именно ИИ майндсету.  Когда ты, глядя на любую новую задачу, в первую очередь думаешь, как нейронки могут облегчить ее выполнение. (желательно чтобы они вообще сделали ее сами, пока ты идешь бахнуть пивка с другими нейроработягами) Ну и в общем, 25го января мы погнали учиться 4 недели. Структура примерно такова: Есть общий чат, есть несколько дополнительных чатов по отдельным направлениям, которые интересны. Я выбрала автоматизацию и чат-боты. Каждым направлением заведует отдельный эксперт. Всего во время обучения было 8 созвонов (4 воркшопа, 4 коворкинга, где можно онлайн поработать вместе с другими участниками, и 4 office hours, где разбираются вопросы участников.) А еще у лабы - гигантская и очень тщательно структурированная база знаний, сделанная в Obsidian (черт, пора уже в него погрузиться, адепты обсидиана меня туда тащат второй год) Там хранятся записи созвонов, их транскрипции и саммари, список инструментов и инструкции и тд Я не успевала участвовать в созвонах реалтайм (потому что у меня всратый часовой пояс сейчас и переезды). Но смотрела записи и общалась в чатах. На примере одной маленькой автоматизации, которую я сделала, опишу процесс: - закинула в чатик свою задачу (когда на почту падает письмо с ключевым словом в заголовке, кидать мне пуш в телегу) - эксперт быстренько предложил несколько вариантов автоматизации готовыми сервисами - покопалась, поняла что привязать к ним gmail не получится из-за того что от сервиса автоматизации нужно согласие получать пересылку - обсудили еще раз - пришли к выводу, что можно сделать руками через сервис гугла Apps Script и javascript кода, который мне напишет чат джипити. Я пошла делать. Процесс занял 20 минут и всего 3 ошибки от Apps Script, с которыми мне, опять же, помог чат джипити. Все работает! Дааааа, и я кстати вообще не умею погромировать, тем более - на javascript. 🌚 Ну и некоторые мои выводы по итогам лабы (меня попросили написать честные впечатления): - формат мощный и реально интенсивный. Чтобы получить максимум, надо на 4 недели вгрузиться максимально. Такой ИИ-выживач. - это сильно проще, чем самому ковыряться. Есть об кого подумать. И есть мотивация не бросить на полпути. - на мой взгляд, эта лаба не подойдет полным гуманитариям. Ну, скажем если вы впервые слышите слова типа \"токен\", \"API\", \"гитхаб\", вам будет очень сложно. Надо быть хоть немножко технарями или готовыми погрузиться. Если один вид джаваскрипта вызывает у вас желание беспомощно заплакать, вам будет тяжело. 😭 - Если у вас аллергия на английский язык - будет тяжело тоже. В ИИ сфере очень много всего пишется на английском, во время лабы презы тоже были на английском (спикеры говорили на русском).  Короче, лет ми спик фром май харт. Если вы из тех кто \"рррряяяя хватит англицизмов\" (у меня есть такие подписчики), то лаба не для вас. Да, собсно 25 марта у них следующий поток, если то, что я написала, вам отзывается, можете вписаться на борт вот тут.\n\n\nhttps://t.me/c/**********/896:\n2025-02-24T21:16:23Z\n[Fedor](https://t.me/id1906): Ребята, я тут играюсь с Claude 3.7 и это полный пипец!  Такого я ещё не видел.  [A new generation of AIs: Claude 3.7 and Grok 3: Yes, AI suddenly got better... again](https://open.substack.com/pub/oneusefulthing/p/a-new-generation-of-ais-claude-37?r=ltq7b&utm_medium=ios) Он реально умнее на голову Claude 3.5 и такой же милый (кажется)\n\n\nhttps://t.me/c/**********/897:\n2025-02-24T21:17:18Z\n[Любимая](https://t.me/AnastasiaVorontsov): Примеры в студию!\n\n\nhttps://t.me/c/**********/899:\n2025-02-24T21:21:24Z\n[Fedor](https://t.me/id1906): Курсор у всех внезапно и резко поумнел\n\n\nhttps://t.me/c/**********/900:\n2025-02-24T21:36:13Z\n[Fedor](https://t.me/id1906): https://x.com/deedydas/status/1894110678027571412 твитер кишит ими\n\n\nhttps://t.me/c/**********/901:\n2025-02-24T21:37:53Z\n[Fedor](https://t.me/id1906): Мне понравился такой: Make me an interactive time machine artifact, let me  travel back in time and interesting things happen. pick unusual times I  can go back to… и Add graphics to it\n\n\nhttps://t.me/c/**********/902:\n2025-02-24T23:03:22Z\n[Fedor](https://t.me/id1906): [Anthropic’s Claude 3.7 Sonnet is available on Vertex AI | Google Cloud Blog: Claude 3.7 Sonnet, Anthropic’s most intelligent model to date and the first hybrid reasoning model on the market, is available on Vertex AI. Get started today.](https://cloud.google.com/blog/products/ai-machine-learning/anthropics-claude-3-7-sonnet-is-available-on-vertex-ai?linkId=13111326) Они еще и в мультиклауд пошли к гуглу. Вау вау. Запомните этот твит: антропик к концу года по капитализации догонит OpenAI.\n\n\nhttps://t.me/c/**********/906:\n2025-02-25T09:26:50Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Но какие-то ошибки, который пропускал 3.5 нашёл\n\n\nhttps://t.me/c/**********/907:\n2025-02-25T09:27:40Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): я то сам в настройках кубера пока не всё знаю, например, не очевидно, что есть специальный флаг, который обязывает ставить самые свежие версии докер контейнеров\n\n\nhttps://t.me/c/**********/909:\n2025-02-25T09:28:07Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): нет\n\n\nhttps://t.me/c/**********/910:\n2025-02-25T09:31:01Z\n[Fedor](https://t.me/id1906): Кажется огонь штуковина\n\n\nhttps://t.me/c/**********/911:\n2025-02-25T09:31:28Z\n[Юрий Воронцов](https://t.me/yuriy_vorontsov): Ты видел что-то кроме демо видео?\n\n\nhttps://t.me/c/**********/912:\n2025-02-25T10:18:19Z\n[Fedor](https://t.me/id1906): Пока нет, не успел ещё. В соседнем чате люди довольные сидят.\n\n\nhttps://t.me/c/**********/914:\n2025-03-04T12:30:21Z\n[Fedor](https://t.me/id1906): [Crossing the uncanny valley of conversational voice: At Sesame, our goal is to achieve “voice presence”—the magical quality that makes spoken interactions feel real, understood, and valued.](https://www.sesame.com/research/crossing_the_uncanny_valley_of_voice)#demo\n\n\nhttps://t.me/c/**********/915:\n2025-03-04T12:30:41Z\n[Fedor](https://t.me/id1906): Попробуйте сервис пожалуйста. О_о\n\n\nhttps://t.me/c/**********/916:\n2025-03-09T13:15:14Z\n[Fedor](https://t.me/id1906): Друзья, похвастаюсь - я сейчас провожу тестовый четырехчасовой воркшоп с нашими head of procurement и head of supply chain. Оба пищат от восторга. Сейчас переходим а практической части, не знаю, как оно обернется, но это можно как есть заворачивать и продавать.\n  [Ilya Briskin](https://t.me/il_37): Можно будет посмотреть запись?\n\n\nhttps://t.me/c/**********/918:\n2025-03-09T13:32:09Z\n[Ilya Briskin](https://t.me/il_37): И/или структуру\n\n\nhttps://t.me/c/**********/919:\n2025-03-09T16:22:56Z\n[Fedor](https://t.me/id1906): Можно, но для этого я предпочел бы собраться оффлайн. :)\n\n\nhttps://t.me/c/**********/920:\n2025-03-09T17:07:53Z\n[Fedor](https://t.me/id1906): Если вкратце, я только что провел четырехчасовой тренинг, который затянулся на лишние полтора часа - те, кого я обучал хотели еще и еще. Я объяснял нашим head of procurement и head of supply chain про helpful questions и problem questions, ограничение языковых моделей и используя знания во второй части мы придумывали об AI как заработать больше денег для компании. Я очень волновался: делал эту презентацию вчера до трех часов ночи и все утро. Но получилось прекрасно: оба head’a пищат от восторга, им супер полезно и идеи упали на благодатную почву. Очень уставший и очень довольный.\n\n\nhttps://t.me/c/**********/921:\n2025-03-09T18:23:32Z\n[Fedor](https://t.me/id1906): Хозяйке на заметку\n\n\nhttps://t.me/c/**********/922:\n2025-03-09T18:23:32Z\n[Fedor](https://t.me/id1906): Our friend Oleg Mosyazh, jointly with his partners from the U.S., is organizing The Venture Award Israel in early April in Tel Aviv.  We encourage investment-ready early-stage startups to apply!  The top 12 startups will be invited to the award ceremony to pitch to 10+ Israeli VCs and syndicates. More details can be found here:\" #theventures #futuround #startupaward #startupnation #meetthejudges | Oleg Mosyazh: 🚀 𝗠𝗲𝗲𝘁 𝘁𝗵𝗲 𝗝𝘂𝗱𝗴𝗲𝘀 𝗼𝗳 𝗧𝗵𝗲 𝗩𝗲𝗻𝘁𝘂𝗿𝗲𝘀 𝗔𝘄𝗮𝗿𝗱 𝗜𝘀𝗿𝗮𝗲𝗹! 🚀 As we gear up for 𝗧𝗵𝗲 𝗩𝗲𝗻𝘁𝘂𝗿𝗲𝘀 𝗔𝘄𝗮𝗿𝗱 𝗜𝘀𝗿𝗮𝗲𝗹, we’re thrilled to introduce the powerhouse panel of judges who will evaluate and support the next wave of groundbreaking startups 𝗼𝗻 𝗔𝗽𝗿𝗶𝗹 𝟯𝗿𝗱 𝗶𝗻 𝗧𝗲𝗹 𝗔𝘃𝗶𝘃. These industry leaders bring extensive expertise in 𝘃𝗲𝗻𝘁𝘂𝗿𝗲 𝗰𝗮𝗽𝗶𝘁𝗮𝗹, 𝗲𝗻𝘁𝗿𝗲𝗽𝗿𝗲𝗻𝗲𝘂𝗿𝘀𝗵𝗶𝗽, 𝗶𝗻𝗻𝗼𝘃𝗮𝘁𝗶𝗼𝗻, and 𝘀𝗰𝗮𝗹𝗶𝗻𝗴 𝗯𝘂𝘀𝗶𝗻𝗲𝘀𝘀𝗲𝘀—ensuring that every award finalist receives the attention it deserves. 🌟 𝗠𝗲𝗲𝘁 𝘁𝗵𝗲 𝗝𝘂𝗱𝗴𝗲𝘀: 🔹 Hilla Ovil-Brenner – Partner, Showcase IL, and Founder, Yazamiyot 🔹 Lior Segal (ADV) – Managing Partner, Horizon Capital 🔹 Anat Tila Cherni – Managing Partner, Aristagora VC 🔹 Ofer SHOSHAN - Venture Partner, OurCrowd 🔹 Cynthia Phitoussi – Co-founder and Managing Partner, SeedIL Ventures 🔹 Dan Gorlitsky – Partner, 5 Eyes, Angel Investor 🔹 Stav Erez – CEO, TEClub VC 🔹 Dmitry Kartvelishvili – Co-founder and CEO, Veligera Investment Group 🔹 Estie Arram Feder – Head of Programming, MassChallenge If you're an early-stage startup 𝗽𝘂𝘀𝗵𝗶𝗻𝗴 𝘁𝗵𝗲 𝗯𝗼𝘂𝗻𝗱𝗮𝗿𝗶𝗲𝘀 𝗼𝗳…\n\n\nhttps://t.me/c/**********/923:\n2025-03-11T00:01:30Z\n[Fedor](https://t.me/id1906): сделал себе пессимистикбота, который на любую реплику втирает умно про бренность всего сущего с легким британским акцентом. https://elevenlabs.io/app/talk-to?agent_id=IqZZ8zqQ9aq5eUv1JnQH В удивительное время живем.  Не пересылайте пожалуйста ссылку - я за каждый разговор плачу чуть-чуть кредитами, которых у меня ограниченное число.\n\n\nhttps://t.me/c/**********/924:\n2025-03-11T14:36:12Z\n[Ilya Briskin](https://t.me/il_37): Хаверим, шалом. Планируем в Тель Авиве делать серию митапов. Тематика: - Project Management  - AI - Стартапы  - GameDev Ищем интересных спикеров. Кому интересно выступить спикером - напишите, пожалуйста, в личные сообщения.\n  [Ilya Briskin](https://t.me/il_37): [Fedor](https://t.me/id1906) fyi\n\n\nhttps://t.me/c/**********/926:\n2025-03-12T23:26:23Z\n[Fedor](https://t.me/id1906): Мне страшно, помогите написать питч что я хороший спикер на тему пожалуйста\n\n\nhttps://t.me/c/**********/927:\n2025-03-12T23:26:57Z\n[Fedor](https://t.me/id1906): Пруф что я хороший спикер\n\n\nhttps://t.me/c/**********/929:\n2025-03-12T23:57:36Z\n[Fedor](https://t.me/id1906): Так норм: Привет! Я внедряю GenAI в компании из Fortune 500 и хотел бы поделиться реальным опытом - не теорией, а практикой с граблями и успехами. Интересно обсудить, как AI меняет корпоративные процессы, и какие подходы реально работают. В последнее время много про этом пишу у себя в линкедине.\n\n\nhttps://t.me/c/**********/930:\n2025-03-12T23:57:40Z\n[Fedor](https://t.me/id1906): ?\n\n\nhttps://t.me/c/**********/931:\n2025-03-13T06:46:35Z\n[Fedor](https://t.me/id1906): Ребята, важный корпоинсайт: сейчас примерно до 15 марта во всех больших корпо паника - нужно верстать бюджеты на конференции и обучения на 25-й иначе в следующем году денек не дадут. Следующие пару дней желающих дать денег сильно больше чем обычно.\n\n\nhttps://t.me/c/**********/932:\n2025-03-13T06:46:36Z\n[Fedor](https://t.me/id1906): Ну там тоже, наверное, есть цикличность, от раздачи венчурного капитала или чего там. Но в тех, кто сам деньги зарабатывает, это стандарт: в 4м квартале экономим на всём, даже на ручках, и уж точно на хайринге, в 1м начинаем жить по новому бюджету, где всё это есть, а дальше уже как пойдёт, но в последние годы идёт одинаково и к 3-4му кварталу опять режим экономии.\n\n\nhttps://t.me/c/**********/933:\n2025-03-13T07:20:34Z\n[Fedor](https://t.me/id1906): Надо ускоряться с публичными демками друзья, чтобы не продолбать хайп. Мы с Юрой уже 2 недели такие штуки тестируем\n\n\nhttps://t.me/c/**********/934:\n2025-03-13T07:20:34Z\n[Fedor](https://t.me/id1906): 🔥ИИ-нетворкер Вау, тут прямо отличный нетворкинг сервис запустили. Звонишь ИИ, он тебе задает несколько вопросов про твою историю (работа, учеба, задачи, которые сейчас решаешь) и потом он тебя “мэтчит” с человеком, который может быть тебе полезен. Знаете, как бывает - говоришь с кем-то, а он тебе потом “ой, тебе надо познакомиться с Х” - и соединяет вас. Тут тоже самое, только ИИ! Почему это круто? Борди (название сервиса) проинтервьюировал уже тысячи человек и у него огромная база контактов, а, поскольку технология еще довольно новая, у людей есть ощущение “теплого” интро и люди охотно идут на контакт.  Сразу идея использования - если вы сейчас ищите работу и не очень уверены, кем стать - расскажите ему свою историю и он предложит соединить вас с людьми, которые прошли похожий путь и смогли себя найти. Или же, если хотите на работу в определенную компанию - узнайте, вдруг у него есть там контакты - но не с целью получить таким образом реферрала, а узнать у человека, как он туда попал, какая там культура. Может быть не конкретная компания, а похожая - все будет полезно и хорошо. В качестве дополнительного бонуса использования Броди - интервью проходит на английском и он повторяет за вами то, что вы ему рассказали, но своими словами - такая техника отлично работает, начинаешь сам лучше понимать, чем ты занимаешься и какие у тебя сейчас проблемы. Если вы зависли - не стесняйтесь спрашивать у него самого, чем он может помочь и что он бы предложил в вашей ситуации, может быть, какие-то идеи у него возникнут. Чтобы начать общение откройте страничку Борди в Линкедине, и напишите ему сообщение с вашим имейлом и номером телефона. Он сразу же перезвонит. Потом можно забронировать такой звонок на раз в неделю. Нонтек работа в хайтеке @nontechitech\n\n\nhttps://t.me/c/**********/935:\n2025-03-13T07:21:23Z\n[Fedor](https://t.me/id1906): Вот вчерашний эксперимент меня и Юры. По-моему полный разъёб\n\n\nhttps://t.me/c/**********/936:\n2025-03-13T07:21:24Z\n[Fedor](https://t.me/id1906): https://elevenlabs.io/app/talk-to?agent_id=KRIcTJBrLXVZrMSlkEhu\n  [Felix T](https://t.me/Felixxx89): Bag словил :-(\n\n\nhttps://t.me/c/**********/939:\n2025-03-13T07:40:52Z\n[Fedor](https://t.me/id1906): Блять, елевенлабс подводят. Попробуй ещё разик\n\n\nhttps://t.me/c/**********/942:\n2025-03-13T14:51:32Z\n[Fedor](https://t.me/id1906): Так, ребята, у меня получилось. Это просто пушка. Послушайте аудио или просто попробуйте сами.\n\n\nhttps://t.me/c/**********/943:\n2025-03-13T14:51:58Z\n[Fedor](https://t.me/id1906): https://elevenlabs.io/app/talk-to?agent_id=KRIcTJBrLXVZrMSlkEhu попробуйте тоже\n\n\nhttps://t.me/c/**********/945:\n2025-03-13T16:33:56Z\n[Fedor](https://t.me/id1906): Помните, как я говорил, что нужно на детях тестировать, чтобы понять хороший продукт, или не очень. Так вот, я ни разу не врал и не шутил. У нас есть продукт, друзья, который охуенный.\n", "keywords": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_at": "2025-03-13T16:33:56Z", "entities": [{"label": "user", "value": "[<PERSON> T](https://t.me/Felixxx89)"}, {"label": "user", "value": "[<PERSON><PERSON><PERSON>](https://t.me/yuri<PERSON>_voro<PERSON>)"}, {"label": "user", "value": "[Любимая](https://t.me/<PERSON>V<PERSON>ov)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/il_37)"}, {"label": "user", "value": "[<PERSON><PERSON>](https://t.me/id1906)"}]}]}