#!/usr/bin/env python3
"""
Fix for the 'NoneType' object is not iterable error in get_embedding_with_retry function.

This file contains the fixed version of the function that properly handles when
the embedding service returns None, which happens during connection errors.

To apply this fix, update the get_embedding_with_retry function in quepasa/indexer/tasks.py
"""

# Original function with issue
"""
@retry_with_backoff(max_retries=10, initial_backoff=2, backoff_factor=2)
def get_embedding_with_retry(provider, model_version, text):
    \"\"\"Get embedding with retry logic\"\"\"
    text_vector = None 
    if QUEPASA_LLM_GATEWAY_API_KEY:
        text_vector = get_embedding_from_gateway(provider, model_version, text)
    elif provider in [EmbeddingProvider.SBERT]:
        text_vector = get_embedding_from_service(provider, model_version, text) 
    else:
        text_vector = get_cached_embedding(provider, model_version, text)
    text_vector_float = [float(f) for f in text_vector]  # <-- ERROR HERE if text_vector is None
    return text_vector_float
"""

# Fixed function with proper None checking
@retry_with_backoff(max_retries=10, initial_backoff=2, backoff_factor=2)
def get_embedding_with_retry(provider, model_version, text):
    """Get embedding with retry logic"""
    text_vector = None 
    if QUEPASA_LLM_GATEWAY_API_KEY:
        text_vector = get_embedding_from_gateway(provider, model_version, text)
    elif provider in [EmbeddingProvider.SBERT]:
        text_vector = get_embedding_from_service(provider, model_version, text) 
    else:
        text_vector = get_cached_embedding(provider, model_version, text)
    
    # Add proper handling for None result
    if text_vector is None:
        # This will trigger the retry mechanism
        raise ValueError("Failed to get embedding - received None result")
    
    # Now we're sure text_vector is not None
    text_vector_float = [float(f) for f in text_vector]
    return text_vector_float

# Alternate implementation with more detailed error handling
@retry_with_backoff(max_retries=10, initial_backoff=2, backoff_factor=2)
def get_embedding_with_retry_detailed(provider, model_version, text):
    """Get embedding with retry logic and detailed error handling"""
    text_vector = None
    source = "unknown"
    
    try:
        if QUEPASA_LLM_GATEWAY_API_KEY:
            source = "gateway"
            text_vector = get_embedding_from_gateway(provider, model_version, text)
        elif provider in [EmbeddingProvider.SBERT]:
            source = "service"
            text_vector = get_embedding_from_service(provider, model_version, text)
        else:
            source = "cache"
            text_vector = get_cached_embedding(provider, model_version, text)

        if text_vector is None:
            raise ValueError(f"Failed to get embedding from {source} - received None result")
        
        return [float(f) for f in text_vector]
    
    except Exception as e:
        logger.warning(f"Retrying get_embedding_with_retry after error from {source}: {str(e)}")
        raise  # Re-raise to trigger retry 