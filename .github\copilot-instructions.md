# Quepasa AI Coding Agent Instructions

## Project Overview
Quepasa is a microservices-based RAG (Retrieval-Augmented Generation) system for document ingestion, transformation, indexing, and search. It is designed for robust, scalable, and multi-format document processing, with a focus on extensibility and production reliability.

## Architecture & Major Components
- **API** (`quepasa/api/`): FastAPI backend for document and batch operations. Exposes endpoints for file, URL, and batch ingestion, as well as LLM/embedding/whisper gateways.
- **<PERSON>rawler** (`quepasa/crawler/`): Handles document ingestion, content extraction, and format-specific processing. Uses Celery for distributed task execution.
- **Data Processor** (`quepasa/data_processor/`): Transforms and normalizes ingested content for downstream indexing.
- **Indexer** (`quepasa/indexer/`): Indexes processed documents into Elasticsearch for search and retrieval.
- **Searcher** (`quepasa/searcher/`): Handles query processing and retrieval from Elasticsearch.
- **Embedding** (`quepasa/embedding/`): Provides vector embedding services for documents and queries.
- **Infrastructure**: Redis (broker/cache), MinIO (object storage), Elasticsearch (search/vector store). All services are orchestrated via Docker Compose and optionally Kubernetes (`k8s/`).

## Developer Workflows
- **Build & Run (Docker):**
	- `docker compose up --build` (all services)
	- `docker compose logs -f <service>` (view logs)
	- `docker compose restart <service>` (restart)
	- `docker compose down` (stop)
- **Local Python:**
	- `python -m venv venv && source venv/bin/activate` (or `venv\Scripts\activate` on Windows)
	- `pip install -r requirements.txt && pip install -e .[dev]`
- **Testing:**
	- `pip install -r tests/requirements-test.txt`
	- `pytest` (all tests)
	- `pytest --cov=quepasa --cov-report=html` (coverage)
	- Tests use `pytest` with asyncio and extensive mocking for S3/MinIO, Redis, and batch utilities.
- **Slow tests marker:**
    - Some long-running integration or I/O heavy tests are marked with the `@pytest.mark.slow` marker.
    - CI skips these by default using `-m "not slow"` (see .github/workflows/tests.yaml).
    - Developers can run only slow tests via: `python -m pytest -m slow` or run all tests with `python -m pytest`
    - Or run fast tests (excluding slow):`python -m pytest -m "not slow"`
    - Duration summary: pytest.ini is configured to show duration only for tests taking ≥ 10 seconds. You can override per run, e.g.: `python -m pytest --durations=0 --durations-min=2.0`
- **Linting/Formatting:**
	- `black .`, `flake8 .`, `mypy quepasa/`

## Key Patterns & Conventions
- **Content Processing:**
	- All content types are processed via dedicated processors in `quepasa/crawler/processors/` (e.g., `TXTProcessor`, `PDFProcessor`, `AudioProcessor`, etc.).
	- Processors must implement the `BaseProcessor` interface and return a dict with `chunks`, `title`, and `filename`.
	- Audio/video transcription uses Replicate API and Whisper diarization models; requires `REPLICATE_API_KEY` and FFmpeg.
- **Batch Operations:**
	- All ingestion (file/URL) and document operations are batched. Batches are tracked by state (`UPLOADED`, `BACKLOG`, etc.) and processed via Celery tasks.
	- Batch status and relaunch are managed via API endpoints and `BatchUtils`.
- **Authentication:**
	- API uses Bearer tokens in the format `Bearer <client_id>:<token>`. Tokens are validated via HMAC and client-specific secrets.
	- See `configuration/base/auth.py` and `quepasa/common/bearer.py` for details.
	- Some endpoints use another mechanism - customerId from the `X-Customer-Id` header, and `client-key` from the `Authorization` header.
- **Configuration:**
	- Environment variables are loaded from `.env` (see `.env.example`). Service-specific configs are in `configuration/`.
- **Testing:**
	- All new features must be covered by tests in `tests/`. Use fixtures and mocks for external services.
- **Error Handling:**
	- All processors and handlers must log errors and return structured error responses. Do not raise raw exceptions to the API layer.
- **Extensibility:**
	- To add a new content type, implement a new processor in `quepasa/crawler/processors/` and register it in the relevant handler.

## Integration Points
- **External APIs:**
	- LLMs: OpenAI, Anthropic, Mistral, Nebius, HuggingFace (via API keys in `.env`)
	- Embeddings: SBERT, OpenAI, etc.
	- Whisper: Replicate API for audio/video transcription
- **Storage:**
	- MinIO for all file/object storage (see `QuepasaFiles` in `src/lib/files.py`)
	- Elasticsearch for search and vector storage
- **Celery:**
	- Used for all distributed/background processing. Queues are service-specific (e.g., `crawler`, `data-processor`, `indexer`).

## Examples & References
- See `dev/QuepasaCookbook-1.ipynb` for end-to-end ingestion and inference examples.
- See `dev/IngestionCookbook.ipynb` for products' ingestion and deletion examples.
- See `quepasa/crawler/processors/README.md` for supported content types and processor details.
- See `CLAUDE.md` and `README.md` for more on architecture and workflows.

---
**If you are unsure about a workflow, integration, or convention, check the referenced files or ask for clarification.**
