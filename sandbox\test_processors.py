# Import processors
from quepasa.crawler.processors.csv_processor import CSVProcessor
from quepasa.crawler.processors.txt_processor import TXTProcessor
from quepasa.crawler.processors.web_processor import WebProcessor
from quepasa.crawler.processors.youtube_processor import YouTubeProcessor
from quepasa.crawler.processors.document_processor import DocumentProcessor
from quepasa.crawler.processors.markdown_processor import MarkdownProcessor
from quepasa.crawler.processors.pdf_processor import PDFProcessor
from quepasa.crawler.processors.pptx_processor import PPTXProcessor

# Create test data directory
from pathlib import Path
test_data_dir = Path(__file__).parent / ".." / "tests" / "data"

def test_processor(processor, file_path_or_url, meta=None):
    """Test a processor with a given file"""
    if not meta:
        if isinstance(file_path_or_url, Path):
            meta = {
                'filename': file_path_or_url.name,
                'extension': file_path_or_url.suffix[1:] if file_path_or_url.suffix else ''
            }
        else:
            meta = {
                'filename': 'youtube_video.txt',
                'extension': 'txt',
                'url': file_path_or_url  # For YouTube URLs
            }
    
    try:
        data = None
        if isinstance(processor, YouTubeProcessor):
            # For YouTube, we pass the URL directly
            data = processor.process(file_path_or_url, meta)

        else:
            with open(file_path_or_url, 'rb') as f:
                content = f.read()
            data = processor.process(content, meta)

        if data['status'] == 'error':
            print(f"Error processing {meta}: {data['error']}")
            return None
        
        result = data['result']
        
        print(f"\nProcessing {meta.get('url', file_path_or_url)}:")
        print(f"Title: {result.get('title', 'N/A')}")
        print(f"Number of chunks: {len(result.get('chunks', []))}")
        print("\nFirst few chunks:")
        chunks = result.get('chunks', [])
        for i, chunk in enumerate(chunks[:5], 1):
            print(f"\nChunk {i}:")
            print(f"Position: {chunk['position']}")
            text = chunk['text']
            print(text[:200] + '...' if len(text) > 200 else text)
        
        if len(chunks) > 5:
            print(f"\n... and {len(chunks) - 5} more chunks")
        
        return result
    
    except Exception as e:
        print(f"Error processing {meta.get('url', file_path_or_url)}: {str(e)}")
        return None

def create_sample_files():
    """Create sample files for testing"""
    # CSV with many rows and columns
    csv_content = """Department,Employee,Position,Salary,Years,Office,Projects,Performance,Education,Skills
Sales,John Smith,Senior Manager,85000,8,New York,Project A|Project B|Project C,Excellent,MBA,Leadership|Communication|Strategy
Marketing,Emily Brown,Director,95000,10,Los Angeles,Project X|Project Y,Outstanding,PhD,Marketing|Analytics|Management
Engineering,Michael Chen,Lead Developer,92000,6,San Francisco,Backend|Frontend|Mobile,Excellent,MSc,Python|Java|Cloud
HR,Sarah Johnson,Manager,75000,5,Chicago,Recruitment|Training,Good,BA,HR|Communication|Organization
Finance,David Wilson,Controller,88000,7,Boston,Budgeting|Forecasting|Audit,Outstanding,CPA,Finance|Excel|Analysis
IT,Lisa Garcia,System Admin,78000,4,Seattle,Infrastructure|Security,Excellent,BSc,Networks|Security|Linux
Sales,James Lee,Account Exec,72000,3,Austin,Client A|Client B,Good,BBA,Sales|Negotiation|CRM
Marketing,Anna White,Specialist,65000,2,Denver,Social Media|Content,Outstanding,BA,Social Media|SEO|Writing
Engineering,Robert Kim,Senior Dev,89000,5,Portland,API|Database,Excellent,MSc,SQL|Python|AWS
HR,Maria Lopez,Coordinator,58000,1,Miami,Onboarding|Benefits,Good,BA,HR|Administration|Communication
""".strip()

    with open(test_data_dir / "sample.csv", "w") as f:
        f.write(csv_content)

    # TXT with multiple paragraphs
    txt_content = """
Introduction to Advanced Technology Systems

In the rapidly evolving landscape of modern technology, understanding the fundamental principles and advanced concepts of technological systems has become increasingly crucial. This comprehensive guide aims to explore various aspects of technology systems, their implementations, and their impact on different sectors.

Chapter 1: Basic Principles and Foundations

The foundation of any technological system lies in its basic principles. These principles encompass various aspects including data processing, system architecture, and interaction models. Understanding these fundamentals is crucial for anyone looking to work with or develop advanced technological solutions.

When we examine the core components of technological systems, we must consider several key elements:
1. System Architecture
2. Data Flow Management
3. User Interface Design
4. Security Implementation
5. Performance Optimization

Chapter 2: Advanced Implementation Strategies

The implementation of technological systems requires careful planning and strategic thinking. This involves considering various factors such as scalability, maintainability, and efficiency. Successful implementation strategies often incorporate the following elements:

- Thorough requirements analysis
- Detailed system design
- Robust testing procedures
- Comprehensive documentation
- Effective deployment methods

Chapter 3: Impact on Various Sectors

The influence of technological systems spans across multiple sectors, transforming traditional processes and enabling new possibilities. Here's how different sectors are affected:

Healthcare:
- Electronic health records
- Telemedicine platforms
- Medical imaging systems
- Patient monitoring devices
- Healthcare analytics

Education:
- Online learning platforms
- Interactive educational tools
- Student performance tracking
- Virtual classrooms
- Educational content management

Finance:
- Digital payment systems
- Automated trading platforms
- Risk management tools
- Fraud detection systems
- Customer relationship management

Chapter 4: Future Trends and Developments

Looking ahead, several emerging trends are shaping the future of technological systems:

Artificial Intelligence and Machine Learning:
The integration of AI and ML is revolutionizing how systems process and analyze data, leading to more intelligent and adaptive solutions.

Internet of Things (IoT):
The proliferation of connected devices is creating new opportunities for data collection and automation across various domains.

Blockchain Technology:
Decentralized systems are gaining prominence, offering new possibilities for secure and transparent transactions.

Quantum Computing:
The advent of quantum computing promises to revolutionize computational capabilities, potentially solving complex problems that are currently intractable.

Chapter 5: Challenges and Considerations

While technological systems offer numerous benefits, they also present various challenges that need to be addressed:

Security Concerns:
- Data privacy protection
- Cyber attack prevention
- Access control management
- Compliance with regulations
- Incident response planning

Performance Optimization:
- Resource utilization
- Response time improvement
- Scalability management
- Load balancing
- Cache optimization

Maintenance and Updates:
- Regular system updates
- Bug fixing procedures
- Feature enhancement
- Documentation management
- Version control

Conclusion

The field of technological systems continues to evolve, presenting both opportunities and challenges. Success in this domain requires a balanced approach that considers technical capabilities, user needs, and practical constraints. As we move forward, the importance of understanding and effectively managing these systems will only continue to grow.
""".strip()

    with open(test_data_dir / "sample.txt", "w") as f:
        f.write(txt_content)

    # Markdown with rich content
    md_content = """
# Comprehensive Guide to Modern Software Development

## Introduction

Software development has evolved significantly over the past decades, incorporating new methodologies, tools, and practices. This guide provides a detailed overview of modern software development practices and principles.

## Agile Development Methodology

### Core Principles

Agile development is based on several key principles:
- Iterative development
- Continuous feedback
- Adaptive planning
- Rapid delivery

### Scrum Framework

Scrum is one of the most popular Agile frameworks, featuring:
1. Sprint Planning
2. Daily Stand-ups
3. Sprint Review
4. Sprint Retrospective
5. Product Backlog Refinement

## DevOps Practices

### Continuous Integration (CI)

CI involves automatically integrating code changes into a shared repository:
- Automated builds
- Unit testing
- Code quality checks
- Integration testing

### Continuous Deployment (CD)

CD extends CI by automatically deploying changes:
- Automated deployment pipelines
- Environment management
- Release automation
- Rollback procedures

## Software Architecture

### Microservices

Benefits of microservices architecture:
- Scalability
- Maintainability
- Technology flexibility
- Independent deployment

### Event-Driven Architecture

Key components:
1. Event producers
2. Event channels
3. Event consumers
4. Event processors

## Testing Strategies

### Test Pyramid

Different levels of testing:
- Unit tests
- Integration tests
- End-to-end tests
- Performance tests

### Test-Driven Development (TDD)

TDD cycle:
1. Write failing test
2. Write minimal code
3. Refactor
4. Repeat

## Security Best Practices

### Authentication & Authorization

Important security measures:
- Multi-factor authentication
- Role-based access control
- OAuth implementation
- JWT tokens

### Data Protection

Key considerations:
1. Encryption at rest
2. Encryption in transit
3. Data backup
4. Access logging

## Performance Optimization

### Frontend Optimization

Techniques for better frontend performance:
- Code splitting
- Lazy loading
- Caching strategies
- Asset optimization

### Backend Optimization

Server-side optimization methods:
1. Database indexing
2. Query optimization
3. Caching layers
4. Load balancing

## Monitoring and Logging

### Application Monitoring

Essential metrics to track:
- Response time
- Error rates
- Resource usage
- User activity

### Log Management

Best practices:
1. Structured logging
2. Log levels
3. Log aggregation
4. Log analysis

## Conclusion

Modern software development requires a comprehensive understanding of various tools, practices, and methodologies. Continuous learning and adaptation are key to success in this rapidly evolving field.
""".strip()

    with open(test_data_dir / "sample.md", "w") as f:
        f.write(md_content)

    # HTML with rich content
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Modern Web Development Guide</title>
</head>
<body>
    <h1>Modern Web Development Guide</h1>
    
    <section>
        <h2>Frontend Development</h2>
        <p>Modern frontend development encompasses various technologies and frameworks that help create responsive and interactive user interfaces. Here are some key aspects to consider:</p>
        
        <h3>JavaScript Frameworks</h3>
        <ul>
            <li>React: Component-based UI library</li>
            <li>Vue.js: Progressive JavaScript framework</li>
            <li>Angular: Full-featured web application framework</li>
            <li>Svelte: Compile-time framework</li>
        </ul>
        
        <h3>CSS Technologies</h3>
        <p>CSS has evolved significantly with new features and methodologies:</p>
        <ul>
            <li>Flexbox for flexible layouts</li>
            <li>Grid for two-dimensional layouts</li>
            <li>CSS Variables for maintainable styling</li>
            <li>CSS Modules for scoped styling</li>
        </ul>
    </section>

    <section>
        <h2>Backend Development</h2>
        <p>Backend development focuses on server-side logic and data management. Key technologies include:</p>
        
        <h3>Server Technologies</h3>
        <ul>
            <li>Node.js: JavaScript runtime</li>
            <li>Python: Versatile programming language</li>
            <li>Java: Enterprise-grade applications</li>
            <li>Go: High-performance systems</li>
        </ul>
        
        <h3>Database Systems</h3>
        <p>Different types of databases serve different purposes:</p>
        <ul>
            <li>PostgreSQL: Relational database</li>
            <li>MongoDB: Document database</li>
            <li>Redis: In-memory data store</li>
            <li>Neo4j: Graph database</li>
        </ul>
    </section>

    <section>
        <h2>DevOps Practices</h2>
        <p>DevOps bridges development and operations:</p>
        
        <h3>Continuous Integration</h3>
        <ul>
            <li>Automated testing</li>
            <li>Code quality checks</li>
            <li>Build automation</li>
            <li>Version control</li>
        </ul>
        
        <h3>Continuous Deployment</h3>
        <ul>
            <li>Automated deployment</li>
            <li>Infrastructure as code</li>
            <li>Monitoring and logging</li>
            <li>Security scanning</li>
        </ul>
    </section>

    <section>
        <h2>Web Security</h2>
        <p>Security is crucial in web development:</p>
        
        <h3>Common Security Measures</h3>
        <ul>
            <li>HTTPS implementation</li>
            <li>Cross-Site Scripting (XSS) prevention</li>
            <li>SQL injection protection</li>
            <li>Authentication and authorization</li>
        </ul>
        
        <h3>Security Best Practices</h3>
        <ul>
            <li>Regular security audits</li>
            <li>Dependency updates</li>
            <li>Security headers</li>
            <li>Input validation</li>
        </ul>
    </section>

    <section>
        <h2>Performance Optimization</h2>
        <p>Optimizing web performance involves various strategies:</p>
        
        <h3>Frontend Optimization</h3>
        <ul>
            <li>Code splitting</li>
            <li>Lazy loading</li>
            <li>Asset optimization</li>
            <li>Caching strategies</li>
        </ul>
        
        <h3>Backend Optimization</h3>
        <ul>
            <li>Database optimization</li>
            <li>Caching layers</li>
            <li>Load balancing</li>
            <li>CDN utilization</li>
        </ul>
    </section>
</body>
</html>
""".strip()

    with open(test_data_dir / "sample.html", "w") as f:
        f.write(html_content)

    print("Sample files created in test_data directory")

def test_all_processors():
    """Test all available processors"""
    # Create sample files first
    create_sample_files()

    # Test YouTube Processor with real video
    print("\nTesting YouTube Processor with real video:")
    youtube_processor = YouTubeProcessor()
    youtube_result = test_processor(youtube_processor, 'https://www.youtube.com/watch?v=GIg4HlSp_ZE')
    print("----- " * 5)
    print()
    print()

    # Test CSV Processor
    print("\nTesting CSV Processor:")
    csv_processor = CSVProcessor()
    csv_result = test_processor(csv_processor, test_data_dir / "sample.csv")
    print("----- " * 5)
    print()
    print()

    # Test TXT Processor
    print("\nTesting TXT Processor:")
    txt_processor = TXTProcessor()
    txt_result = test_processor(txt_processor, test_data_dir / "sample.txt")
    print("----- " * 5)
    print()
    print()

    # Test Markdown Processor
    print("\nTesting Markdown Processor:")
    md_processor = MarkdownProcessor()
    md_result = test_processor(md_processor, test_data_dir / "sample.md")
    print("----- " * 5)
    print()
    print()

    # Test Web Processor
    print("\nTesting Web Processor:")
    web_processor = WebProcessor()
    web_result = test_processor(web_processor, test_data_dir / "sample.html")
    print("----- " * 5)
    print()
    print()

    # Test PDF Processor
    print("\nTesting PDF Processor:")
    pdf_processor = PDFProcessor()
    pdf_result = test_processor(pdf_processor, test_data_dir / "sample.pdf")
    print("----- " * 5)
    print()
    print()

    # Test Document Processor with DOCX
    print("\nTesting Document Processor with DOCX:")
    doc_processor = DocumentProcessor()
    docx_result = test_processor(doc_processor, test_data_dir / "sample.docx")
    print("----- " * 5)
    print()
    print()

    # Test Document Processor with XLSX
    print("\nTesting Document Processor with XLSX:")
    xlsx_result = test_processor(doc_processor, test_data_dir / "sample.xlsx")
    print("----- " * 5)
    print()
    print()

    # Test PPTX Processor
    print("\nTesting PPTX Processor:")
    pptx_processor = PPTXProcessor()
    pptx_result = test_processor(pptx_processor, test_data_dir / "sample.pptx")
    print("----- " * 5)
    print()
    print()

    print("\nAll processors have been tested!")

if __name__ == "__main__":
    test_all_processors() 