# Test Design: Story 1.2

Date: 2025-01-11
Designer: <PERSON> (Test Architect)

## Test Strategy Overview

- Total test scenarios: 12
- Unit tests: 8 (67%)
- Integration tests: 3 (25%)
- E2E tests: 1 (8%)
- Priority distribution: P0: 8, P1: 3, P2: 1

## Test Scenarios by Acceptance Criteria

### AC1: The existing or new service can be called from the /conversation handler

#### Scenarios

| ID           | Level       | Priority | Test                                    | Justification                      |
| ------------ | ----------- | -------- | --------------------------------------- | ---------------------------------- |
| 1.2-INT-001  | Integration | P0       | Hand<PERSON> calls source factory correctly | Critical service integration      |
| 1.2-UNIT-001 | Unit        | P1       | Request configuration set before call  | Validate config setup logic       |

### AC2: The service logic correctly uses source: agentic

#### Scenarios

| ID           | Level       | Priority | Test                                    | Justification                      |
| ------------ | ----------- | -------- | --------------------------------------- | ---------------------------------- |
| 1.2-UNIT-002 | Unit        | P0       | Source parameter set to "agentic"      | Core business requirement         |
| 1.2-INT-002  | Integration | P0       | Source factory receives agentic config | Validate service contract         |

### AC3: The service fetches product data and includes all variant attributes

#### Scenarios

| ID           | Level       | Priority | Test                                    | Justification                      |
| ------------ | ----------- | -------- | --------------------------------------- | ---------------------------------- |
| 1.2-UNIT-003 | Unit        | P0       | Product data extraction from response   | Critical data transformation      |
| 1.2-UNIT-004 | Unit        | P0       | Variant attributes mapped to allMeta    | Essential product data structure  |

### AC4: The final response object matches the ChatResponse schema

#### Scenarios

| ID           | Level       | Priority | Test                                    | Justification                      |
| ------------ | ----------- | -------- | --------------------------------------- | ---------------------------------- |
| 1.2-UNIT-005 | Unit        | P0       | ChatResponse model validation          | API contract compliance           |
| 1.2-UNIT-006 | Unit        | P0       | Required fields present in response    | Schema compliance verification    |
| 1.2-INT-003  | Integration | P0       | End-to-end schema compliance           | Full transformation validation    |

### AC5: The response format can be switched between markdown and plain_text

#### Scenarios

| ID           | Level       | Priority | Test                                    | Justification                      |
| ------------ | ----------- | -------- | --------------------------------------- | ---------------------------------- |
| 1.2-UNIT-007 | Unit        | P1       | Format parameter parsing logic         | Query parameter handling          |
| 1.2-UNIT-008 | Unit        | P1       | Content format transformation          | Format switching logic            |

### AC6: A visitorId is generated and returned if one is not provided

#### Scenarios

| ID           | Level       | Priority | Test                                    | Justification                      |
| ------------ | ----------- | -------- | --------------------------------------- | ---------------------------------- |
| 1.2-UNIT-009 | Unit        | P0       | VisitorId generation when missing      | User tracking requirement         |
| 1.2-E2E-001  | E2E         | P2       | Complete conversation flow validation  | End-user journey verification     |

## Risk Coverage

Based on the story analysis, these tests cover:

- **DATA-001**: Invalid response format transformation - AC4 tests
- **INTEGRATION-001**: Source factory communication failure - AC1, AC2 tests  
- **BUSINESS-001**: Product data loss during transformation - AC3 tests
- **USER-001**: Missing visitor tracking - AC6 tests

## Recommended Execution Order

1. P0 Unit tests (fail fast on core logic)
   - 1.2-UNIT-002: Source parameter validation
   - 1.2-UNIT-003: Product data extraction
   - 1.2-UNIT-004: Variant attributes mapping
   - 1.2-UNIT-005: ChatResponse validation
   - 1.2-UNIT-006: Required fields validation
   - 1.2-UNIT-009: VisitorId generation

2. P0 Integration tests (service boundaries)
   - 1.2-INT-001: Handler-to-factory integration
   - 1.2-INT-002: Source configuration flow
   - 1.2-INT-003: End-to-end schema compliance

3. P1 tests (secondary features)
   - 1.2-UNIT-001: Request configuration setup
   - 1.2-UNIT-007: Format parameter parsing
   - 1.2-UNIT-008: Content format transformation

4. P2 tests (as time permits)
   - 1.2-E2E-001: Complete conversation flow

## Test Implementation Notes

### Unit Test Focus Areas

**1.2-UNIT-002**: Source Parameter Validation
```python
def test_source_set_to_agentic():
    # Verify self.config.request.source = "agentic" assignment
    # Test isolated configuration logic only
```

**1.2-UNIT-003**: Product Data Extraction  
```python
def test_extract_products_from_quepasa_response():
    # Mock QuepasaResponse with product data
    # Verify extraction logic transforms correctly
```

**1.2-UNIT-004**: Variant Attributes Mapping
```python
def test_variant_attributes_in_allmeta():
    # Mock product with variant attributes
    # Verify allMeta field contains all variant data
```

### Integration Test Focus Areas

**1.2-INT-001**: Handler-Factory Integration
```python
async def test_conversation_handler_calls_source_factory():
    # Mock source_factory.get_answer()
    # Verify handler makes correct call with stream=False
```

**1.2-INT-003**: End-to-End Schema Compliance
```python
async def test_complete_response_transformation():
    # Mock full QuepasaResponse
    # Verify complete ChatResponse structure
```

### E2E Test Scope

**1.2-E2E-001**: Complete Conversation Flow
- Full request through conversation endpoint
- Verify response structure matches OpenAPI spec
- Limited to critical path validation only

## Quality Checklist

- [x] Every AC has test coverage
- [x] Test levels are appropriate (minimal duplication)
- [x] No duplicate coverage across levels
- [x] Priorities align with business risk
- [x] Test IDs follow naming convention
- [x] Scenarios are atomic and independent

## Anti-Superficial Testing Notes

**Avoided Superficial Tests:**
- Framework behavior testing (FastAPI internals)
- Mock object validation (testing the test, not the code)
- Trivial getters/setters without business logic
- Third-party library behavior validation

**Focus on Business Logic:**
- Source factory integration (critical business requirement)
- Product data transformation (core value delivery)
- Schema compliance (API contract fulfillment)
- Format switching (user-facing feature)

**Risk-Based Prioritization:**
- P0 tests target revenue-critical paths (product data, API compliance)
- P1 tests cover user experience features (format switching)
- P2 tests provide end-user validation but not blocking