from typing import Optional, List, Dict, Any, Union, Tuple
from quepasa.searcher.models.request import QuepasaRequest
from src.lib.llm.providers import LLMProvider
from .base_config import BaseConfig
from src.lib.constants import (
    SOURCE_DOCUMENTS,
)

class AgenticConfig(BaseConfig):
    """Base configuration for agentic functionality."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def get_agentic_tools(self) -> Optional[Any]:
        """Get configuration for agentic tools
        
        Returns:
            Optional[Any]
        """
        return []
    
    def get_agentic_function_thinking(self, function_name: str, arguments: dict) -> str:
        """
        Get thinking message for an agentic call.

        Args:
            function_name: Function name
            arguments: Function arguments
        
        Returns:
            str
        """
        return ""

    def get_agentic_function(self, function_name: str, arguments: dict) -> Union[dict, List[dict], str]:
        """Get list of custom agentic functions
        
        Args:
            function_name: Function name
            arguments: Function arguments
        
        Returns:
            Dict or str

        Examples:
            {
                'source': SOURCE_DOCUMENTS,
                'domain': "products",
                'question': "What is the price of the product?"
            }

            or 

            [
                {
                    'source': SOURCE_DOCUMENTS,
                    'domain': "products",
                    'question': "What is the price of the product?"
                },
                ...
            ]

            or 

            "The weather in Rishon LeZion is pleasant, with a temperature of 27 °C."
        """
        return f"Function: {function_name} is not implemented yet"
    