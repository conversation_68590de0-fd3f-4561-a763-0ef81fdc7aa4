function runQAJudje() {
  var activeSheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  config = JSON.parse(activeSheet.getRange("C1").getValue())
  config.type = "judge";
  runQA( config );
}

function runQAJudjeFull() {
  var activeSheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  config = JSON.parse(activeSheet.getRange("C1").getValue())
  config.type = "judge_full";
  runQA( config );
}

function runQAGolden() {
  var activeSheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  config = JSON.parse(activeSheet.getRange("C1").getValue())
  config.type = "golden";
  runQA( config );
}

function runQAGoldenFull() {
  var activeSheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  config = JSON.parse(activeSheet.getRange("C1").getValue())
  config.type = "golden_full";
  runQA( config );
}

function runQASideBySide() {
  var activeSheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  config = JSON.parse(activeSheet.getRange("C1").getValue())
  config.type = "side_by_side";
  runQA( config );
}

function runQASideBySideFull() {
  var activeSheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  config = JSON.parse(activeSheet.getRange("C1").getValue())
  config.type = "side_by_side_full";
  runQA( config );
}
//
// GET QA RESULTS
//
function runQA( config ) {

  if (!config.question) {
    config.question = "QUESTION"
  }

  if (!config.answer) {
    config.answer = "ANSWER"
  }

  if (!config.golden_answer) {
    config.golden_answer = "GOLDEN_ANSWER"
  }

  var activeSheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  var selectedRange = activeSheet.getActiveRange();

  for (var i = 1; i <= selectedRange.getNumRows(); i++) {
    var numRow = selectedRange.getRow() + i - 1;
    runQAByNumRow(config, activeSheet, numRow);
  }
}

function runQAByNumRow( config, activeSheet, numRow ) {

  if (config.type == "judge_full") {
    var messages = getMessages(activeSheet, config.question, config.answer, config.golden_answer, numRow);
    var response = quepasa_qa_llm_as_a_judje(messages);
    var result = response.response;

    var answerColumn = findColumnNumByName(activeSheet, config.answer);

    var sum = 0;
    var count = 0;

    for (var i = 1; i <= 10; i = i + 2) {
      var metricName = activeSheet.getRange(2, i + answerColumn).getValue();
      var score = result[metricName]['score'];
      var justification = result[metricName]['justification'];
      activeSheet.getRange(numRow, i + answerColumn).setValue(score);
      activeSheet.getRange(numRow, i + 1 + answerColumn).setValue(justification);

      if (score !== "N/A" && typeof score === "number") {
        sum += score;
        count += 1;
      }
    }

    var averageScore = count > 0 ? (sum / count) : "N/A";
    activeSheet.getRange(numRow, answerColumn + 11).setValue(averageScore);
    activeSheet.getRange(numRow, answerColumn + 12).setValue(result['Recommendations']);

  } else if (config.type == "side_by_side_full") {
    var messages = getMessages(activeSheet, config.question, config.answer, config.golden_answer, numRow);
    var goldenMessages = getMessages(activeSheet, config.question, config.golden_answer, null, numRow);
    var response = quepasa_qa_side_by_side(messages, goldenMessages);
    var result = response.response;

    for (var key in result) {
      var keyColumn = findColumnNumByName(activeSheet, key);
      if (typeof result[key] === 'object') {
        activeSheet.getRange(numRow, keyColumn).setValue(result[key]['better']);
        activeSheet.getRange(numRow, keyColumn + 1).setValue(result[key]['justification']);
      } else {
        activeSheet.getRange(numRow, keyColumn).setValue(result[key]);
      }
    }

  } else if (config.type == "golden_full") {
    var messages = getMessages(activeSheet, config.question, config.answer, config.golden_answer, numRow);
    var goldenMessages = getMessages(activeSheet, config.question, config.golden_answer, null, numRow);
    var response = quepasa_qa_golden(messages, goldenMessages);
    var result = response.response;

    //Browser.msgBox(JSON.stringify(result));

    for (var key in result) {
      var keyColumn = findColumnNumByName(activeSheet, key);
      if (typeof result[key] === 'object') {
        activeSheet.getRange(numRow, keyColumn).setValue(result[key]['ok']);
        activeSheet.getRange(numRow, keyColumn + 1).setValue(result[key]['justification']);
      } else {
        activeSheet.getRange(numRow, keyColumn).setValue(result[key]);
      }
    }
  } else {

    var result = "";
    var resultColumn = findColumnNumByName(activeSheet, config.result);

    if (config.type == "judge") {
      var messages = getMessages(activeSheet, config.question, config.answer, config.golden_answer, numRow);

      var response = quepasa_qa_llm_as_a_judje(messages);
      result = response.response;

      //Browser.msgBox(JSON.stringify(result));
      
      var sum = 0;
      var count = 0;

      for (var key in result) {
        if (typeof result[key] === 'object') {
          var score = result[key]['score'];
          if (score !== "N/A" && typeof score === "number") {
            sum += score;
            count += 1;
          }
        }
      }

      var averageScore = count > 0 ? (sum / count) : "N/A";
      activeSheet.getRange(numRow, resultColumn).setValue(averageScore);

    } else if (config.type == "side_by_side") {
      var messages = getMessages(activeSheet, config.question, config.answer, config.golden_answer, numRow);
      var goldenMessages = getMessages(activeSheet, config.question, config.golden_answer, null, numRow);

      var response = quepasa_qa_side_by_side(messages, goldenMessages); 
      result = response.response;

      activeSheet.getRange(numRow, resultColumn).setValue(result['Overall Winner']);

    } else if (config.type == "golden") {
      var messages = getMessages(activeSheet, config.question, config.answer, config.golden_answer, numRow);
      var goldenMessages = getMessages(activeSheet, config.question, config.golden_answer, null, numRow);

      var response = quepasa_qa_golden(messages, goldenMessages);
      result = response.response;

      activeSheet.getRange(numRow, resultColumn).setValue(result['Overall Evaluation']);

    }

    //Browser.msgBox(JSON.stringify(result));
    var fullResponseColumn = findColumnNumByName(activeSheet, config.full_result);
    activeSheet.getRange(numRow, fullResponseColumn).setValue(formatJSON(result));

  }
}

function getMessages(activeSheet, questionField, answerField, goldenAnswerField=null, numRow) {

  var questionColumn = findColumnNumByName(activeSheet, questionField)
  var answerColumn = findColumnNumByName(activeSheet, answerField)
  var goldenAnswerColumn = answerColumn;
  if (goldenAnswerField) {
    goldenAnswerColumn = findColumnNumByName(activeSheet, goldenAnswerField)
  }
  var idColumn = findColumnNumByName(activeSheet, 'ID')
  var replyColumn = findColumnNumByName(activeSheet, 'REPLY TO')

  var messages = []

  var i = numRow

  while (true) {
    var question = activeSheet.getRange(i, questionColumn).getValue()
    var answer = ""
    if (i == numRow) {
      answer = activeSheet.getRange(i, answerColumn).getValue()
    } else {
      answer = activeSheet.getRange(i, goldenAnswerColumn).getValue()
    }
    messages.push({'role': 'assistant', 'content': answer})
    messages.push({'role': 'user', 'content': question})
    if (activeSheet.getRange(i, replyColumn).getValue() == "") {
      break
    } else {
      i = findRowNumByValue(activeSheet, idColumn, activeSheet.getRange(i, replyColumn).getValue())
    }
  }

  messages.reverse()
  return messages
}

function getHeaders() {
  headers = {};
  headers["Content-Type"] = "application/json";
  headers['Authorization'] = "Bearer rezolve_shoeby_dev:bG9pBcfatJzClAHr9sBN+c6alfFP32eohiS5dCpp+5hO8V1iVfxumPa3/6Cgjhu7";
  return headers;
}

function getEndpoint() {
  return "https://quepasa-api.dev.az.rezolve.com/judgement/v1/";
}

function quepasa_qa_llm_as_a_judje( messages ) {
  var options = {
    'headers': getHeaders(),
    "method": "post",
    "contentType": "application/json",
    "payload": JSON.stringify({
        'messages': messages
    }),
  };

  var response = UrlFetchApp.fetch( getEndpoint() + "scores", options );

  try {
    var responseJson = JSON.parse( response.getContentText() );
    if ( responseJson != null ) {
      return responseJson;
    }

    return null;

  } catch ( e ) {
    return "Error: " + e.toString();
  }
}

function quepasa_qa_side_by_side( messages, golden_messages ) {
  var options = {
    'headers': getHeaders(),
    "method": "post",
    "contentType": "application/json",
    "payload": JSON.stringify({
        'messages': messages,
        'golden_messages': golden_messages
    }),
  };

  var response = UrlFetchApp.fetch( getEndpoint() + "side-by-side", options );

  try {
    var responseJson = JSON.parse( response.getContentText() );
    if ( responseJson != null ) {
      return responseJson;
    }

    return null;

  } catch ( e ) {
    return "Error: " + e.toString();
  }
}

function quepasa_qa_golden( messages, golden_messages ) {
  var options = {
    'headers': getHeaders(),
    "method": "post",
    "contentType": "application/json",
    "payload": JSON.stringify({
        'messages': messages,
        'golden_messages': golden_messages        
    }),
  };

  var response = UrlFetchApp.fetch( getEndpoint() + "golden-match", options );

  try {
    var responseJson = JSON.parse( response.getContentText() );
    if ( responseJson != null ) {
      return responseJson;
    }

    return null;

  } catch ( e ) {
    return "Error: " + e.toString();
  }
}

function findRowNumByValue( activeSheet, column, value ) {
  for (var i = 1; i < activeSheet.getMaxRows(); i++) {
    if (activeSheet.getRange(i, column).getValue() == value ) {
      return i;
    }
  }
  return -1
}

function findColumnNumByName(activeSheet, name) {
  var lastCol = activeSheet.getLastColumn();
  var secondRow = activeSheet.getRange(2, 1, 1, lastCol).getValues()[0];

  for (var col = 0; col < secondRow.length; col++) {
    var cellValue = secondRow[col];
    if (cellValue && cellValue.toString().trim() === name.trim()) {
      return col + 1; // Apps Script columns are 1-based
    }
  }

  return -1; // not found
}

function formatJSON(jsonData, indent = 0) {
  var result = '';
  var indentStr = '  '.repeat(indent); // Два пробела на уровень

  for (var key in jsonData) {
    if (!jsonData.hasOwnProperty(key)) continue;

    var value = jsonData[key];

    if (typeof value === 'object' && value !== null) {
      result += `${indentStr}${key}:\n` + formatJSON(value, indent + 1);
    } else {
      result += `${indentStr}${key}: ${value}\n`;
    }
  }

  return result;
}
