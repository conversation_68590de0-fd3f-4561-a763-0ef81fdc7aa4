import os
import sys
import pytest
import pytest_asyncio
import logging

from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from fastapi import FastAP<PERSON>, Request, HTTPException
from httpx import AsyncClient, ASGITransport
from quepasa.api.auth import verify_auth
from quepasa.api.handlers.document_handler import router as document_router, DocumentHandler
from quepasa.api.handlers.file_handler import router as file_router
from quepasa.api.handlers.batch_handler import router as batch_router
from botocore.exceptions import ClientError
from src.lib.files import QuepasaFiles
from src.lib.logger import QuepasaLogger
import hashlib

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

def pytest_configure(config):
    """Configure pytest-asyncio to use function scope for async fixtures"""
    config.option.asyncio_default_fixture_loop_scope = "function"

@pytest.fixture(autouse=True)
def configure_logging():
    """Configure logging for tests to prevent 'I/O operation on closed file' errors"""
    root = logging.getLogger()
    root.setLevel(logging.INFO)
    
    # Remove any existing handlers
    for handler in root.handlers[:]:
        root.removeHandler(handler)
    
    # Add stdout handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    root.addHandler(handler)
    
    yield
    
    # Clean up
    for handler in root.handlers[:]:
        handler.close()
        root.removeHandler(handler)

@pytest.fixture
def mock_redis():
    """Mock Redis for tests"""
    with patch('redis.Redis') as mock_redis:
        mock_redis.return_value.get.return_value = None
        mock_redis.return_value.set.return_value = True
        yield mock_redis

@pytest.fixture
def test_config():
    """Test configuration for batch tests"""
    return {
        "env": "test",
        "image": "quepasa/data_processor:latest",
        "client_id": "test_client",
        "task_type": "processor"
    }

@pytest.fixture(autouse=True)
def setup_test_env():
    """Setup test environment variables."""
    # Store original environment variables
    original_env = {
        'MINIO_HOST': os.environ.get('MINIO_HOST'),
        'MINIO_PORT': os.environ.get('MINIO_PORT'),
        'MINIO_ACCESS_KEY': os.environ.get('MINIO_ACCESS_KEY'),
        'MINIO_SECRET_KEY': os.environ.get('MINIO_SECRET_KEY'),
        'MINIO_BUCKET_NAME': os.environ.get('MINIO_BUCKET_NAME'),
    }
    
    # Set test environment variables
    os.environ['MINIO_HOST'] = 'localhost'
    os.environ['MINIO_PORT'] = '9000'
    os.environ['MINIO_ACCESS_KEY'] = 'minioadmin'
    os.environ['MINIO_SECRET_KEY'] = 'minioadmin'
    os.environ['MINIO_BUCKET_NAME'] = 'quepasa-files'
    
    yield
    
    # Restore original environment variables
    for key, value in original_env.items():
        if value is not None:
            os.environ[key] = value
        elif key in os.environ:
            del os.environ[key]

@pytest.fixture(autouse=True)
def mock_embedding_env():
    """Ensure embedding providers have required API keys set during tests."""
    with patch.dict('os.environ', {
        'OPENAI_API_KEY': os.environ.get('OPENAI_API_KEY', 'test-openai-key'),
        'ASK_EMBEDDING_API_KEY': os.environ.get('ASK_EMBEDDING_API_KEY', 'test-ask-key'),
        'NEBIUS_API_KEY': os.environ.get('NEBIUS_API_KEY', 'test-nebius-key'),
    }, clear=False):
        yield

@pytest_asyncio.fixture
async def test_client():
    # Mock configuration hub
    mock_config_hub = MagicMock()
    mock_config_hub.validate_auth_token.return_value = True
    
    # Create test app
    app = FastAPI()
    app.include_router(document_router, prefix="/api/v1/documents")
    app.include_router(file_router, prefix="/api/v1/files")
    app.include_router(batch_router, prefix="/api/v1/batches")
    
    # Mock verify_auth to properly handle invalid tokens
    async def mock_verify_auth(request: Request) -> str:
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise HTTPException(401, "Missing Authorization header")
            
        if not auth_header.startswith("Bearer "):
            raise HTTPException(401, "Invalid Authorization header format")
            
        token = auth_header.split(" ")[1]
        token_parts = token.split(":")
        if len(token_parts) != 2:
            raise HTTPException(401, "Invalid token format")
            
        client_id, auth_token = token_parts
        if client_id != "test_client" or auth_token != "test_token":
            raise HTTPException(401, "Invalid token")
            
        return client_id
    
    # Mock dependencies
    app.dependency_overrides[verify_auth] = mock_verify_auth
    
    # Create async test client using httpx with ASGITransport (httpx>=0.28)
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test", headers={"Authorization": "Bearer test_client:test_token"}) as client:
        yield client

@pytest_asyncio.fixture
async def invalid_auth_client():
    # Create test app
    app = FastAPI()
    app.include_router(document_router, prefix="/api/v1/documents")
    app.include_router(file_router, prefix="/api/v1/files")
    app.include_router(batch_router, prefix="/api/v1/batches")
    
    # Create async test client without auth header
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        yield client

@pytest.fixture
def mock_files():
    """Mock S3 client for tests"""
    # Mock data for test document
    test_doc = {
        'id': 'test_doc_id',
        'url': 'http://example.com/test',
        'language': 'en',
        'chunks': [{'language': 'en', 'text': 'Test document content'}]
    }
    
    # Create mock S3 client
    mock_client = MagicMock()
    
    # Mock head_object method
    def mock_head_object(Bucket, Key):
        print(f"\nChecking head_object for: {Key}")
        # Get the domain hash
        domain_hash = hashlib.md5('test_domain'.encode('utf-8')).hexdigest()
        # Check if path contains domain hash and ends with .zlib.json
        expected_path = f"prod/storage/data-processor/api-v1/test_client/{domain_hash}/test_doc_id.zlib.json"
        if Key == expected_path:
            print(f"Found document at {Key}")
            return {'ContentLength': 100}
        if 'list_documents.json' in Key:
            print(f"Found list_documents.json at {Key}")
            return {'ContentLength': 100}
        print(f"File not found at {Key}")
        raise ClientError(
            error_response={'Error': {'Code': '404', 'Message': 'Not Found'}},
            operation_name='head_object'
        )
    mock_client.head_object = Mock(side_effect=mock_head_object)
    
    # Mock get_object method
    def mock_get_object(Bucket, Key):
        print(f"\nGetting object: {Key}")
        # Get the domain hash
        domain_hash = hashlib.md5('test_domain'.encode('utf-8')).hexdigest()
        # Return test document if path is in the correct domain
        expected_path = f"prod/storage/data-processor/api-v1/test_client/{domain_hash}/test_doc_id.zlib.json"
        if Key == expected_path:
            print(f"Returning document data for {Key}")
            import json
            import zlib
            content = json.dumps(test_doc).encode('utf-8')
            compressed = zlib.compress(content)
            return {'Body': MagicMock(read=lambda: compressed)}
        if 'list_documents.json' in Key:
            print(f"Returning document list for {Key}")
            content = json.dumps({'test_domain': ['doc1', 'doc2']}).encode('utf-8')
            return {'Body': MagicMock(read=lambda: content)}
        print(f"File not found at {Key}")
        raise ClientError(
            error_response={'Error': {'Code': '404', 'Message': 'Not Found'}},
            operation_name='get_object'
        )
    mock_client.get_object = Mock(side_effect=mock_get_object)
    
    # Mock put_object method
    def mock_put_object(Bucket, Key, Body, **kwargs):
        print(f"\nPutting object: {Key}")
        return {'ResponseMetadata': {'HTTPStatusCode': 200}}
    mock_client.put_object = Mock(side_effect=mock_put_object)
    
    # Create QuepasaFiles instance with debug flag
    files = QuepasaFiles(
        bucket_name='test-bucket',
        endpoint_url='http://test:9000',
        aws_access_key_id='test',
        aws_secret_access_key='test',
        debug_flag=True
    )
    
    # Override the S3 client
    files.s3_client = mock_client
    
    return files

@pytest.fixture
def mock_batch_utils():
    """Mock BatchUtils for tests"""
    with patch('src.lib.batch_utils.BatchUtils') as mock:
        # Mock create_batch
        mock.create_batch = Mock(return_value="test_batch_id")
        mock.get_generated_batch_id = Mock(return_value="test_batch_id")
        
        # Mock get_document_filenames
        def get_document_filenames(client_id, domain, doc_id):
            domain_hash = hashlib.md5(str(domain).encode('utf-8')).hexdigest()
            data_dir = f"prod/storage/data-processor/api-v1/{client_id}/{domain_hash}"
            base_path = doc_id  # For test simplicity, use doc_id directly
            document_filename = f"{data_dir}/{base_path}"
            document_file_meta_json = document_filename + ".meta.json"
            document_file_zlib_json = document_filename + ".zlib.json"
            print(f"\nGenerated document paths:")
            print(f"- Meta JSON: {document_file_meta_json}")
            print(f"- Zlib JSON: {document_file_zlib_json}")
            return document_file_zlib_json, document_file_meta_json
        mock.get_document_filenames = Mock(side_effect=get_document_filenames)
        
        # Mock get_storage_dir
        def get_storage_dir(storage, client_id, domain):
            domain_hash = hashlib.md5(str(domain).encode('utf-8')).hexdigest()
            return f"prod/storage/data-processor/api-v1/{client_id}/{domain_hash}"
        mock.get_storage_dir = Mock(side_effect=get_storage_dir)
        
        return mock

@pytest.fixture
def document_handler(mock_files, mock_batch, mock_batch_utils):
    """Create a document handler with mocked dependencies"""
    # Create document handler with mocked files
    handler = DocumentHandler(mock_files)
    
    # Patch BatchUtils in both the document handler and batch utils module
    with patch('quepasa.api.handlers.document_handler.qp_batch', mock_batch), \
         patch('quepasa.api.handlers.document_handler.BatchUtils', mock_batch_utils), \
         patch('src.lib.batch_utils.BatchUtils', mock_batch_utils), \
         patch('src.lib.files.QuepasaFiles', return_value=mock_files):
        return handler 