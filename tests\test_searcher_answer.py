import pytest
from datetime import datetime
from quepasa.searcher.models.answer import AnswerFormatter, FormattedAnswer
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.response import QuepasaResponse
from quepasa.searcher.models.document import QuepasaDocument
from src.lib.constants import ANSWER_TYPE_STRICT, ANSWER_TYPE_NO_LINKS, DOCUMENT_TYPE_DOCUMENT
from unittest.mock import patch, Mock

@pytest.fixture
def sample_request():
    return QuepasaRequest(
        question="test query",
        protocol="http"
    )

@pytest.fixture
def sample_document():
    return QuepasaDocument(
        root_id="test_root",
        id="test_id",
        chunk_index=0,
        client="test_client",
        domain="test.com",
        provider="web",
        type="web",
        kind="article",
        level=0,
        language="en",
        url="https://example.com",
        title="Test Document",
        text="Sample text content",
        keywords=[],
        tokens=100,
        chunks=1,
        start_position=0,
        end_position=100,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        embeddings=[],
        score=0.8
    )

@pytest.fixture
def mock_request():
    return QuepasaRequest(
        client="test_client",
        question="Test question?",
        source="web"
    )

@pytest.fixture
def mock_document():
    return QuepasaDocument(
        root_id="test:example.com:doc1",
        id="test:example.com:doc1:0",
        chunk_index=0,
        client="test_client",
        domain="example.com",
        provider="web",
        type=DOCUMENT_TYPE_DOCUMENT,
        kind="document",
        level="info",
        url="https://example.com",
        language="en",
        title="Example Document",
        keywords=["example", "test"],
        text="This is an example document for testing.",
        tokens=10,
        chunks=1,
        start_position=0,
        end_position=10,
        created_at="2023-01-01T00:00:00Z",
        updated_at=None,
        embeddings=None,
        score=0.95
    )

def test_formatted_answer_creation():
    references = [{
        "type": "web",
        "url": "https://example.com",
        "title": "Test",
        "text": "Test content",
        "created_at": datetime.now().isoformat()
    }]
    
    answer = FormattedAnswer(
        type=ANSWER_TYPE_STRICT,
        text="Test answer [1]",
        markdown="Test answer [Test](https://example.com)",
        references=references
    )
    
    assert answer.text == "Test answer [1]"
    assert answer.markdown == "Test answer [Test](https://example.com)"
    assert len(answer.references) == 1

def test_answer_formatter_no_sources():
    formatter = AnswerFormatter()
    request = QuepasaRequest(
        question="test",
        protocol="http"
    )
    
    result = formatter.format_answer(request, "Simple answer without sources")
    
    assert result.text == "Simple answer without sources"
    assert not result.references

def test_answer_formatter_with_sources(mock_request, mock_document):
    # Create a source hash with one document
    source_hash = {1: mock_document}
    
    # Create test answer text with a reference
    answer_text = "Answer with reference [1]"
    
    with patch('quepasa.searcher.models.answer.QuepasaConfigurationHub') as mock_hub:
        # Create a mock config
        mock_config = Mock()
        mock_config.get_source_reference.return_value = "🌐 example.com"
        mock_source_ref = Mock(
            label="🌐 example.com",
            source_type="web",
            url="https://example.com"
        )
        mock_config.get_source_reference_object.return_value = mock_source_ref
        
        # Configure the mock hub to return our mock config
        mock_hub.from_request.return_value = mock_config
        
        # Create the formatter and format the answer
        formatter = AnswerFormatter()
        formatted = formatter.format_answer(
            request=mock_request,
            text=answer_text,
            source_hash=source_hash
        )
        
        # Assert that our reference was correctly inserted
        assert "🌐 example.com" in formatted.markdown
        assert formatted.references[1].url == "https://example.com"
        assert formatted.references[1].type == DOCUMENT_TYPE_DOCUMENT

def test_answer_formatter_markdown_escaping(sample_request):
    formatter = AnswerFormatter()
    text = "Test with *asterisks* and _underscores_"
    
    result = formatter.format_answer(sample_request, text)
    
    assert "*" in result.markdown  # Asterisks should be escaped
    assert "_" in result.markdown  # Underscores should be escaped 