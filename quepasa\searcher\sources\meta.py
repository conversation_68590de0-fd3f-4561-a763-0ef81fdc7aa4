import math
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Union, Generator, Set, List

from configuration.main.default import QuepasaConfigurationHub
from src.lib.logger import <PERSON><PERSON>aLogger
from src.lib.markdown_converter import product_to_markdown
from .base import BaseSource
from .mixins import AnswerRetrieverMixin
from ..core.rag_search import RagSearchManager
from ..core.spd_search import SPDSearchManager
from ..models.document import QuepasaDocument
from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse, QuepasaStreamAnswer
from ..models.spd_result import SPDSearchResult
from ..sources.base import STATUS_SUCCESS

logger = QuepasaLogger().get_instance(__name__)
_source_rag = "RAG"
_source_spd = "SPD"


class MetaSource(BaseSource, AnswerRetrieverMixin):
    """MetaSearch source for retrieval augmented generation"""

    def __init__(self):
        """Initialize RAG source"""
        super().__init__()
        self.search_engine_rag = RagSearchManager()
        self.search_engine_spd = SPDSearchManager()

    @staticmethod
    def _mix_results(rag_records: List[QuepasaDocument], spd_records: List[SPDSearchResult], ratio_rag: int, ratio_spd: int) -> List[
        Union[QuepasaDocument, SPDSearchResult]]:
        # use gcd to reduce ratio, e.g. 25:75 -> 1:3
        gcd = math.gcd(ratio_rag, ratio_spd)
        ratio_rag //= gcd
        ratio_spd //= gcd

        if ratio_rag > ratio_spd:
            schedule = [_source_rag] * ratio_rag + [_source_spd] * ratio_spd
        elif ratio_rag < ratio_spd:
            schedule = [_source_spd] * ratio_spd + [_source_rag] * ratio_rag
        else:
            schedule = [_source_rag, _source_spd]

        used_skus: Set[str] = set()
        result: List[Union[QuepasaDocument, SPDSearchResult]] = []

        len_rag, len_spd = len(rag_records), len(spd_records)
        i = j = 0

        def take_next(records: List[Union[QuepasaDocument, SPDSearchResult]], idx: int) -> int:
            # skip already-used ids
            while idx < len(records) and records[idx].get_product_id() in used_skus:
                idx += 1
            if idx < len(records):
                item = records[idx]
                result.append(item)
                if item.get_product_id():
                    used_skus.add(item.get_product_id())
                idx += 1
            return idx

        while i < len_rag or j < len_spd:
            progressed = False
            for source in schedule:
                if source == _source_rag and i < len_rag:
                    new_i = take_next(rag_records, i)
                    progressed |= (new_i != i)
                    i = new_i
                elif source == _source_spd and j < len_spd:
                    new_j = take_next(spd_records, j)
                    progressed |= (new_j != j)
                    j = new_j
            # If a full schedule pass made no progress, we’re done
            if not progressed:
                break

        return result

    def _search(self, request: QuepasaRequest) -> List:
        config = QuepasaConfigurationHub.from_request(request)
        only_promoted = config.get_spd_include_promos_only()
        ratios = config.get_spd_engine_ratios()

        ratio_rag, ratio_spd = ratios.rag, ratios.spd
        if ratio_rag is None or ratio_rag < 0 or ratio_spd is None or ratio_spd < 0 or ratio_rag + ratio_spd == 0:
            logger.warning(
                f"RAG and/or SPD ratios are misconfigured (r: {ratio_rag}, s: {ratio_spd}), using default RAG only",
                extra={"client": request.client}
            )
            ratio_rag = 1
            ratio_spd = 0

        if ratio_rag > 0 and ratio_spd == 0:
            return self.search_engine_rag.search(request)
        elif ratio_spd > 0 and ratio_rag == 0:
            return self.search_engine_spd.search(request)

        with ThreadPoolExecutor(max_workers=2) as pool:
            futures = {
                _source_rag: pool.submit(self.search_engine_rag.search, request),
                _source_spd: pool.submit(self.search_engine_spd.search, request),
            }
            results = {}
            for source, future in futures.items():
                try:
                    results[source] = future.result()
                except Exception as e:
                    logger.error(f"{source.upper()} search failed: {e}")
                    results[source] = []

        rag_docs = results[_source_rag]
        spd_docs = results[_source_spd]

        if only_promoted:
            spd_docs = [d for d in spd_docs if isinstance(d, SPDSearchResult) and d.is_promoted()]

        return self._mix_results(rag_records=rag_docs,
                                 spd_records=spd_docs,
                                 ratio_rag=ratio_rag,
                                 ratio_spd=ratio_spd)

    def search(self, request: QuepasaRequest) -> QuepasaResponse:
        try:
            # Get search results
            documents = self._search(request)

            # Process search results using mixin
            return QuepasaResponse(status=STATUS_SUCCESS, data=documents)
        except Exception as e:
            return self._handle_error(request, f"Error searching: {str(e)}")

    def get_answer(self, request: QuepasaRequest, stream: bool = False) -> Union[
        QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """Get answer for request

        Args:
            request: Search request
            stream: Whether to stream the response

        Returns:
            Search response or generator of stream responses
        """
        try:
            # Get search results
            documents = self._search(request)

            # add $.text as markdown formatted product description
            for i, doc in enumerate(documents):
                if isinstance(doc, SPDSearchResult):
                    doc.text = product_to_markdown(doc.metadata)

            # Process search results using mixin
            return self.retrieve_answer(request=request, source=request.source, documents=documents, stream=stream)

        except Exception as e:
            return self._handle_error(request, f"Error getting answer: {str(e)}", stream)
