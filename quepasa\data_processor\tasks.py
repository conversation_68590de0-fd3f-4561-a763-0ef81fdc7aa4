import os
import json
import hashlib
from typing import Dict, List, Union, Optional, Tuple
from datetime import datetime, timezone
from celery import group

from src.lib.files import QuepasaFiles
from src.lib.logger import QuepasaLogger
from src.lib.utils import has_str_field_in_dict
from src.lib.batch_utils import (
    BatchStorage, 
    BatchAction, 
    DataProcessorAction, 
    BatchUtils,
    BatchState
)

from src.lib.celery_app import app

from src.lib.constants import DOCUMENT_TYPE_DOCUMENT


# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize QuepasaFiles
qp_files = QuepasaFiles()

# Task definitions
@app.task(bind=True, max_retries=3, queue='data-processor', name='data-processor.process_document_upsert')
def process_document_upsert(self, client_id: str, batch_id: str, domain: str, doc_or_path: Union[str, Dict]) -> Optional[Union[str, DataProcessorAction]]:
    """Process a single document for upsert/replace action"""
    logger.info(f"Processing document upsert for {doc_or_path}, client_id: {client_id}, domain: {domain}")
    try:
        # Get document from path or directly from dict
        if isinstance(doc_or_path, str):
            doc = qp_files.get_json_zlib(doc_or_path)
        elif isinstance(doc_or_path, dict):
            doc = doc_or_path
        else:
            logger.error(f"[{client_id}, {batch_id}] Invalid document type: {type(doc_or_path)}")
            return None, None

        # Validate document
        if not all(has_str_field_in_dict(key, doc) for key in ['id', 'url']):
            logger.error(f"[{client_id}, {batch_id}] Invalid document: {doc}")
            return None, None

        doc['domain'] = str(domain)
        if 'updated_at' not in doc:
            doc['updated_at'] = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        
        for key in ['title', 'keywords']:
            if not has_str_field_in_dict(key, doc):
                doc[key] = ""

        # Process chunks
        chunks = []
        unique_languages = []

        if 'chunks' in doc and isinstance(doc['chunks'], list) and doc['chunks']:
            # Process existing chunks
            for chunk in doc['chunks']:
                if not all(has_str_field_in_dict(key, chunk) for key in ['language', 'text']):
                    logger.error(f"[{client_id}, {batch_id}] Invalid chunk: {chunk}")
                    continue

                if 'keywords' not in chunk:
                    chunk['keywords'] = ""

                if chunk['language'] not in unique_languages:
                    unique_languages.append(chunk['language'])

                # Clean chunk object
                clean_chunk = {
                    'language': chunk['language'],
                    'text': chunk['text']
                }
                if 'position' not in chunk:
                    clean_chunk['position'] = None
                if 'keywords' not in chunk:
                    clean_chunk['keywords'] = None
                chunks.append(clean_chunk)

        if not chunks:
            logger.error(f"[{client_id}, {batch_id}] No chunks found for document: {doc}")
            return None, None

        # Update document
        doc['chunks'] = chunks
        doc['languages'] = unique_languages

        if 'type' not in doc:
            doc['type'] = DOCUMENT_TYPE_DOCUMENT

        # Clean document object
        clean_doc = {
            key: doc[key] for key in [
                'provider', 'domain', 'access', 'type',
                'id', 'url', 'title', 'keywords',
                'sku', 'price_from', 'price_to', # Product specific fields
                'chunks', 'languages', 'created_at', 'updated_at'
            ] if key in doc
        }

        # Generate filenames
        document_file_zlib_json, document_file_meta_json = BatchUtils.get_document_filenames(client_id, domain, clean_doc['id'])

        # Check if content changed
        content_md5 = hashlib.md5(json.dumps(clean_doc).encode('utf-8')).digest()
        properties = qp_files.get_properties_safe(document_file_zlib_json)

        if not properties or properties['content_md5'] != content_md5:
            # Set created_at
            if 'created_at' not in clean_doc:
                if not properties:
                    clean_doc['created_at'] = clean_doc['updated_at']
                else:
                    old_doc = qp_files.get_json_zlib(document_file_zlib_json)
                    clean_doc['created_at'] = old_doc['created_at']

            # Save document
            qp_files.set_json_zlib(document_file_zlib_json, clean_doc, content_md5)

            if qp_files.exists(document_file_meta_json):
                qp_files.delete_file(document_file_meta_json)

            logger.info(f"[{client_id}, {batch_id}] Processed document: {clean_doc['id']}")
            return clean_doc['id'], DataProcessorAction.UPSERT

    except Exception as e:
        logger.error(f"[{client_id}, {batch_id}] Failed to process document: {str(e)}", extra={
            'document': doc_or_path,
            'client_id': client_id,
            'domain': domain
        })
        self.retry(exc=e, countdown=3)
    return None, None

@app.task(bind=True, max_retries=3, queue='data-processor', name='data-processor.process_document_remove')
def process_document_remove(self, client_id: str, batch_id: str, domain: str, doc_id: str) -> Optional[Union[str, DataProcessorAction]]:
    """Process document removal (delete or reset)"""
    logger.info(f"[{client_id}, {batch_id}] Processing document removal for {doc_id}, client_id: {client_id}, domain: {domain}")
    try:
        document_file_zlib_json, document_file_meta_json = BatchUtils.get_document_filenames(client_id, domain, doc_id)
        if qp_files.exists(document_file_zlib_json):
            qp_files.delete_file(document_file_zlib_json)

        qp_files.set_json(document_file_meta_json, {
            'id': doc_id,
            'domain': domain,
            'status': "removed"
        })

        logger.info(f"[{client_id}, {batch_id}] Removed document: {doc_id}")
        return doc_id, DataProcessorAction.DELETE

    except Exception as e:
        logger.error(f"[{client_id}, {batch_id}] Failed to remove document: {str(e)}", extra={
            'document_id': doc_id,
            'client_id': client_id,
            'domain': domain
        })
        self.retry(exc=e, countdown=3)
    return None, None

@app.task(bind=True, max_retries=3, queue='data-processor', name='data-processor.process_batch')
def process_batch(self, client_id: str, batch_id: str) -> Dict:
    """Process documents in a batch"""
    batch_file = BatchUtils.get_batch_filename(client_id, batch_id, BatchState.BACKLOG)
    if not qp_files.exists(batch_file):
        logger.error(f"[{client_id}, {batch_id}] Batch file {batch_file} does not exist")
        return {
            'status': 'error',
            'message': f"Batch file {batch_file} does not exist"
        }

    try:
        batch = qp_files.get_json_zlib(batch_file)
        if 'domain' not in batch:
            raise ValueError("'domain' is required in batch")
        
        if 'action' not in batch:
            raise ValueError("'action' is required in batch")
        
        domain = batch.get('domain')    
        action = batch.get('action')
        skip_indexing = batch.get('skip_indexing', False)
        
        logger.info(f"[{client_id}, {batch_id}] Running process_batch for domain={domain}, action={action}, skip_indexing={skip_indexing}")
        workflow = (
            process_documents(client_id, batch_id, batch) | 
            collect_results.s(client_id, batch_id) | 
            save_results.s(client_id, batch_id, domain, action, skip_indexing)
        )
        async_result = workflow.apply_async()
        
        return {
            'client_id': client_id,
            'task_id': async_result.id,
            'status': 'pending',
            'started_at': datetime.now(timezone.utc).isoformat(),
            'stats': {
                'total_tasks': 1,
                'processed_tasks': 0
            },
            'skip_indexing': skip_indexing
        }
    
    except Exception as exc:
        logger.error(f"[{client_id}, {batch_id}] Batch processing failed: {str(exc)}", exc_info=True)
        self.retry(exc=exc, countdown=60)

@app.task(bind=True, queue='data-processor', name='data-processor.collect_results')
def collect_results(self, results: List[Tuple[str, DataProcessorAction]], client_id: str, batch_id: str) -> Dict:
    """Collect and process results from the group of tasks"""
    logger.info(f"[{client_id}, {batch_id}] Collecting results")
    
    # Filter out None results and format errors
    processed_results = []
    success_count = 0
    error_count = 0
    
    # Handle list of results
    if isinstance(results, list):
        for doc_id, action in results:
            if isinstance(doc_id, Exception):
                error_count += 1
                processed_results.append({
                    'error': str(doc_id),
                    'status': 'error'
                })

            elif doc_id and action:  # Handle document ID
                success_count += 1
                processed_results.append({
                    'id': doc_id,
                    'action': action,
                    'status': 'success'
                })

    logger.info(f"[{client_id}, {batch_id}] Collected results, success={success_count}, error={error_count}")
    return {
        'client_id': client_id,
        'results': processed_results,
        'status': 'success',
        'completed_at': datetime.now(timezone.utc).isoformat(),
        'stats': {
            'total': len(processed_results),
            'success': success_count,
            'error': error_count
        }
    }

@app.task(bind=True, queue='data-processor', name='data-processor.process_documents')
def process_documents(self, client_id: str, batch_id: str, batch: Dict) -> List[Union[str, DataProcessorAction]]:
    """Process documents in a batch and return list of processed document IDs"""
    logger.info(f"[{client_id}, {batch_id}] Processing documents with batch")

    domain = batch['domain']
    action = batch['action']

    if action in [BatchAction.UPSERT, BatchAction.REPLACE]:
        # Create a group of tasks for parallel processing
        tasks = group([
            process_document_upsert.s(client_id, batch_id, domain, doc_or_path)
            for doc_or_path in batch['documents']
        ])

        if action == BatchAction.UPSERT:
            return tasks

        # Chain group with result processing
        return (tasks | process_replace_results.s(client_id, batch_id, domain))

    elif action == BatchAction.DELETE:
        # Create a group of tasks for parallel deletion
        tasks = group([
            process_document_remove.s(client_id, batch_id, domain, doc['id'])
            for doc in batch['documents']
        ])
        return tasks

    elif action == BatchAction.RESET:
        removed_files = []
        data_out_dir = BatchUtils.get_storage_dir(BatchStorage.DATA_PROCESSOR, client_id, domain)
        for document_file in qp_files.get_files(data_out_dir, True):
            if document_file.endswith(".zlib.json"):
                doc = qp_files.get_json_zlib(document_file)
                removed_files.append(doc['id'])

        if removed_files:
            # Create a group of tasks for parallel reset
            tasks = group([
                process_document_remove.s(client_id, batch_id, domain, doc_id)
                for doc_id in removed_files
            ])
            return tasks

    return group([])

@app.task(bind=True, queue='data-processor', name='data-processor.process_replace_results')
def process_replace_results(self, processed_results: List[Tuple[str, DataProcessorAction]], client_id: str, batch_id: str, domain: str):
    """Process results from replace operations"""
    logger.info(f"[{client_id}, {batch_id}] Cleaning up old documents for REPLACE action")
    update_paths = []
    for doc_id, _ in processed_results:
        if doc_id:  # Make sure doc_id is not None
            document_file_zlib_json, _ = BatchUtils.get_document_filenames(client_id, domain, doc_id)
            update_paths.append(document_file_zlib_json.lower())
    
    removed_files = []
    data_out_dir = BatchUtils.get_storage_dir(BatchStorage.DATA_PROCESSOR, client_id, domain)
    for document_file in qp_files.get_files(data_out_dir, True):
        logger.info(f"[{client_id}, {batch_id}] Checking document file: {document_file}")
        if document_file.endswith(".zlib.json") and document_file.lower() not in update_paths:
            doc = qp_files.get_json_zlib(document_file)
            removed_files.append(doc['id'])
            logger.info(f"[{client_id}, {batch_id}] Removing document: {doc['id']}")

    logger.info(f"[{client_id}, {batch_id}] Removed files: {removed_files}")
    
    # Create additional results for removed documents
    remove_results = []
    for doc_id in removed_files:
        # Process each document for removal and add to results
        result = process_document_remove(client_id, batch_id, domain, doc_id)
        if result[0]:  # Only add if processing was successful
            remove_results.append(result)
    
    # Combine original results with removal results
    combined_results = list(processed_results) + remove_results
    return combined_results

@app.task(bind=True, queue='data-processor', name='data-processor.save_results')
def save_results(self, results: Dict, client_id: str, batch_id: str, domain: str, action: str, skip_indexing: bool) -> Dict:
    """Save the processed results to a file"""
    logger.info(f"[{client_id}, {batch_id}] Saving results for domain={domain}, action={action}")
    
    new_batch_data = {
        'client_id': client_id,
        'domain': domain,
        'action': action,
        'processed_ids': [],
        'changes': {
            DataProcessorAction.UPSERT.value: [],
            DataProcessorAction.DELETE.value: []
        },
        'skip_indexing': skip_indexing
    }

    for item in results.get('results', []):
        if item.get('status') == 'success':
            new_batch_data['processed_ids'].append(f"{domain}:{item.get('id')}")
            new_batch_data['changes'][item.get('action')].append(item.get('id'))

    # Delete original batch file
    batch_file = BatchUtils.get_batch_filename(client_id, batch_id, BatchState.BACKLOG)
    if qp_files.exists(batch_file):
        qp_files.delete_file(batch_file)

    # Save to appropriate state file based on results
    batch_state = None
    if len(new_batch_data['processed_ids']) > 0:
        # Add indexer task only if skip_indexing is False
        if not skip_indexing:
            batch_state = BatchState.IN_PROGRESS
            backlog_batch_file = BatchUtils.get_batch_filename(client_id, batch_id, batch_state)
            qp_files.set_json_zlib(backlog_batch_file, new_batch_data)

            BatchUtils.add_task('indexer', client_id, batch_id)
            logger.info(f"[{client_id}, {batch_id}] Added to indexer queue for indexing")

        else:
            batch_state = BatchState.DONE_WITHOUT_INDEXING
            backlog_batch_file = BatchUtils.get_batch_filename(client_id, batch_id, batch_state)
            qp_files.set_json_zlib(backlog_batch_file, new_batch_data)

            logger.info(f"[{client_id}, {batch_id}] Skipping indexer stage as requested by skip_indexing flag")
        
    else:
        batch_state = BatchState.DONE
        done_batch_file = BatchUtils.get_batch_filename(client_id, batch_id, batch_state)
        qp_files.set_json_zlib(done_batch_file, new_batch_data)
    
    logger.info(f"[{client_id}, {batch_id}] Batch processed successfully, action={action}, processed_ids={len(new_batch_data['processed_ids'])}")

    return {
        'status': 'success',
        'client_id': client_id,
        'batch_id': batch_id,
        'processed_count': len(new_batch_data['processed_ids']),
        'state': batch_state,
        'skip_indexing': skip_indexing
    }