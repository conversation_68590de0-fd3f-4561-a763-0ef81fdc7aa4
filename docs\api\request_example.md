# API Request Examples

## Overview

The Quepasa API allows you to send conversational queries with optional attachments and context to receive product recommendations and answers.

## Endpoint

```
POST /conversation
```

## Streaming Support

The `/conversation` endpoint supports both streaming and non-streaming responses:

- **Query parameter**: Add `?stream=true` to enable streaming
- **Accept header**: Set `Accept: text/event-stream` for streaming responses
- **Default**: Non-streaming JSON responses

## Content Format

Control response content format:

- **Query parameter**: Add `?format=markdown` (default) or `?format=plain_text`
- **Default**: Markdown formatting

## Authentication

All requests require two authentication headers:

```
X-Customer-Id: <customerId>
Authorization: client-key <client_key_from_brainstudio>
```

## Request Headers

| Header | Value | Required | Description |
|--------|--------|----------|-------------|
| `Content-Type` | `application/json` | Yes | Specifies JSON content |
| `X-Customer-Id` | `<customerId>` | Yes | Customer identifier for tenant validation |
| `Authorization` | `client-key <client_key>` | Yes | Client key authentication from BrainStudio |
| `Accept` | `text/event-stream` | No | Enable streaming responses (alternative to `?stream=true`) |

## Request Body Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `message` | string | Yes | The user's query or message |
| `sessionId` | string | Yes | Session identifier for conversation tracking |
| `collection` | string | Yes | Product collection to search within |
| `area` | string | Yes | Environment area (Production, Staging, etc.) |
| `visitorId` | string | No | Unique visitor identifier |
| `attachments` | array | No | Array of attachment objects (images, etc.) |
| `context` | object | No | User and session context information |
| `options` | object | No | Configuration options for the request |

### Attachment Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `type` | string | Yes | Type of attachment (`image`, etc.) |
| `url` | string | Yes | URL to the attachment resource |

### Context Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `userInfo` | object | No | User information object |

### User Info Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | No | Name of user |

### Options Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `includeHistory` | boolean | No | Include conversation history in response |
| `debug` | boolean | No | Enable debug information in response |

## Python Request Example

```python
import requests

# Non-streaming request
response = requests.post(
    "https://api.example.com/conversation",
    headers={
        'Content-Type': 'application/json',
        'X-Customer-Id': 'your-customer-id',
        'Authorization': 'client-key your-brainstudio-client-key',
    },
    json={
        "message": "I'm looking for some summer clothing",
        "sessionId": "session67890_from_beacontracker",
        "collection": "housewares",
        "area": "Production",
        "visitorId": "user12345_from_beacontracker",
        "attachments": [
            {
                "type": "image",
                "url": "https://cdn.example.com/uploads/inspo.jpg"
            }
        ],
        "context": {
            "userInfo": {
                "name": "John Smith"
            }
        },
        "options": {
            "includeHistory": true,
            "debug": false
        }
    }
)

# Handle the response
if response.status_code == 200:
    result = response.json()
    print(result['content'])
else:
    print(f"Error: {response.status_code} - {response.text}")

# Streaming request using query parameter
streaming_response = requests.post(
    "https://api.example.com/conversation?stream=true",
    headers={
        'Content-Type': 'application/json',
        'X-Customer-Id': 'your-customer-id',
        'Authorization': 'client-key your-brainstudio-client-key',
    },
    json={
        "message": "I'm looking for some summer clothing",
        "sessionId": "session67890_from_beacontracker",
        "collection": "housewares",
        "area": "Production"
    },
    stream=True
)

# Handle streaming response
for line in streaming_response.iter_lines():
    if line:
        print(line.decode('utf-8'))

# Streaming request using Accept header
streaming_response = requests.post(
    "https://api.example.com/conversation",
    headers={
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'X-Customer-Id': 'your-customer-id',
        'Authorization': 'client-key your-brainstudio-client-key',
    },
    json={
        "message": "I'm looking for some summer clothing",
        "sessionId": "session67890_from_beacontracker",
        "collection": "housewares",
        "area": "Production"
    },
    stream=True
)

# Handle streaming response
for line in streaming_response.iter_lines():
    if line:
        print(line.decode('utf-8'))
```

## Request Body Example

```json
{
  "message": "I'm looking for some summer clothing",
  "sessionId": "session67890_from_beacontracker",
  "collection": "housewares",
  "area": "Production",
  "visitorId": "user12345_from_beacontracker",
  "attachments": [
    {
      "type": "image",
      "url": "https://cdn.example.com/uploads/inspo.jpg"
    }
  ],
  "context": {
    "userInfo": {
      "name": "John Smith"
    }
  },
  "options": {
    "includeHistory": true,
    "debug": false
  }
}
```

## HTTP Status Codes

| Code | Description                                                                                                              |
|------|--------------------------------------------------------------------------------------------------------------------------|
| 200  | Success - Request processed successfully                                                                                 |
| 400  | Bad Request - Invalid request format or missing required fields                                                          |
| 401  | Unauthorized - Invalid or missing client-key in the `Authorization` header, or missing or invalid `X-Customer-Id` header |
| 429  | Too Many Requests - Rate limit exceeded                                                                                  |
| 500  | Internal Server Error - Server processing error                                                                          |