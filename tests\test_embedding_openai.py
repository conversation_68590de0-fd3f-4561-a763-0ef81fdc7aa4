import os
import pytest
from unittest.mock import Mock, patch
from openai import OpenAI, OpenAIError
from src.lib.embedding.openai import OpenAIEmbedding
from src.lib.embedding.providers import EmbeddingProvider

@pytest.fixture
def embedding(tmp_path):
    """Fixture that provides an OpenAI embedding instance with mocked client."""
    with patch.dict('os.environ', {'OPENAI_API_KEY': 'test-key'}):
        embedding = OpenAIEmbedding()
        embedding._local_cache_dir = str(tmp_path / "cache")
        os.makedirs(embedding._local_cache_dir, exist_ok=True)
        return embedding

def test_provider(embedding):
    """Test provider property."""
    assert embedding.provider == EmbeddingProvider.OPENAI

def test_truncate_text(embedding):
    """Test text truncation."""
    # Create a long text that exceeds token limit
    long_text = "test\n" * 10000
    truncated = embedding._truncate_text(long_text)
    
    # Check that truncated text is shorter
    assert len(truncated) < len(long_text)
    # Check that truncated text ends with a complete line
    assert truncated.endswith('test')

def test_get_embedding_success(embedding):
    """Test successful embedding generation."""
    test_embedding = [0.1, 0.2, 0.3]
    
    # Mock OpenAI client response
    mock_response = Mock()
    mock_response.data = [Mock(embedding=test_embedding)]
    embedding.client.embeddings.create = Mock(return_value=mock_response)
    
    result = embedding.get_embedding("test-model", "test text")
    
    assert result == test_embedding
    embedding.client.embeddings.create.assert_called_once_with(
        model='test-model',
        input="test text"
    )

def test_get_embedding_failure(embedding):
    """Test error handling in embedding generation."""
    # Mock OpenAI client to raise an exception
    embedding.client.embeddings.create = Mock(side_effect=Exception("API error"))
    
    result = embedding.get_embedding("test-model", "test text")
    
    assert result is None
    embedding.client.embeddings.create.assert_called_once()

def test_missing_api_key(tmp_path):
    """Test error handling for missing API key."""
    with patch.dict('os.environ', clear=True):
        with pytest.raises(ValueError) as exc_info:
            OpenAIEmbedding()
        assert "OPENAI_API_KEY environment variable is not set" in str(exc_info.value)

def test_get_embedding_with_long_text(embedding):
    """Test embedding generation with long text that needs truncation."""
    test_embedding = [0.1, 0.2, 0.3]
    long_text = "test\n" * 10000
    
    # Mock OpenAI client response
    mock_response = Mock()
    mock_response.data = [Mock(embedding=test_embedding)]
    embedding.client.embeddings.create = Mock(return_value=mock_response)
    
    result = embedding.get_embedding("test-model", long_text)
    
    assert result == test_embedding
    # Verify that truncated text was sent to API
    called_text = embedding.client.embeddings.create.call_args[1]['input']
    assert len(called_text) < len(long_text) 