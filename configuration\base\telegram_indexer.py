from typing import List, Union
from quepasa.searcher.models.request import QuepasaRequest
from dataclasses import dataclass
from .telegram_utils import TelegramUtils

@dataclass
class TelegramThreadRequirements:
    """Requirements for indexing a Telegram thread."""
    min_word_count: int
    min_word_count_with_urls: int
    authoritative_usernames: List[str]

class TelegramIndexerConfig(TelegramUtils):
    """Base configuration for Telegram-specific indexing functionality."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def get_earliest_index_date(self) -> str:
        """Get earliest date to start indexing Telegram messages from.
        
        Previously: get_indexer_startdate()
        
        Returns:
            Date string in YYYY-MM-DD format
        """
        return "2014-03-01"

    def get_min_thread_word_count(self, channel_id: str) -> int:
        """Get minimum word count required to index a Telegram thread.
        
        Previously: get_minimal_number_of_words()
        
        Args:
            channel_id: ID of the Telegram channel/chat being indexed
            
        Returns:
            Minimum number of words required
        """
        return 30

    def get_min_url_thread_word_count(self, channel_id: str) -> int:
        """Get minimum word count required to index a Telegram thread containing URLs.
        
        Previously: get_minimal_number_of_words_for_url()
        
        Args:
            channel_id: ID of the Telegram channel/chat being indexed
            
        Returns:
            Minimum number of words required for threads with URLs
        """
        return 5

    def get_authoritative_users(self) -> List[str]:
        """Get list of Telegram usernames whose presence makes a thread indexable.
        
        Previously: get_master_users()
        
        Returns:
            List of authoritative Telegram usernames
        """
        return []

    def get_telegram_thread_requirements(self, channel_id: str) -> TelegramThreadRequirements:
        """Get complete thread requirements for a Telegram channel.
        
        Args:
            channel_id: ID of the Telegram channel/chat
            
        Returns:
            Complete thread requirements configuration
        """
        return TelegramThreadRequirements(
            min_word_count=self.get_min_thread_word_count(channel_id),
            min_word_count_with_urls=self.get_min_url_thread_word_count(channel_id),
            authoritative_usernames=self.get_authoritative_users()
        ) 