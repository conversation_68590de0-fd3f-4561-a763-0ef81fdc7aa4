"""
Configuration Validation Package

This package contains scripts and utilities for validating YAML configuration files
and ensuring they match the original hardcoded values.

Scripts:
- validate_config.py: Main validation script for all client configurations
- validate_yaml_values.py: Direct YAML structure and value validation
- test_config_comparison.py: Compare YAML vs original hardcoded values
- test_yaml_prompts.py: Test prompt template functionality
- original_hardcoded.py: Backup of original hardcoded configuration for testing

Usage:
    python tests/configuration/validate_config.py
    python tests/configuration/validate_yaml_values.py
    python tests/configuration/test_config_comparison.py
    python tests/configuration/test_yaml_prompts.py
"""