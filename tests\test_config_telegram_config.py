import pytest
from unittest.mock import patch
from configuration.base.telegram_config import TelegramConfig
from src.lib.files import QuepasaFiles

@pytest.fixture
def telegram_config():
    """Fixture that provides a TelegramConfig instance."""
    return TelegramConfig("test_client")

def test_telegram_config_init(telegram_config):
    """Test TelegramUtils initialization."""
    assert telegram_config.client_code == "test_client"
    assert telegram_config._telegram_config is None
    assert telegram_config._telegram_config_last_updated is None

def test_telegram_config_cache_behavior(telegram_config):
    """Test caching behavior of Telegram configuration."""
    mock_config = {"key": "value"}
    
    with patch('time.time') as mock_time, \
         patch.object(QuepasaFiles, 'exists') as mock_exists, \
         patch.object(QuepasaFiles, 'get_json') as mock_get_json:
        
        # First call at t=0
        mock_time.return_value = 0
        mock_exists.return_value = True
        mock_get_json.return_value = mock_config
        
        config1 = telegram_config._get_telegram_config()
        assert mock_get_json.called
        assert config1 == mock_config
        
        # Second call within cache window
        mock_time.return_value = 300  # 5 minutes
        mock_get_json.reset_mock()
        config2 = telegram_config._get_telegram_config()
        assert not mock_get_json.called
        assert config2 == config1
        
        # Third call after cache expiration
        mock_time.return_value = 660  # 11 minutes
        mock_get_json.return_value = {"key": "updated_value"}
        config3 = telegram_config._get_telegram_config()
        assert mock_get_json.called
        assert config3 == {"key": "updated_value"}

def test_telegram_config_file_error_handling(telegram_config):
    """Test error handling for config file operations."""
    with patch.object(QuepasaFiles, 'exists') as mock_exists, \
         patch.object(QuepasaFiles, 'get_json') as mock_get_json:
        
        # Test file not found
        mock_exists.return_value = False
        config1 = telegram_config._get_telegram_config()
        assert config1 is None
        
        # Test file read error
        mock_exists.return_value = True
        mock_get_json.side_effect = Exception("File read error")
        config2 = telegram_config._get_telegram_config()
        assert config2 is None

def test_telegram_config_path_format(telegram_config):
    """Test correct path format for Telegram configuration file."""
    with patch.object(QuepasaFiles, 'exists') as mock_exists, \
         patch.object(QuepasaFiles, 'get_json') as mock_get_json:
        
        mock_exists.return_value = True
        telegram_config._get_telegram_config()
        
        # Verify the correct path format is used
        mock_exists.assert_called_once_with(f"prod/shared/telegram/saas/test_client.json")

def test_telegram_config_update_after_error(telegram_config):
    """Test configuration update after encountering an error."""
    with patch('time.time') as mock_time, \
         patch.object(QuepasaFiles, 'exists') as mock_exists, \
         patch.object(QuepasaFiles, 'get_json') as mock_get_json:
        
        # First call fails
        mock_time.return_value = 0
        mock_exists.return_value = True
        mock_get_json.side_effect = Exception("File read error")
        
        config1 = telegram_config._get_telegram_config()
        assert config1 is None
        
        # Second call succeeds
        mock_time.return_value = 660  # After cache expiration
        mock_get_json.side_effect = None
        mock_get_json.return_value = {"test": "config"}
        
        config2 = telegram_config._get_telegram_config()
        assert config2 == {"test": "config"} 