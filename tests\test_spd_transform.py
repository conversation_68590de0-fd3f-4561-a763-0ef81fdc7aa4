#!/usr/bin/env python3
"""
Unit test for SPD search extraction using <PERSON><PERSON>
Tests the transformation from SPD response to extracted records
"""

import unittest
from unittest.mock import Mock, patch
from typing import Dict, Any, List

class MockSPDSearchManager:
    """Mock SPD Search Manager for testing"""
    
    def search(self, request):
        """Mock search method that returns extracted records"""
        mock_response = {
            "records": [
                {
                    "_id": "record1",
                    "_u": "http://example.com/product1",
                    "_t": "Blue Jeans",
                    "allMeta": {
                        "id": "PROD001",
                        "title": "Blue Jeans",
                        "description": "Comfortable blue jeans for everyday wear."
                    }
                },
                {
                    "_id": "record2",
                    "_u": "http://example.com/product2", 
                    "_t": "Black Jeans",
                    "allMeta": {
                        "id": "PROD002",
                        "title": "Black Jeans",
                        "description": "Classic black jeans for any occasion."
                    }
                }
            ]
        }
        
        records: List[Dict[str, Any]] = []
        for rec in mock_response.get("records", []):
            if isinstance(rec, dict):
                all_meta = rec.get("allMeta")
                if isinstance(all_meta, dict):
                    records.append(all_meta)
        
        return records


class TestSPDExtractionWithMock(unittest.TestCase):
    """Test cases for SPD record extraction using Mock"""

    def setUp(self):
        """Set up test fixtures"""
        self.mock_search_manager = MockSPDSearchManager()
        self.mock_request = Mock()
        self.mock_request.question = "jeans"
        self.mock_request.client = "test_client"

    def test_spd_extraction_with_mock(self):
        """Test SPD extraction using mock data"""
        records = self.mock_search_manager.search(self.mock_request)
        
        self.assertEqual(len(records), 2)
        self.assertEqual(records[0]["id"], "PROD001")
        self.assertEqual(records[0]["title"], "Blue Jeans")
        self.assertEqual(records[0]["description"], "Comfortable blue jeans for everyday wear.")
        self.assertEqual(records[1]["id"], "PROD002")
        self.assertEqual(records[1]["title"], "Black Jeans")
        self.assertEqual(records[1]["description"], "Classic black jeans for any occasion.")
        

    def test_spd_extraction_with_patch(self):
        """Test SPD extraction using unittest.mock.patch"""
        mock_spd_response = {
            "id": "search-123",
            "query": "shirts",
            "records": [
                {
                    "_id": "record1",
                    "allMeta": {
                        "id": "SHIRT001",
                        "title": "Cotton T-Shirt",
                        "description": "Comfortable cotton t-shirt",
                        "variants": [
                            {"id": "SHIRT001-S", "sizes": ["S"], "price": 19.99},
                            {"id": "SHIRT001-M", "sizes": ["M"], "price": 19.99}
                        ]
                    }
                }
            ]
        }
        
        def extract_records(response_json):
            records: List[Dict[str, Any]] = []
            for rec in response_json.get("records", []):
                if isinstance(rec, dict):
                    all_meta = rec.get("allMeta")
                    if isinstance(all_meta, dict):
                        records.append(all_meta)
            return records
        
        records = extract_records(mock_spd_response)
        
        self.assertEqual(len(records), 1)
        record = records[0]
        
        self.assertEqual(record["id"], "SHIRT001")
        self.assertEqual(record["title"], "Cotton T-Shirt")
        self.assertEqual(record["description"], "Comfortable cotton t-shirt")
        self.assertEqual(len(record["variants"]), 2)
        

    def test_spd_extraction_mock_side_effect(self):
        """Test SPD extraction with mock side_effect"""
        mock_manager = Mock()
        
        mock_manager.search.side_effect = [
            [{"id": "PROD001", "title": "Simple Product", "description": "Basic product"}],
            [{"id": "PROD002", "title": "Complex Product", "description": "Product with variants", 
              "variants": [{"id": "PROD002-S", "sizes": ["S"]}]}]
        ]
        
        records1 = mock_manager.search(self.mock_request)
        self.assertEqual(len(records1), 1)
        self.assertEqual(records1[0]["id"], "PROD001")
        
        records2 = mock_manager.search(self.mock_request)
        self.assertEqual(len(records2), 1)
        self.assertEqual(records2[0]["id"], "PROD002")
        self.assertEqual(len(records2[0]["variants"]), 1)
        

    def test_spd_extraction_mock_return_value(self):
        """Test SPD extraction with mock return_value"""
        mock_manager = Mock()
        mock_manager.search.return_value = [
            {
                "id": "MOCK001",
                "title": "Mock Product",
                "description": "This is a mock product",
                "attributes": {"color": "Red", "size": "L"}
            }
        ]
        
        records = mock_manager.search(self.mock_request)
        
        self.assertEqual(len(records), 1)
        record = records[0]
        
        self.assertEqual(record["id"], "MOCK001")
        self.assertEqual(record["title"], "Mock Product")
        self.assertEqual(record["attributes"]["color"], "Red")
        
        mock_manager.search.assert_called_once_with(self.mock_request)
        

    def test_spd_extraction_error_handling_mock(self):
        """Test SPD extraction error handling with mock"""
        mock_manager = Mock()
        mock_manager.search.side_effect = Exception("SPD API Error")
        
        with self.assertRaises(Exception) as context:
            mock_manager.search(self.mock_request)
        
        self.assertEqual(str(context.exception), "SPD API Error")
        

    def test_spd_extraction_empty_response_mock(self):
        """Test SPD extraction with empty response using mock"""
        mock_manager = Mock()
        mock_manager.search.return_value = []
        
        records = mock_manager.search(self.mock_request)
        
        self.assertEqual(len(records), 0)
        self.assertIsInstance(records, list)
        

    def test_spd_extraction_mock_assertions(self):
        """Test SPD extraction with mock assertions"""
        mock_manager = Mock()
        mock_manager.search.return_value = [
            {"id": "ASSERT001", "title": "Assertion Test", "description": "Testing assertions"}
        ]
        
        records = mock_manager.search(self.mock_request)
        
        mock_manager.search.assert_called_once()
        mock_manager.search.assert_called_with(self.mock_request)
        self.assertEqual(mock_manager.search.call_count, 1)
        self.assertEqual(len(records), 1)
        self.assertEqual(records[0]["id"], "ASSERT001")
        


if __name__ == "__main__":
    unittest.main(verbosity=2)
