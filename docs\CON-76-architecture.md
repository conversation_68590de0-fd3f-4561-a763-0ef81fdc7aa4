# New Conversational Endpoint - Brownfield Architecture

**Document Status:** READY FOR DEVELOPMENT  
**Author:** <PERSON><PERSON><PERSON>, Architect  
**Last Updated:** 2025-09-11

**Related Documents:**
- Product Requirements Document (PRD): `/docs/CON-76-prd.md`
- Target OpenAPI Specification: `/docs/api/conversational_shopping_assistant_v1.yaml`
- Existing Searcher Service OpenAPI: `/docs/api/openapi-searcher.yaml`
- Request Examples: `/docs/api/request_example.md`
- Response Examples: `/docs/api/response_example.md`

## Overview

This document outlines the brownfield architectural approach for integrating a new /conversation endpoint into the existing searcher service. The goal is to introduce a rich, conversational shopping experience by leveraging and extending current functionalities, including the core RAG (Retrieval-Augmented Generation) engine, conversation history, and authentication mechanisms.

This architecture is designed to fulfill the requirements specified in CON-76-prd.md, focusing on minimal disruption to the existing system while delivering the required new features, such as a structured product response format, dual authentication, and server-sent events (SSE) for streaming.

## Current State Architecture

The existing searcher service is a Python FastAPI application responsible for product discovery. Its primary functions are exposed via two main endpoints:

- **/retrieve/answer:** Provides a complete search result in a single HTTP response.
- **/retrieve/answer/stream:** Streams search results using a proprietary format.

Key characteristics of the current system include:

- **Core Logic:** A RAG pipeline that processes user queries to find and rank relevant products and information.
- **Authentication:** A bearer token-based authentication system.
- **Conversation History:** A separate component `HistoryManager` that stores and retrieves conversation history based on a user_id.
- **Deployment:** The service is containerized and deployed in a cloud environment (presumably Kubernetes), sitting behind an API Gateway.

## Proposed Brownfield Architecture

The proposed architecture introduces the new `/conversation` endpoint by adding a new handler and integrating it with existing components. The core principle is reuse over rebuild. Given the tight project deadlines, we will reuse existing, proven code wherever possible to reduce development effort and minimize risk. We will wrap and adapt existing logic where necessary to avoid code duplication and reduce the chance of regressions.

### 3.1. High-Level Architectural Diagram

The following diagram illustrates the interaction between the new and existing components.

```mermaid
graph TD
    subgraph Client
        A[User via Client App]
    end

    subgraph API Gateway
        B[Gateway]
        B -- Rate Limiting, Routing --> C
    end

    subgraph "Searcher Service (FastAPI)"
        C("fa:fa-route Conversation Handler <br> /conversation")
        C -- Validates --> D{"Auth Handler <br> (Reused & Adapted)"}
        C -- Manages --> E{"HistoryManager <br> (Reused)"}
        C -- Invokes --> F{"Searcher Core RAG <br> (Reused)"}
        C -- Formats --> G{"Response Shaper <br> (New)"}
        C -- Streams via --> H{"SSE Handler <br> (Reused Logic)"}
    end

    subgraph "External Services"
        I[Auth Service / DB]
        J[Conversation History Service]
    end

    A -- "POST /conversation <br> X-Customer-Id, <br> Auth: client-key" --> B
    D -- "Verifies Client Key" --> I
    E -- "Get/Save History <br> (sessionId as user_id)" --> J
    F -- "Executes Search" --> F
    G -- "Shapes JSON" --> C
    H -- "Streams Chunks" --> B
    B -- "Streams / JSON" --> A

    style C fill:#D5F5E3
    style G fill:#D5F5E3
    style H fill:#D5F5E3
```

### 3.2. Component Breakdown

#### 3.2.1. API Gateway (Existing)

The gateway's role remains largely the same. It will be configured to route requests for /conversation to the searcher service. It continues to be responsible for cross-cutting concerns like SSL termination, rate limiting, and request logging.

#### 3.2.2. Searcher Service (Modified)

**Conversation Handler (New):**
- The entry point for all `POST /conversation` requests.
- Orchestrates the entire request lifecycle, coordinating calls to auth, history, and the core searcher logic.
- Determines whether to respond with a standard JSON object or initiate a stream based on request headers (Accept: text/event-stream) or query parameters (?stream=true).

**Developer Note:** This handler should be a lightweight orchestrator. The majority of the existing Agentic flow logic resides in `quepasa/searcher/sources/factory.py` and must be reused by this handler to process the conversation request.

**Auth Handler (Adapted):**
- The existing bearer token authentication logic (`/quepasa/auth/bearer.py`) will be adapted to create a new dependency-injected security handler.
- This new handler will enforce the presence and validity of two headers: Authorization: `client-key <key>` and `X-Customer-Id`.
- This ensures the new endpoint uses the required security scheme without affecting the existing /retrieve/* endpoints.

**HistoryManager (Reused):**
- The existing client for the conversation history service will be reused without modification.
- The Conversation Handler will act as an adapter, mapping the sessionId from the incoming request body to the user_id expected by the history service. This fulfills the PRD requirement of reusing the service as-is.

**Searcher Core RAG (Reused):**
- The core business logic for performing retrieval-augmented generation will be reused entirely.
- The Conversation Handler will call this component with the user's message, any retrieved conversation history, and the hardcoded source: agentic parameter.

**Response Shaper (New):**
- **Analysis:** The existing `/retrieve/answer/stream` endpoint in `/quepasa/searcher/generator/answer.py` mixes data transformation with streaming generation, yielding a proprietary JSON structure. This logic is not reusable.
- **Implementation:** A new, dedicated module is required. It will be responsible for transforming the raw output from the Searcher Core RAG into the strict, structured format defined in `conversational_shopping_assistant_v1.yaml`. This includes formatting product variants, actions, and references, and handling the markdown vs. plain_text content setting. This de-coupling of shaping from streaming is a key architectural improvement.

**Developer Note:** It may not be possible to completely separate data transformation from streaming generation, as some shaping logic might need to occur within the async generator itself to properly structure event chunks.

**SSE Handler (Reused Logic):**
- **Analysis:** The existing `/retrieve/answer/stream` endpoint in `/quepasa/searcher/api/v1/search.py` already correctly uses FastAPI's StreamingResponse with media_type='text/event-stream'.
- **Implementation:** We will reuse this fundamental SSE transport mechanism. The new handler will, however, consume data from an async generator that uses the new Response Shaper. It will generate a responseId at the start of the request and ensure it remains constant across all event chunks, setting status: IN_PROGRESS for intermediate chunks and status: COMPLETE for the final one, as required.

### 3.3. Data Flow

#### 3.3.1. Non-Streaming Request Flow

1. A client sends a `POST /conversation` request with a JSON body.
2. The API Gateway routes the request to the searcher service.
3. The Conversation Handler receives the request.
4. The Auth Handler is invoked to validate the X-Customer-Id and client-key.
5. The Conversation Handler calls the HistoryManager, passing the sessionId to retrieve previous messages.
6. The handler invokes the Searcher Core RAG with the user's query and the conversation history.
7. The Searcher Core RAG returns the complete result set.
8. The Response Shaper transforms the result into the final JSON structure.
9. The Conversation Handler saves the new turn (user query + final response) to the history service.
10. A single 200 OK response with the full JSON payload is returned to the client.

#### 3.3.2. Streaming Request Flow

```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Searcher Service
    participant Conversation History
    participant Searcher Core RAG

    Client->>API Gateway: POST /conversation?stream=true
    API Gateway->>Searcher Service: Forward Request
    Searcher Service->>Searcher Service: Validate Auth & Generate responseId
    Searcher Service->>Conversation History: Get History (sessionId)
    Conversation History-->>Searcher Service: Return History
    Searcher Service->>Searcher Core RAG: Execute search (stream mode)

    Note over Searcher Core RAG, Client: RAG streams back partial results
    Searcher Core RAG-->>Searcher Service: Yields chunk 1
    Searcher Service-->>Client: SSE Chunk (status: IN_PROGRESS)
    Searcher Core RAG-->>Searcher Service: Yields chunk 2
    Searcher Service-->>Client: SSE Chunk (status: IN_PROGRESS)
    Searcher Core RAG-->>Searcher Service: Stream End

    Searcher Service-->>Client: SSE Chunk (status: COMPLETE)
    Note over Searcher Service, Conversation History: Asynchronously save the full conversation turn
    Searcher Service->>Conversation History: Save full transcript
```

## Key Decisions & Rationale

**Architecture Pattern:** Adapter/Wrapper pattern.

**Rationale:** We are adapting existing, stable components (Auth, History, Searcher Core) to fit the contract of a new endpoint. This is a low-risk, high-reuse approach ideal for brownfield development, and aligns with the project's tight deadlines by reducing new development effort. It minimizes changes to core logic, reducing the chance of regressions on existing endpoints.

**Authentication:** Dual-header check (client-key + X-Customer-Id).

**Rationale:** This directly implements the requirement from CON-76.md. By creating a new, distinct security dependency, we isolate this logic from the existing Bearer token scheme, ensuring the two can coexist without interference.

**History Integration:** sessionId mapped to user_id.

**Rationale:** This is the most efficient way to meet the PRD's constraint of not modifying the existing history service. The searcher service acts as the integration point, performing the necessary data mapping.

**Decoupled Response Shaping:** A dedicated Response Shaper component.

**Rationale:** The new response format is significantly different from the existing one. Decoupling this transformation logic makes the code cleaner and easier to maintain. It separates the "what" (core search results) from the "how" (the final JSON structure). This is an improvement over the existing streaming implementation which mixes these concerns.

## Risks and Mitigation

**Performance Overhead:** The orchestration in the Conversation Handler adds a small amount of latency.

**Mitigation:** The service is built on FastAPI, which is asynchronous by nature. All I-O-bound calls (to history, auth, etc.) will be non-blocking, minimizing performance impact. Caching strategies for authentication/authorization data can be explored if needed.

**Regression on Existing Endpoints:** Changes to shared components could break `/retrieve/answer`.

**Mitigation:** Our approach explicitly avoids modifying the core RAG logic. Authentication changes are isolated to a new handler. The existing regression test suite for the `/retrieve` endpoints must be run and pass completely before merging, as stipulated in the PRD.