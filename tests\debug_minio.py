import s3fs

fs = s3fs.S3FileSystem(
    key='minioadmin',
    secret='minioadmin',
    endpoint_url='http://localhost:9000',
    client_kwargs={'region_name': 'us-east-1'}
)

print("Listing all bucket contents:")
try:
    files = fs.ls('test-bucket', detail=True)
    for f in files:
        print(f"Name: {f['name']}, Type: {f['type']}")
    
    print("\nListing with find:")
    files = fs.find('test-bucket')
    for f in files:
        print(f"Path: {f}")
        
    print("\nChecking specific test files:")
    test_files = [
        'test-bucket/test/a.txt',
        'test-bucket/test/b.txt',
        'test-bucket/test/sub/c.txt'
    ]
    for file in test_files:
        try:
            exists = fs.exists(file)
            if exists:
                info = fs.info(file)
                print(f"File {file} exists: {info}")
            else:
                print(f"File {file} does not exist")
        except Exception as e:
            print(f"File {file} error: {e}")
            
    print("\nListing test directory:")
    try:
        files = fs.ls('test-bucket/test', detail=True)
        for f in files:
            print(f"Name: {f['name']}, Type: {f['type']}")
    except Exception as e:
        print(f"Error listing test directory: {e}")
except Exception as e:
    print(f"Error: {e}") 