import os
import base64
from typing import Optional, List
from openai import <PERSON><PERSON><PERSON>

from .base import Base<PERSON>mbedding
from .cache import EmbeddingCacheMixin
from .providers import EmbeddingProvider
from src.lib.utils import get_tokenizer
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)

class PrivateEmbedding(BaseEmbedding, EmbeddingCacheMixin):
    """Private GPU-based embedding provider.
    
    This class provides access to a private GPU-based embedding service.
    """    
    def __init__(self):
        """Initialize private embedding provider."""
        super().__init__()

        # Get host and auth token from environment
        self.endpoint = os.getenv('PRIVATE_EMBEDDING_ENDPOINT')
        if not self.endpoint:
            raise ValueError("PRIVATE_EMBEDDING_ENDPOINT environment variable is not set")

        auth_token = os.getenv('PRIVATE_EMBEDDING_AUTH_TOKEN')
        if not auth_token:
            raise ValueError("PRIVATE_EMBEDDING_AUTH_TOKEN environment variable is not set")

        # Create basic auth header
        username, password = auth_token.split(':')
        basic_token = base64.b64encode(f"{username}:{password}".encode()).decode()

        # Initialize OpenAI client with custom base URL and auth
        self.client = OpenAI(
            api_key="ignored",  # API key is not used with basic auth
            base_url=self.endpoint,
            default_headers={"Authorization": f"Basic {basic_token}"}
        )
        self.max_tokens = 8192

    @property
    def provider(self) -> EmbeddingProvider:
        return EmbeddingProvider.PRIVATE

    def _truncate_text(self, text: str) -> str:
        """Truncate text to fit within token limit.
        
        Args:
            text: Text to truncate
            
        Returns:
            Truncated text that fits within the model's token limit
        """
        if len(get_tokenizer().encode(text)) > self.max_tokens:
            new_text = ""
            for line in text.split("\n"):
                line_nl = "\n" + line
                if len(get_tokenizer().encode((new_text + line_nl).strip())) > self.max_tokens:
                    break
                new_text += line_nl
            return new_text.strip()
        return text

    def get_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get embedding from private GPU service.
        
        Args:
            model_version: Model version to use
            text: Text to get embedding for
            
        Returns:
            List of embedding values if successful, None otherwise
        """
        try:
            text = self._truncate_text(text)
            response = self.client.embeddings.create(
                model=model_version,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error getting embedding from private service: {str(e)}")
            return None 