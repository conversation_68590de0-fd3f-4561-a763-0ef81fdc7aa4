import pytest
from configuration.base.crawler import CrawlerConfig

def test_crawler_config_initialization():
    config = CrawlerConfig("test_client")
    assert config.client_code == "test_client"

def test_get_telegram_sources_default():
    config = CrawlerConfig("test_client")
    sources = config.get_telegram_sources()
    assert isinstance(sources, list)
    assert len(sources) == 0  # Default implementation returns empty list

def test_get_pipeline_priority_default():
    config = CrawlerConfig("test_client")
    priority = config.get_pipeline_priority()
    assert isinstance(priority, int)
    assert priority == 1  # Default implementation returns 1 