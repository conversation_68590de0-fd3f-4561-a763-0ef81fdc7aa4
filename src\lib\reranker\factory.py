from __future__ import annotations

from typing import Dict, Type, Union

from .base import BaseReranker
from .together import TogetherReranker
from .providers import RerankerProvider

class RerankerFactory:
    """Factory class for creating reranker providers."""
    
    _providers: Dict[RerankerProvider, Type[BaseReranker]] = {
        RerankerProvider.TOGETHER: TogetherReranker,
    }

    @classmethod
    def get_reranker(cls, provider: Union[str, RerankerProvider]) -> BaseReranker:
        """Get a reranker instance for the specified provider.
        
        Args:
            provider: The reranker provider (string or enum)
            
        Returns:
            An instance of the specified reranker provider
            
        Raises:
            ValueError: If the provider is not supported
        """
        if isinstance(provider, str):
            provider = RerankerProvider.from_str(provider)
            
        if provider not in cls._providers:
            raise ValueError(f"Unknown reranker provider: {provider}. "
                           f"Supported providers: {[p.value for p in cls._providers.keys()]}")
        
        return cls._providers[provider]() 