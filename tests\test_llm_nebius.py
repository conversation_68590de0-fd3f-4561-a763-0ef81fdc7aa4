import os
import pytest
from unittest.mock import patch, MagicMock

from src.lib.llm.nebius import NebiusLLM
from src.lib.llm.providers import LLMProvider

@pytest.fixture
def nebius_llm():
    with patch.dict(os.environ, {'NEBIUS_API_KEY': 'test_key'}):
        return NebiusLLM()

def test_provider_type(nebius_llm):
    """Test that provider type is correct."""
    assert nebius_llm.provider == LLMProvider.NEBIUS

def test_missing_api_key():
    """Test that missing API key raises error."""
    with patch.dict(os.environ, {}, clear=True):
        with pytest.raises(ValueError) as exc_info:
            NebiusLLM()
        assert "NEBIUS_API_KEY environment variable is not set" in str(exc_info.value)

def test_get_answer(nebius_llm):
    """Test getting an answer."""
    mock_completion = MagicMock()
    mock_completion.choices[0].message.content = "Test response"
    nebius_llm.client.chat.completions.create = MagicMock(return_value=mock_completion)
    
    response = nebius_llm.get_answer(
        model_version="meta-llama/Meta-Llama-3.1-70B-Instruct",
        prompt_list=[{"role": "user", "content": "Hello"}],
        answer_prompt_size=100
    )
    
    assert response == "Test response"
    nebius_llm.client.chat.completions.create.assert_called_once()

def test_get_answer_with_json_mode(nebius_llm):
    """Test getting an answer in JSON mode."""
    mock_completion = MagicMock()
    mock_completion.choices[0].message.content = '{"response": "Test"}'
    nebius_llm.client.chat.completions.create = MagicMock(return_value=mock_completion)
    
    response = nebius_llm.get_answer(
        model_version="meta-llama/Meta-Llama-3.1-70B-Instruct",
        prompt_list=[{"role": "user", "content": "Hello"}],
        answer_prompt_size=100,
        json_mode=True
    )
    
    assert response == '{"response": "Test"}'
    nebius_llm.client.chat.completions.create.assert_called_once_with(
        model="meta-llama/Meta-Llama-3.1-70B-Instruct",
        messages=[{"role": "user", "content": "Hello"}],
        temperature=0.0,
        max_tokens=100,
        response_format={"type": "json_object"}
    )

def test_get_streaming_answer(nebius_llm):
    """Test getting a streaming answer."""
    mock_chunk1 = MagicMock()
    mock_chunk1.choices[0].delta.content = "Hello"
    mock_chunk2 = MagicMock()
    mock_chunk2.choices[0].delta.content = " World"
    
    nebius_llm.client.chat.completions.create = MagicMock(return_value=[mock_chunk1, mock_chunk2])
    
    chunks = list(nebius_llm.get_streaming_answer(
        model_version="meta-llama/Meta-Llama-3.1-70B-Instruct",
        prompt_list=[{"role": "user", "content": "Hi"}],
        answer_prompt_size=100
    ))
    
    assert chunks == ["Hello", " World"]
    nebius_llm.client.chat.completions.create.assert_called_once() 