import os
import tik<PERSON><PERSON>
from typing import Optional, List
from openai import OpenAI

from .base import <PERSON>Embedding
from .cache import EmbeddingCacheMixin
from .providers import EmbeddingProvider
from src.lib.utils import get_tokenizer
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)

OPENAI_EMBEDDING_3_SMALL = "text-embedding-3-small"

class OpenAIEmbedding(BaseEmbedding, EmbeddingCacheMixin):
    """OpenAI embedding provider.
    
    This class provides access to OpenAI's embedding models.
    It supports various models like text-embedding-3-small and text-embedding-3-large.
    """    
    def __init__(self):
        """Initialize OpenAI embedding provider."""
        super().__init__()

        # Check for API key
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key or self.api_key == '':
            raise ValueError("OPENAI_API_KEY environment variable is not set")
        self.client = OpenAI(api_key=self.api_key)
        self.max_tokens = 8192

    @property
    def provider(self) -> EmbeddingProvider:
        return EmbeddingProvider.OPENAI

    def _truncate_text(self, text: str) -> str:
        """Truncate text to fit within token limit.
        
        Args:
            text: Text to truncate
            
        Returns:
            Truncated text that fits within the model's token limit
        """
        if len(get_tokenizer().encode(text)) > self.max_tokens:
            new_text = ""
            for line in text.split("\n"):
                line_nl = "\n" + line
                if len(get_tokenizer().encode((new_text + line_nl).strip())) > self.max_tokens:
                    break
                new_text += line_nl
            return new_text.strip()
        return text

    def get_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get embedding from OpenAI API.
        
        Args:
            model_version: The model version to use for embeddings
            text: The text to get embedding for
            
        Returns:
            List of floats representing the embedding, or None if the request fails
        """
        text = self._truncate_text(text)
        try:
            return self.client.embeddings.create(model=model_version, input=text).data[0].embedding
        
        except Exception as e:
            logger.error(f"Failed to get embedding from OpenAI: {str(e)}")
            return None 