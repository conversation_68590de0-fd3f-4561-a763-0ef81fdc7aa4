from __future__ import annotations

import os
import json
from typing import List, Dict, Any, Generator, <PERSON><PERSON>
from openai import OpenAI

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .base import BaseLLM
from .cache import LLMCacheMixin
from .providers import LLMProvider
from src.lib.utils import get_tool_call_dict_list

logger = QuepasaLogger().get_instance(__name__)

class NebiusLLM(BaseLLM, LLMCacheMixin):
    """Nebius LLM provider using OpenAI-compatible API."""
    
    def __init__(self):
        """Initialize Nebius LLM provider."""
        super().__init__()

        # Check for API key
        self.api_key = os.getenv('NEBIUS_API_KEY')
        if not self.api_key:
            raise ValueError("NEBIUS_API_KEY environment variable is not set")
        
        self.client = OpenAI(
            base_url="https://api.studio.nebius.ai/v1/",
            api_key=self.api_key
        )

    @property
    def provider(self) -> LLMProvider:
        return LLMProvider.NEBIUS

    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a completion from Nebius API.
        
        Args:
            model_version: Model version (e.g., 'meta-llama/Meta-Llama-3.1-70B-Instruct')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Returns:
            Generated response text
        """
        try:
            completion = self.client.chat.completions.create(
                model=model_version,
                messages=prompt_list,
                temperature=0.0,  # Use deterministic output
                max_tokens=answer_prompt_size,
                response_format={"type": "json_object"} if json_mode else None
            )
            return completion.choices[0].message.content or ""
            
        except Exception as e:
            logger.error(f"Messages: {prompt_list}")
            logger.error(f"Failed to get answer from Nebius: {str(e)}")
            return ""

    def get_streaming_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> Generator[str, None, None]:
        """Get a streaming completion from Nebius API.
        
        Args:
            model_version: Model version (e.g., 'meta-llama/Meta-Llama-3.1-70B-Instruct')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Yields:
            Generated response chunks
        """
        try:
            completion = self.client.chat.completions.create(
                model=model_version,
                messages=prompt_list,
                temperature=0.0,  # Use deterministic output
                max_tokens=answer_prompt_size,
                response_format={"type": "json_object"} if json_mode else None,
                stream=True
            )
            
            for chunk in completion:
                if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Messages: {prompt_list}")
            logger.error(f"Failed to get streaming answer from Nebius: {str(e)}")
            yield "" 

    def get_tools_answer(self, model_version: str, prompt_list: List[Dict[str, str]], tools: List[Dict[str,Any]], answer_prompt_size: int) -> Tuple[str, list]:
        """Get a completion from the LLM with function calling.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            tools: Tools used for Agentic/Function calling
            answer_prompt_size: Maximum response size
            
        Returns:
            The generated response
        """
        try:
            response = self.client.chat.completions.create(
                model=model_version,
                messages=prompt_list,
                tools=tools,
                tool_choice="auto",
                max_tokens=answer_prompt_size,
                temperature=0.0
            )

            message = response.choices[0].message
            content = None
            if message.content and message.content.strip():
                content = message.content.strip()
            return content, get_tool_call_dict_list(message.tool_calls)
            
        except Exception as e:
            logger.error(f"Messages: {prompt_list}")
            logger.error(f"Failed to get answer from Nebius: {str(e)}")
            raise

    def _fix_tool_calls(self, tool_calls: list) -> list:
        """Fix tool calls if they are malformed."""
        fixed_tool_calls = []
        for tool_call in tool_calls:
            if (
                'function' in tool_call
                and 'arguments' in tool_call['function']
                and tool_call['function']['arguments']
            ):
                args = tool_call['function']['arguments']
                try:    
                    json.loads(args)

                except json.JSONDecodeError:
                    if args and not args.endswith('}'):
                        # Try to close the JSON if it's incomplete
                        try:
                            json.loads(args + '}') # Try to fix the JSON
                            tool_call['function']['arguments'] = args + '}'

                        except json.JSONDecodeError:
                            pass

                fixed_tool_calls.append(tool_call)

        return fixed_tool_calls

    def get_streaming_tools_answer(self, model_version: str, prompt_list: List[Dict[str, str]], tools: List[Dict[str,Any]], answer_prompt_size: int) -> Generator[Tuple[str, list], None, None]:
        """Get a streaming completion from the LLM with function calling.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            tools: Tools used for Agentic/Function calling
            answer_prompt_size: Maximum response size
            
        Yields:
            Generated response chunks with tool calls
        """
        try:
            stream = self.client.chat.completions.create(
                model=model_version,
                messages=prompt_list,
                tools=tools,
                tool_choice="auto",
                max_tokens=answer_prompt_size,
                temperature=0.0,
                stream=True
            )
            
            content = ""
            tool_calls = []
            
            for chunk in stream:
                if chunk.choices[0].delta.content:
                    content += chunk.choices[0].delta.content
                    yield content, tool_calls
                
                if chunk.choices[0].delta.tool_calls:
                    # logger.info(f"Tool calls: {chunk.choices[0].delta.tool_calls}")
                    for tool_call in chunk.choices[0].delta.tool_calls:
                        # Find existing tool call by index or create new one
                        existing_tool_call = None
                        tool_call_index = getattr(tool_call, 'index', 0)
                        
                        for existing in tool_calls:
                            if existing.get('index') == tool_call_index:
                                existing_tool_call = existing
                                break
                        
                        if existing_tool_call is None:
                            # Create new tool call as dictionary
                            new_tool_call = {
                                'id': tool_call.id,
                                'type': tool_call.type,
                                'index': tool_call_index,
                                'function': {
                                    'name': tool_call.function.name if tool_call.function else None,
                                    'arguments': tool_call.function.arguments if tool_call.function else ""
                                }
                            }
                            tool_calls.append(new_tool_call)
                            
                        else:
                            # Update existing tool call
                            if tool_call.id:
                                existing_tool_call['id'] = tool_call.id

                            if tool_call.type:
                                existing_tool_call['type'] = tool_call.type

                            if tool_call.function:
                                if tool_call.function.name:
                                    existing_tool_call['function']['name'] = tool_call.function.name

                                if tool_call.function.arguments:
                                    # Ensure arguments is a string before concatenating
                                    if existing_tool_call['function']['arguments'] is None:
                                        existing_tool_call['function']['arguments'] = ""

                                    # Concatenate the arguments string and ensure it's valid JSON
                                    existing_tool_call['function']['arguments'] += tool_call.function.arguments

                    yield content, tool_calls

            yield content, self._fix_tool_calls(tool_calls)
                    
        except Exception as e:
            logger.error(f"Messages: {prompt_list}")
            logger.error(f"Failed to get streaming tools answer from Nebius: {str(e)}")
            raise
