import hashlib
from typing import Dict, Any, List

from src.lib.utils import get_filename_id, get_basepath
from src.lib.batch_utils import (
    BatchAction,
    DATA_PROCESSOR_STORAGE
)
from src.lib.files import QuepasaFiles
from quepasa.indexer.tasks import process_batch_documents
from celery.result import AsyncResult

# Test configuration
qp_files = QuepasaFiles(
    bucket_name='quepasa-files',
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin',
    debug_flag=False
)

# Client ID for testing
TEST_CLIENT_ID = 'test_data_processor_batch_integration_sandbox'
TEST_DOMAIN = 'test'

DOMAIN_STR = hashlib.md5(str(TEST_DOMAIN).encode('utf-8')).hexdigest()

doc_id = "test-doc-001"
file_id = get_basepath(get_filename_id(doc_id))
doc_file_path = f"{DATA_PROCESSOR_STORAGE}/{TEST_CLIENT_ID}/{DOMAIN_STR}/{file_id}.zlib.json"
qp_files.set_json_zlib(doc_file_path, {
    "id": doc_id,
        "client_id": TEST_CLIENT_ID,
        "domain": TEST_DOMAIN,
        "url": "https://test.com/doc1",
        "title": "Test Document",
        "languages": ["en"],
        "created_at": "2023-12-30T12:00:00Z",
        "updated_at": "2023-12-30T12:00:00Z",
        "chunks": [
            {
                "text": "This is a test document for indexing. It contains important information about testing.",
                "language": "en",
                "keywords": "test, document, indexing"
            },
            {
                "text": "This is the second page of the test document.",
                "language": "en",
                "keywords": "test, document, page2"
            }
        ]
})

# Create test batch
test_batch = {
    "client_id": TEST_CLIENT_ID,
    "domain": TEST_DOMAIN,
    'action': BatchAction.UPSERT,
    "processed_ids": [doc_id],
    "changes": {
        "upsert": [doc_id],
        "delete": []
    }
}

def test_batch_processing():
    """Test batch document processing"""
    print("Test batch configuration:")
    print(f"Client ID: {test_batch['client_id']}")
    print(f"Domain: {test_batch['domain']}")
    print(f"Action: {test_batch['action']}")
    print("\nStarting batch processing...")
    
    # Process the batch
    task_result = process_batch_documents.apply(args=[test_batch]).get()
    print("RESULT", task_result)

    task_id = task_result['task_id']
    print("TASK ID", task_id)

    async_task_result = AsyncResult(task_id)
    print("ASYNC TASK RESULT", async_task_result)

    # Get the result (this will block until the task completes)
    result = async_task_result.get()  # Only call this if status is 'SUCCESS'
    print("RESULT", result)

    # Print results
    print("\nBatch processing results:")
    print("-" * 50)
    print(f"Client ID: {result['client_id']}")
    print(f"Status: {result['status']}")
    print(f"Completed at: {result['completed_at']}")
    print(f"Total tasks: {result['stats']['total_tasks']}")
    print(f"Processed tasks: {result['stats']['processed_tasks']}")
    print()

if __name__ == "__main__":
    print("=== Testing Batch Processing ===")
    test_batch_processing()