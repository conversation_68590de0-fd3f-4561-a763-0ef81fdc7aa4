import pytest
from configuration.base.user_history import UserHistoryConfig, HistoryConfig

def test_history_config_dataclass():
    config = HistoryConfig(
        forget_after_seconds=3600,
        max_last_messages=10,
        use_roles=["user", "assistant"]
    )
    assert config.forget_after_seconds == 3600
    assert config.max_last_messages == 10
    assert config.use_roles == ["user", "assistant"]

def test_get_history_config_default():
    config = UserHistoryConfig("test_client")
    history_config = config.get_history_config()
    
    assert isinstance(history_config, HistoryConfig)
    assert history_config.forget_after_seconds == 4 * 3600  # 4 hours
    assert history_config.max_last_messages == 5
    assert history_config.use_roles == ["user", "assistant"]

def test_get_history_config_with_data():
    config = UserHistoryConfig("test_client")
    history_config = config.get_history_config()
    
    assert isinstance(history_config, HistoryConfig)
    # Test that data parameter doesn't affect default behavior
    assert history_config.forget_after_seconds == 4 * 3600
    assert history_config.max_last_messages == 5
    assert history_config.use_roles == ["user", "assistant"]

@pytest.mark.parametrize("seconds,messages,roles", [
    (0, 0, []),  # Edge case: zero values
    (-1, -1, []),  # Edge case: negative values
    (999999, 100, ["user", "assistant", "system"]),  # Large values
    (1, 1, ["custom_role"]),  # Minimum values with custom role
])
def test_history_config_edge_cases(seconds, messages, roles):
    config = HistoryConfig(
        forget_after_seconds=seconds,
        max_last_messages=messages,
        use_roles=roles
    )
    assert config.forget_after_seconds == seconds
    assert config.max_last_messages == messages
    assert config.use_roles == roles

@pytest.mark.parametrize("data,expected_roles", [
    ({"chat_type": "private"}, ["user", "assistant"]),
    ({"chat_type": "group"}, ["user", "assistant"]),
    ({"chat_type": None}, ["user", "assistant"]),
    ({}, ["user", "assistant"]),
])
def test_get_history_config_chat_types(data, expected_roles):
    config = UserHistoryConfig("test_client")
    history_config = config.get_history_config()
    assert history_config.use_roles == expected_roles

@pytest.mark.parametrize("data", [
    {"user_id": 123},
    {"user_id": "string_id"},
    {"user_id": None},
    {"user_id": ""},
])
def test_get_history_config_user_contexts(data):
    config = UserHistoryConfig("test_client")
    history_config = config.get_history_config()
    assert isinstance(history_config, HistoryConfig)
    # Verify default values are maintained regardless of user context
    assert history_config.forget_after_seconds == 4 * 3600
    assert history_config.max_last_messages == 5
    assert history_config.use_roles == ["user", "assistant"] 