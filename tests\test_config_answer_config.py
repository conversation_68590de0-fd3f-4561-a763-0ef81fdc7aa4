import pytest
from datetime import datetime
from unittest.mock import Mock, patch
from configuration.base.answer import AnswerConfig
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.web import WebSearchResult
from quepasa.indexer.tasks import DOCUMENT_TYPE_DOCUMENT

@pytest.fixture
def answer_config():
    with patch('src.lib.files.QuepasaFiles') as mock_files:
        config = AnswerConfig("test_client")
        return config

class TestAnswerConfig:
    def setup_method(self):
        self.mock_files = patch('src.lib.files.QuepasaFiles').start()
        self.answer_config = AnswerConfig("test_client")
        
    def teardown_method(self):
        patch.stopall()
        
    def test_is_no_answer_response_default(self):
        """Test default no-answer response behavior"""
        request = QuepasaRequest(
            client="test_client", 
            question="test question",
            source="telegram"
        )
        self.answer_config.set_request(request)
        
        result = self.answer_config.is_no_answer_response(
            source=request.source,
            answer="I don't know",
            references=[]
        )
        assert result == False
        
    def test_format_context_chunk(self):
        """Test context chunk formatting"""
        request = QuepasaRequest(
            client="test_client", 
            question="test question",
            source="telegram"
        )
        self.answer_config.set_request(request)
        
        document = QuepasaDocument(
            root_id="1",
            id="1",
            chunk_index=0,
            client="test_client",
            domain="example.com",
            provider="test_provider",
            type=DOCUMENT_TYPE_DOCUMENT,
            kind="document",
            level="info",
            url="https://example.com",
            language="en",
            title="Example",
            keywords=["example", "test"],
            text="Example text",
            tokens=0,
            chunks=1,
            start_position=0,
            end_position=0,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            embeddings={"embedding": [0.1, 0.2, 0.3]},
            score=0.8
        )
        result = self.answer_config.format_context_chunk(
            source=request.source,
            source_index=1,
            document=document
        )
        
        assert "[Start of source #1]" in result
        assert "Example text" in result
        assert "[End of source #1]" in result
        
    @patch('src.lib.llm.factory.LLMFactory.get_llm', autouse=False)
    def test_get_llm_parameters(self, mock_llm):
        """Test LLM parameter retrieval"""
        request = QuepasaRequest(
            client="test_client", 
            question="test question",
            source="telegram"
        )
        self.answer_config.set_request(request)
        
        result = self.answer_config.get_llm_parameters(source=request.source)
        
        assert isinstance(result, dict)
        assert "temperature" in result
        assert "top_p" in result
        assert "presence_penalty" in result
        assert "frequency_penalty" in result
        
    @patch('src.lib.llm.factory.LLMFactory.get_llm', autouse=False)
    @pytest.mark.slow
    def test_get_llm_prompt_template_general(self, mock_llm):
        """Test general prompt template retrieval"""
        request = QuepasaRequest(
            client="test_client",
            question="test question",
            source="telegram",
            classification={"class": "general"}
        )
        self.answer_config.set_request(request)
        
        result = self.answer_config.get_llm_prompt_template(source=request.source)
        
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]["role"] == "system"
        assert "bot-assistant" in result[0]["content"]
        
    @patch('src.lib.llm.factory.LLMFactory.get_llm', autouse=False)
    @pytest.mark.slow
    def test_get_llm_prompt_template_wiki(self, mock_llm):
        """Test wiki prompt template retrieval"""
        request = QuepasaRequest(
            client="test_client", 
            question="test question",
            source="telegram"
        )
        self.answer_config.set_request(request)
        
        result = self.answer_config.get_llm_prompt_template(source=request.source)
        
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]["role"] == "system"
        assert "bot-assistant" in result[0]["content"]
        
    @patch('src.lib.llm.factory.LLMFactory.get_llm', autouse=False)
    def test_get_llm_model_name(self, mock_llm):
        """Test LLM model name retrieval"""
        request = QuepasaRequest(
            client="test_client", 
            question="test question",
            source="telegram"
        )
        self.answer_config.set_request(request)
        
        provider, model_version = self.answer_config.get_llm_model_name(source=request.source)
        
        assert provider.value == "nebius"
        assert "qwen" in model_version.lower()
        
    @patch('src.lib.llm.factory.LLMFactory.get_llm', autouse=False)
    def test_get_max_prompt_tokens(self, mock_llm):
        """Test max prompt tokens retrieval"""
        request = QuepasaRequest(
            client="test_client", 
            question="test question",
            source="telegram"
        )
        self.answer_config.set_request(request)
        
        result = self.answer_config.get_max_prompt_tokens(source=request.source)
        assert result == 8110
        
    @patch('src.lib.llm.factory.LLMFactory.get_llm', autouse=False)
    def test_get_max_response_tokens(self, mock_llm):
        """Test max response tokens retrieval"""
        request = QuepasaRequest(
            client="test_client", 
            question="test question",
            source="telegram"
        )
        self.answer_config.set_request(request)
        
        result = self.answer_config.get_max_response_tokens(source=request.source, model="test-model")
        assert result == 900 