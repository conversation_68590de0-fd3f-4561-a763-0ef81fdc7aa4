---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: quepasa
  name: job-creator
rules:
- apiGroups: ["batch"]
  resources: ["jobs", "jobs/status"]
  verbs: ["create", "get", "list", "watch", "delete"]
- apiGroups: [""]
  resources: ["pods", "pods/log"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: job-creator-binding
  namespace: quepasa
subjects:
- kind: ServiceAccount
  name: default  # The service account used by your pods
  namespace: quepasa
roleRef:
  kind: Role
  name: job-creator
  apiGroup: rbac.authorization.k8s.io 