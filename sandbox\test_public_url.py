from src.lib.files import QuepasaFiles
import os

def test_public_url():
    # Initialize QuepasaFiles
    qp_files = QuepasaFiles(
        bucket_name='quepasa-files',
        endpoint_url='http://localhost:9000',
        aws_access_key_id='minioadmin',
        aws_secret_access_key='minioadmin',
        debug_flag=True
    )
    
    # Test file path
    test_file = "test.txt"
    remote_path = "test/test.txt"
    
    try:
        # Upload the test file
        print(f"\nUploading test file {test_file} to {remote_path}")
        qp_files.upload_file(test_file, remote_path)
        
        # Generate public URL
        print("\nGenerating public URL...")
        url = qp_files.get_public_url(remote_path, hours=1)
        print(f"\nGenerated URL (expires in 1 hour):\n{url}")
        
        # Verify the file exists
        print("\nVerifying file exists...")
        if qp_files.exists(remote_path):
            print("File exists in storage")
        else:
            print("File not found in storage")
            
    except Exception as e:
        print(f"Error during test: {str(e)}")
    finally:
        # Clean up
        print("\nCleaning up...")
        try:
            # qp_files.delete_file(remote_path)
            print("Test file deleted from storage")
        except:
            pass

if __name__ == "__main__":
    test_public_url() 