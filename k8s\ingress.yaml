apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: quepasa-ingress
  namespace: quepasa
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      rewrite ^/$ / break;
      rewrite ^/api(/|$)(.*) /$2 break;
      rewrite ^/search(/|$)(.*) /$2 break;
spec:
  ingressClassName: nginx
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api
            port:
              number: 8080
      - path: /api(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: api
            port:
              number: 8080
      - path: /search(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: searcher
            port:
              number: 8080 