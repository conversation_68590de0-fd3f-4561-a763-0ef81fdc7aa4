from typing import Dict, Any, List, Optional, Union, Generator
import time

from .base import BaseSource
from .mixins import AnswerRetrieverMixin
from ..core.rag_search import Rag<PERSON>earchManager
from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse, QuepasaStreamAnswer
from ..sources.base import STATUS_SUCCESS

from src.lib.logger import QuepasaLogger

logger = QuepasaLogger().get_instance(__name__)

class RAGSource(BaseSource, AnswerRetrieverMixin):
    """RAG source for retrieval augmented generation"""
    
    def __init__(self):
        """Initialize RAG source"""
        super().__init__()
        self.search_engine = RagSearchManager()
                
    def search(self, request: QuepasaRequest) -> QuepasaResponse:
        """Execute search request
        
        Args:
            request: Search request
            
        Returns:
            Search response
        """
        try:
            # Get search results
            documents = self.search_engine.search(request)
            
            # Process search results using mixin
            return QuepasaResponse(
                status=STATUS_SUCCESS,
                data=documents
            )
            
        except Exception as e:
            return self._handle_error(request, f"Error searching: {str(e)}")
        
    def get_answer(self, request: QuepasaRequest, stream: bool = False) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """Get answer for request
        
        Args:
            request: Search request
            stream: Whether to stream the response
            
        Returns:
            Search response or generator of stream responses
        """
        try:
            # Get search results
            documents = self.search_engine.search(request)
            
            # Process search results using mixin
            return self.retrieve_answer(request, request.source, documents, stream)
            
        except Exception as e:
            return self._handle_error(request, f"Error getting answer: {str(e)}", stream)
