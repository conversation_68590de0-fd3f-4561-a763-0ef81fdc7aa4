from typing import List, Dict, Any, Optional
from src.lib.reranker.factory import RerankerFactory

def get_reranker(provider: str, model_version: str, query: str, documents: List[Dict[str, Any]], top_n: Optional[int] = None) -> List[Dict[str, Any]]:
    """Get reranked results from the specified provider.
    
    Args:
        provider: The reranker provider to use (e.g., 'together')
        model_version: Model version to use (e.g., 'Salesforce/Llama-Rank-v1')
        query: Search query
        documents: List of documents to rerank
        top_n: Number of top documents to return. If None, returns all documents
        
    Returns:
        List of reranked documents
    """
    reranker = RerankerFactory.get_reranker(provider)
    return reranker.get_results(model_version, query, documents, top_n)

def get_cached_reranker(provider: str, model_version: str, query: str, documents: List[Dict[str, Any]], top_n: Optional[int] = None) -> List[Dict[str, Any]]:
    """Get cached reranked results from the specified provider.
    
    Args:
        provider: The reranker provider to use (e.g., 'together')
        model_version: Model version to use (e.g., 'Salesforce/Llama-Rank-v1')
        query: Search query
        documents: List of documents to rerank
        top_n: Number of top documents to return. If None, returns all documents
        
    Returns:
        List of reranked documents from cache or computed fresh
    """
    reranker = RerankerFactory.get_reranker(provider)
    return reranker.get_cached_results(model_version, query, documents, top_n) 