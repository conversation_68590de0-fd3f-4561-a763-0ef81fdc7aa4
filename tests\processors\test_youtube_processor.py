import pytest
from unittest.mock import Mock, patch, MagicMock
from quepasa.crawler.processors.youtube_processor import YouTubeProcessor
from quepasa.crawler.exceptions import ProcessorError
from botocore.exceptions import ClientError

@pytest.fixture
def sample_metadata():
    return {
        'url': 'https://www.youtube.com/watch?v=test_video_id',
        'filename': 'test_video.txt'
    }

@pytest.fixture
def mock_youtube_captions():
    caption = MagicMock()
    caption.code = "en.en-US"
    return [caption]

@pytest.fixture
def mock_s3_client():
    mock_client = MagicMock()
    
    # Configure head_object to simulate file not found
    def head_object(*args, **kwargs):
        raise ClientError(
            error_response={'Error': {'Code': '404', 'Message': 'Not Found'}},
            operation_name='head_object'
        )
    
    mock_client.head_object = MagicMock(side_effect=head_object)
    return mock_client

@pytest.fixture
def mock_qp_files(mock_s3_client):
    mock_qp = MagicMock()
    mock_qp.exists.return_value = False
    mock_qp.s3_client = mock_s3_client
    return mock_qp

class TestYouTubeProcessor:
    @patch('quepasa.crawler.processors.youtube_processor.YouTube')
    @patch('quepasa.crawler.processors.youtube_processor.YouTubeTranscriptApi')
    @patch('quepasa.crawler.processors.youtube_processor.QuepasaFiles')
    @patch('quepasa.crawler.processors.youtube_processor.qp_files')
    def test_process_youtube_video_success(self, mock_module_qp, mock_qf_class, mock_transcript_api, mock_youtube, sample_metadata, mock_youtube_captions, mock_qp_files):
        # Configure mocks
        mock_yt = MagicMock()
        mock_yt.author = 'Test Author'
        mock_yt.title = 'Test Video'
        mock_yt.captions.keys.return_value = mock_youtube_captions
        mock_youtube.return_value = mock_yt

        # Mock transcript API
        mock_transcript = [
            {'text': 'First caption', 'start': 0.0, 'duration': 2.0},
            {'text': 'Second caption', 'start': 2.0, 'duration': 2.0}
        ]
        mock_transcript_api.list_transcripts.return_value.find_transcript.return_value.fetch.return_value = mock_transcript

        # Mock module-level QuepasaFiles instance
        mock_module_qp.exists.return_value = False
        mock_module_qp.get_json.return_value = mock_transcript

        processor = YouTubeProcessor()
        result = processor.process('https://www.youtube.com/watch?v=test_video_id', sample_metadata)
        
        assert result['status'] == 'success'
        result = result['result']
        assert result['title'] == 'Test Author — Test Video'
        assert isinstance(result['chunks'], list)
        assert len(result['chunks']) > 0
        
        # Verify chunk structure
        for chunk in result['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('timestamp ')
            assert ' - ' in chunk['position']  # Check for timestamp range format
        
        assert result['language'] == 'en'

    @patch('quepasa.crawler.processors.youtube_processor.YouTube')
    @patch('quepasa.crawler.processors.youtube_processor.YouTubeTranscriptApi')
    @patch('quepasa.crawler.processors.youtube_processor.QuepasaFiles')
    @patch('quepasa.crawler.processors.youtube_processor.qp_files')
    def test_process_youtube_video_with_cache(self, mock_module_qp, mock_qf_class, mock_transcript_api, mock_youtube, sample_metadata, mock_youtube_captions, mock_qp_files):
        # Configure mocks
        mock_yt = MagicMock()
        mock_yt.author = 'Test Author'
        mock_yt.title = 'Test Video'
        mock_yt.captions.keys.return_value = mock_youtube_captions
        mock_youtube.return_value = mock_yt

        # Mock module-level QuepasaFiles instance
        mock_module_qp.exists.return_value = True
        mock_module_qp.get_json.return_value = [
            {'text': 'Cached caption', 'start': 0.0, 'duration': 2.0}
        ]

        processor = YouTubeProcessor()
        result = processor.process('https://www.youtube.com/watch?v=test_video_id', sample_metadata)
        
        assert result['status'] == 'success'
        result = result['result']
        assert result['title'] == 'Test Author — Test Video'
        assert not mock_transcript_api.list_transcripts.called  # Should not call API when cache exists
        assert isinstance(result['chunks'], list)
        assert len(result['chunks']) > 0
        
        # Verify chunk structure
        for chunk in result['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('timestamp ')
            assert ' - ' in chunk['position']  # Check for timestamp range format
        
        assert result['language'] == 'en'

    @patch('quepasa.crawler.processors.youtube_processor.YouTube')
    def test_process_youtube_video_no_captions(self, mock_youtube, sample_metadata):
        # Configure mock with no captions
        mock_yt = MagicMock()
        mock_yt.author = 'Test Author'
        mock_yt.title = 'Test Video'
        mock_yt.captions.keys.return_value = []
        mock_youtube.return_value = mock_yt

        processor = YouTubeProcessor()
        result = processor.process('https://www.youtube.com/watch?v=test_video_id', sample_metadata)
        assert result['status'] == 'error'
        assert 'No captions available for this video' in result['error']

    def test_extract_video_id(self):
        processor = YouTubeProcessor()
        
        # Test standard YouTube URL
        url1 = 'https://www.youtube.com/watch?v=test_video_id'
        assert processor._extract_video_id(url1) == 'test_video_id'
        
        # Test shortened URL
        url2 = 'https://youtu.be/test_video_id'
        assert processor._extract_video_id(url2) == 'test_video_id'
        
        # Test invalid URL
        with pytest.raises(ProcessorError):
            processor._extract_video_id('https://invalid-url.com')

    def test_get_webvtt(self):
        processor = YouTubeProcessor()
        transcript = [
            {'text': 'First caption', 'start': 0.0, 'duration': 2.0},
            {'text': 'Second caption', 'start': 2.0, 'duration': 2.0}
        ]
        
        chunks = processor._get_webvtt(transcript)
        assert isinstance(chunks, list)
        assert len(chunks) > 0
        
        # Verify chunk structure
        for chunk in chunks:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('timestamp ')
            assert ' - ' in chunk['position']  # Check for timestamp range format
            assert any(caption['text'] in chunk['text'] for caption in transcript) 