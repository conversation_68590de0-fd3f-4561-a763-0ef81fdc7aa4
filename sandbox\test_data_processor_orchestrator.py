import os
import time
import pytest
import hashlib
from src.lib.files import QuepasaFiles
from quepasa.data_processor.orchestrator import DataProcessorOrchestrator
from src.lib.batch_utils import ENV, BatchUtils, BatchState, CRAWLER_STORAGE, DATA_PROCESSOR_STORAGE, BatchAction
from src.lib.utils import get_filename_id, get_basepath

# Test configuration
TEST_CONTAINER_ID = 'test_container'
TEST_CLIENT_ID = 'test_client_data_processor'
TEST_DOMAIN = 'test_domain'

qp_files = QuepasaFiles(
    bucket_name='quepasa-files',
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin',
    debug_flag=False
)

DOMAIN_STR = hashlib.md5(str(TEST_DOMAIN).encode('utf-8')).hexdigest()
DATA_OUT_DIR = f"{DATA_PROCESSOR_STORAGE}/{TEST_CLIENT_ID}/{DOMAIN_STR}"

doc2_id = 'doc2'
file2_id = get_basepath(get_filename_id(doc2_id))
doc2_file_path = f"{CRAWLER_STORAGE}/{TEST_CLIENT_ID}/{DOMAIN_STR}/{file2_id}.zlib.json"
qp_files.set_json_zlib(doc2_file_path, {
    'id': doc2_id,
    'url': 'https://example.com/doc2',
    'title': 'Sample Document 2',
    'keywords': 'test, another, document',
    'chunks': [
        {
            'language': 'en',
            'keywords': 'introduction',
            'position': "line 1",
            'text': 'This is the first chunk of document 2. Testing different content.'
        },
        {
            'language': 'es',
            'keywords': 'contenido',
            'position': "line 2",
            'text': 'Este es el segundo fragmento del documento 2. Probando contenido en español.'
        }
    ]
})

def create_test_batch():
    """Create a test batch file in the backlog"""
    test_batch = {
        'client_id': TEST_CLIENT_ID,
        'domain': TEST_DOMAIN,
        'action': BatchAction.UPSERT,
        'documents': [
            {
                'id': 'doc1',
                'url': 'https://example.com/doc1',
                'title': 'Sample Document 1',
                'keywords': 'test, sample, document',
                'chunks': [
                    {
                        'language': 'en',
                        'keywords': 'introduction, overview',
                        'position': "line 1",
                        'text': 'This is the first chunk of document 1. It contains sample text for testing.'
                    },
                    {
                        'language': 'en',
                        'keywords': 'details, content',
                        'position': "line 2",
                        'text': 'This is the second chunk of document 1. It contains more sample text for testing.'
                    }
                ]
            },
            doc2_file_path
        ]
    }

    # Create batch in UPLOADED state
    batch_id = BatchUtils.create_batch(TEST_CLIENT_ID, BatchState.BACKLOG, test_batch)
    
    print("Created test batch with ID:", batch_id)
    return batch_id

def test_data_processor_orchestrator():
    """Test the DataProcessorOrchestrator functionality"""
    
    # Create test batch
    batch_id = create_test_batch()

    # Initialize orchestrator
    orchestrator = DataProcessorOrchestrator()
    
    # Run the orchestrator
    orchestrator.run(TEST_CLIENT_ID)

    # Verify batch state transition
    state, data = BatchUtils.get_batch_status(TEST_CLIENT_ID, batch_id)
    print("Final batch state:", state)
    print("Final batch data:", data)
    
    # The batch should move to either IN_PROGRESS or DONE state
    assert state in [BatchState.IN_PROGRESS, BatchState.DONE], f"Unexpected batch state: {state}"

if __name__ == '__main__':
    pytest.main([__file__]) 