import re
import copy
import json
import hashlib
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from elasticsearch import Elasticsearch

from src.lib.utils import INDEX_NAME_PREFIX, get_elasticsearch_config
from src.lib.embedding.providers import EmbeddingProvider
from src.lib.embedding_service import get_embedding_from_service
from src.lib.gateway import get_embedding_from_gateway
from src.lib.embedding_utils import get_cached_embedding
from src.lib.reranker_utils import get_cached_reranker
from src.lib.reranker.providers import RerankerProvider
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>ogger
from configuration.main.default import QuepasaConfigurationHub
from ..utils import get_field_values, remove_emoji, QUESTION_PLACEHOLDER
from ..models.request import QuepasaRequest
from ..models.document import QuepasaDocument
from src.lib.constants import (
    DOCUMENT_TYPE_DOCUMENT,
    DOCUMENT_TYPE_PRODUCT,
    DOCUMENT_TYPE_DIALOG,
    SOURCE_DIALOGS,
    SOURCE_DOCUMENTS,
    SOURCE_META_SEARCH,
    SOURCE_PRODUCTS,
    FACTOID_LEVEL_DOCUMENT,
    FACTOID_LEVEL_CHUNK,
    FACTOID_KIND_TEXT,
    FACTOID_KIND_FACTOID,
    KIND_ALL,
    KIND_SUMMARY
)
from configuration.base.searcher import KindWeights, SearchWeights
from src.lib.files import QuepasaFiles
QUERY_VECTOR_KEY = "query_vector"
QUERY_VECTOR_PREFIX = f"{QUERY_VECTOR_KEY}__"

RERANKER_PROVIDER = RerankerProvider.TOGETHER
RERANKER_MODEL_VERSION = "Salesforce/Llama-Rank-v1"

# Initialize logger
logger = QuepasaLogger().get_instance(__name__)

# Initialize Elasticsearch
es = Elasticsearch(**get_elasticsearch_config())

# Initialize files
qp_files = QuepasaFiles()

# Create retryable versions of our external API calls
QUEPASA_LLM_GATEWAY_API_KEY = os.environ.get("QUEPASA_LLM_GATEWAY_API_KEY")

# Initialize debug mode
DEBUG_MODE = False # os.environ.get('DEBUG', 'false').lower() == 'true'

class RagSearchManager:
    """Core search engine functionality"""
    
    def __init__(self):
        """Initialize search engine with Elasticsearch connection"""
        pass
        
    def search(self, request: QuepasaRequest) -> List[QuepasaDocument]:
        """Execute search request
        
        Args:
            request: Search request
            
        Returns:
            Search response
            
        Raises:
            ValueError: If required configuration is missing
            RuntimeError: If search operation fails
        """
        try:
            logger.info(f"Starting search with request: {request}")
            filters = []

            config = QuepasaConfigurationHub.from_request(request)

            # Get search parameters
            client_code = config.get_client_code()
            if not client_code:
                raise ValueError("Client code is not configured")
                
            index_name = config.get_index_name()
            if not index_name:
                raise ValueError("Index name is not configured")

            logger.info(f"Using index: {index_name}, client: {client_code}")

            question = request.question

            # Update question
            if (
                request.classification
                and hasattr(request.classification, 'query')
                and request.classification.query
                and request.classification.query.strip()
            ):
                question = request.classification.query.strip()

            elif request.history:
                previous_questions = []
                for history_item in request.history:
                    if history_item.role == "user":
                        previous_questions.append(history_item.content)

                if len(previous_questions) > 0:
                    previous_questions.reverse()
                    previous_questions.append(question)
                    question = " ".join(previous_questions)

            source = request.source
            kind = request.kind
            language_code = config.get_language_code()
            max_results = config.get_max_results_limit(source)

            logger.info(f"Search parameters - question: {question}, source: {source}, kind: {kind}, language: {language_code}, max_results: {max_results}")
            filters.append({"term": {"client": client_code}})

            # Types
            types = []
            document_language_config = config.get_indexed_languages()
            if source == SOURCE_DIALOGS:
                types = [DOCUMENT_TYPE_DIALOG]
            elif source == SOURCE_DOCUMENTS:
                types = [DOCUMENT_TYPE_DOCUMENT]
            elif source in [SOURCE_PRODUCTS, SOURCE_META_SEARCH]:
                types = [DOCUMENT_TYPE_PRODUCT]
            else:
                types = list(document_language_config.keys())

            logger.info(f"Document types to search: {types}")
            filters.append({"terms": {"type": types}})

            # Kind
            kind_object = {"term": {"kind": FACTOID_KIND_TEXT}}
            if kind:
                if kind == KIND_ALL:
                    kind_object = {"terms": {"kind": [FACTOID_KIND_TEXT, FACTOID_KIND_FACTOID]}}
                elif kind == KIND_SUMMARY:
                    kind_object = {"term": {"kind": FACTOID_KIND_FACTOID}}

            filters.append(kind_object)
            logger.info(f"Kind filter: {kind_object}")

            # Add field filters
            for field in ['id', 'url', 'country', 'provider', 'domain']:
                values = get_field_values(request, field)
                if len(values) > 0:
                    field_key = field
                    if field in ['url']:
                        field_key = f"{field}.keyword"

                    filters.append({
                        "terms": { field_key: values }
                    })
                    logger.info(f"Added filter for {field}: {values}")

            # Add date filter
            if (
                config.should_filter_by_date(source)
                and request.classification != None
                and 'date' in request.classification
                and request.classification.date is not None
                and request.classification.date.strip() != ""
            ):
                try:
                    dates = [dt.strip() for dt in request.classification.date.strip('/ ').split('/')]
                    from_date = dates[0]
                    if (
                        len(from_date) == 10
                        and re.match(r'\d{4}\-\d{2}\-\d{2}', from_date)
                        and (datetime.now() - datetime.strptime(from_date, "%Y-%m-%d")).seconds <= 4 * 3600
                    ):
                        from_date = "now-1d/d"

                    if from_date != "":
                        till_date = "now"
                        if (
                            len(from_date) == 7
                            and re.match(r'\d{4}\-\d{2}', from_date)
                        ):
                            from_date += "-01"
                            till_date = (datetime.strptime(from_date, "%Y-%m-%d") + relativedelta(months=1)).strftime('%Y-%m-%d')

                        elif (
                            len(dates) > 1
                            and len(dates[1]) == 10
                            and re.match(r'\d{4}\-\d{2}\-\d{2}', dates[1])
                        ):
                            till_date = (datetime.strptime(dates[1], "%Y-%m-%d") + timedelta(days=1)).strftime('%Y-%m-%d')

                        filters.append({
                            "range": { "created_at": {
                                "gte": from_date,
                                "lt": till_date,
                            }}
                        })

                except Exception as e:
                    logger.error(f"Error processing date filter: {str(e)}")

            # Prepare to query database
            question_embedding_hash = {}
            question_full_text_match_list = []
            question_knn_list = []
            question_semantic_formula_list = []
            question_semantic_formula_params = {}
            if question:
                # Translate question to all required languages
                translation_hash = config.get_translated_question_hash(question, language_code, types)

                simple_translation_hash = {}
                for type in types:
                    for language in translation_hash[type]:
                        if language not in simple_translation_hash:
                            simple_translation_hash[language] = translation_hash[type][language]

                filters.append({"terms": {"language": list(simple_translation_hash.keys())}})

                fields = [
                    "keywords^3",
                    "title^2",
                    "text",
                ]

                # Add full text match queries for each language
                for language in simple_translation_hash:
                    translated_question = simple_translation_hash[language]
                    extended_question = config.expand_question(source, translated_question, language)
                    question_full_text_match_list.append({
                        "bool": {
                            "filter": [{"term": { "language": language }}],
                            "must": [{
                                "multi_match": {
                                    "query": extended_question,
                                    "fields": fields,
                                    "fuzziness": "AUTO",
                                    "prefix_length": config.get_fuzzy_match_prefix_length(source),
                                }
                            }],
                        }
                    })

                    # Filter words that contain numbers
                    words = re.split('\s+', re.sub('\W+', ' ', translated_question.strip()))
                    words_with_numbers = [word for word in words if any(char.isdigit() for char in word)]
                    if len(words_with_numbers) > 0:
                        translated_question_numbers_only = " ".join(words_with_numbers)
                        question_full_text_match_list.append({
                            "bool": {
                                "filter": [{ "term": { "language": language } }],
                                "must": [{
                                    "multi_match": {
                                        "query": translated_question_numbers_only,
                                        "fields": fields,
                                    }
                                }],
                            }
                        })

                # Get embeddings
                embedding_provider, embedding_version = config.get_search_embedding_model(source)
                logger.info(f"Using embedding model - provider: {embedding_provider}, version: {embedding_version}")
                
                question_embedding = self._get_question_embedding(config, question, embedding_provider, embedding_version)
                if question_embedding:
                    logger.info(f"Generated embedding for question (showing first 5 values): {question_embedding[:5]}...")
                    question_embedding_hash[embedding_version] = question_embedding
                else:
                    logger.warning("Failed to generate embedding for question")

                # Build KNN queries
                for embedding_version in question_embedding_hash:
                    query_vector_key = QUERY_VECTOR_PREFIX + embedding_version.replace('-', '_').replace('/', '__')
                    query_vector_field = f"embedding__{embedding_version.replace('/', '__')}"
                    logger.info(f"Adding KNN query for field: {query_vector_field}")
                    question_knn_list.append({
                        "field": query_vector_field,
                        "query_vector": question_embedding_hash[embedding_version],
                    })

                    formula = f"""
                        (doc['{query_vector_field}'].size() == 0 ? 0.0 : 
                            cosineSimilarity(params.{query_vector_key}, '{query_vector_field}'))
                    """
                    logger.info(f"Adding semantic formula for field: {query_vector_field}")
                    logger.info(f"Semantic formula: {formula}")
                    question_semantic_formula_list.append(formula)
                    question_semantic_formula_params[query_vector_key] = question_embedding_hash[embedding_version]

            # Add filters from request
            if request.filter:
                for item in request.filter:
                    filters.append( item )

            # Get weights
            relevance_weights = config.get_relevance_weights(source)
            document_weights = config.get_document_relevance_weights(source)
            chunk_weights = config.get_chunk_relevance_weights(source)

            # Get reranker provider, model and prompt
            reranker_model, reranker_model_version = config.get_search_reranker_model(source)
            document_reranker_prompt = None
            document_reranker_confidence_threshold = None
            chunk_reranker_prompt = None
            chunk_reranker_confidence_threshold = None
            if reranker_model and reranker_model_version:
                document_reranker_prompt = config.get_document_reranker_prompt(source)
                document_reranker_confidence_threshold = config.get_reranker_confidence_threshold_document(source)
                chunk_reranker_prompt = config.get_chunk_reranker_prompt(source)
                chunk_reranker_confidence_threshold = config.get_reranker_confidence_threshold_chunk(source)

            show_keywords = request.show_keywords
            show_embedding = request.show_embedding

            # Get excluded search fields
            excluded_search_fields = config.get_excluded_search_fields(source)

            # Get KNN search parameters
            knn_params = config.get_knn_and_rescore_parameters(source)
            should_apply_document_filter = config.should_apply_document_filter(source)
            
            # Step 0: Collect stats for document and chunk scoring
            logger.info("Collecting search stats...")
            stats = self._collect_search_stats(index_name, filters, question_full_text_match_list)
            logger.info(f"Search stats: {stats}")
            
            # Step 1: Get document scores
            logger.info("Getting document scores...")
            document_scores = self._get_document_scores(
                index_name,
                question, filters, knn_params,
                question_full_text_match_list, question_knn_list, question_semantic_formula_list, question_semantic_formula_params, 
                stats, document_weights, 
                reranker_model, reranker_model_version, document_reranker_prompt, document_reranker_confidence_threshold,
                max_results
            )
            logger.info(f"Document scores (showing first 5): {dict(list(document_scores.items())[:5])}")

            # Step 2: Get chunk scores with document context
            logger.info("Getting chunk scores...")
            documents = self._get_chunk_scores(
                index_name,
                excluded_search_fields,
                source, question, filters, knn_params, should_apply_document_filter,
                question_full_text_match_list, question_knn_list, question_semantic_formula_list, question_semantic_formula_params, 
                stats, document_scores,
                relevance_weights, chunk_weights,
                reranker_model, reranker_model_version, chunk_reranker_prompt, chunk_reranker_confidence_threshold,
                show_keywords, show_embedding,
                max_results
            )
            logger.info(f"Found {len(documents)} documents")
            
            return documents
            
        except Exception as e:
            logger.error(f"Search error: {str(e)}", exc_info=True)
            raise RuntimeError(f"Search error: {str(e)}")
            
    def _collect_search_stats(
            self, 
            index_name: str,
            filters: List[Dict[str, Any]],
            question_full_text_match_list: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Collect statistics for scoring
        
        Args:
            index_name: Index name
            filters: Filters
            question_full_text_match_list: Full text match queries
            
        Returns:
            Search statistics
        """
        # Build stats query
        query = {
            "_source": {"excludes": ["*"]},
            "query": {
                "bool": {
                    "should": question_full_text_match_list,
                    "minimum_should_match": 1 if len(question_full_text_match_list) > 0 else 0,
                    "filter": filters
                }
            },
            "collapse": {"field": "level"},
        }
            
        # Execute query
        logger.info("Executing stats query...")
        try:
            if DEBUG_MODE:
                query_data_json = f"GET /{INDEX_NAME_PREFIX}-{index_name}/_search\n" + json.dumps(query, ensure_ascii = False, indent = 2)
                query_data_md5 = hashlib.md5(query_data_json.encode('utf-8')).hexdigest()
                query_data_fullname = f"console/query-factory/queries/{query_data_md5}.txt"
                qp_files.set_text(query_data_fullname, query_data_json)

            response = es.search(index=f"{INDEX_NAME_PREFIX}-{index_name}", **query)
            logger.info(f"Stats query returned {len(response['hits']['hits'])} hits")
        
        except Exception as e:
            logger.error(f"Error executing chunk query: {str(e)}", exc_info=True)
            raise
        
        # Process stats
        stats = {
            "bm25_max_scores": {
                "document": 10000,
                "chunk": 10000
            }
        }
        
        # Get max scores
        for hit in response["hits"]["hits"]:
            if "fields" in hit and hit["fields"].get("level"):
                level = hit["fields"]["level"][0]
                stats["bm25_max_scores"][level] = hit["_score"]
                    
        return stats
        
    def _get_document_scores(
            self, 
            index_name: str,
            question: str,
            filters: List[Dict[str, Any]],
            knn_params: Dict[str, Any],
            question_full_text_match_list: List[Dict[str, Any]],
            question_knn_list: List[Dict[str, Any]],
            question_semantic_formula_list: List[str],
            question_semantic_formula_params: Dict[str, Any],
            stats: Dict[str, Any],
            document_weights: SearchWeights,
            reranker_model: str,
            reranker_model_version: str,
            document_reranker_prompt: str,
            document_reranker_confidence_threshold: Optional[float],
            max_results_size: int
    ) -> Dict[str, float]:
        """Get document-level scores
        
        Args:
            index_name: Index name
            question: Question
            filters: Filters
            question_full_text_match_list: Full text match queries
            question_knn_list: KNN queries
            question_semantic_formula_list: Semantic formula list
            question_semantic_formula_params: Semantic formula parameters
            stats: Search statistics
            document_weights: Document weights
            reranker_model: Reranker model
            reranker_model_version: Reranker model version
            document_reranker_prompt: Document reranker prompt
            document_reranker_confidence_threshold: Document reranker confidence threshold
            max_results_size: Maximum results size
            
        Returns:
            Document scores
        """
        logger.info("Building document query...")
        document_scores = {}
        
        # Get reranker prompt
        query_document_score_source = {"excludes": ["*"]}
        if document_reranker_prompt != None:
            query_document_score_source = {"includes": ["title"]}

        # Copy semantic formula parameters
        document_question_semantic_formula_params = copy.deepcopy(question_semantic_formula_params)
        document_question_semantic_formula_params['document_text_weight'] = document_weights.text
        document_question_semantic_formula_params['document_semantic_weight'] = document_weights.semantic
        document_question_semantic_formula_params['document_bm25_max_score'] = stats['bm25_max_scores'][FACTOID_LEVEL_DOCUMENT]
        
        # Build document query
        query = {
            "_source": query_document_score_source,
            "knn": [
                {
                    "field": knn['field'],
                    "query_vector": knn['query_vector'],
                    "k": knn_params['k'],
                    "num_candidates": knn_params['num_candidates'],
                    "filter": filters + [
                        {"term": {"level": FACTOID_LEVEL_DOCUMENT}},
                    ],
                } for knn in question_knn_list
            ],
            "rescore": {
                "window_size": knn_params['window_size'],
                "query": {
                    "rescore_query": {
                        "script_score": {
                            "query": {
                                "bool": {
                                    "should": question_full_text_match_list,
                                    "minimum_should_match": 0,
                                    "filter": filters + [
                                        {"term": {"level": FACTOID_LEVEL_DOCUMENT}},
                                    ],
                                }
                            },
                            "script": {
                                "source": f"""
                                    params.document_text_weight * _score / params.document_bm25_max_score + 
                                    params.document_semantic_weight * (0.5 + 0.499999 * ({'+'.join(question_semantic_formula_list)}))
                                """,
                                "params": document_question_semantic_formula_params,
                            }
                        }
                    },
                    "query_weight": 0,
                }
            },
            "size": max_results_size
        }

        logger.info(f"Document query KNN fields: {[knn['field'] for knn in question_knn_list]}")
        logger.info(f"Document query script: {query['rescore']['query']['rescore_query']['script_score']['script']['source']}")
                    
        # Execute query
        logger.info("Executing document query...")
        try:
            if DEBUG_MODE:
                query_data_json = f"GET /{INDEX_NAME_PREFIX}-{index_name}/_search\n" + json.dumps(query, ensure_ascii = False, indent = 2)
                query_data_md5 = hashlib.md5(query_data_json.encode('utf-8')).hexdigest()
                query_data_fullname = f"console/query-factory/queries/{query_data_md5}.txt"
                qp_files.set_text(query_data_fullname, query_data_json)

            response = es.search(index=f"{INDEX_NAME_PREFIX}-{index_name}", **query)
            logger.info(f"Document query returned {len(response['hits']['hits'])} hits")
            
        except Exception as e:
            logger.error(f"Error executing document query: {str(e)}", exc_info=True)
            raise
        
        # Process scores
        documents = []
        for hit in response["hits"]["hits"]:
            document_scores[hit["_id"]] = hit["_score"]

            if (
                document_reranker_prompt
                and "_source" in hit
            ):
                documents.append({
                    "id": hit["_id"],
                    "title": hit["_source"].get("title", ""),
                })

        if document_reranker_prompt:
            document_scores = self._rerank_items(
                question, documents, 
                reranker_model, reranker_model_version, document_reranker_prompt, document_reranker_confidence_threshold
            )
            
        return document_scores
        
    def _get_chunk_scores(
            self, 
            index_name: str,
            excluded_search_fields: List[str],
            source: str,
            question: str,
            filters: List[Dict[str, Any]],
            knn_params: Dict[str, Any],
            should_apply_document_filter: bool,
            question_full_text_match_list: List[Dict[str, Any]],
            question_knn_list: List[Dict[str, Any]],
            question_semantic_formula_list: List[str],
            question_semantic_formula_params: Dict[str, Any],
            stats: Dict[str, Any],
            document_scores: Dict[str, float],
            relevance_weights: KindWeights,
            chunk_weights: SearchWeights,
            reranker_model: str,
            reranker_model_version: str,
            chunk_reranker_prompt: str,
            chunk_reranker_confidence_threshold: Optional[float],
            show_keywords: bool,
            show_embedding: bool,
            max_results_size: int
    ) -> List[Dict[str, Any]]:
        """Get chunk-level scores and final documents
        
        Args:
            index_name: Index name
            excluded_search_fields: Excluded search fields
            source: Source type
            question: Question
            filters: Filters
            knn_params: KNN search parameters
            should_apply_document_filter: Whether to apply document filter
            question_full_text_match_list: Full text match queries
            question_knn_list: KNN queries
            question_semantic_formula_list: Semantic formula list
            question_semantic_formula_params: Semantic formula parameters
            stats: Search statistics
            document_scores: Document-level scores
            relevance_weights: Kind weights
            chunk_weights: Search weights
            reranker_model: Reranker model
            reranker_model_version: Reranker model version
            chunk_reranker_prompt: Chunk reranker prompt
            chunk_reranker_confidence_threshold: Chunk reranker confidence threshold
            show_keywords: Show keywords
            show_embedding: Show embedding
            max_results_size: Maximum results size
            
        Returns:
            Final scored and processed documents
        """
        logger.info("Building chunk query...")

        # Copy semantic formula parameters
        chunk_question_semantic_formula_params = copy.deepcopy(question_semantic_formula_params)
        chunk_question_semantic_formula_params['document_weight'] = relevance_weights.document
        chunk_question_semantic_formula_params['chunk_weight'] = relevance_weights.chunk
        chunk_question_semantic_formula_params['chunk_text_weight'] = chunk_weights.text
        chunk_question_semantic_formula_params['chunk_semantic_weight'] = chunk_weights.semantic
        chunk_question_semantic_formula_params['chunk_bm25_max_score'] = stats['bm25_max_scores'][FACTOID_LEVEL_CHUNK]

        # Calculate minimum document weight for chunks without parent doc and apply document filter if enabled
        logger.info(f"Calculating minimum document weight with {len(document_scores)} document scores, applying document filter: {should_apply_document_filter}")
        document_filter = []
        min_doc_weight = 0.0
        if document_scores:
            min_doc_weight = 0.75 * min(document_scores.values())
            logger.info(f"Using minimum document weight: {min_doc_weight}")

            chunk_question_semantic_formula_params['weights'] = document_scores

            if should_apply_document_filter:
                document_filter = [
                    {"terms": {"root_id": list(document_scores.keys())}},
                ]

        chunk_question_semantic_formula_params['document_min_weight'] = min_doc_weight

        # Build chunk query
        query = {
            "_source": {
                "excludes": excluded_search_fields
            },
            "knn": [
                {
                    "field": knn['field'],
                    "query_vector": knn['query_vector'],
                    "k": knn_params['k'],
                    "num_candidates": knn_params['num_candidates'],
                    "filter": filters + document_filter + [
                        {"term": {"level": FACTOID_LEVEL_CHUNK}},
                    ],
                } for knn in question_knn_list
            ],
            "rescore": {
                "window_size": knn_params['window_size'],
                "query": {
                    "rescore_query": {
                        "script_score": {
                            "query": {
                                "bool": {
                                    "should": question_full_text_match_list,
                                    "minimum_should_match": 0,
                                    "filter": filters + document_filter + [
                                        {"term": {"level": FACTOID_LEVEL_CHUNK}},
                                    ],
                                }
                            },
                            "script": {
                                "source": f"""
                                    params.document_weight * (
                                        params.weights.containsKey( doc['root_id'].value ) ? params.weights[doc['root_id'].value] : params.document_min_weight
                                    )
                                    + params.chunk_weight * (
                                        params.chunk_text_weight * _score / params.chunk_bm25_max_score + 
                                        params.chunk_semantic_weight * (0.5 + 0.499999 * ({'+'.join(question_semantic_formula_list)}))
                                    )
                                """,
                                "params": chunk_question_semantic_formula_params,
                            }
                        }
                    },
                    "query_weight": 0,
                }
            },
            "size": max_results_size
        }

        logger.info(f"Chunk query KNN fields: {[knn['field'] for knn in question_knn_list]}")
        logger.info(f"Chunk query script: {query['rescore']['query']['rescore_query']['script_score']['script']['source']}")
        
        # Execute query
        logger.info("Executing chunk query...")
        try:
            if DEBUG_MODE:
                query_data_json = f"GET /{INDEX_NAME_PREFIX}-{index_name}/_search\n" + json.dumps(query, ensure_ascii = False, indent = 2)
                query_data_md5 = hashlib.md5(query_data_json.encode('utf-8')).hexdigest()
                query_data_fullname = f"console/query-factory/queries/{query_data_md5}.txt"
                qp_files.set_text(query_data_fullname, query_data_json)

            response = es.search(index=f"{INDEX_NAME_PREFIX}-{index_name}", **query)
            logger.info(f"Chunk query returned {len(response['hits']['hits'])} hits")
            
        except Exception as e:
            logger.error(f"Error executing chunk query: {str(e)}", exc_info=True)
            raise
        
        # Process results
        documents = []
        
        # Get base scores for each hit
        chunk_scores = {}
        if chunk_reranker_prompt:
            # Get chunks for reranking
            chunks = []
            for hit in response["hits"]["hits"]:
                if "_source" in hit:
                    chunks.append({
                        "id": hit["_id"],
                        "title": hit["_source"].get("title", ""),
                        "text": hit["_source"].get("text", "")
                    })

            chunk_scores = self._rerank_items(
                question, chunks, 
                reranker_model, reranker_model_version, chunk_reranker_prompt, chunk_reranker_confidence_threshold
            )
                    
        else:
            # Use original scores
            for hit in response["hits"]["hits"]:
                chunk_scores[hit["_id"]] = hit["_score"]
                
        # Process all results
        for hit in response["hits"]["hits"]:
            doc = hit["_source"]
            score = chunk_scores[hit["_id"]]
            if (
                chunk_reranker_confidence_threshold
                and score <= chunk_reranker_confidence_threshold
            ):
                continue
                
            # Create base document
            document = QuepasaDocument(
                id=doc.get("id"),
                root_id=doc.get("root_id"),
                chunk_index=doc.get("chunk_index"),
                client=doc.get("client"),
                domain=doc.get("domain"),
                provider=doc.get("provider"),
                type=doc.get("type"),
                kind=doc.get("kind"),
                level=doc.get("level"),
                url=doc.get("url"),
                language=doc.get("language"),
                title=doc.get("title"),
                keywords=None,
                text=remove_emoji(doc.get("text", "")).strip(),
                tokens=doc.get("tokens"),
                chunks=doc.get("chunks"),
                start_position=doc.get("start_position"),
                end_position=doc.get("end_position"),
                created_at=doc.get("created_at"),
                updated_at=doc.get("updated_at"),
                embeddings=None,
                score=score,
                sku=doc.get("sku"),  # Retrieve sku field if present
                metadata=doc.get("metadata")  # Retrieve metadata field if present
            )
            
            # Add keywords if requested
            if show_keywords and "keywords" in doc:
                document.keywords = doc["keywords"]
                
            # Add embeddings if requested
            if show_embedding:
                embeddings = {}
                for field in doc:
                    if field.startswith("embedding__"):
                        embeddings[field] = doc[field]
                if embeddings:
                    document.embeddings = embeddings
                        
            documents.append(document)
            
        logger.info(f"Processed {len(documents)} documents with scores")
        return documents
    
    def _rerank_items(
            self, 
            question: str,
            documents: List[Dict[str, Any]],
            reranker_model: str,
            reranker_model_version: str,
            reranker_prompt: str,
            reranker_confidence_threshold: Optional[float] = None
    ) -> Dict[str, float]:
        """Rerank documents
        
        Args:
            question: Question
            documents: Documents
            reranker_model: Reranker model
            reranker_model_version: Reranker model version
            document_reranker_prompt: Document reranker prompt
            
        Returns:
            Document scores after reranking
        """
        # Get reranked scores
        reranked_documents = get_cached_reranker(
            provider=reranker_model,
            model_version=reranker_model_version,
            query=reranker_prompt.replace(QUESTION_PLACEHOLDER, question),
            documents=documents
        )
        
        document_scores = {}
        for doc in reranked_documents:
            if reranker_confidence_threshold is None or doc.get("score", 0) > reranker_confidence_threshold:
                document_scores[doc["id"]] = doc["score"]

        return document_scores
                
    def _get_question_embedding(self, config: QuepasaConfigurationHub, question: str, provider: str, model: str) -> Optional[List[float]]:
        """Get embedding for question text
        
        Args:
            question: Question text
            provider: Embedding provider
            model: Embedding model name
            
        Returns:
            List of embedding values or None if unavailable
        """
        try:
            question_with_instruct = config.get_question_with_instruct(model, question)
            text_vector = None 
            if QUEPASA_LLM_GATEWAY_API_KEY:
                text_vector = get_embedding_from_gateway(provider, model, question_with_instruct)
            elif provider in [EmbeddingProvider.SBERT.value]:
                text_vector = get_embedding_from_service(provider, model, question_with_instruct) 
            else:
                text_vector = get_cached_embedding(provider, model, question_with_instruct)
            text_vector_float = [float(f) for f in text_vector]
            return text_vector_float
            
        except Exception as e:
            logger.error(f"Error getting question embedding: {str(e)}")
            return None
