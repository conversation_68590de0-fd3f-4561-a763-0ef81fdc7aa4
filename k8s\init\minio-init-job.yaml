apiVersion: batch/v1
kind: Job
metadata:
  name: minio-init
  namespace: quepasa
spec:
  backoffLimit: 2
  template:
    spec:
      containers:
      - name: minio-setup
        image: minio/mc:latest
        command: ["/bin/sh", "-c"]
        args:
        - |
          mc alias set myminio http://minio:9000 minioadmin minioadmin && \
          mc mb --ignore-existing myminio/quepasa-files && \
          mc policy set download myminio/quepasa-files && \
          echo "MinIO initialization completed successfully"
        env:
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: shared-secrets
              key: MINIO_SECRET_KEY
      restartPolicy: Never 