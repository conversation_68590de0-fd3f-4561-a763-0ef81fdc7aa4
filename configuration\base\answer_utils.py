import re
from typing import List, Pattern, Match, Optional, Tuple

class AnswerUtils:
    """Utility functions for answer processing."""
    
    @staticmethod
    def _get_reference_regexp() -> Pattern:
        """Get regex pattern for extracting links from answers.
        
        Previously: get_links_regexp()
        
        Returns:
            Compiled regex pattern for matching links
        """
        reference_regexp = '\[\d{1,2}\]'
        reference_group_regexp = f'(?:{reference_regexp}(?:[\s\.,]{{0,3}}|\s+and\s+))+'
        return re.compile(reference_group_regexp)
        
    @staticmethod
    def _get_number_of_continuous_refs(refs_line: str) -> int:
        """Count number of continuous numbered references.
        
        Previously: get_number_of_continues_refs()
        
        Args:
            refs_line: String containing reference numbers
            
        Returns:
            Number of continuous references found, or 0 if non-continuous
        """
        refs = re.sub(r'\D+', ' ', refs_line).strip().split(' ')
        if len(refs) <= 1:
            return 0

        count = 0
        for index, ref in enumerate(refs):
            if int(ref) != index + 1:
                return 0
            count += 1
        return count
        
    @staticmethod
    def _has_bad_reference_by_regexp(regexp: str, line: str, count: int = 3) -> bool:
        """Check if text contains malformed references.
        
        Previously: has_bad_reference_by_regexp()
        
        Args:
            regexp: Regular expression pattern to match
            line: Text line to check
            count: Minimum number of consecutive references to consider bad
            
        Returns:
            True if bad reference pattern found, False otherwise
        """
        m = re.search(regexp, line, re.IGNORECASE)
        if m and AnswerUtils._get_number_of_continuous_refs(m.group()) >= count:
            return True
        return False
