import os
import pytest
import time
from src.lib.files import QuepasaFiles

def print_timing(msg):
    print(f"{time.time():.2f}: {msg}")

@pytest.fixture(scope="session")
def storage():
    """Create a QuepasaFiles instance connected to real MinIO."""
    print_timing("Creating QuepasaFiles instance")
    start = time.time()
    instance = QuepasaFiles(
        bucket_name='quepasa-files',
        endpoint_url='http://localhost:9000',
        aws_access_key_id='minioadmin',
        aws_secret_access_key='minioadmin',
        debug_flag=False
    )
    end = time.time()
    print(f"QuepasaFiles instance creation took {end - start:.2f} seconds")
    return instance

def test_basic_operations(storage):
    """Test basic file operations."""
    print_timing("Starting basic operations test")
    test_data = "Hello, MinIO Integration!"
    test_file = "integration_test/hello.txt"

    try:
        start_total = time.time()
        
        print_timing("Setting text")
        start = time.time()
        storage.set_text(test_file, test_data)
        end = time.time()
        print(f"set_text operation took {end - start:.2f} seconds")

        print_timing("Checking existence")
        start = time.time()
        assert storage.exists(test_file)
        end = time.time()
        print(f"exists operation took {end - start:.2f} seconds")

        print_timing("Getting text")
        start = time.time()
        retrieved_data = storage.get_text(test_file)
        end = time.time()
        print(f"get_text operation took {end - start:.2f} seconds")
        assert retrieved_data == test_data

        print_timing("Listing files")
        start = time.time()
        files = storage.get_files("integration_test/")
        end = time.time()
        print(f"get_files operation took {end - start:.2f} seconds")
        assert test_file in files

        end_total = time.time()
        print(f"Total test operations took {end_total - start_total:.2f} seconds")

    finally:
        print_timing("Cleanup")
        start = time.time()
        if storage.exists(test_file):
            storage.delete_file(test_file)
        end = time.time()
        print(f"Cleanup operations took {end - start:.2f} seconds")
        print_timing("Cleanup complete")

def test_json_operations(storage):
    """Test JSON operations."""
    test_data = {"message": "Hello, MinIO!", "number": 42}
    test_file = "integration_test/test.json"

    try:
        # Regular JSON
        storage.set_json(test_file, test_data)
        retrieved_data = storage.get_json(test_file)
        assert retrieved_data == test_data

        # Compressed JSON
        compressed_file = "integration_test/test.json.zlib"
        storage.set_json_zlib(compressed_file, test_data)
        retrieved_compressed = storage.get_json_zlib(compressed_file)
        assert retrieved_compressed == test_data

    finally:
        # Cleanup
        for f in [test_file, compressed_file]:
            if storage.exists(f):
                storage.delete_file(f)

def test_directory_operations(storage):
    """Test directory-related operations."""
    test_dir = "integration_test/nested/deep/directory"
    test_file = f"{test_dir}/test.txt"
    test_data = "Test content"

    try:
        # Create directories and file
        storage.set_text(test_file, test_data)
        
        # List files recursively
        files = storage.get_files("integration_test/", recursive=True)
        assert test_file in files
        
        # List files with properties
        files_with_props = storage.get_files_with_properties("integration_test/")
        assert any(f['path'] == test_file for f in files_with_props)

    finally:
        # Cleanup
        if storage.exists(test_file):
            storage.delete_file(test_file)

def test_file_properties(storage):
    """Test file properties operations."""
    test_file = "integration_test/props_test.txt"
    test_data = "Test content"
    content_settings = {
        'content_type': 'text/plain',
        'content_encoding': 'utf-8'
    }
    metadata = {
        'test_key': 'test_value'
    }

    try:
        # Upload with properties
        storage.set_data(test_file, test_data.encode(), content_settings, metadata)
        
        # Check properties
        props = storage.get_properties(test_file)
        assert props['content_type'] == 'text/plain'
        assert props['metadata']['test_key'] == 'test_value'
        
        # Check safe properties
        safe_props = storage.get_properties_safe(test_file)
        assert safe_props is not None
        assert safe_props['metadata']['test_key'] == 'test_value'

    finally:
        # Cleanup
        if storage.exists(test_file):
            storage.delete_file(test_file) 