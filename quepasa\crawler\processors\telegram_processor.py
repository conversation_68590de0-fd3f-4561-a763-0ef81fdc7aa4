import asyncio
import os
import json
from src.lib.files import QuepasaFiles
from src.lib.logger import <PERSON>pas<PERSON><PERSON>og<PERSON>
from src.lib.telegram.client import QuepasaTelegramClient

# Import components from our new directory structure
from quepasa.crawler.processors.telegram import (
    TelegramMessageDownloader,
    ThreadOrganizer,
    AudioTranscriber
)

logger = QuepasaLogger().get_instance(__name__)

qp_files = QuepasaFiles()

class TelegramProcessor:
    def __init__(self, files=None):
        self.qp_files = files or qp_files

        self._local_cache_dir = os.path.join(os.path.dirname(__file__), "../../../cache/telegram")        
        self._audio_dir = f"{self._local_cache_dir}/audio"
        self._transcription_dir = f"{self._local_cache_dir}/transcriptions"

        # Ensure directories exist
        for dir in [self._local_cache_dir, self._audio_dir, self._transcription_dir]:
            os.makedirs(dir, exist_ok=True)

    async def process(self, chat_url, meta):
        """Process a Telegram channel and extract messages"""
        try:
            # Extract necessary metadata
            client_id = meta.get('client_id')

            # Get the phone number for the client
            phone_number = None 
            if meta.get('phone_number'):
                phone_number = meta.get('phone_number')
            else:
                phone_metadata = QuepasaTelegramClient.get_phone_metadata(client_id)
                if phone_metadata:
                    phone_number = phone_metadata.get('default_phone')

            logger.info(f"Phone number: {phone_number}")

            if not phone_number:
                return {
                    'status': 'error',
                    'error': 'No phone number found for this client'
                }
            
            # Get external username mappings
            external_username_mappings = QuepasaTelegramClient.get_external_username_mappings(client_id)
            logger.info(f"External username mappings: {len(external_username_mappings)} items")
            
            logger.info(f"Processing Telegram channel: {chat_url}")
            
            # Create the main components needed for processing
            downloader = TelegramMessageDownloader(client_id, phone_number)
            thread_organizer = ThreadOrganizer(external_username_mappings)
            transcriber = AudioTranscriber()
            
            # Process the channel directly - no need for an event loop here
            result = await self._process_url(client_id, downloader, thread_organizer, transcriber, chat_url, meta)

            return result
            
        except Exception as e:
            logger.error(f"Error processing Telegram channel: {str(e)}", exc_info=True)
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _process_url(self, client_id: str, downloader: TelegramMessageDownloader, thread_organizer: ThreadOrganizer, transcriber: AudioTranscriber, chat_url: str, meta: dict):
        """Async method to process a channel and extract messages"""
        try:
            logger.info(f"Processing Telegram channel: {chat_url}")

            # Connect to Telegram
            await downloader.connect()
            
            # Get channel info
            chat_info = await downloader.get_chat_info(chat_url)
            
            if not chat_info:
                return {
                    'status': 'error',
                    'error': f"Could not get channel info for {chat_url}"
                }
            
            title = chat_info.get('title', f"Channel/Group {chat_url}")
            
            # Set the chat title in thread organizer
            thread_organizer.set_chat_title(title)
            
            # Retrieve all existing messages from storage
            existing_messages = []
            start_id = 0
            
            # List all message files recursively
            try:
                message_files = self.qp_files.get_files(f"prod/storage/custom/telegram/{client_id}/{chat_info['id']}/", recursive=True)
                logger.info(f"Found {len(message_files)} historical message files for {chat_url}")
                
                # Process each message file
                for file_path in message_files:
                    if file_path.endswith('.jsonl'):
                        try:
                            # Read the file content
                            file_content = self.qp_files.get_text(file_path)
                            
                            # Parse each line as a JSON object
                            for line in file_content.strip().split('\n'):
                                if line:
                                    message = json.loads(line)
                                    existing_messages.append(message)

                        except Exception as e:
                            logger.error(f"Error reading message file {file_path}: {str(e)}")
                
                # Sort messages by ID and find the highest ID
                if existing_messages:
                    existing_messages.sort(key=lambda x: int(x['id']))
                    start_id = int(existing_messages[-1]['id'])
                    logger.info(f"Retrieved {len(existing_messages)} historical messages, highest ID: {start_id}")

            except Exception as e:
                logger.error(f"Error retrieving historical messages: {str(e)}")
                # Continue with start_id = 0 if we couldn't retrieve historical messages
            
            # Download new messages starting from the highest ID we've seen
            logger.info(f"Downloading new messages starting from ID: {start_id}")
            new_messages = await downloader.download_messages(chat_url, start_id, chat_info)
            logger.info(f"Downloaded {len(new_messages)} new messages")
            
            if not new_messages:
                return {
                    'status': 'success',
                    'results': existing_messages
                }
            
            # Process any audio/video messages for transcription
            await self._process_audio_messages(downloader, transcriber, new_messages, self._audio_dir, self._transcription_dir)

            # Combine existing and new messages
            messages = existing_messages + new_messages

            # Sort messages again to ensure proper order
            messages.sort(key=lambda x: int(x['id']))
            logger.info(f"Processing {len(messages)} total messages")

            # Organize messages into threads
            threads = thread_organizer.organize_messages(messages)
            logger.info(f"Organized {len(threads)} threads")
            
            documents = thread_organizer.generate_documents(threads, chat_info)
            logger.info(f"Generated {len(documents)} documents")
            
            # Return the processed data
            return {
                'status': 'success',
                'results': documents
            }
            
        except Exception as e:
            logger.error(f"Error in _process_url: {str(e)}", exc_info=True)
            return {
                'status': 'error',
                'error': str(e)
            }
        
        finally:
            if downloader:
                await downloader.disconnect()
    
    async def _process_audio_messages(self, downloader, transcriber, messages, audio_dir, transcription_dir):
        """Process audio and video messages for transcription"""
        try:
            # Ensure directories exist
            os.makedirs(audio_dir, exist_ok=True)
            os.makedirs(transcription_dir, exist_ok=True)
            
            logger.info(f"Processing {len(messages)} messages for audio/video content")
            for message in messages:
                if not message:
                    continue
                    
                # Safely check for voice audio or video with nested get() calls
                media = message.get('media', {})
                if not media:
                    continue
                    
                document = media.get('document', {})
                if not document:
                    continue
                    
                mime_type = document.get('mime_type', '')
                if not (mime_type.startswith('audio/') or mime_type.startswith('video/')):
                    continue
                    
                attributes = document.get('attributes', [])
                
                # Check for voice messages or video messages with audio
                is_voice = any(
                    attr.get('_') == 'DocumentAttributeAudio' and attr.get('voice')
                    for attr in attributes
                )
                is_video_with_audio = any(
                    attr.get('_') == 'DocumentAttributeVideo' and not attr.get('nosound', True)
                    for attr in attributes
                )
                
                if not (is_voice or is_video_with_audio):
                    continue

                try:
                    # Download audio/video
                    logger.info(f"Downloading audio/video for message {message.get('id')}")
                    if is_video_with_audio:
                        file_path = await downloader.download_video(message, audio_dir)
                    else:
                        file_path = await downloader.download_audio(message, audio_dir)
                        
                    if not file_path:
                        continue
                    
                    # Transcribe audio
                    if os.path.exists(file_path):
                        logger.info(f"Downloaded {'video' if is_video_with_audio else 'audio'} from message {message.get('id')} to {file_path}")
                        
                        transcription_path = transcriber.transcribe(file_path, transcription_dir)
                        if transcription_path:
                            logger.info(f"Transcribed audio to {transcription_path}")
                                
                except Exception as e:
                    logger.error(f"Failed to process audio from message {message.get('id', 'unknown')}: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error in _process_audio_messages: {str(e)}", exc_info=True)
    