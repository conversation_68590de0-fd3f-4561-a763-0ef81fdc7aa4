import io
from typing import Dict, Any, List
from pptx import Presentation
from .base import BaseProcessor
from ...utils import split_text

class PPTXProcessor(BaseProcessor):
    """Processor for PowerPoint presentations"""

    def _process_impl(self, content: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract text from PowerPoint content
        
        Args:
            content: PowerPoint file content
            meta: Metadata about the presentation
            
        Returns:
            Dictionary containing extracted text and title
        """
        # Validate metadata
        error = self.validate_meta(meta, ['filename'])
        if error:
            raise ValueError(error)

        # Save content to temporary file for processing
        temp_file = io.BytesIO(content)
        chunks = []
        title = None
        
        try:
            prs = Presentation(temp_file)
            
            # Try to get title from core properties first
            try:
                core_props = prs.core_properties
                if core_props.title:
                    title = core_props.title.strip()
            except:
                self.logger.warning("Failed to extract title from core properties", exc_info=True)
            
            # If no title in properties, try first slide title
            if not title and len(prs.slides) > 0:
                first_slide = prs.slides[0]
                # Look for title in the title placeholder
                for shape in first_slide.shapes:
                    if shape.is_placeholder and shape.placeholder_format.type == 1:  # 1 = title placeholder
                        if shape.has_text_frame and shape.text.strip():
                            title = shape.text.strip()
                            break
                
                # If still no title, try first text box
                if not title:
                    for shape in first_slide.shapes:
                        if shape.has_text_frame and shape.text.strip():
                            title = shape.text.strip()
                            break
            
            # Process each slide
            for slide_num, slide in enumerate(prs.slides, 1):
                slide_texts = []
                
                # Process shapes in the slide
                for shape in slide.shapes:
                    # Get text from text frames
                    if shape.has_text_frame:
                        text = shape.text.strip()
                        if text:
                            slide_texts.append(text)
                    
                    # Get text from tables
                    elif shape.has_table:
                        table_texts = []
                        for row in shape.table.rows:
                            row_texts = []
                            for cell in row.cells:
                                cell_text = cell.text.strip()
                                if cell_text:
                                    row_texts.append(cell_text)
                            if row_texts:
                                table_texts.append('\t'.join(row_texts))
                        if table_texts:
                            slide_texts.append("# Table")
                            slide_texts.extend(table_texts)
                
                # Get text from notes if they exist
                if slide.has_notes_slide:
                    notes_texts = []
                    notes_text_frame = slide.notes_slide.notes_text_frame
                    if notes_text_frame and notes_text_frame.text:
                        for paragraph in notes_text_frame.paragraphs:
                            note_text = paragraph.text.strip()
                            if note_text:
                                notes_texts.append(note_text)
                    if notes_texts:
                        slide_texts.append("# Notes")
                        slide_texts.extend(notes_texts)
                
                if slide_texts:
                    # Split slide content into chunks
                    raw_chunks = split_text('\n'.join(slide_texts))
                    for chunk in raw_chunks:
                        chunks.append({
                            'text': chunk,
                            'position': f"slide {slide_num}"
                        })
            
            return {
                'chunks': chunks,
                'title': title,
                'filename': meta['filename']
            }
            
        except Exception as e:
            self.logger.error(f"Failed to process PowerPoint: {str(e)}", exc_info=True)
            raise
        finally:
            temp_file.close() 