import threading
import requests
from typing import Dict, <PERSON>, <PERSON><PERSON>
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .base import BaseAPIHandler
from src.lib.constants import ANSWER_TYPE_ERROR
from configuration.main.default import QuepasaConfigurationHub
from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse
from ..sources.base import STATUS_ERROR

logger = QuepasaLogger().get_instance(__name__)

class WebhookHandler(BaseAPIHandler):
    """Webhook API handler for asynchronous responses"""
    
    def __init__(self, config: QuepasaConfigurationHub):
        super().__init__(config)

    def _handle_request_internal(self, method: str, url: str, headers: Dict[str, str]) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Handle webhook request internally"""
        try:
            # Validate webhook configuration
            webhook_config = self.config.request.webhook
            if not webhook_config or not webhook_config.get('endpoint'):
                error_headers = {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-store'
                }
                return {'error': 'Missing webhook configuration', 'status': 'error'}, 400, error_headers

            # Start processing in background thread to not block the response
            thread = threading.Thread(
                target=self._process_webhook_async,
                args=(webhook_config)
            )
            thread.daemon = True
            thread.start()

            # Return immediate response
            response_headers = {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-store'
            }
            return {
                'status': 'accepted',
                'message': 'Webhook processing started'
            }, 202, response_headers

        except Exception as e:
            logger.error(f"Error in webhook handler: {str(e)}")
            error_headers = {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-store'
            }
            return {'error': str(e), 'status': 'error'}, 500, error_headers

    def _process_webhook_async(self, webhook_config: Dict[str, Any]) -> None:
        """Process webhook request asynchronously"""
        try:
            # Process request through search factory
            response = self.source_factory.get_answer(stream=False)
            
            # Prepare webhook response
            webhook_response = response.data.to_dict()

            # Include original metadata if provided
            if webhook_config.get('meta'):
                webhook_response['meta'] = webhook_config['meta']

            # Send webhook
            self._send_webhook(
                webhook_config['endpoint'],
                webhook_response,
                webhook_config.get('headers', {})
            )

        except Exception as e:
            logger.error(f"Error in webhook async processing: {str(e)}")
            error_response = {
                'status': STATUS_ERROR,
                'error': str(e)
            }

            if webhook_config.get('meta'):
                error_response['meta'] = webhook_config['meta']
            
            self._send_webhook(
                webhook_config['endpoint'],
                error_response,
                webhook_config.get('headers', {})
            )

    def _send_webhook(self, endpoint: str, data: Dict[str, Any], headers: Dict[str, str]) -> None:
        """Send webhook request"""
        try:
            headers['Content-Type'] = 'application/json'
            response = requests.post(endpoint, json=data, headers=headers)
            response.raise_for_status()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Webhook request failed: {str(e)}")
            # We don't raise here since we're in an async thread 