import time
import threading
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from telegram import (
    <PERSON><PERSON>, 
    InlineKeyboardMarkup, 
    InlineKeyboardButton,
    constants
)
from telegram.constants import ParseMode, ChatAction

from ..core.auth import AuthR<PERSON>ult
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.lib.utils import escape_telegram_markdown
from .base import BaseAPIHandler
from configuration.main.default import QuepasaConfigurationHub
from src.lib.constants import (
    ANSWER_TYPE_WEAK,
    ANSWER_TYPE_NO_LINKS
)

logger = QuepasaLogger().get_instance(__name__)

class TelegramHandler(BaseAPIHandler):
    """Telegram bot handler for search responses"""
    
    MAX_MESSAGE_LENGTH = 3000
    UPDATE_INTERVAL = 2.0  # Time between updates

    def __init__(self, config: QuepasaConfigurationHub):
        super().__init__(config)
        self.bot = Bot(token=config.get_telegram_access_token())

    def _authenticate_request(self, headers: Dict[str, str]) -> AuthResult:
        """Authenticate Telegram user"""
        if (
            not hasattr(self.config.request, 'user_info')
            or not hasattr(self.config.request.user_info, 'id')
        ):
            return AuthResult(is_authorized=False, error='Missing user information')

        # Use client's Telegram auth check
        user_info = self.config.request.user_info
        if not self.config.is_telegram_user_authorized(user_info):
            return AuthResult(is_authorized=False, error='Unauthorized Telegram user')

        return AuthResult(is_authorized=True)

    def _split_message(self, text: str) -> List[str]:
        """Split message into chunks if too long"""
        if len(text) <= self.MAX_MESSAGE_LENGTH:
            return [text]
            
        chunks = []
        current_chunk = ""
        
        for line in text.split("\n"):
            if len(current_chunk) + len(line) + 1 > self.MAX_MESSAGE_LENGTH:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = line
            else:
                current_chunk += "\n" + line if current_chunk else line
                
        if current_chunk:
            chunks.append(current_chunk.strip())
            
        return chunks

    def _send_message_chunks(self, chat_id: int, text: str, reply_to_message_id: Optional[int] = None, 
                             buttons: Optional[List[List[InlineKeyboardButton]]] = None) -> Optional[int]:
        """Send message in chunks if needed, returns ID of first message"""
        chunks = self._split_message(text)
        first_message_id = None
        
        for i, chunk in enumerate(chunks):
            is_last_chunk = (i == len(chunks) - 1)
            try:
                message = self.bot.send_message(
                    chat_id=chat_id,
                    text=chunk,
                    reply_to_message_id=reply_to_message_id,
                    parse_mode=ParseMode.MARKDOWN_V2,
                    reply_markup=InlineKeyboardMarkup(buttons) if is_last_chunk and buttons else None,
                    disable_web_page_preview=True
                )

                if first_message_id is None:
                    first_message_id = message.message_id
                    
            except Exception as e:
                logger.error(f"Error sending message chunk: {str(e)}")
                
        return first_message_id

    def _handle_request_internal(self, method: str, url: str, headers: Dict[str, str]) -> Tuple[Dict[str, Any], int, Dict[str, str]]:
        """Handle Telegram message"""
        try:
            user_info = self.config.request.user_info
            
            # Authenticate request
            auth_result = self._authenticate_request(headers)
            if not auth_result.is_authorized:
                return {'error': auth_result.error}, 400, {}
            
            # Show initial typing indicator
            chat_id = user_info.get('chat_id')
            if chat_id:
                self.bot.send_chat_action(chat_id, ChatAction.TYPING)

            # Start processing in background thread
            thread = threading.Thread(
                target=self._process_telegram_async,
                args=()
            )
            thread.daemon = True  # Allow program to exit even if thread is running
            thread.start()

            # Return immediately
            return {'status': 'OK'}, 200, {}

        except Exception as e:
            logger.error(f"Error handling Telegram message: {str(e)}")
            self._send_error_message(user_info, str(e))
            return {'error': str(e)}, 500, {}

    def _process_telegram_async(self):
        """Process Telegram request asynchronously"""
        try:
            user_info = self.config.request.user_info
            chat_id = user_info.get('chat_id')

            # Initialize streaming state
            current_message = ""
            last_update_time = 0
            message_id = None
            reply_to_message_id = None if user_info.get('id') == chat_id else user_info.get('message_id')

            # Process stream
            for response_data in self.source_factory.get_answer(stream=True):
                is_done = not response_data.loading and not response_data.streaming
                current_message = response_data.text
                now = time.time()

                # Check if we should update the message
                should_update = (
                    message_id is None  # First message
                    or now - last_update_time >= self.UPDATE_INTERVAL  # Rate limit check
                    or is_done  # Last message
                )

                if should_update:
                    # Format and escape message
                    message_markdown = escape_telegram_markdown(current_message)

                    question = self.config.request.question
                    language_code = self.config.get_language_code()
                    
                    # Get buttons for final message
                    buttons = None
                    if is_done and response_data.type not in [ANSWER_TYPE_WEAK, ANSWER_TYPE_NO_LINKS]:
                        buttons = [[
                            InlineKeyboardButton("👍", callback_data=f"{language_code}👍 {question}"),
                            InlineKeyboardButton("👎", callback_data=f"{language_code}👎 {question}")
                        ]]

                    if message_id is None:
                        # Send first message
                        message_id = self._send_message_chunks(
                            chat_id,
                            text=message_markdown,
                            reply_to_message_id=reply_to_message_id,
                            buttons=None  # No buttons on first message
                        )

                    else:
                        # Try to update existing message, fall back to new message if too long
                        try:
                            self.bot.edit_message_text(
                                chat_id=chat_id,
                                message_id=message_id,
                                text=message_markdown,
                                parse_mode=ParseMode.MARKDOWN_V2,
                                reply_markup=InlineKeyboardMarkup(buttons) if buttons else None,
                                disable_web_page_preview=True
                            )

                        except Exception as e:
                            # If message is too long, send as new message
                            self._send_message_chunks(
                                chat_id,
                                text=message_markdown,
                                reply_to_message_id=reply_to_message_id,
                                buttons=buttons
                            )

                    last_update_time = now
                    
                    # Break if this was the final message
                    if is_done:
                        break

        except Exception as e:
            logger.error(f"Error in Telegram background processing: {str(e)}")
            self._send_error_message(user_info, str(e))

    def _send_unauthorized_message(self, error: Optional[str] = None):
        """Send unauthorized message to user"""
        user_info = self.config.request.user_info
        chat_id = user_info.get('chat_id')
        if chat_id:
            message = error or "You are not authorized to use this bot"
            self._send_message_chunks(chat_id, escape_telegram_markdown(message))

    def _send_error_message(self, user_info: Dict[str, Any], error: str):
        """Send error message"""
        chat_id = user_info.get('chat_id')
        message_id = user_info.get('message_id')
        
        reply_to_message_id = None
        if user_info.get('id') != chat_id:
            reply_to_message_id = message_id
            
        self._send_message_chunks(
            chat_id,
            text=escape_telegram_markdown(f"Error: {error}"),
            reply_to_message_id=reply_to_message_id
        ) 