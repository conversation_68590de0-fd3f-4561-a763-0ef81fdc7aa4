openapi: 3.1.0
info:
  title: QuePasa API Service
  description: API service for document management, file operations, AI services gateway, and LLM-as-a-Judge evaluation.
  version: 1.0.0

servers:
  - url: https://quepasa-api.dev.az.rezolve.com
    description: Production server
  - url: http://localhost:8000
    description: Dev server

tags:
  - name: Core
    description: Core API endpoints.
    x-displayOrder: 1
  - name: Documents
    description: Operations related to managing documents.
    x-displayOrder: 2
  - name: Files
    description: Operations related to managing files.
    x-displayOrder: 3
  - name: Batches
    description: Operations related to batch processing.
    x-displayOrder: 4
  - name: Telegram
    description: Operations related to Telegram integration.
    x-displayOrder: 5
  - name: Public
    description: Public file upload and download endpoints.
    x-displayOrder: 6
  - name: Gateway
    description: AI service gateway endpoints for embeddings, LLM, and Whisper.
    x-displayOrder: 7
  - name: Judgement
    description: LLM-as-a-Judge evaluation endpoints for AI assistant performance.
    x-displayOrder: 8

paths:
  /:
    get:
      summary: Welcome message
      description: Public endpoint - Welcome message
      operationId: root
      tags:
        - Core
      responses:
        '200':
          description: Welcome message
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Welcome to QuePasa API"

  /health:
    get:
      summary: Health check
      description: Public endpoint - Health check
      operationId: healthCheck
      tags:
        - Core
      responses:
        '200':
          description: Health status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"

  /docs:
    get:
      summary: API documentation
      description: Public endpoint - API documentation
      operationId: getDocs
      tags:
        - Core
      responses:
        '200':
          description: OpenAPI specification

  /data/v1/documents:
    get:
      summary: List all documents
      description: List all document IDs in all available domains.
      operationId: listAllDocuments
      tags:
        - Documents
      responses:
        '200':
          description: Operation accepted. Batch ID returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DomainListDetail'
        '500':
          description: Operation failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationFailedStatus'

  /data/v1/documents/{domain}:
    get:
      summary: List documents
      description: List all document IDs in the specified domain.
      operationId: listDocuments
      tags:
        - Documents
      parameters:
        - name: domain
          in: path
          required: true
          schema:
            type: string
          description: The domain name.
      responses:
        '200':
          description: Operation accepted. Batch ID returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DomainDetail'
        '500':
          description: Operation failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationFailedStatus'

    post:
      summary: Upsert documents
      description: |
        Insert new documents or update existing ones based on the ID.
      operationId: upsertDocuments
      tags:
        - Documents
      parameters:
        - name: domain
          in: path
          required: true
          schema:
            type: string
          description: The domain name.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              description: List of documents to upsert.
              items:
                $ref: '#/components/schemas/Document'
      responses:
        '200':
          description: Operation accepted. Batch ID returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatedBatchStatus'
        '500':
          description: Operation failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationFailedStatus'

    put:
      summary: Replace documents
      description: |
        Replace all documents in the specified domain with the provided documents.
      operationId: replaceDocuments
      tags:
        - Documents
      parameters:
        - name: domain
          in: path
          required: true
          schema:
            type: string
          description: The domain name.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              description: List of documents to replace.
              items:
                $ref: '#/components/schemas/Document'
      responses:
        '200':
          description: Operation accepted. Batch ID returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatedBatchStatus'
        '500':
          description: Operation failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationFailedStatus'

    delete:
      summary: Remove domain
      description: Remove all documents from the specified domain.
      operationId: removeDomain
      tags:
        - Documents
      parameters:
        - name: domain
          in: path
          required: true
          schema:
            type: string
          description: The domain name.
      responses:
        '200':
          description: Operation accepted. Batch ID returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatedBatchStatus'
        '500':
          description: Operation failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationFailedStatus'

  /data/v1/documents/{domain}/{id}:
    get:
      summary: Get document details
      description: Retrieve details of a document by its domain and ID.
      operationId: getDocument
      tags:
        - Documents
      parameters:
        - name: domain
          in: path
          required: true
          schema:
            type: string
            default: "default"
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Document details retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentDetail'
        '404':
          description: Document not found.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentNotFound'

    delete:
      summary: Remove document
      description: Remove a specific document by its domain and ID.
      operationId: removeDocument
      tags:
        - Documents
      parameters:
        - name: domain
          in: path
          required: true
          schema:
            type: string
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Operation accepted. Batch ID returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatedBatchStatus'
        '500':
          description: Operation failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationFailedStatus'

  /data/v1/files/{domain}:
    post:
      summary: Upload file
      description: |
        Upload and upsert files into the document system.

        Supported formats: txt, md, html, pdf, doc, docx, xls, xlsx, ppt, pptx
      operationId: uploadFile
      tags:
        - Files
      parameters:
        - name: domain
          in: path
          required: true
          schema:
            type: string
          description: The domain name.
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: The file to be uploaded.
                access:
                  type: string
                  description: Access control settings as JSON string.
                language:
                  type: string
                  description: Two-character language code (e.g., 'en').
                skip_indexing:
                  type: boolean
                  description: Whether to skip indexing.
                  default: false
              required:
                - file
      responses:
        '200':
          description: Operation accepted. Batch ID returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatedBatchStatus'
        '500':
          description: Operation failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationFailedStatus'

  /data/v1/urls/{domain}:
    post:
      summary: Process URLs
      description: |
        Upsert URLs into the document system.

        Supported formats: txt, md, html, pdf, doc, docx, xls, xlsx, ppt, pptx, Youtube transcripts (from Youtube videos), Telegram channels (with authentication)
      operationId: uploadUrls
      tags:
        - Files
      parameters:
        - name: domain
          in: path
          required: true
          schema:
            type: string
          description: The domain name.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                urls:
                  type: array
                  items:
                    type: string
                  description: List of URLs to process.
                access:
                  type: object
                  description: Access control settings.
                language:
                  type: string
                  description: Two-character language code (e.g., 'en').
                skip_indexing:
                  type: boolean
                  description: Whether to skip indexing.
                  default: false
              required:
                - urls
      responses:
        '200':
          description: Operation accepted. Batch ID returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatedBatchStatus'
        '500':
          description: Operation failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationFailedStatus'

  /data/v1/batches/{batch_id}:
    get:
      summary: Get batch status
      description: Retrieve the status of a batch using its batch ID.
      operationId: getBatchStatus
      tags:
        - Batches
      parameters:
        - name: batch_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Batch status retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchStatus'
        '404':
          description: Batch not found.

  /data/v1/batches/{batch_id}/relaunch:
    post:
      summary: Relaunch batch
      description: Relaunch a failed batch.
      operationId: relaunchBatch
      tags:
        - Batches
      parameters:
        - name: batch_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Batch relaunched successfully.

  /data/v1/telegram:
    patch:
      summary: Setup Telegram integration
      description: |
        Configure Telegram for notifications or integrations.
      operationId: setupTelegram
      tags:
        - Telegram
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: Telegram bot token.
                commands:
                  type: object
                  description: Telegram bot commands.
                  properties:
                    start:
                      type: object
                      description: Telegram /start command.
                      properties:
                        name:
                          type: string
                          description: Menu label.
                        message:
                          type: string
                          description: Message details.
                    ask:
                      type: object
                      description: Telegram /ask command.
                      properties:
                        name:
                          type: string
                          description: Menu label.
                        message:
                          type: string
                          description: Message details.
                domain:
                  type: string
                  description: (Optional) The name of a group of documents.
                kind:
                  type: string
                  description: (Experimental) Specifies the type of chunk. Can be "text" for raw text chunks, "summary" for chunks that are summaries of raw text, or "all" to include both types.
                  example: "text"
                llm:
                  type: string
                  description: (Optional) This is the model that will generate answers to questions based on the retrieved search results.
                prompt:
                  type: string
                  description: (Optional) The prompt used for RAG, with placeholders like {{LANGUAGE}} for the language in which the question was asked, and {{SOURCES}} for listing the relevant chunks.
                user_names:
                  type: array
                  items:
                    type: string
                  description: (Optional) Telegram account names allowed to access the bot.

      responses:
        '200':
          description: Telegram successfully set up.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TelegramStatus'
        '500':
          description: Operation failed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationFailedStatus'

  /data/v1/telegram/request-code:
    post:
      summary: Request verification code
      description: Request a verification code for Telegram integration.
      operationId: requestTelegramCode
      tags:
        - Telegram
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                phone:
                  type: string
                  description: Phone number in international format.
              required:
                - phone
      responses:
        '200':
          description: Verification code sent successfully.

  /data/v1/telegram/verify-code:
    post:
      summary: Verify code
      description: Verify the received code for Telegram integration.
      operationId: verifyTelegramCode
      tags:
        - Telegram
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                phone:
                  type: string
                  description: Phone number in international format.
                code:
                  type: string
                  description: Verification code received.
              required:
                - phone
                - code
      responses:
        '200':
          description: Code verified successfully.

  /data/v1/telegram/set-username-mappings:
    post:
      summary: Set username mappings
      description: Set mappings between Telegram usernames and internal user IDs.
      operationId: setUsernameMappings
      tags:
        - Telegram
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mappings:
                  type: object
                  description: Mapping of Telegram usernames to user IDs.
              required:
                - mappings
      responses:
        '200':
          description: Username mappings set successfully.

  /data/v1/upload:
    post:
      summary: Upload file and get public URL
      description: |
        Upload a file and get a public URL for accessing it without authentication.
        
        The file will be stored with a unique timestamped filename and can be accessed via the returned public URL.
      operationId: uploadFilePublic
      tags:
        - Public
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: The file to be uploaded.
                expires_in_hours:
                  type: integer
                  description: Number of hours until the URL expires - default 24.
                  default: 24
              required:
                - file
      responses:
        '200':
          description: File uploaded successfully. Public URL returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublicUrlResponse'
        '400':
          description: Bad request - invalid file or parameters.
        '500':
          description: Internal server error.

  /data/v1/download/{client_id}/{filename}/info:
    get:
      summary: Get public file information
      description: |
        Get file information and public URL as JSON without authentication.
        
        This endpoint allows clients to retrieve metadata about a publicly uploaded file.
      operationId: getFileInfoPublic
      tags:
        - Public
      parameters:
        - name: client_id
          in: path
          required: true
          schema:
            type: string
          description: Client identifier.
        - name: filename
          in: path
          required: true
          schema:
            type: string
          description: The unique filename (e.g., 20250804_122433_document.pdf).
        - name: expires_in_hours
          in: query
          required: false
          schema:
            type: integer
            default: 24
          description: Number of hours until the URL expires.
      responses:
        '200':
          description: File information retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileInfoResponse'
        '404':
          description: File not found.
        '500':
          description: Internal server error.

  /data/v1/download/{client_id}/{filename}:
    get:
      summary: Download public file content
      description: |
        Download file content directly without authentication.
        
        This endpoint streams the file content for public access.
      operationId: downloadFileContentPublic
      tags:
        - Public
      parameters:
        - name: client_id
          in: path
          required: true
          schema:
            type: string
          description: Client identifier.
        - name: filename
          in: path
          required: true
          schema:
            type: string
          description: The unique filename (e.g., 20250804_122433_document.pdf).
      responses:
        '200':
          description: File content streamed successfully.
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '404':
          description: File not found.
        '500':
          description: Internal server error.

  /gateway/v1/embedding:
    post:
      summary: Get embeddings
      description: |
        Get embeddings for a text using various AI providers.
        
        This endpoint provides access to different embedding models through a unified interface.
      operationId: getEmbedding
      tags:
        - Gateway
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmbeddingRequest'
      responses:
        '200':
          description: Embeddings generated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmbeddingResponse'
        '500':
          description: Internal server error.

  /gateway/v1/llm:
    post:
      summary: Get LLM response
      description: |
        Get response from various Large Language Models.
        
        This endpoint provides access to different LLM providers through a unified interface with optional caching.
      operationId: getLLMResponse
      tags:
        - Gateway
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LLMRequest'
      responses:
        '200':
          description: LLM response generated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LLMResponse'
        '500':
          description: Internal server error.

  /gateway/v1/whisper:
    post:
      summary: Get audio transcription
      description: |
        Get audio transcription using Whisper models.
        
        This endpoint provides access to speech-to-text transcription services.
      operationId: getWhisperTranscription
      tags:
        - Gateway
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Audio file to transcribe.
                provider:
                  type: string
                  description: Whisper provider to use.
                model:
                  type: string
                  description: Whisper model to use.
                meta:
                  type: string
                  description: Additional metadata as JSON string.
                  default: "{}"
              required:
                - file
                - provider
                - model
      responses:
        '200':
          description: Transcription completed successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WhisperResponse'
        '500':
          description: Internal server error.

  /judgement/v1/scores:
    post:
      summary: Evaluate AI assistant performance with scores
      description: |
        Evaluate an AI assistant's performance using a 5-part assessment methodology.
        
        This endpoint uses LLM-as-a-Judge to score the assistant across intent understanding, role adherence, product recommendation performance, fallback handling, and knowledge retention.
      operationId: getJudgementScores
      tags:
        - Judgement
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JudgementScoresRequest'
      responses:
        '200':
          description: Evaluation completed successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JudgementResponse'
        '500':
          description: Internal server error.

  /judgement/v1/side-by-side:
    post:
      summary: Compare two AI assistant dialogs
      description: |
        Compare two AI assistant dialogs side-by-side across 5 key performance metrics.
        
        This endpoint evaluates which dialog performs better in each category and determines an overall winner.
      operationId: getJudgementSideBySide
      tags:
        - Judgement
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JudgementSideBySideRequest'
      responses:
        '200':
          description: Comparison completed successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JudgementResponse'
        '500':
          description: Internal server error.

  /judgement/v1/golden-match:
    post:
      summary: Compare dialog against golden standard
      description: |
        Compare a current AI assistant dialog against a golden standard reference dialog.
        
        This endpoint uses binary criteria to assess whether the assistant's behavior was appropriate.
      operationId: getJudgementGoldenMatch
      tags:
        - Judgement
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JudgementGoldenMatchRequest'
      responses:
        '200':
          description: Evaluation completed successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JudgementResponse'
        '500':
          description: Internal server error.

  /judgement/v1/answer:
    post:
      summary: Legacy judgement endpoint (deprecated)
      description: |
        Legacy judgement endpoint that automatically chooses between scores and side-by-side evaluation.
        
        This endpoint is deprecated and will be removed in future versions.
      operationId: getJudgement
      tags:
        - Judgement
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JudgementRequest'
      responses:
        '200':
          description: Evaluation completed successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JudgementResponse'
        '500':
          description: Internal server error.

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: Opaque

  schemas:
    # Core schemas
    CreatedBatchStatus:
      type: object
      properties:
        status:
          type: string
          description: Status of the batch (e.g., "uploaded", "backlog", "in_progress", "done").
        data:
          type: object
          properties:
            batch_id:
              type: string
              description: Batch ID
            processed_ids:
              type: array
              items:
                type: string
      required:
        - status
        - data

    BatchStatus:
      type: object
      properties:
        status:
          type: string
          description: |
            Status of the batch (e.g., "uploaded", "backlog", "in_progress", "done").
            Possible values:
            - Batch state: uploaded
            - Batch state: backlog
            - Batch state: in_progress
            - Batch state: done
        data:
          type: object
          properties:
            domain:
              type: string
              description: The name of a group of documents.
            processed_ids:
              type: array
              items:
                type: string
      required:
        - status

    TelegramStatus:
      type: object
      properties:
        status:
          type: string
          description: Status of the operation.
      required:
        - status

    Document:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the document.
        url:
          type: string
          description: Original URL of the document.
        text:
          type: string
          description: Raw text content of the document.
        html:
          type: string
          description: Raw HTML content of the document.
        markdown:
          type: string
          description: Raw Markdown content of the document.
        pages:
          type: array
          items:
            type: object
            properties:
              text:
                type: string
                description: The content of the chunk.
              language:
                type: string
                description: Two-character language code (e.g., 'en').
            required:
              - text
              - language
        language:
          type: string
          description: Two-character language code (e.g., 'en').
        title:
          type: string
          description: Optional title of the document.
        keywords:
          type: string
          description: Optional keywords for search optimization.
        created_at:
          type: string
          format: date-time
          description: Creation date in ISO 8601 format.
        updated_at:
          type: string
          format: date-time
          description: Update date in ISO 8601 format.
      required:
        - id
        - url

    DomainDetail:
      type: object
      properties:
        status:
          type: string
          description: Status.
        data:
          $ref: '#/components/schemas/DomainDataDetail'
      required:
        - status
        - data

    DomainListDetail:
      type: object
      properties:
        status:
          type: string
          description: Status.
        data:
          type: array
          items:
            $ref: '#/components/schemas/DomainDataDetail'
      required:
        - status
        - data

    DomainDataDetail:
      type: object
      properties:
        domain:
          type: string
          description: The name of a group of documents.
        processed_ids:
          type: array
          items:
            type: string
      required:
        - domain
        - processed_ids

    DocumentDetail:
      type: object
      properties:
        status:
          type: string
          description: Status.
        data:
          type: object
          properties:
            id:
              type: string
              description: Unique identifier for the document.
            url:
              type: string
              description: Original URL of the document.
            domain:
              type: string
              description: The name of a group of documents. Defaults to "default".
            title:
              type: string
              description: Optional keywords for search optimization.
            keywords:
              type: string
              description: Optional keywords for search optimization.
            pages:
              type: array
              items:
                type: object
                properties:
                  text:
                    type: string
                    description: The content of the chunk.
                  language:
                    type: string
                    description: Two-character language code (e.g., 'en').
                  keywords:
                    type: string
                    description: Optional keywords for search optimization.
                required:
                  - text
                  - language
            languages:
              type: array
              items:
                type: string
                description: Two-character language code (e.g., 'en').
            created_at:
              type: string
              format: date-time
              description: Creation date in ISO 8601 format.
            updated_at:
              type: string
              format: date-time
              description: Update date in ISO 8601 format.
          required:
            - id
            - url
            - domain
            - title
            - keywords
            - pages
            - languages
            - created_at
            - updated_at
      required:
        - status
        - data

    DocumentNotFound:
      type: object
      properties:
        status:
          type: string
          description: Status.
      required:
        - status

    OperationFailedStatus:
      type: object
      properties:
        status:
          type: string
          description: Status.
      required:
        - status

    # Public file schemas
    PublicUrlResponse:
      type: object
      properties:
        public_url:
          type: string
          description: Public URL for accessing the uploaded file.
        expires_in_hours:
          type: integer
          description: Number of hours until the URL expires.
        file_size:
          type: integer
          description: Size of the uploaded file in bytes.
        content_type:
          type: string
          description: MIME type of the uploaded file.
      required:
        - public_url
        - expires_in_hours
        - file_size
        - content_type

    FileInfoResponse:
      type: object
      properties:
        filename:
          type: string
          description: The unique filename.
        public_url:
          type: string
          description: Public URL for accessing the file.
        expires_in_hours:
          type: integer
          description: Number of hours until the URL expires.
        file_size:
          type: integer
          description: Size of the file in bytes.
        content_type:
          type: string
          description: MIME type of the file.
        last_modified:
          type: string
          description: Last modification timestamp.
        exists:
          type: boolean
          description: Whether the file exists.
      required:
        - filename
        - public_url
        - exists

    # Gateway schemas
    EmbeddingRequest:
      type: object
      properties:
        provider:
          type: string
          description: Embedding provider to use.
        model:
          type: string
          description: Embedding model to use.
        text:
          type: string
          description: Text to generate embeddings for.
        bypass_cache:
          type: boolean
          description: Whether to bypass cache.
          default: false
      required:
        - provider
        - model
        - text

    EmbeddingResponse:
      type: object
      properties:
        embedding:
          type: array
          items:
            type: number
          description: The generated embedding vector.
        cached:
          type: boolean
          description: Whether the result was retrieved from cache.
          default: false
      required:
        - embedding

    LLMRequest:
      type: object
      properties:
        provider:
          type: string
          description: LLM provider to use.
        model:
          type: string
          description: LLM model to use.
        messages:
          type: array
          items:
            type: object
            properties:
              role:
                type: string
                description: Role of the message sender.
              content:
                type: string
                description: Content of the message.
            required:
              - role
              - content
          description: List of messages for the conversation.
        max_tokens:
          type: integer
          description: Maximum number of tokens to generate.
        json_mode:
          type: boolean
          description: Whether to force JSON output.
          default: false
        use_cache:
          type: boolean
          description: Whether to use cached responses.
          default: true
      required:
        - provider
        - model
        - messages

    LLMResponse:
      type: object
      properties:
        text:
          type: string
          description: The generated text response.
        cached:
          type: boolean
          description: Whether the result was retrieved from cache.
          default: false
      required:
        - text

    WhisperResponse:
      type: object
      properties:
        segments:
          type: object
          description: The transcription segments.
        cached:
          type: boolean
          description: Whether the result was retrieved from cache.
          default: false
      required:
        - segments

    # Judgement schemas
    JudgementBaseRequest:
      type: object
      properties:
        llm:
          type: string
          description: LLM provider and model to use for evaluation.
          default: "openai:gpt-4o"
        prompt:
          type: string
          description: Custom evaluation prompt.
        max_tokens:
          type: integer
          description: Maximum number of tokens to generate.
      required:
        - llm

    JudgementScoresRequest:
      allOf:
        - $ref: '#/components/schemas/JudgementBaseRequest'
        - type: object
          properties:
            messages:
              type: array
              items:
                type: object
                properties:
                  role:
                    type: string
                    description: Role of the message sender.
                  content:
                    type: string
                    description: Content of the message.
                required:
                  - role
                  - content
              description: List of messages for the conversation to evaluate.
          required:
            - messages

    JudgementSideBySideRequest:
      allOf:
        - $ref: '#/components/schemas/JudgementBaseRequest'
        - type: object
          properties:
            messages:
              type: array
              items:
                type: object
                properties:
                  role:
                    type: string
                    description: Role of the message sender.
                  content:
                    type: string
                    description: Content of the message.
                required:
                  - role
                  - content
              description: List of messages for the first dialog.
            golden_messages:
              type: array
              items:
                type: object
                properties:
                  role:
                    type: string
                    description: Role of the message sender.
                  content:
                    type: string
                    description: Content of the message.
                required:
                  - role
                  - content
              description: List of messages for the second dialog to compare against.
          required:
            - messages
            - golden_messages

    JudgementGoldenMatchRequest:
      allOf:
        - $ref: '#/components/schemas/JudgementBaseRequest'
        - type: object
          properties:
            messages:
              type: array
              items:
                type: object
                properties:
                  role:
                    type: string
                    description: Role of the message sender.
                  content:
                    type: string
                    description: Content of the message.
                required:
                  - role
                  - content
              description: List of messages for the current dialog.
            golden_messages:
              type: array
              items:
                type: object
                properties:
                  role:
                    type: string
                    description: Role of the message sender.
                  content:
                    type: string
                    description: Content of the message.
                required:
                  - role
                  - content
              description: List of messages for the golden standard reference dialog.
          required:
            - messages
            - golden_messages

    JudgementRequest:
      allOf:
        - $ref: '#/components/schemas/JudgementBaseRequest'
        - type: object
          properties:
            messages:
              type: array
              items:
                type: object
                properties:
                  role:
                    type: string
                    description: Role of the message sender.
                  content:
                    type: string
                    description: Content of the message.
                required:
                  - role
                  - content
              description: List of messages for the conversation to evaluate.
            golden_messages:
              type: array
              items:
                type: object
                properties:
                  role:
                    type: string
                    description: Role of the message sender.
                  content:
                    type: string
                    description: Content of the message.
                required:
                  - role
                  - content
              description: List of messages for the golden standard reference dialog.
              default: []
          required:
            - messages

    JudgementResponse:
      type: object
      properties:
        status:
          type: string
          description: Status of the evaluation.
          example: "OK"
        response:
          type: object
          description: The evaluation results.
      required:
        - status
        - response

security:
  - bearerAuth: [] 