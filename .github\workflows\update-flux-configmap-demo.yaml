name: Update Flux demo ConfigMap

on:
  workflow_dispatch: # Allow manual triggering

jobs:
  update-configmap:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout quepasa repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Checkout flux repository
        uses: actions/checkout@v4
        with:
          repository: rezolved/fluxcd
          token: ${{ secrets.FLUX_REPO_TOKEN }}
          path: flux-repo
          fetch-depth: 1

      - name: Read configuration files
        id: read-config
        run: |
          # Read default.py and encode it for YAML
          echo "Reading default.py..."
          DEFAULT_PY_CONTENT=$(cat configuration/main/default.py)
          
          # Read cli_create_index.py and encode it for YAML
          echo "Reading cli_create_index.py..."
          CLI_CREATE_INDEX_CONTENT=$(cat configuration/main/cli_create_index.py)
          
          # Save content to files for the next step
          cat configuration/main/default.py > /tmp/default.py
          cat configuration/main/cli_create_index.py > /tmp/cli_create_index.py

      - name: Update ConfigMap YAML
        run: |
          CONFIGMAP_PATH="flux-repo/clusters/AKS-prod-k8s/releases/eu-demo/quepasa/configmap.yaml"
          
          # Create the updated ConfigMap
          cat > "$CONFIGMAP_PATH" << 'EOF'
          apiVersion: v1
          kind: ConfigMap
          metadata:
            name: quepasa-config
            namespace: eu-demo
          data:
            default.py: |
          EOF
          
          # Add default.py content with proper indentation
          sed 's/^/    /' /tmp/default.py >> "$CONFIGMAP_PATH"

          # Add cli_create_index.py section (with newline between keys!)
          echo "" >> "$CONFIGMAP_PATH"
          echo "  cli_create_index.py: |" >> "$CONFIGMAP_PATH"
          sed 's/^/    /' /tmp/cli_create_index.py >> "$CONFIGMAP_PATH"

      - name: Commit and push changes
        run: |
          cd flux-repo
          
          # Configure git
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
          # Check if there are changes
          if git diff --quiet; then
            echo "No changes to commit"
            exit 0
          fi
          
          # Add and commit changes
          git add clusters/AKS-prod-k8s/releases/eu-demo/quepasa/configmap.yaml
          git commit -m "Update quepasa ConfigMap from configuration changes
          
          Updated from quepasa repository commit: ${{ github.sha }}
          Triggered by: ${{ github.event_name }}
          Files changed: ${{ github.event.head_commit.modified || 'manual trigger' }}"
          
          # Push changes
          git push

      - name: Summary
        run: |
          echo "✅ ConfigMap successfully updated in flux repository"
          echo "📝 Updated files:"
          echo "  - default.py ($(wc -l < /tmp/default.py) lines)"
          echo "  - cli_create_index.py ($(wc -l < /tmp/cli_create_index.py) lines)"
          echo "🔄 Changes will be applied by Flux CD automatically" 
