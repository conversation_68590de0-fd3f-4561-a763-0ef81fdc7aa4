import os
import math
import tempfile
import subprocess
from typing import Dict, Any, List

from src.lib.whisper_utils import get_cached_segments
from src.lib.whisper.providers import Whisper<PERSON>rovider
from src.lib.whisper.base import WhisperModelType
from src.lib.gateway import get_segments_from_gateway

from .base import BaseProcessor
from ..exceptions import ProcessorError
from ...config import VS_CHUNK_SIZE

# Get API key for gateway
QUEPASA_LLM_GATEWAY_API_KEY = os.environ.get("QUEPASA_LLM_GATEWAY_API_KEY")

class AudioProcessor(BaseProcessor):
    """Processor for audio files to extract transcription"""
    
    def __init__(self):
        """Initialize AudioProcessor."""
        super().__init__()
        
    def _process_impl(self, content: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process audio file and extract transcription
        
        Args:
            content: Audio file content
            meta: Metadata about the audio file
            
        Returns:
            Dictionary containing extracted transcription
        """
        # Validate metadata
        error = self.validate_meta(meta, ['filename', 'extension'])
        if error:
            raise ProcessorError(error)
            
        # Check if extension is supported
        extension = meta['extension'].lower()
        supported_extensions = ['.mp3', '.wav', '.m4a', '.ogg', '.flac', '.aac']
        if extension not in supported_extensions:
            raise ProcessorError(f"Unsupported audio file type: {extension}")
            
        try:
            # For operations that require file path (ffprobe), we still need a temp file
            with tempfile.NamedTemporaryFile(suffix=extension, delete=False) as temp_file:
                temp_file.write(content)
                temp_path = temp_file.name
                
            # Get audio duration using ffprobe
            duration = self._get_audio_duration(temp_path)
            self.logger.info(f"Audio duration: {duration} seconds")
            
            # Process audio file
            if duration > 600:  # More than 10 minutes
                transcription = self._process_long_audio(temp_path, meta)
            else:
                # For short audio, we can avoid temp file for the actual transcription
                transcription = self._process_audio_from_bytes(content, meta)
                
            # Clean up temporary file
            os.unlink(temp_path)
            
            # Format results
            return {
                'chunks': self._format_transcription(transcription, meta),
                'title': meta.get('filename', ''),
                'filename': meta.get('filename', '')
            }
            
        except Exception as e:
            # Clean up temporary file in case of error
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.unlink(temp_path)
            raise ProcessorError(f"Error processing audio file: {str(e)}")
    
    def _process_audio_from_bytes(self, audio_data: bytes, meta: Dict[str, Any]) -> Dict:
        """Process audio bytes directly without creating a temporary file."""
        try:
            self.logger.info(f"Transcribing audio file: {meta.get('filename')}")
            
            # Get language from metadata if available
            language = meta.get('language', None)
            self.logger.info(f"Using language: {language}")
            
            # Check if we should use gateway
            if QUEPASA_LLM_GATEWAY_API_KEY:
                self.logger.info("Using gateway service for transcription")
                output = get_segments_from_gateway(WhisperProvider.REPLICATE, WhisperModelType.DIARIZATION, audio_data, meta)
            else:
                self.logger.info("Using local cached service for transcription")
                # Use get_cached_segments from whisper_utils
                output = get_cached_segments(WhisperProvider.REPLICATE, WhisperModelType.DIARIZATION, audio_data, meta)
                
            self.logger.info(f"Transcription complete")
            
            # Format result
            if not output:
                raise ProcessorError("No transcription output received")
                
            return output
                
        except Exception as e:
            self.logger.error(f"Error transcribing audio: {str(e)}")
            raise ProcessorError(f"Error transcribing audio: {str(e)}")
    
    def _process_audio(self, audio_path: str, meta: Dict[str, Any]) -> Dict:
        """Process audio file and extract transcription using whisper-diarization model."""
        try:
            self.logger.info(f"Transcribing audio file from path: {meta.get('filename')}")
            
            with open(audio_path, 'rb') as audio_file:
                audio_data = audio_file.read()
                return self._process_audio_from_bytes(audio_data, meta)
                
        except Exception as e:
            self.logger.error(f"Error transcribing audio: {str(e)}")
            raise ProcessorError(f"Error transcribing audio: {str(e)}")
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """Get duration of audio file in seconds."""
        try:
            # Get duration using ffprobe
            probe_cmd = [
                'ffprobe', 
                '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                audio_path
            ]
            duration = float(subprocess.check_output(probe_cmd).decode().strip())
            return duration
        except Exception as e:
            self.logger.error(f"Error getting audio duration: {str(e)}")
            return 0.0
    
    def _process_long_audio(self, audio_path: str, meta: Dict[str, Any]) -> Dict:
        """Process long audio file by splitting into chunks."""
        try:
            # Create temp directory for chunks
            with tempfile.TemporaryDirectory() as temp_dir:
                # Split audio into 10-minute chunks with 10-second overlap
                chunks = self._split_audio(audio_path, temp_dir, chunk_duration=600, overlap=10)
                
                # Process each chunk
                results = []
                for chunk in chunks:
                    chunk_result = self._process_audio(chunk['file'], meta)
                    # Add start time to segments
                    if 'segments' in chunk_result:
                        for segment in chunk_result['segments']:
                            segment['start'] += chunk['start']
                            segment['end'] += chunk['start']
                    results.append(chunk_result)
                
                # Combine transcriptions
                combined = self._combine_transcriptions(results)
                return combined
                
        except Exception as e:
            self.logger.error(f"Error processing long audio: {str(e)}")
            raise ProcessorError(f"Error processing long audio: {str(e)}")
    
    def _split_audio(self, audio_path: str, temp_dir: str, chunk_duration: int = 600, overlap: int = 10) -> List[Dict]:
        """Split audio file into chunks with overlap using ffmpeg."""
        try:
            duration = self._get_audio_duration(audio_path)
            
            self.logger.info(f"Splitting audio into {chunk_duration}s chunks with {overlap}s overlap")
            
            chunks = []
            current_position = 0
            
            while current_position < duration:
                chunk_end = min(current_position + chunk_duration, duration)
                
                # Break if we can't make progress
                if chunk_end - current_position < 10:  # Less than 10 seconds remaining
                    break
                
                # Create temp file for this chunk
                chunk_path = os.path.join(temp_dir, f"chunk_{current_position}.mp3")
                
                # Use ffmpeg to extract chunk
                cmd = [
                    'ffmpeg',
                    '-y',  # Overwrite output
                    '-i', audio_path,
                    '-ss', str(current_position),
                    '-t', str(chunk_end - current_position),
                    '-ac', '1',  # Convert to mono
                    '-ar', '16000',  # Sample rate
                    '-ab', '64k',  # Bitrate
                    chunk_path
                ]
                
                subprocess.run(cmd, capture_output=True, check=True)
                
                chunks.append({
                    'file': chunk_path,
                    'start': current_position,
                    'end': chunk_end
                })
                
                # Advance position by chunk_duration - overlap
                current_position += (chunk_duration - overlap)
                # Ensure we don't go backwards
                current_position = min(current_position, duration)
            
            self.logger.info(f"Created {len(chunks)} audio chunks")
            return chunks
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"FFmpeg error: {e.stderr.decode() if e.stderr else str(e)}")
            raise ProcessorError(f"Error splitting audio: {str(e)}")
        except Exception as e:
            self.logger.error(f"Error splitting audio: {str(e)}")
            raise ProcessorError(f"Error splitting audio: {str(e)}")
    
    def _combine_transcriptions(self, results: List[Dict]) -> Dict:
        """Combine multiple transcription results into one."""
        combined = {
            'segments': [],
            'text': '',
        }
        
        for result in results:
            if 'segments' in result and result['segments']:
                combined['segments'].extend(result['segments'])
        
        # Sort segments by start time
        combined['segments'].sort(key=lambda x: x['start'])
        
        # Combine text
        combined['text'] = ' '.join(segment['text'] for segment in combined['segments'])
        
        return combined
    
    def _format_transcription(self, transcription: Dict, meta: Dict[str, Any]) -> List[Dict]:
        """Format transcription for API storage format using text size-based chunking."""
        chunks = []
        
        if 'segments' in transcription and transcription['segments']:
            segments = transcription['segments']
            
            # Initialize chunking variables
            text = ""
            start = 0.0
            duration = 0.0
            last_start = None
            last_duration = None
            last_speaker = None
            overlap_text = ""  # Text to be used for overlap
            
            for segment in segments:
                speaker = segment.get('speaker', 'UNKNOWN_SPEAKER')
                current_speaker = speaker
                
                if text == "":
                    start = segment.get('start', 0.0) or 0.0
                    duration = 0.0
                    # Add overlap text from previous chunk if available
                    if overlap_text:
                        text = overlap_text + "\n"

                # Safely handle potential None values
                segment_start = segment.get('start', 0.0) or 0.0
                segment_duration = segment.get('duration', 0.0) or 0.0
                
                duration += (
                    segment_duration +
                    (segment_start - last_start if last_start is not None else 0.0) -
                    (last_duration if last_duration is not None else 0.0)
                )

                # Add segment text to current chunk with speaker prefix
                segment_text = f"[{speaker}]: " if speaker != last_speaker else ""
                segment_text += f"{segment.get('text', '')}"
                text += segment_text + "\n"

                last_speaker = current_speaker

                if len(text) > VS_CHUNK_SIZE:
                    overlap_size = math.ceil(VS_CHUNK_SIZE * 0.2)

                    # Store the last part of this chunk for overlap with the next chunk
                    if len(text) > overlap_size:
                        # Find a good breaking point near overlap_size
                        # Try to break at a newline if possible
                        overlap_text = text[-overlap_size:]

                        is_trimmed = False
                        nl_pos = overlap_text.find('\n')
                        if nl_pos > 0:
                            # If newline found in the first half of overlap, adjust overlap text
                            if nl_pos < overlap_size // 2:
                                overlap_text = overlap_text[nl_pos + 1:]
                                is_trimmed = True

                        # If no newline found, try to break at a space
                        if not is_trimmed:
                            nl_pos = overlap_text.find(' ')
                            if nl_pos > 0:
                                overlap_text = overlap_text[nl_pos + 1:]

                    else:
                        overlap_text = text
                    
                    # Safely get start and end times with defaults
                    segment_start = segment.get('start', 0.0) or 0.0
                    segment_duration = segment.get('duration', 0.0) or 0.0
                    
                    chunks.append({
                        'text': text.strip(),
                        'position': f"timestamp {self._format_timestamp(start)} - {self._format_timestamp(segment_start + segment_duration)}"
                    })
                    text = ""
                    last_speaker = None

                last_start = segment.get('start', 0.0) or 0.0
                last_duration = segment.get('duration', 0.0) or 0.0
            
            # Add the final chunk if there's any text left
            if text != "":
                start_time = self._format_timestamp(start)
                end_time = self._format_timestamp(last_start + last_duration)
                chunks.append({
                    'text': text.strip(),
                    'position': f"timestamp {start_time} - {end_time}",
                })
        
        elif 'text' in transcription and transcription['text']:
            # If no segments but we have text, create a single chunk
            chunks.append({
                'text': transcription['text'],
                'position': 'full transcription',
            })
        
        return chunks
        
    def _format_timestamp(self, seconds: float) -> str:
        """Format timestamp in HH:MM:SS.mmm format."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}" 