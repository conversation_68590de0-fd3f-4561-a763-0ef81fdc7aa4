from __future__ import annotations

import os
from typing import List, Dict, Any, Optional, Tuple, Generator
import replicate

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>og<PERSON>
from .base import BaseLL<PERSON>
from .cache import LLMCacheMixin
from .providers import LL<PERSON>rovider

logger = QuepasaLogger().get_instance(__name__)

class ReplicateLLM(BaseLLM, LLMCacheMixin):
    """Replicate LLM provider.
    
    This class provides access to various language models hosted on
    Replicate's platform.
    """
    
    def __init__(self):
        """Initialize Replicate LLM provider."""
        super().__init__()

        # Check for API key
        self.api_key = os.getenv('REPLICATE_API_KEY')
        if not self.api_key:
            raise ValueError("REPLICATE_API_KEY environment variable is not set")
        self.client = replicate.Client(api_token=self.api_key)

    @property
    def provider(self) -> LLMProvider:
        return LLMProvider.REPLICATE

    def _prepare_prompts(self, prompt_list: List[Dict[str, str]]) -> <PERSON><PERSON>[str, Optional[str]]:
        """Prepare prompts for Replicate API.
        
        Replicate's API expects prompts in a different format from other providers.
        This method converts the standard format to Replicate's format.
        
        Args:
            prompt_list: List of prompts with 'role' and 'content'
            
        Returns:
            Tuple of (message_prompt, system_prompt)
        """
        system_prompts = []
        message_prompts = []
        
        for prompt in prompt_list:
            if prompt['role'] == "system":
                system_prompts.append(prompt['content'].strip())
            else:
                message_prompts.append(prompt['content'].strip())
                
        return "\n".join(message_prompts), "\n".join(system_prompts) if system_prompts else None

    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a completion from Replicate API.
        
        Args:
            model_version: Model version (e.g., 'replicate:meta/llama-2-70b-chat')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format
            
        Returns:
            Generated response text
        """
        try:
            message_prompt, system_prompt = self._prepare_prompts(prompt_list)
            
            input_params = {
                "top_p": 1,
                "prompt": message_prompt,
                "temperature": 0.0,
                "repetition_penalty": 1,
                "min_new_tokens": answer_prompt_size
            }
            
            if system_prompt:
                input_params['system_prompt'] = system_prompt

            # Replicate does not support JSON mode
            # if json_mode:
            #     input_params['format'] = 'json'

            output = self.client.run(model_version, input=input_params)
            return output if isinstance(output, str) else "".join(output)
            
        except Exception as e:
            logger.error(f"Failed to get answer from Replicate: {str(e)}")
            return ""

    def get_streaming_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> Generator[str, None, None]:
        """Get a streaming completion from Replicate API.
        
        Args:
            model_version: Model version (e.g., 'replicate:meta/llama-3-70b-instruct')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format (not supported)
            
        Yields:
            Generated response chunks
        """
        try:
            message_prompt, system_prompt = self._prepare_prompts(prompt_list)
            
            input_params = {
                "prompt": message_prompt,
                "temperature": 0.0,
                "repetition_penalty": 1,
                "min_new_tokens": answer_prompt_size
            }
            
            if system_prompt:
                input_params['system_prompt'] = system_prompt

            # Use Replicate's streaming API
            for chunk in self.client.stream(model_version, input=input_params):
                if chunk:  # Skip empty chunks
                    yield chunk
                    
        except Exception as e:
            logger.error(f"Failed to get streaming answer from Replicate: {str(e)}")
            yield ""
