name: "Run Tests"

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    services:
      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch:8.12.2
        env:
          discovery.type: single-node
          xpack.security.enabled: false
          xpack.security.enrollment.enabled: false
          xpack.security.http.ssl.enabled: false
          xpack.security.transport.ssl.enabled: false
          ES_JAVA_OPTS: "-Xms512m -Xmx512m"
          cluster.routing.allocation.disk.watermark.low: 85%
          cluster.routing.allocation.disk.watermark.high: 90%
          cluster.routing.allocation.disk.watermark.flood_stage: 95%
        ports:
          - 9200:9200
        options: >-
          --health-cmd "curl -f http://localhost:9200/_cluster/health || exit 1"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 10

      minio:
        image: bitnami/minio:2024
        env:
          MINIO_ROOT_USER: minioadmin
          MINIO_ROOT_PASSWORD: minioadmin
          MINIO_DEFAULT_BUCKETS: quepasa-files
        ports:
          - 9000:9000
          - 9001:9001
        options: >-
          --health-cmd "curl -f http://localhost:9000/minio/health/live || exit 1"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"


    - name: Setup uv
      uses: astral-sh/setup-uv@v3

    - name: Get uv cache dir
      id: uv-cache
      run: echo "dir=$(uv cache dir)" >> $GITHUB_OUTPUT

    - name: Cache uv
      uses: actions/cache@v4
      with:
        path: ${{ steps.uv-cache.outputs.dir }}
        key: ${{ runner.os }}-uv-${{ hashFiles('requirements.txt', 'quepasa/api/requirements.txt', 'quepasa/crawler/requirements.txt', 'quepasa/data_processor/requirements.txt', 'quepasa/indexer/requirements.txt', 'quepasa/searcher/requirements.txt', 'tests/requirements-test.txt', 'setup.py') }}
        restore-keys: |
          ${{ runner.os }}-uv-

    - name: Install dependencies (uv)
      run: |
        uv pip install --system \
          -r requirements.txt \
          -r quepasa/api/requirements.txt \
          -r quepasa/crawler/requirements.txt \
          -r quepasa/data_processor/requirements.txt \
          -r quepasa/indexer/requirements.txt \
          -r quepasa/searcher/requirements.txt \
          -r tests/requirements-test.txt
        
        # Install the package with dev extras for testing
        uv pip install --system -e .[test]

    - name: Run tests (skip slow)
      env:
        OPENAI_API_KEY: test-openai
        ASK_EMBEDDING_API_KEY: test-askrobot
        NEBIUS_API_KEY: test-nebius
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        CELERY_BROKER_URL: redis://localhost:6379/0
        CELERY_RESULT_BACKEND: redis://localhost:6379/0
        ELASTICSEARCH_HOST: localhost
        ELASTICSEARCH_PORT: 9200
        ELASTICSEARCH_USERNAME: elastic
        ELASTICSEARCH_PASSWORD: elastic123
        MINIO_HOST: localhost
        MINIO_PORT: 9000
        MINIO_ACCESS_KEY: minioadmin
        MINIO_SECRET_KEY: minioadmin
        MINIO_BUCKET_NAME: quepasa-files
      run: |
        pytest -q -m "not slow"