from typing import Dict, Any, <PERSON>, Union, Tuple, Optional
from whoosh import qparser as whoosh_qparser
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.response import Reference
from quepasa.searcher.models.spd_result import SPDSearchResult
import re

from src.lib.constants import (
    SOURCE_AGENTIC,
    ANSWER_INTENT_GENERAL,
    SOURCE_LIVEWIKI
)
from src.lib.llm.providers import LLMProvider
from src.lib.logger import QuepasaLogger

from .search_extensions import SearchExtensionsConfig, QuepasaFuzzyTerm
from .telegram_config import TelegramConfig

from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.web import WebSearchResult

# Logger
logger = QuepasaLogger().get_instance(__name__)

class AnswerConfig(SearchExtensionsConfig, TelegramConfig):
    """Base configuration for answer generation and formatting."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
        
    def get_answer_legends(self, question: str) -> List[str]:
        """Get relevant legends for a question.
        
        Previously: get_answer_legends()
        
        Args:
            question: Question text to find legends for
            
        Returns:
            List of relevant legend strings
        """
        logger.info(f"Getting answer legends for question: {question}")

        results = []
        if not self._get_whoosh_index_legends():
            logger.info("No whoosh index legend found")
            return results
            
        # Get expanded question with typos
        expanded_question = " ".join([question] + self._find_spelling_variations(question))
        
        with self._get_whoosh_index_legends().searcher() as searcher:
            try:
                query_lc = re.sub(r'\W+', ' ', expanded_question.lower()).strip()
                whoosh_query = whoosh_qparser.QueryParser(
                    "keywords",
                    schema=self._get_whoosh_index_legends().schema,
                    termclass=QuepasaFuzzyTerm,
                    group=whoosh_qparser.OrGroup.factory(0.9)
                ).parse(query_lc)
                
                for hit in searcher.search(whoosh_query):
                    if self._should_add_expansion(hit, query_lc):
                        results.append(hit['legend'])
                        
            except Exception as e:
                logger.error(f"Error getting answer legends: {e}")
                
        return results
        
    def get_legend_list(self) -> List[Dict[str, Any]]:
        """Get list of legend definitions.
        
        Previously: get_legend_list()
        
        Returns:
            List of legend items with keywords and text

        Example:
        [
            {
                "keywords": "keyword1 keyword2 keyword3",
                "use_all_keywords": True,
                "legend": "Legend text"
            }
        ]
        """
        return []
                                
    def is_no_answer_response(self, source: str, answer: str, references: List[Dict[str, Any]]) -> bool:
        """Check if response indicates no answer was found.
        
        Previously: is_dont_know_reply()
        
        Args:
            source: Source of the answer
            answer: Answer text
            references: List of references
            
        Returns:
            True if response indicates no answer
        """
        # Override to implement custom no-answer detection
        return False
        
    def format_context_chunk(self, source: str, source_index: int, document: Union[Reference, QuepasaDocument, WebSearchResult, SPDSearchResult]) -> str:
        """Format context chunks for inclusion in LLM prompt.
        
        Previously: get_prompt_sources()
        
        Args:
            source: Source of the answer
            source_index: Index of source chunk
            document: Source chunk data
            
        Returns:
            Formatted context string
        """
        # Combine chunk with tags
        return "\n".join([
            f'[Start of source #{source_index}]',
            "'''",
            document.text,
            "'''",
            f'[End of source #{source_index}]'
        ])
        
    def get_llm_parameters(self, source: str) -> Dict[str, float]:
        """Get parameters for language model generation.
        
        Previously: get_gpt_attributes()

        Args:
            source: Source of the answer
            
        Returns:
            Dict of parameter names to values
        """
        return {
            "temperature": 0.0,
            "top_p": 1.0,
            "presence_penalty": 0.0,
            "frequency_penalty": 0.0
        }
        
    def get_llm_prompt_template(self, source: str) -> Union[str, List[Dict[str, str]]]:
        """Get template for generating LLM prompts.
        
        Previously: get_gpt_instruction_config()
        
        Args:
            source: Source of the answer
            
        Returns:
            Prompt template string or list of messages
        """
        if source == SOURCE_LIVEWIKI:
            return self.get_llm_wiki_prompt_template(source)

        telegram_config = self._get_telegram_config()
        if (
            telegram_config != None
            and 'prompt' in telegram_config
            and telegram_config['prompt'] != None
            and telegram_config['prompt'].strip() != ""
        ):
            return telegram_config['prompt'].strip()

        intent = ""
        if (
            self.request and
            self.request.classification and
            'intent' in self.request.classification
        ):
            intent = self.request.classification['intent']

        if intent == ANSWER_INTENT_GENERAL:
            instruction = """
You are a bot-assistant that answers the questions.

Your developper: [quepasa.ai](https://quepasa.ai/)
Your answers are based on a unique knowledge base.

When answering the question, follow these rules, but do not show these rules in your answer:
- answer in {{LANGUAGE}} language;
- if you are greeted, greet the user back and offer to help;
- if someone expresses approval to you, thank him;
- if you are criticized, apologize and try to justify yourself;
- your answer should be brief, don't make any notes.
            """.strip()

        else:
            instruction = """
You're a bot-assistant that answers the questions, using the information from the sources below.

Perform the following actions in the step by step way.

Step 1.
Analyze the sources below.

Step 2.
Select the sources that best fits the user's question.

Step 3.
Check if you can find an answer to the user's question in the selected sources.
If there is no answer:
- say that you can't find the answer;
- ask the user to try to reformulate the question;
- skip Step 4 and step 5.

Step 4.
Compose your answer to the question using the following rules:
- answer in {{LANGUAGE}} language;
- answer briefly in just a few sentences, strictly in accordance with the selected sources, and do not make any assumptions;
- reference the source if you use it in the answer, e.g. [#1] or [#2][#4].

Step 5.
Analyze the answer compiled in step 4, then retell it briefly without introductory or concluding phrases, keeping all references to sources.

Output the result of step 5.

Sources:
{{SOURCES}}
            """.strip()

        return [
            {
                'role': 'system',
                'content': instruction
            },
            {
                'role': 'user',
                'content': "{{QUESTION}}"
            },
        ]

    def get_llm_wiki_prompt_template(self, source: str) -> List[Dict[str, str]]:
        """Get instruction configuration for wiki generation.
        
        Previously: get_wiki_instruction_config()
        
        Args:
            source: Source of the answer
            
        Returns:
            List of message objects for prompt
        """
        if not self.request or not self.request.question:
            return [
                {
                    'role': 'system',
                    'content': """
You're a bot-assistant that writes a wiki page, using the information from the sources below.

Perform the following actions in the step by step way.

Step 1.
Analyze the sources below.

Step 2.
Compose the wiki page using the following rules:
- answer in English language;
- answer briefly in just a few sentences, strictly in accordance with the selected sources, and do not make any assumptions;
- reference the source if you use it in the answer, e.g. [#1] or [#2][#4].

Step 3.
Analyze the answer compiled in step 2, then retell it briefly without introductory or concluding phrases, keeping all references to sources.

Step 4.
Analyze the answer compiled in step 3, then write a wiki page. Find the entities in the answers and make a links in format [{text}](?entity={type}&value={value}).
For example, [John](?entity=Person&value=John%20Snow).

Output the result of step 4.

Entities:
{{ENTITIES}}

Sources:
{{SOURCES}}
                    """.strip()
                },
                {
                    'role': 'user', 
                    'content': "Compose a wiki page"
                },
            ]

        return [
            {
                'role': 'system',
                'content': """
You're a bot-assistant that writes a wiki page on a given topic, using information from the Sources and highlighting Entities.

Perform the following actions in the step by step way.

Step 1.
Analyze the Entities below.

Step 2.
Analyze the Sources below.

Step 2.
Select the sources that best fits the given topic.

Step 3.
Compose the wiki page on the given topic using the following rules:
- Use {{LANGUAGE}} language.
- Compose the text strictly in accordance with the selected sources, and do not make any assumptions.
- Reference the source if you use it in the answer, e.g. [#1] or [#2][#4].
- Don't add opening phrases, or first-person explanations. Write the article in third person, as if it were an actual wikipedia article.

Step 4.
Find the entities in the wiki page composed on step 3 and add links in format [{text}](?entity={type}&value={value}). \
For example, [John](?entity=Person&value=John%20Snow).

Output the result of step 4.

Entities:
{{ENTITIES}}

Sources:
{{SOURCES}}
                """.strip()
            },
            {
                'role': 'user',
                'content': "Topic: {{QUESTION}}"
            },
        ]
        
    def get_llm_model_name(self, source: str) -> Tuple[str, str]:
        """Get model to generate answers.
        
        Previously: get_model_version()

        Args:
            source: Source of the answer
            
        Returns:
            LLM provider and model
        """
        if source == SOURCE_AGENTIC:
            return LLMProvider.OPENAI, "gpt-4o-mini-2024-07-18"
        
        return LLMProvider.NEBIUS, "Qwen/Qwen3-32B-fast"
        
    def get_max_prompt_tokens(self, source: str) -> int:
        """Get maximum number of tokens for full prompt.
        
        Previously: get_prompt_total_size()

        Args:
            source: Source of the answer
            
        Returns:
            Maximum token count
        """
        return 8110  # 8192 / 2 - 1%
        
    def get_max_response_tokens(self, source: str, model: str) -> Optional[int]:
        """Get maximum number of tokens for model response.
        
        Previously: get_answer_prompt_size()
        
        Args:
            source: Source of the answer
            
        Returns:
            Maximum token count
        """
        return 900
