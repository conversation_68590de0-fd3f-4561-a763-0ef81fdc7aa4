apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-flower
  namespace: quepasa
spec:
  replicas: 1
  selector:
    matchLabels:
      app: celery-flower
  template:
    metadata:
      labels:
        app: celery-flower
    spec:
      containers:
      - name: celery-flower
        image: qpreg.azurecr.io/quepasa/crawler:v1.0.184
        imagePullPolicy: Always
        command: ["/bin/sh", "-c"]
        args:
          - |
            export FLOWER_UNAUTHENTICATED_API=true
            pip install flower
            celery -A quepasa.crawler.tasks flower --port=5555 --address=0.0.0.0 --persistent=True --broker_api=$CELERY_BROKER_URL
        env:
        - name: QUEPASA_EXECUTION_MODE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: QUEPASA_EXECUTION_MODE
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: KUBERNETES_NAMESPACE
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: REDIS_PORT
        - name: CELERY_BROKER_URL
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: CELERY_BROKER_URL
        - name: CELERY_RESULT_BACKEND
          valueFrom:
            configMapKeyRef:
              name: shared-config
              key: CELERY_RESULT_BACKEND
        - name: PYTHONPATH
          value: /app
        envFrom:
        - configMapRef:
            name: shared-config
        - secretRef:
            name: shared-secrets
        ports:
        - containerPort: 5555
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "300m"
            memory: "512Mi" 