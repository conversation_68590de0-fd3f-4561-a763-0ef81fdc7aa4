from typing import Optional, Dict, Any, Union
from quepasa.searcher.models.request import QuepasaRequest
from ..base.telegram_auth import TelegramAuthConfig
from ..base.telegram_bot import TelegramBotConfig
from ..base.telegram_indexer import TelegramIndexerConfig
from ..base.telegram_logging import TelegramLoggingConfig
from ..base.telegram_ui import TelegramUIConfig
from ..base.telegram_utils import TelegramUtils

class TelegramMixin(TelegramAuthConfig, TelegramBotConfig, TelegramIndexerConfig, 
                    TelegramLoggingConfig, TelegramUIConfig, TelegramUtils):
    """Mixin that combines all Telegram-related configurations."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
