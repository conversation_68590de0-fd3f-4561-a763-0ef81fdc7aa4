import os
import sys

from src.lib.utils import get_elasticsearch_config
from elasticsearch import Elasticsearch
from src.lib.utils import INDEX_NAME_PREFIX

# Initialize Elasticsearch client with config from common lib
es = Elasticsearch(**get_elasticsearch_config())

# Index settings for Elasticsearch 8.x
index_settings = {
    "settings": {
        "index": {
            "analysis": {
                "filter": {
                    "front_ngram": {
                        "type": "edge_ngram",
                        "min_gram": "1",
                        "max_gram": "12"
                    },
                    "bigram_joiner": {
                        "max_shingle_size": "2",
                        "token_separator": "",
                        "output_unigrams": "false",
                        "type": "shingle"
                    },
                    "bigram_max_size": {
                        "type": "length",
                        "max": "16",
                        "min": "0"
                    },
                    "en-stem-filter": {
                        "name": "light_english",
                        "type": "stemmer",
                        "language": "light_english"
                    },
                    "bigram_joiner_unigrams": {
                        "max_shingle_size": "2",
                        "token_separator": "",
                        "output_unigrams": "true",
                        "type": "shingle"
                    },
                    "delimiter": {
                        "split_on_numerics": "true",
                        "generate_word_parts": "true",
                        "preserve_original": "false",
                        "catenate_words": "true",
                        "generate_number_parts": "true",
                        "catenate_all": "true",
                        "split_on_case_change": "true",
                        "type": "word_delimiter_graph",
                        "catenate_numbers": "true",
                        "stem_english_possessive": "true"
                    },
                    "en-stop-words-filter": {
                        "type": "stop",
                        "stopwords": "_english_"
                    }
                },
                "analyzer": {
                    "i_prefix": {
                        "filter": [
                            "cjk_width",
                            "lowercase",
                            "asciifolding",
                            "front_ngram"
                        ],
                        "type": "custom",
                        "tokenizer": "standard"
                    },
                    "iq_text_delimiter": {
                        "filter": [
                            "delimiter",
                            "cjk_width",
                            "lowercase",
                            "asciifolding",
                            "en-stop-words-filter",
                            "en-stem-filter"
                        ],
                        "type": "custom",
                        "tokenizer": "whitespace"
                    },
                    "q_prefix": {
                        "filter": [
                            "cjk_width",
                            "lowercase",
                            "asciifolding"
                        ],
                        "type": "custom",
                        "tokenizer": "standard"
                    },
                    "iq_text_base": {
                        "filter": [
                            "cjk_width",
                            "lowercase",
                            "asciifolding",
                            "en-stop-words-filter"
                        ],
                        "type": "custom",
                        "tokenizer": "standard"
                    },
                    "iq_text_stem": {
                        "filter": [
                            "cjk_width",
                            "lowercase",
                            "asciifolding",
                            "en-stop-words-filter",
                            "en-stem-filter"
                        ],
                        "type": "custom",
                        "tokenizer": "standard"
                    },
                    "i_text_bigram": {
                        "filter": [
                            "cjk_width",
                            "lowercase",
                            "asciifolding",
                            "en-stem-filter",
                            "bigram_joiner",
                            "bigram_max_size"
                        ],
                        "type": "custom",
                        "tokenizer": "standard"
                    },
                    "q_text_bigram": {
                        "filter": [
                            "cjk_width",
                            "lowercase",
                            "asciifolding",
                            "en-stem-filter",
                            "bigram_joiner_unigrams",
                            "bigram_max_size"
                        ],
                        "type": "custom",
                        "tokenizer": "standard"
                    }
                }
            },
            "number_of_shards": 2,
            "number_of_replicas": 1
        }
    },
    "mappings": {
        "dynamic": "true",
        "dynamic_templates": [
            {
                "all_text_fields": {
                    "match_mapping_type": "string",
                    "mapping": {
                        "analyzer": "iq_text_base",
                        "fields": {
                            "delimiter": {
                                "analyzer": "iq_text_delimiter",
                                "type": "text",
                                "index_options": "freqs"
                            },
                            "joined": {
                                "search_analyzer": "q_text_bigram",
                                "analyzer": "i_text_bigram",
                                "type": "text",
                                "index_options": "freqs"
                            },
                            "prefix": {
                                "search_analyzer": "q_prefix",
                                "analyzer": "i_prefix",
                                "type": "text",
                                "index_options": "docs"
                            },
                            "enum": {
                                "ignore_above": 2048,
                                "type": "keyword"
                            },
                            "stem": {
                                "analyzer": "iq_text_stem",
                                "type": "text"
                            }
                        }
                    }
                }
            }
        ],
        "properties": {
            "root_id": {
                "type": "keyword",
                "index": True
            },
            "id": {
                "type": "keyword",
                "index": True
            },
            "sku": {
                "type": "keyword",
                "ignore_above": 256,
                "fields": {
                    "prefix": {
                        "type": "text",
                        "analyzer": "i_prefix",
                        "search_analyzer": "q_prefix"
                    }
                }
            },
            "chunk_index": {
                "type": "integer",
                "index": True
            },
            "client": {
                "type": "keyword",
                "ignore_above": 256,
                "index": True
            },
            "env": {
                "type": "keyword",
                "ignore_above": 8,
                "index": True
            },
            "domain": {
                "type": "keyword",
                "ignore_above": 256,
                "index": True
            },
            "access_tags": {
                "type": "keyword",
                "doc_values": False,
                "ignore_above": 16384  # 16 Kb
            },
            "provider": {
                "type": "keyword",
                "ignore_above": 64
            },
            "type": {
                "type": "keyword",
                "ignore_above": 16,
                "index": True
            },
            "kind": {
                "type": "keyword",
                "ignore_above": 16,
                "index": True
            },
            "level": {
                "type": "keyword",
                "ignore_above": 16,
                "index": True
            },
            "price_cent_from": {
                "type": "long"
            },
            "price_cent_to": {
                "type": "long"
            },
            "depth": {
                "type": "integer"
            },
            "tokens": {
                "type": "integer"
            },
            "chunks": {
                "type": "integer"
            },
            "url": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword"
                    }
                }
            },
            "language": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 8,
                        "index": True
                    }
                },
                "index": True
            },
            "title": {
                "type": "text",
                "fields": {
                    "delimiter": {
                        "type": "text",
                        "index_options": "freqs",
                        "analyzer": "iq_text_delimiter"
                    },
                    "enum": {
                        "type": "keyword",
                        "ignore_above": 2048
                    },
                    "joined": {
                        "type": "text",
                        "index_options": "freqs",
                        "analyzer": "i_text_bigram",
                        "search_analyzer": "q_text_bigram"
                    },
                    "prefix": {
                        "type": "text",
                        "index_options": "docs",
                        "analyzer": "i_prefix",
                        "search_analyzer": "q_prefix"
                    },
                    "stem": {
                        "type": "text",
                        "analyzer": "iq_text_stem"
                    }
                },
                "analyzer": "iq_text_base",
                "index": True
            },
            "keywords": {
                "type": "text",
                "fields": {
                    "delimiter": {
                        "type": "text",
                        "index_options": "freqs",
                        "analyzer": "iq_text_delimiter"
                    },
                    "enum": {
                        "type": "keyword",
                        "ignore_above": 2048
                    },
                    "joined": {
                        "type": "text",
                        "index_options": "freqs",
                        "analyzer": "i_text_bigram",
                        "search_analyzer": "q_text_bigram"
                    },
                    "prefix": {
                        "type": "text",
                        "index_options": "docs",
                        "analyzer": "i_prefix",
                        "search_analyzer": "q_prefix"
                    },
                    "stem": {
                        "type": "text",
                        "analyzer": "iq_text_stem"
                    }
                },
                "analyzer": "iq_text_base",
                "index": True
            },
            "text": {
                "type": "text",
                "fields": {
                    "delimiter": {
                        "type": "text",
                        "index_options": "freqs",
                        "analyzer": "iq_text_delimiter"
                    },
                    "enum": {
                        "type": "keyword",
                        "ignore_above": 2048
                    },
                    "joined": {
                        "type": "text",
                        "index_options": "freqs",
                        "analyzer": "i_text_bigram",
                        "search_analyzer": "q_text_bigram"
                    },
                    "prefix": {
                        "type": "text",
                        "index_options": "docs",
                        "analyzer": "i_prefix",
                        "search_analyzer": "q_prefix"
                    },
                    "stem": {
                        "type": "text",
                        "analyzer": "iq_text_stem"
                    }
                },
                "analyzer": "iq_text_base",
                "index": True
            },
            "start_position": {
                "type": "integer"
            },
            "end_position": {
                "type": "integer"
            },
            "embedding__sentence-transformers__multi-qa-mpnet-base-dot-v1": {
                "type": "dense_vector",
                "dims": 768,
                "index": True,
                "similarity": "cosine"
            },
            # "embedding__text-embedding-3-small": {
            #     "type": "dense_vector",
            #     "dims": 1536,
            #     "index": True,
            #     "similarity": "cosine"
            # },
            "entities": {
                "type": "nested",
                "properties": {
                    "label": {
                        "type": "keyword",
                        "ignore_above": 32
                    },
                    "value": {
                        "type": "keyword",
                        "ignore_above": 128
                    }
                }
            },
            "created_at": {
                "type": "date"
            },
            "updated_at": {
                "type": "date"
            }
        }
    }
}

def create_index(index_name):
    """Create an Elasticsearch index with the specified settings"""
    try:
        if es.indices.exists(index=index_name):
            print(f"Index {index_name} already exists")
            return False
        
        es.indices.create(index=index_name, body=index_settings)
        print(f"Successfully created index {index_name}")
        return True
    
    except Exception as e:
        print(f"Error creating index {index_name}: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python cli_create_index.py <index_name>")
        sys.exit(1)
    
    index_name = sys.argv[1]
    # create_index(index_name) 

    prefix = f"{INDEX_NAME_PREFIX}-v2"

    create_index(f"{prefix}-common-saas-v2") 
    create_index(f"{prefix}-financebench-openai") 
    create_index(f"{prefix}-financebench-bge") 
    create_index(f"{prefix}-financebench-bge-icl")
    create_index(f"{prefix}-financebench-qwen3")
    create_index(f"{prefix}-rezolve-shoeby-openai")
    create_index(f"{prefix}-rezolve-shoeby-bge")
    create_index(f"{prefix}-rezolve-shoeby-bge-icl")
    create_index(f"{prefix}-rezolve-shoeby-qwen3")