"""
YAML Configuration Loader with Validation

This module loads client configurations from YAML files and validates 
that all required fields are present before allowing the system to start.
"""

import copy
import yaml
from typing import Dict, Any, List
from pathlib import Path


class ConfigurationValidationError(Exception):
    """Raised when YAML configuration is missing required fields."""
    pass


class YAMLConfigurationLoader:
    """Loads and validates client configurations from YAML files."""
    
    def __init__(self, config_root: str = "configuration/clients"):
        self.config_root = Path(config_root)
        self._loaded_configs = {}
        
    def load_client_config(self, client_code: str) -> Dict[str, Any]:
        """Load and validate configuration for a specific client."""
        if client_code in self._loaded_configs:
            return self._loaded_configs[client_code]
            
        yaml_path = self.config_root / f"{client_code}.yaml"
        
        if not yaml_path.exists():
            raise ConfigurationValidationError(
                f"Configuration file not found: {yaml_path}"
            )
            
        try:
            with open(yaml_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise ConfigurationValidationError(
                f"Invalid YAML in {yaml_path}: {e}"
            )
        
        # Handle configuration inheritance
        if 'base_config' in config:
            base_config = self._load_base_config(config['base_config'])
            config = self._merge_configurations(base_config, config)
        
        # Validate configuration completeness
        self._validate_configuration(config, client_code)
        
        # Cache the validated configuration
        self._loaded_configs[client_code] = config
        return config
    
    def _load_base_config(self, base_config_name: str) -> Dict[str, Any]:
        """Load a base configuration file."""
        base_yaml_path = self.config_root / f"{base_config_name}.yaml"
        
        if not base_yaml_path.exists():
            raise ConfigurationValidationError(
                f"Base configuration file not found: {base_yaml_path}"
            )
            
        try:
            with open(base_yaml_path, 'r', encoding='utf-8') as f:
                base_config = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise ConfigurationValidationError(
                f"Invalid YAML in base config {base_yaml_path}: {e}"
            )
            
        return base_config
    
    def _merge_configurations(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """Merge override configuration into base configuration."""
        merged_config = copy.deepcopy(base_config)
        
        # Override top-level fields
        for key, value in override_config.items():
            if key == 'base_config':
                # Skip the base_config directive
                continue
            elif isinstance(value, dict) and key in merged_config and isinstance(merged_config[key], dict):
                # Deep merge for dictionary values
                merged_config[key] = {**merged_config[key], **value}
            else:
                # Direct override for non-dict values or new keys
                merged_config[key] = value
        
        return merged_config
    
    def _validate_configuration(self, config: Dict[str, Any], client_code: str):
        """Validate that all required configuration fields are present."""
        missing_fields = []
        
        # Required top-level fields - basic configuration only
        required_fields = [
            'client_code',
            'query_expansions',
            'language', 
            'search',
            'user_history',
            'llm_models',
            'store_info'
        ]
        
        for field in required_fields:
            if field not in config:
                missing_fields.append(field)
        
        # Validate basic nested structures
        if 'language' in config:
            language_fields = ['fallback_language', 'language_mapping', 'indexed_languages']
            for field in language_fields:
                if field not in config['language']:
                    missing_fields.append(f'language.{field}')
        
        if 'search' in config:
            if 'max_results_limit' not in config['search']:
                missing_fields.append('search.max_results_limit')
        
        if 'user_history' in config:
            history_fields = ['forget_after_seconds', 'max_last_messages', 'use_roles']
            for field in history_fields:
                if field not in config['user_history']:
                    missing_fields.append(f'user_history.{field}')
        
        if 'llm_models' in config:
            model_fields = ['agentic_source', 'document_source']
            for field in model_fields:
                if field not in config['llm_models']:
                    missing_fields.append(f'llm_models.{field}')
        
        if 'store_info' in config:
            store_fields = ['name', 'offerings']
            for field in store_fields:
                if field not in config['store_info']:
                    missing_fields.append(f'store_info.{field}')
                    
        # Validate query expansions structure
        if 'query_expansions' in config:
            if not isinstance(config['query_expansions'], list):
                missing_fields.append('query_expansions (must be a list)')
            else:
                for i, expansion in enumerate(config['query_expansions']):
                    if not isinstance(expansion, dict) or 'keywords' not in expansion or 'query' not in expansion:
                        missing_fields.append(f'query_expansions[{i}] (must have keywords and query)')
        
        

        if missing_fields:
            raise ConfigurationValidationError(
                f"Missing required configuration fields for {client_code}: {missing_fields}"
            )
    
    def validate_all_clients(self, client_codes: List[str]) -> Dict[str, Any]:
        """Validate configurations for all specified clients."""
        validation_results = {}
        
        for client_code in client_codes:
            try:
                self.load_client_config(client_code)
                validation_results[client_code] = {"status": "valid"}
            except ConfigurationValidationError as e:
                validation_results[client_code] = {"status": "invalid", "error": str(e)}
        
        return validation_results
    
    def get_available_clients(self) -> List[str]:
        """Get list of available client configuration files."""
        if not self.config_root.exists():
            return []
        
        yaml_files = list(self.config_root.glob("*.yaml"))
        return [f.stem for f in yaml_files]


# Global loader instance
_loader = None

def get_yaml_loader() -> YAMLConfigurationLoader:
    """Get singleton YAML configuration loader."""
    global _loader
    if _loader is None:
        _loader = YAMLConfigurationLoader()
    return _loader


def load_client_config(client_code: str) -> Dict[str, Any]:
    """Load configuration for a specific client."""
    return get_yaml_loader().load_client_config(client_code)


def validate_client_config(client_code: str) -> bool:
    """Validate configuration for a specific client. Returns True if valid."""
    try:
        load_client_config(client_code)
        return True
    except ConfigurationValidationError:
        return False