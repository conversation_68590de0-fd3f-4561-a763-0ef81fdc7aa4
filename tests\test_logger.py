import pytest
import os
import json
import logging
import io
import sys
from unittest.mock import patch, MagicMock
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QUIET_MODULES

@pytest.fixture
def captured_stdout():
    """Fixture to capture stdout for testing log output"""
    stdout = io.StringIO()
    original_stdout = sys.stdout
    sys.stdout = stdout
    yield stdout
    sys.stdout = original_stdout
    stdout.close()

@pytest.fixture
def logger(captured_stdout):
    """Fixture to provide a fresh QuepasaLogger instance for each test"""
    # Reset singleton state
    QuepasaLogger._instance = None
    QuepasaLogger._initialized = False
    # Create logger after stdout is captured
    return QuepasaLogger()

def test_singleton_pattern():
    """Test that QuepasaLogger follows the singleton pattern"""
    logger1 = QuepasaLogger()
    logger2 = QuepasaLogger()
    assert logger1 is logger2

def test_json_formatting(logger, captured_stdout):
    """Test that logs are properly formatted as JSON"""
    test_logger = logger.get_instance('test')
    test_message = "Test log message"
    test_logger.info(test_message)
    
    log_lines = [line for line in captured_stdout.getvalue().strip().split('\n') if line]
    assert len(log_lines) > 0
    log_data = json.loads(log_lines[0])
    
    assert log_data['message'] == test_message
    assert log_data['level'] == 'INFO'
    assert log_data['logger'] == 'test'
    assert 'timestamp' in log_data

def test_log_level_setting(logger, captured_stdout):
    """Test setting different log levels"""
    test_logger = logger.get_instance('test')
    
    # Set level to ERROR
    QuepasaLogger.set_level(logging.ERROR)
    test_logger.info("This should not appear")
    test_logger.error("This should appear")
    
    log_lines = [line for line in captured_stdout.getvalue().strip().split('\n') if line]
    assert len(log_lines) == 1  # Only ERROR message should be logged
    log_data = json.loads(log_lines[0])
    assert log_data['level'] == 'ERROR'
    assert log_data['message'] == "This should appear"

def test_quiet_modules_configuration():
    """Test that quiet modules are configured with WARNING level"""
    for module in QUIET_MODULES:
        module_logger = logging.getLogger(module)
        assert module_logger.level == logging.WARNING
        assert not module_logger.propagate

def test_exception_logging(logger, captured_stdout):
    """Test that exceptions are properly logged"""
    test_logger = logger.get_instance('test')
    try:
        raise ValueError("Test exception")
    except ValueError:
        test_logger.exception("An error occurred")
    
    log_lines = [line for line in captured_stdout.getvalue().strip().split('\n') if line]
    assert len(log_lines) > 0
    log_data = json.loads(log_lines[0])
    
    assert 'exception' in log_data
    assert 'ValueError: Test exception' in log_data['exception']
    assert log_data['message'] == "An error occurred"

def test_extra_fields_logging(logger, captured_stdout):
    """Test logging with extra fields"""
    test_logger = logger.get_instance('test')
    test_logger = logging.LoggerAdapter(test_logger, {'extra': {'user_id': '123', 'action': 'test'}})
    test_logger.info("Test with extra fields")
    
    log_lines = [line for line in captured_stdout.getvalue().strip().split('\n') if line]
    assert len(log_lines) > 0
    log_data = json.loads(log_lines[0])
    
    assert log_data['user_id'] == '123'
    assert log_data['action'] == 'test'
    assert log_data['message'] == "Test with extra fields"

@pytest.mark.parametrize('log_level,expected_count', [
    (logging.DEBUG, 2),
    (logging.INFO, 1),
    (logging.WARNING, 0),
    (logging.ERROR, 0)
])
def test_multiple_log_levels(logger, captured_stdout, log_level, expected_count):
    """Test logging behavior at different log levels"""
    test_logger = logger.get_instance('test')
    QuepasaLogger.set_level(log_level)
    
    test_logger.debug("Debug message")
    test_logger.info("Info message")
    
    log_lines = [line for line in captured_stdout.getvalue().strip().split('\n') if line]
    assert len(log_lines) == expected_count