from __future__ import annotations

import os
import json
from typing import List, Dict, Any, AsyncGenerator, Tuple, Optional, Callable, Generator

import google.generativeai as genai

from src.lib.logger import <PERSON><PERSON>aLog<PERSON>
from .base import BaseLLM
from .cache import LLMCacheMixin
from .providers import LLMProvider
from src.lib.utils import get_tool_call_dict_list

logger = QuepasaLogger().get_instance(__name__)

Message = Dict[str, Any]  # expects {"role": "...", "content"/"parts": ...}

def _pluck_system(messages: List[Message]) -> Optional[str]:
    """Extract system message from message list."""
    sys_msgs = [m for m in messages if m.get("role") == "system"]
    if not sys_msgs:
        return None
    # Accept either "content" string or "parts" list
    parts = []
    for m in sys_msgs:
        if "content" in m and isinstance(m["content"], str):
            parts.append(m["content"])
        elif "parts" in m:
            if isinstance(m["parts"], list):
                # if parts are dicts ({"text": ...}) or plain strings
                for p in m["parts"]:
                    if isinstance(p, dict) and "text" in p:
                        parts.append(str(p["text"]))
                    else:
                        parts.append(str(p))
            else:
                parts.append(str(m["parts"]))
    return "\n".join(p for p in parts if p)

def _to_history(messages: List[Message], drop_system: bool = True) -> List[Dict[str, Any]]:
    """Convert messages to Gemini history format."""
    out: List[Dict[str, Any]] = []
    for m in messages:
        role = m.get("role", "user")
        if drop_system and role == "system":
            continue
        if role == "assistant":
            role = "model"
        elif role not in ("user", "model", "tool"):
            # default to user if unknown
            role = "user"

        # Normalize parts
        if "parts" in m and m["parts"] is not None:
            parts = m["parts"]
            # ensure list of strings or dicts {"text": "..."}
            if isinstance(parts, str):
                parts = [parts]
        else:
            content = m.get("content", "")
            parts = [content] if isinstance(content, str) else content

        out.append({"role": role, "parts": parts})
    return out

def _concat_text(resp: Any) -> str:
    """Extract text from Gemini response."""
    # Fallback text join
    out: List[str] = []
    for cand in getattr(resp, "candidates", []) or []:
        parts = getattr(getattr(cand, "content", None), "parts", []) or []
        for p in parts:
            t = getattr(p, "text", None)
            if t:
                out.append(t)
    return "".join(out)

def _extract_function_calls(resp: Any) -> List[Dict[str, Any]]:
    """Extract function calls from Gemini response."""
    calls: List[Dict[str, Any]] = []
    for cand in getattr(resp, "candidates", []) or []:
        parts = getattr(getattr(cand, "content", None), "parts", []) or []
        for p in parts:
            fc = getattr(p, "function_call", None)
            if fc:
                calls.append({
                    "name": getattr(fc, "name", None),
                    "args": dict(getattr(fc, "args", {}) or {})
                })
    return calls

class GeminiLLM(BaseLLM, LLMCacheMixin):
    """Class for interacting with Google's Gemini language models."""

    def __init__(self, api_key: Optional[str] = None):
        """Initialize the Gemini LLM.
        
        Args:
            api_key: The API key for Gemini. If not provided,
                will try to get it from GEMINI_API_KEY environment variable.
        """
        super().__init__()
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        genai.configure(api_key=self.api_key)
        self.default_system = None

    @property
    def provider(self) -> LLMProvider:
        """Get the provider type."""
        return LLMProvider.GEMINI

    def _make_model(self, system: Optional[str], model_name: Optional[str] = None) -> Any:
        """Create a GenerativeModel instance."""
        return genai.GenerativeModel(
            model_name=model_name,
            system_instruction=system if system else None
        )

    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a completion from the Gemini model.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size (ignored for now)
            json_mode: Whether to force JSON output format (ignored for now)
            
        Returns:
            The generated response text
        """
        try:
            # Resolve system instruction
            sys_msg = _pluck_system(prompt_list) or self.default_system
            model = self._make_model(sys_msg, model_version)

            history = _to_history(prompt_list, drop_system=True)

            resp = model.generate_content(history)
            return getattr(resp, "text", "") or _concat_text(resp)
            
        except Exception as e:
            logger.error(f"Error in Gemini get_answer: {str(e)}")
            raise

    async def get_streaming_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> AsyncGenerator[str, None]:
        """Get a streaming completion from the Gemini model.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size (ignored for now)
            json_mode: Whether to force JSON output format (ignored for now)
            
        Yields:
            Generated response chunks
        """
        try:
            # Resolve system instruction
            sys_msg = _pluck_system(prompt_list) or self.default_system
            model = self._make_model(sys_msg, model_version)

            history = _to_history(prompt_list, drop_system=True)

            it = model.generate_content(history, stream=True)
            for chunk in it:
                if getattr(chunk, "text", None):
                    yield chunk.text
                    
        except Exception as e:
            logger.error(f"Error in Gemini get_streaming_answer: {str(e)}")
            raise

    def get_tools_answer(self, model_version: str, prompt_list: List[Dict[str, str]], tools: List[Dict[str, Any]], answer_prompt_size: int) -> Tuple[str, list]:
        """Get a completion from the Gemini model with function calling.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            tools: Tools used for Agentic/Function calling
            answer_prompt_size: Maximum response size (ignored for now)
            
        Returns:
            Tuple of (response_text, tool_calls)
        """
        try:
            # Resolve system instruction
            sys_msg = _pluck_system(prompt_list) or self.default_system
            model = self._make_model(sys_msg, model_version)

            history = _to_history(prompt_list, drop_system=True)

            # Convert tools to Gemini format
            gemini_tools = self._convert_tools_to_gemini_format(tools)

            # First ask (allow function calls)
            resp = model.generate_content(history, tools=gemini_tools)
            calls = _extract_function_calls(resp)

            if not calls:
                # No function calls, return the response
                return getattr(resp, "text", "") or _concat_text(resp), []

            # Execute function calls and get final response
            follow = list(history)
            for c in calls:
                # Reflect the model's function_call
                follow.append({"role": "model", "parts": [{"function_call": {"name": c["name"], "args": c["args"]}}]})
                # For now, we don't have tool handlers in this context, so we'll return the calls
                # In a real implementation, you'd execute the tools here
                follow.append({"role": "tool", "parts": [{"function_response": {"name": c["name"], "response": {"error": "Tool execution not implemented in this context"}}}]})

            # Final answer
            final = model.generate_content(follow, tools=gemini_tools)
            return getattr(final, "text", "") or _concat_text(final), calls
            
        except Exception as e:
            logger.error(f"Error in Gemini get_tools_answer: {str(e)}")
            raise

    async def get_streaming_tools_answer(self, model_version: str, prompt_list: List[Dict[str, str]], tools: List[Dict[str, Any]], answer_prompt_size: int) -> AsyncGenerator[Tuple[str, list], None]:
        """Get a streaming completion from the Gemini model with function calling.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            tools: Tools used for Agentic/Function calling
            answer_prompt_size: Maximum response size (ignored for now)
            
        Yields:
            Generated response chunks with tool calls
        """
        try:
            # For streaming tools, we'll get the non-streaming result and yield it
            # This is a limitation of the current implementation
            content, tool_calls = self.get_tools_answer(model_version, prompt_list, tools, answer_prompt_size)
            yield content, tool_calls
            
        except Exception as e:
            logger.error(f"Error in Gemini get_streaming_tools_answer: {str(e)}")
            raise

    def _convert_tools_to_gemini_format(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert tools to Gemini function calling format."""
        gemini_tools = []
        for tool in tools:
            if "function_declarations" in tool:
                # Already in Gemini format
                gemini_tools.append(tool)
            elif "functions" in tool:
                # Convert from OpenAI format
                function_declarations = []
                for func in tool["functions"]:
                    gemini_func = {
                        "name": func["name"],
                        "description": func.get("description", ""),
                        "parameters": func.get("parameters", {})
                    }
                    function_declarations.append(gemini_func)
                gemini_tools.append({"function_declarations": function_declarations})
            else:
                # Assume it's a single function
                gemini_func = {
                    "name": tool.get("name", "unknown"),
                    "description": tool.get("description", ""),
                    "parameters": tool.get("parameters", {})
                }
                gemini_tools.append({"function_declarations": [gemini_func]})
        
        return gemini_tools

    def get_cached_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a cached answer from the Gemini model."""
        return self._get_cached_response(
            model_version, prompt_list, answer_prompt_size, json_mode,
            self.get_answer
        )