#!/usr/bin/env python3
"""
MinIO Bucket Initialization Script
This script waits for MinIO to be ready and then creates the specified bucket.
"""

import os
import time
from minio import Minio
from minio.error import S3Error

def main():
    # Get environment variables
    minio_host = os.getenv('MINIO_HOST', 'minio:9000')
    access_key = os.getenv('MINIO_ACCESS_KEY', 'minioadmin')
    secret_key = os.getenv('MINIO_SECRET_KEY', 'minioadmin123')
    bucket_name = os.getenv('MINIO_BUCKET_NAME', 'quepasa-files')
    
    print(f"MinIO Host: {minio_host}")
    print(f"Bucket Name: {bucket_name}")
    
    # Wait for Min<PERSON> to be ready
    print('Waiting for MinIO to be ready...')
    while True:
        try:
            client = Minio(minio_host, access_key=access_key, secret_key=secret_key, secure=False)
            client.list_buckets()
            print('<PERSON><PERSON> is ready, creating bucket...')
            break
        except Exception as e:
            print(f'<PERSON><PERSON> not ready yet, waiting... ({e})')
            time.sleep(2)
    
    # Create bucket
    try:
        client.make_bucket(bucket_name)
        print(f'Bucket {bucket_name} created successfully!')
    except S3Error as e:
        if e.code == 'BucketAlreadyExists':
            print(f'Bucket {bucket_name} already exists')
        else:
            print(f'Error creating bucket: {e}')
            # exit(1)
    
    # List buckets
    buckets = client.list_buckets()
    print('Available buckets:')
    for bucket in buckets:
        print(f'  - {bucket.name}')
    
    print('Bucket initialization completed successfully!')

if __name__ == '__main__':
    main() 