import pytest

from quepasa.searcher.models.request import QuepasaRequest
from src.lib.constants import HTTP_PROTOCOL


def test_from_dict_requires_question_and_string():
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({})
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({"question": 123})
    # minimal valid
    r = QuepasaRequest.from_dict({"question": "hi"})
    assert isinstance(r, QuepasaRequest)


def test_from_dict_validates_protocol_and_engine():
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({"question": "q", "protocol": "bad"})
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({"question": "q", "engine": "nope"})


def test_from_dict_validates_filter_and_documents_and_flags():
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({"question": "q", "filter": {}})
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({"question": "q", "documents": {}})
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({"question": "q", "documents": [1,2,3]})
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({"question": "q", "show_query": "yes"})


def test_from_dict_validates_nested_user_info_and_webhook():
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({"question": "q", "user_info": "not a dict"})
    # empty dict is currently treated as falsy and passes validation
    r = QuepasaRequest.from_dict({"question": "q", "user_info": {}})
    assert isinstance(r, QuepasaRequest)
    with pytest.raises(ValueError):
        QuepasaRequest.from_dict({"question": "q", "webhook": "nope"})
    # empty dict is treated as falsy and passes validation
    r2 = QuepasaRequest.from_dict({"question": "q", "webhook": {}})
    assert isinstance(r2, QuepasaRequest)


def test_post_init_conversions_and_defaults():
    r = QuepasaRequest.from_dict({
        "question": "  hi  ",
        "protocol": HTTP_PROTOCOL,
        "user_info": {"id": "u1", "name": "User"},
        "history": [{"role": "user", "content": "c"}],
        "webhook": {"endpoint": "https://x"},
        "constants": {"smth_went_wrong": "x"},
        "llm": "gpt",
    })
    assert r.question == "hi"
    assert r.answer_llm == "gpt" and r.agent_llm == "gpt"
    assert r.user_info.id == "u1"
    assert r.history[0].role == "user"
    assert r.webhook.endpoint == "https://x"
    assert isinstance(r.ts, int) and isinstance(r.now, str)
