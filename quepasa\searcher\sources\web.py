from typing import Dict, Any, List, Optional, Union, Generator
import os
import requests
import time

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>og<PERSON>
from src.lib.constants import (
    SOURCE_WEB,
    ANSWER_TYPE_STRICT,
    ANSWER_TYPE_WEAK,
    ANSWER_TYPE_NO_LINKS
)
from src.lib.llm_utils import get_cached_streaming_chunks

from .base import (
    BaseSource,
    ERROR_MESSAGE_NO_SEARCH_RESULTS
)
from .mixins import AnswerRetrieverMixin
from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse, QuepasaStreamAnswer
from ..core.web_search import WebSearchManager, WebSearchResult
from ..sources.base import STATUS_SUCCESS

logger = QuepasaLogger().get_instance(__name__)

class WebSource(BaseSource, AnswerRetrieverMixin):
    """Web search source using Bing API"""
    
    def __init__(self):
        """Initialize web source"""
        super().__init__()
        self.web_search = WebSearchManager()
        
    def search(self, request: QuepasaRequest) -> QuepasaResponse:
        """Execute search request
        
        Args:
            request: Search request
            
        Returns:
            Search response
        """
        try:
            # Get search results
            documents = self.web_search.search(request)
            
            # Return response
            return QuepasaResponse(
                status=STATUS_SUCCESS,
                data=documents
            )
            
        except Exception as e:
            return self._handle_error(request, f"Error searching: {str(e)}")
        
    def get_answer(self, request: QuepasaRequest, stream: bool = False) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """Get answer for request
        
        Args:
            request: Search request
            stream: Whether to stream the response
            
        Returns:
            Search response or generator of stream responses
        """
        try:
            # Get search results
            documents = self.web_search.search(request)
            if not documents:
                return self._handle_error(request, ERROR_MESSAGE_NO_SEARCH_RESULTS, stream)
            
            # Process search results using mixin
            return self.retrieve_answer(request, SOURCE_WEB, documents, stream)
            
        except Exception as e:
            return self._handle_error(request, f"Error getting answer: {str(e)}", stream)
