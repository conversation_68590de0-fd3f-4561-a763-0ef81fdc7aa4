import pytest
from src.lib.embedding.providers import EmbeddingProvider

def test_provider_values():
    """Test provider enum values."""
    assert EmbeddingProvider.OPENAI.value == 'openai'
    assert EmbeddingProvider.ASKROBOT.value == 'askrobot'
    assert EmbeddingProvider.SBERT.value == 'sbert'
    assert EmbeddingProvider.NEBIUS.value == 'nebius'

def test_from_str_valid():
    """Test string to enum conversion with valid values."""
    assert EmbeddingProvider.from_str('openai') == EmbeddingProvider.OPENAI
    assert EmbeddingProvider.from_str('askrobot') == EmbeddingProvider.ASKROBOT
    assert EmbeddingProvider.from_str('sbert') == EmbeddingProvider.SBERT
    assert EmbeddingProvider.from_str('nebius') == EmbeddingProvider.NEBIUS

def test_from_str_case_insensitive():
    """Test case-insensitive string to enum conversion."""
    assert EmbeddingProvider.from_str('OPENAI') == EmbeddingProvider.OPENAI
    assert EmbeddingProvider.from_str('askRobot') == EmbeddingProvider.ASKROBOT
    assert EmbeddingProvider.from_str('SbErT') == EmbeddingProvider.SBERT
    assert EmbeddingProvider.from_str('NeBiUs') == EmbeddingProvider.NEBIUS

def test_from_str_invalid():
    """Test error handling for invalid provider strings."""
    with pytest.raises(ValueError) as exc_info:
        EmbeddingProvider.from_str('invalid_provider')
    assert 'Unknown embedding provider' in str(exc_info.value)
    assert 'Supported providers' in str(exc_info.value)

def test_provider_uniqueness():
    """Test that all provider values are unique."""
    values = [provider.value for provider in EmbeddingProvider]
    assert len(values) == len(set(values)) 