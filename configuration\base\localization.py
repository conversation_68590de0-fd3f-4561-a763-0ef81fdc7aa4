from typing import Dict, Any, List, Union
from quepasa.searcher.models.request import QuepasaRequest
from .base_config import BaseConfig
from src.lib.utils import ALLOWED_LANGUAGES

class LocalizationConfig(BaseConfig):
    """Base configuration for question processing."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)       
        
    def get_fallback_language(self) -> str:
        """Get default language code to use when language detection fails.
        
        Previously: get_default_language_code()
        
        Returns:
            Default language code
        """
        return 'en'
        
    def get_language_mapping(self) -> Dict[str, str]:
        """Get mapping of language codes to standardized codes.
        
        Previously: get_languages()
        
        Returns:
            Dict mapping variant codes to standard codes
        """
        return ALLOWED_LANGUAGES
    
    def get_language_code(self) -> str:
        language_code = self.get_fallback_language()
        if (
            hasattr(self.request, 'classification')
            and self.request.classification != None
            and hasattr(self.request.classification, 'language_code')
            and self.request.classification.language_code in self.get_language_mapping()
        ):
            return self.request.classification.language_code
        return language_code
