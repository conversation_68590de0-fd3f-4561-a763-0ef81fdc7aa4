from pathlib import Path
from quepasa.crawler.tasks import process_batch, app
from src.lib.files import QuepasaFiles

import os
os.environ['QUEPASA_ENV'] = 'test'  # Mock ENV before importing config

qp_files = QuepasaFiles(
    bucket_name='quepasa-files',
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin',
    debug_flag=False
)

# Client ID for testing
TEST_CLIENT_ID = 'test_client_batch_integration_sandbox'

base_path = f"test/storage/crawler/api-v1/{TEST_CLIENT_ID}"

# Configure Celery for testing
app.conf.task_always_eager = True

def create_test_files():
    # CSV with sample data
    qp_files.set_text(f'{base_path}/sample.csv', """
Department,Employee,Position,Salary,Years,Office,Projects,Performance,Education,Skills
Sales,<PERSON>,Senior Manager,85000,8,New York,Project A|Project B|Project C,Excellent,MBA,Leadership|Communication|Strategy
Marketing,<PERSON>,Director,95000,10,<PERSON>,Project X|Project Y,Outstanding,PhD,Marketing|Analytics|Management
Engineering,<PERSON>,Lead <PERSON>eloper,92000,6,<PERSON>,Backend|Frontend|Mobile,Excellent,MSc,Python|Java|Cloud
    """.strip())
    
    qp_files.set_text(f'{base_path}/sample.md', """
# Comprehensive Guide to Modern Software Development

## Introduction

Software development has evolved significantly over the past decades, incorporating new methodologies, tools, and practices. This guide provides a detailed overview of modern software development practices and principles.

## Agile Development Methodology

### Core Principles

Agile development is based on several key principles:
- Iterative development
- Continuous feedback
- Adaptive planning
- Rapid delivery
    """.strip())
    
    qp_files.set_text(f'{base_path}/sample.txt', """
Introduction to Advanced Technology Systems

In the rapidly evolving landscape of modern technology, understanding the fundamental principles and advanced concepts of technological systems has become increasingly crucial. This comprehensive guide aims to explore various aspects of technology systems, their implementations, and their impact on different sectors.

Chapter 1: Basic Principles and Foundations

The foundation of any technological system lies in its basic principles. These principles encompass various aspects including data processing, system architecture, and interaction models.
    """.strip())

def test_batch_processing():
    # Create test files
    create_test_files()

    # Test data with both URLs and local files
    test_batch = {
        'client_id': TEST_CLIENT_ID,
        'domain': 'test',
        'action': 'upsert',
        'urls': [
            'https://raw.githubusercontent.com/openai/openai-cookbook/main/examples/Question_answering_using_embeddings.md',
            'https://www.youtube.com/watch?v=GIg4HlSp_ZE',  # A real YouTube video about AI
            'https://www.pdf995.com/samples/pdf.pdf',  # A sample PDF
        ],
        'files': [
            f'{base_path}/sample.csv',
            f'{base_path}/sample.md',
            f'{base_path}/sample.txt'
        ]
    }
        
    print("Test batch configuration:")
    print("URLs to process:", test_batch['urls'])
    print("Files to process:", test_batch['files'])
    print("\nStarting batch processing...")
    
    # Process the batch
    result = process_batch.apply(args=[test_batch]).get()
    
    # Print results
    print("\nBatch processing results:")
    print("-" * 50)
    print()
    print()

    print(f"Status: {result['status']}")
    print(f"Client ID: {result['client_id']}")
    print("\nProcessed items:")

    for i, item in enumerate(result['results'], 1):
        print(f"\nItem {i}:")
        if isinstance(item, dict):
            if 'id' in item:
                print(f"ID: {item['id']}")

            if 'status' in item:
                print(f"Status: {item['status']}")

            if 'error' in item:
                print(f"Error: {item['error']}")

            if 'file' in item:
                print(f"File: {item['file']}")

        else:
            print("Result:", item)

        print("----- " * 5)
        print()
        print()

if __name__ == '__main__':
    test_batch_processing() 