# Build stage
FROM python:3.9-slim AS builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    DOCKER_BUILDKIT=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    g++ \
    protobuf-compiler \
    pkg-config \
    python3-dev \
    libprotobuf-dev \
    make \
    build-essential \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Upgrade pip and install build tools
RUN pip install --upgrade pip setuptools wheel

# Copy only necessary files first
COPY requirements.txt .
COPY quepasa/indexer/requirements.txt quepasa/indexer/
COPY setup.py .

#  Install dependencies with caching disabled
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r quepasa/indexer/requirements.txt

# Install PyArmor 7.x specifically for compatibility with obfuscate command
RUN pip install --no-cache-dir pyarmor==7.7.4

# Copy application code first
COPY configuration/ configuration/
COPY quepasa/common/ quepasa/common/
COPY quepasa/indexer/ quepasa/indexer/
COPY quepasa/searcher/models/ quepasa/searcher/models/
COPY quepasa/searcher/*.py quepasa/searcher/
COPY quepasa/*.py quepasa/
COPY src/ src/

# Obfuscate the Python code
RUN mkdir -p /app/obfuscated/quepasa && \
    # Obfuscate the Indexer module
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/indexer quepasa/indexer/*.py && \
    # Obfuscate other required modules
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/common quepasa/common/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher/models quepasa/searcher/models/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa/searcher quepasa/searcher/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/quepasa quepasa/*.py && \
    # Handle src directory
    mkdir -p /app/obfuscated/src && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/embedding src/lib/embedding/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/llm src/lib/llm/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/whisper src/lib/whisper/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib/reranker src/lib/reranker/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src/lib src/lib/*.py && \
    pyarmor obfuscate --recursive --output /app/obfuscated/src src/*.py && \
    # Verify files were created (for debugging)
    echo "Checking obfuscated directories:" && \
    ls -la /app/obfuscated/quepasa && \
    ls -la /app/obfuscated/quepasa/indexer || true

# Final stage
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libgbm1 \
    libasound2 \
    libpango-1.0-0 \
    busybox \
    && mkdir -p cache \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && pip cache purge

# Copy only necessary files from builder
COPY --from=builder /usr/local/lib/python3.9/site-packages/ /usr/local/lib/python3.9/site-packages/
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# Copy obfuscated files instead of the original source
COPY --from=builder /app/obfuscated/quepasa /app/quepasa 
COPY --from=builder /app/obfuscated/src /app/src

# IMPORTANT: Copy PyArmor runtime files from each obfuscated directory
COPY --from=builder /app/obfuscated/quepasa/pytransform /app/pytransform
COPY --from=builder /app/obfuscated/quepasa/indexer/pytransform /app/quepasa/indexer/pytransform
COPY --from=builder /app/obfuscated/quepasa/common/pytransform /app/quepasa/common/pytransform
COPY --from=builder /app/obfuscated/quepasa/searcher/pytransform /app/quepasa/searcher/pytransform

COPY --from=builder /app/configuration /app/configuration

# Set environment variables
ENV PYTHONPATH=/app \
    LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH \
    PATH="/usr/local/bin:$PATH"

# Set working directory
WORKDIR /app

# Run the application
CMD ["/bin/sh","-lc", "\
    celery -A quepasa.indexer.tasks worker -Q indexer -n indexer@%h -c 12 --loglevel=info & \
    celery -A quepasa.indexer.tasks worker -Q indexer-save -n indexer-save@%h -c 1 --loglevel=info & \
    wait" ]