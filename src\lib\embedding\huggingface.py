import os
import requests
from typing import Optional, List

from .base import <PERSON><PERSON>mbedding
from .cache import EmbeddingCacheMixin
from .providers import EmbeddingProvider
from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.lib.utils import get_tokenizer, get_max_embedding_model_token_length

logger = QuepasaLogger().get_instance(__name__)

class HuggingfaceEmbedding(BaseEmbedding, EmbeddingCacheMixin):
    """Huggingface embedding provider.
    
    This class provides access to Huggingface's embedding models via their Inference API.
    """
    def __init__(self):
        """Initialize Huggingface embedding provider."""
        super().__init__()

        # Check for API key
        self.api_key = os.getenv('HUGGINGFACE_API_KEY')
        if not self.api_key or self.api_key == '':
            raise ValueError("HUGGINGFACE_API_KEY environment variable is not set")

    @property
    def provider(self) -> EmbeddingProvider:
        return EmbeddingProvider.HUGGINGFACE

    def _truncate_text(self, model_version: str, text: str) -> str:
        """Truncate text to fit within token limit.
        
        Args:
            text: Text to truncate
            
        Returns:
            Truncated text that fits within the model's token limit
        """
        max_tokens = get_max_embedding_model_token_length(model_version)
        if len(get_tokenizer().encode(text)) > max_tokens:
            new_text = ""
            for line in text.split("\n"):
                line_nl = "\n" + line
                if len(get_tokenizer().encode((new_text + line_nl).strip())) > max_tokens:
                    break
                new_text += line_nl
            return new_text.strip()
        return text

    def get_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get embedding from Huggingface Inference API.
        
        Args:
            model_version: The model to use for embeddings (e.g., 'mixedbread-ai/mxbai-embed-large-v1')
            text: The text to get embedding for
            
        Returns:
            List of floats representing the embedding, or None if the request fails
        """
        text = self._truncate_text(model_version, text)
        api_url = f"https://api-inference.huggingface.co/pipeline/feature-extraction/{model_version}"
        headers = {"Authorization": f"Bearer {self.api_key}"}
        payload = {"inputs": text}

        try:
            response = requests.post(api_url, headers=headers, json=payload)
            response.raise_for_status()  # Raise exception for HTTP errors
            return response.json()
        
        except Exception as e:
            logger.error(f"Failed to get embedding from Huggingface: {str(e)}")
            return None 