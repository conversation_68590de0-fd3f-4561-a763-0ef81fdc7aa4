from typing import Dict, Any, Optional
from .base import BaseProcessor
from ...utils import split_text, get_title_by_filename
from ..utils import get_language  # Prefer service-based language detection when available
from configuration.main.default import QuepasaConfigurationHub

class TXTProcessor(BaseProcessor):
    """Processor for text files"""

    def __init__(self, client_id: Optional[str] = None):
        super().__init__()
        self.config = QuepasaConfigurationHub.from_client_code(client_id) if client_id else None

    def _detect_language(self, text: str, meta: Dict[str, Any]) -> str:
        """Return the document language.

        Priority:
        1) meta['language'] if provided and non-empty string
        2) embedding service via get_language(text, default='en')
        3) config fallback language
        4) hard default 'en'
        """
        # 1) explicit meta
        lang = meta.get('language') if isinstance(meta.get('language'), str) else ''
        lang = lang.strip().lower() if lang else ''
        if lang:
            return lang

        # 2) service-based detection
        try:
            detected = get_language(text, default='en')
            if detected:
                return str(detected).strip().lower() or 'en'
        except Exception as e:
            self.logger.debug(f"_detect_language service error, defaulting: {e}")

        # 3) config fallback
        if self.config:
            return self.config.get_fallback_language()

        # 4) hard default
        return 'en'


    def _process_impl(self, content: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract text from text file content
        
        Args:
            content: Text file content
            meta: Metadata about the file
            
        Returns:
            Dictionary containing extracted text and title
        """
        # Validate metadata
        error = self.validate_meta(meta, ['filename'])
        if error:
            self.logger.error(f"Metadata validation failed: {error}")
            raise ValueError(error)

        # Try different encodings
        encodings = ['utf-8', 'latin1', 'cp1252']
        text_content = None
        decode_errors = []

        for encoding in encodings:
            try:
                text_content = content.decode(encoding)
                self.logger.info(f"Successfully decoded content with {encoding} encoding")
                break
            except UnicodeDecodeError as e:
                self.logger.warning(f"Failed to decode content with {encoding} encoding")
                decode_errors.append(f"{encoding}: {str(e)}")
                continue

        if text_content is None:
            error_msg = "Could not decode text content with any supported encoding"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # Determine language once per document via helper
        language = self._detect_language(text_content, meta)

        # Split text into chunks and track line numbers by position
        raw_chunks = split_text(text_content)
        chunks = []
        current_pos = 0

        for chunk in raw_chunks:
            # Find start and end positions of chunk in full text
            chunk_start_pos = current_pos
            chunk_end_pos = current_pos + len(chunk)

            # Count newlines before start and end to get line numbers
            start_line = text_content[:chunk_start_pos].count('\n') + 1
            end_line = text_content[:chunk_end_pos].count('\n') + 1

            chunks.append({
                'language': language,
                'text': chunk,
                'position': f"lines {start_line}-{end_line}"
            })
            current_pos = chunk_end_pos + 1  # +1 to skip newline

        return {
            'chunks': chunks,
            'title': get_title_by_filename(meta['filename']),
            'filename': meta['filename']
        }