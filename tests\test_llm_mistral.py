import os
import pytest
from unittest.mock import Mock, patch, AsyncMock
from src.lib.llm.mistral import MistralLLM
from src.lib.llm.providers import LLMProvider

@pytest.fixture
def llm(tmp_path):
    """Fixture that provides a Mistral LLM instance with mocked client."""
    with patch.dict('os.environ', {'MISTRAL_API_KEY': 'test-key'}):
        llm = MistralLLM()
        llm._local_cache_dir = str(tmp_path / "cache")
        os.makedirs(llm._local_cache_dir, exist_ok=True)
        return llm

def test_provider(llm):
    """Test provider property."""
    assert llm.provider == LLMProvider.MISTRAL

def test_get_answer_success(llm):
    """Test successful answer generation."""
    test_response = Mock()
    test_response.choices = [Mock(message=Mock(content="Test response"))]
    prompts = [{"role": "user", "content": "test prompt"}]
    
    with patch.object(llm.client.chat, 'complete', return_value=test_response) as mock_complete:
        result = llm.get_answer("mistral-large", prompts, 100)
    
    assert result == "Test response"
    mock_complete.assert_called_once()

def test_get_answer_with_json_mode(llm):
    """Test answer generation with JSON mode enabled."""
    test_response = Mock()
    test_response.choices = [Mock(message=Mock(content='{"key": "value"}'))]
    prompts = [{"role": "user", "content": "test prompt"}]
    
    with patch.object(llm.client.chat, 'complete', return_value=test_response) as mock_complete:
        result = llm.get_answer("mistral-large", prompts, 100, json_mode=True)
    
    assert result == '{"key": "value"}'
    mock_complete.assert_called_once()

def test_get_answer_failure(llm):
    """Test handling of API errors."""
    prompts = [{"role": "user", "content": "test prompt"}]
    
    with patch.object(llm.client.chat, 'complete', side_effect=Exception("API error occurred")):
        with pytest.raises(Exception, match="API error occurred"):
            llm.get_answer("mistral-large", prompts, 100)

def test_missing_api_key():
    """Test initialization with missing API key."""
    with patch.dict('os.environ', clear=True):
        with pytest.raises(ValueError, match="MISTRAL_API_KEY"):
            MistralLLM()

@pytest.mark.asyncio
async def test_get_streaming_answer(llm):
    """Test streaming answer generation."""
    test_chunks = ["Hello", " world", "!"]
    prompts = [{"role": "user", "content": "test prompt"}]
    
    # Mock streaming response
    mock_response = AsyncMock()
    mock_response.__aiter__.return_value = [
        Mock(data=Mock(choices=[Mock(delta=Mock(content=chunk))])) 
        for chunk in test_chunks
    ]
    
    with patch.object(llm.client.chat, 'stream_async', return_value=mock_response) as mock_stream:
        chunks = []
        async for chunk in llm.get_streaming_answer("mistral-large", prompts, 100):
            chunks.append(chunk)
        
        assert chunks == test_chunks
        mock_stream.assert_called_once()

@pytest.mark.slow
@pytest.mark.asyncio
async def test_get_cached_streaming_chunks(llm):
    """Test cached streaming chunks."""
    test_chunks = ["Hello", " world", "!"]
    prompts = [{"role": "user", "content": "test prompt"}]
    
    # Mock streaming response for cache miss
    mock_response = AsyncMock()
    mock_response.__aiter__.return_value = [
        Mock(data=Mock(choices=[Mock(delta=Mock(content=chunk))])) 
        for chunk in test_chunks
    ]
    
    # Test when cache exists
    with patch('os.path.exists', return_value=True), \
         patch('builtins.open', create=True) as mock_open, \
         patch.object(llm.client.chat, 'stream_async', return_value=mock_response):
        # Return the joined chunks as a single string
        mock_open.return_value.__enter__.return_value.read.return_value = ''.join(test_chunks)
        
        chunks = []
        async for chunk in llm.get_cached_streaming_chunks("mistral-large", prompts, 100):
            chunks.append(chunk)
        
        # Expect a single chunk with the complete response
        assert chunks == [''.join(test_chunks)]
    
    # Test when cache doesn't exist
    with patch('os.path.exists', return_value=False), \
         patch.object(llm.client.chat, 'stream_async', return_value=mock_response) as mock_stream:
        chunks = []
        async for chunk in llm.get_cached_streaming_chunks("mistral-large", prompts, 100):
            chunks.append(chunk)
        
        # When no cache exists, we get individual chunks
        assert chunks == test_chunks
        mock_stream.assert_called_once() 