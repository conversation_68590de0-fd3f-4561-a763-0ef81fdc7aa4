from __future__ import annotations

import os
from typing import List, Dict, Any, Generator
import together

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .base import BaseLLM
from .cache import LL<PERSON>acheMixin
from .providers import LLMProvider

logger = QuepasaLogger().get_instance(__name__)

class TogetherLLM(BaseLLM, LLMCacheMixin):
    """Together LLM provider.
    
    This class provides access to various language models hosted on
    Together.ai's platform.
    """
    
    def __init__(self):
        """Initialize Together LLM provider."""
        super().__init__()

        # Check for API key
        self.api_key = os.getenv('TOGETHERAI_API_KEY')
        if not self.api_key:
            raise ValueError("TOGETHERAI_API_KEY environment variable is not set")
        together.api_key = self.api_key

    @property
    def provider(self) -> LLMProvider:
        return LLMProvider.TOGETHER

    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get a completion from Together API.
        
        Args:
            model_version: Model version (e.g., 'meta-llama/Meta-Llama-3-8B-Instruct-Turbo')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format (not supported)
            
        Returns:
            Generated response text
        """
        try:
            response = together.Complete.create(
                model=model_version,
                messages=prompt_list,
                max_tokens=answer_prompt_size,
                temperature=0.0
            )
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Failed to get answer from Together: {str(e)}")
            return ""

    def get_streaming_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> Generator[str, None, None]:
        """Get a streaming completion from Together API.
        
        Args:
            model_version: Model version (e.g., 'meta-llama/Meta-Llama-3-8B-Instruct-Turbo')
            prompt_list: List of prompts with 'role' and 'content'
            answer_prompt_size: Maximum response size
            json_mode: Whether to force JSON output format (not supported)
            
        Yields:
            Generated response chunks
        """
        try:
            stream = together.Complete.create(
                model=model_version,
                messages=prompt_list,
                max_tokens=answer_prompt_size,
                temperature=0.0,
                stream=True
            )

            for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Failed to get streaming answer from Together: {str(e)}")
            yield "" 