from typing import Dict, Any, Optional, Union       
from dataclasses import dataclass

from quepasa.searcher.models.spd_result import SPDSearchResult
from src.lib.constants import SOURCE_ALL, DOCUMENT_TYPE_DIALOG, DOCUMENT_TYPE_DOCUMENT, DOCUMENT_TYPE_PRODUCT
from src.lib.utils import get_link_index_from_date, get_link_index_from_url
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.web import WebSearchResult
from .localization import LocalizationConfig
@dataclass
class ReferenceFormat:
    """Configuration for reference formatting."""
    emoji: str
    format: str  # 'date', 'host', or 'number'

@dataclass
class SourceReference:
    """Source reference object for API responses."""
    label: str
    url: Optional[str] = None
    source_type: Optional[str] = None
    created_at: Optional[str] = None
    position: Optional[str] = None

class SourceReferenceConfig(LocalizationConfig):
    """Base configuration for source reference formatting."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)
    
    def get_source_reference(self, source: str, document: Union[QuepasaDocument, WebSearchResult, SPDSearchResult]) -> str:
        """Get formatted reference string for display.
        
        Previously: get_source_reference()
        
        Args:
            source: Source type (e.g. SOURCE_ALL, SOURCE_DOCUMENT, SOURCE_DIALOG)
            document: Source document
            
        Returns:
            Formatted reference string
        """
        ref_obj = self.get_source_reference_object(source, document)
        if ref_obj and hasattr(ref_obj, 'label'):
            return ref_obj.label
        return None
        
    def get_source_reference_object(self, source: str, document: Union[QuepasaDocument, WebSearchResult, SPDSearchResult]) -> SourceReference:
        """Get reference object for API response.
        
        Previously: get_source_reference_object()
        
        Args:
            source: Source type (e.g. SOURCE_ALL, SOURCE_DOCUMENT, SOURCE_DIALOG)
            document: Source document
            
        Returns:
            SourceReference object
        """
        # Get format based on source type
        label_format = self._get_label_format(source, document)
        
        # Get reference ID based on format
        label = label_format.emoji + " " + self._get_label(document, label_format.format)
            
        if isinstance(document, QuepasaDocument):
            return SourceReference(
                label=label,
                url=document.url,
                source_type=document.type,
                created_at=document.created_at
            )
        
        elif isinstance(document, WebSearchResult):
            return SourceReference(
                label=label,
                url=document.url,
            )
        
        url = None
        if document:
            if hasattr(document, 'url'):
                url = document.url
            elif isinstance(document, dict) and 'url' in document:
                url = document['url']

        return SourceReference(
            label=label,
            url=url
        )
        
    def _get_label_format(self, source: str, document: Union[QuepasaDocument, WebSearchResult, SPDSearchResult]) -> ReferenceFormat:
        """Get reference format configuration based on source type.
        
        Args:
            source: Source type
            document: Source document
            
        Returns:
            ReferenceFormat configuration
        """
        emoji = ""
        format = "title"
        
        if (
            isinstance(document, QuepasaDocument) and document.type in [DOCUMENT_TYPE_DOCUMENT, DOCUMENT_TYPE_PRODUCT]
            or isinstance(document, WebSearchResult)
        ):
            emoji = "🌐"
            format = "host"

        elif isinstance(document, QuepasaDocument) and document.type == DOCUMENT_TYPE_DIALOG:
            emoji = "💬"
            format = "date"
                
        return ReferenceFormat(
            emoji=emoji,
            format=format
        )
        
    def _get_label(self, document: Union[QuepasaDocument, WebSearchResult, SPDSearchResult], format: str) -> str:
        """Get reference ID based on format.
        
        Args:
            document: Source document
            format: Reference format ('date', 'host', or 'number')
            
        Returns:
            Reference ID string
        """
        if format == 'date':
            return get_link_index_from_date(self.get_language_code(), document.created_at)
        
        elif format == 'host':
            return get_link_index_from_url(document.url)
            
        return document.get('title', "")  # Default to title format
