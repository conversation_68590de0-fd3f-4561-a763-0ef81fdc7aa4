import pytest
from unittest.mock import patch
from configuration.base.question_classification import QuestionClassificationConfig
from quepasa.searcher.models.request import QuepasaRequest
from src.lib.constants import (
    ANSWER_INTENT_GENERAL,
    ANSWER_INTENT_SEARCH,
    ANSWER_INTENT_UNKNOWN
)
from src.lib.llm.providers import LLMProvider

@pytest.fixture
def question_config():
    return QuestionClassificationConfig("test_client")

@pytest.fixture
def base_request():
    return QuepasaRequest(
        client="test_client",
        question="test question",
        source="telegram"
    )

def test_get_classification_model_name(question_config, base_request):
    """Test getting classification model name."""
    question_config.set_request(base_request)
    provider, model = question_config.get_classification_model_name(source="telegram")
    assert provider == LLMProvider.NEBIUS
    assert model == "Qwen/Qwen2.5-72B-Instruct-fast"

def test_get_intents(question_config, base_request):
    """Test getting available intents."""
    question_config.set_request(base_request)
    intents = question_config.get_intents(source="telegram")
    assert ANSWER_INTENT_GENERAL in intents
    assert ANSWER_INTENT_SEARCH in intents

def test_get_classification_messages(question_config, base_request):
    """Test getting classification messages."""
    question_config.set_request(base_request)
    messages = question_config.get_classification_messages(source="telegram")
    assert isinstance(messages, list)
    assert len(messages) > 0
    assert all(isinstance(m, dict) for m in messages)
    assert all("role" in m and "content" in m for m in messages)

def test_get_classification_with_empty_question(question_config, base_request):
    """Test classification with empty question."""
    # Empty question should raise ValueError
    with pytest.raises(ValueError, match="Question must be a non-empty string"):
        request = QuepasaRequest(
            client="test_client",
            question="",
            source="telegram"
        )
        
    # Test with whitespace-only question
    with pytest.raises(ValueError, match="Question must be a non-empty string"):
        request = QuepasaRequest(
            client="test_client",
            question="   ",
            source="telegram"
        )

def test_get_classification_with_non_english_question(question_config, base_request):
    """Test classification with non-English question."""
    request = QuepasaRequest(
        client="test_client",
        question="¿Cómo estás?",
        source="telegram",
        language="es"
    )
    question_config.set_request(request)
    with patch.object(question_config, 'get_classification_model_name', return_value=("replicate", "meta-llama/Llama-3.2-1B")):
        result = question_config.get_question_classification(source="telegram")
        assert result["intent"] in [ANSWER_INTENT_GENERAL, ANSWER_INTENT_SEARCH, ANSWER_INTENT_UNKNOWN]

def test_get_classification_with_history(question_config, base_request):
    """Test classification with chat history."""
    request = QuepasaRequest(
        client="test_client",
        question="What's the weather?",
        source="telegram"
    )
    question_config.set_request(request)
    
    history_list = [
        {"role": "user", "content": "Hi"},
        {"role": "assistant", "content": "Hello", "request": {"classification": {"class": "general"}}}
    ]
    
    with patch.object(question_config, 'get_classification_model_name', return_value=("replicate", "meta-llama/Llama-3.2-1B")):
        result = question_config.get_question_classification(source="telegram", history_list=history_list)
        assert result["intent"] in [ANSWER_INTENT_GENERAL, ANSWER_INTENT_SEARCH, ANSWER_INTENT_UNKNOWN]

def test_get_classification_with_invalid_llm_response(question_config, base_request):
    """Test classification with invalid LLM response."""
    question_config.set_request(base_request)
    with patch.object(question_config, 'get_classification_model_name', return_value=("replicate", "meta-llama/Llama-3.2-1B")):
        result = question_config.get_question_classification(source="telegram")
        assert result["intent"] in [ANSWER_INTENT_GENERAL, ANSWER_INTENT_SEARCH, ANSWER_INTENT_UNKNOWN]
