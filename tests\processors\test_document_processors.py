import pytest
import requests
from pathlib import Path
from quepasa.crawler.processors.document_processor import DocumentProcessor
from quepasa.crawler.processors.pdf_processor import PDFProcessor
from quepasa.crawler.processors.pptx_processor import PPTXProcessor
from pptx import Presentation
import io

# Test file URLs - these are common sample files from public sources
SAMPLE_FILES = {
    'pdf': 'https://www.learningcontainer.com/wp-content/uploads/2019/09/sample-pdf-file.pdf',
    'docx': 'https://calibre-ebook.com/downloads/demos/demo.docx',
    'xlsx': 'https://filesamples.com/samples/document/xlsx/sample1.xlsx',
    'pptx': 'https://wiki.documentfoundation.org/images/4/47/Extlst-test.pptx'
}

@pytest.fixture(scope="session")
def test_files_dir():
    """Create and return a directory for test files"""
    dir_path = Path(__file__).parent / "../data"
    dir_path.mkdir(exist_ok=True)
    return dir_path

@pytest.fixture(scope="session")
def sample_files(test_files_dir):
    """Download and cache test files"""
    downloaded_files = {}
    
    for file_type, url in SAMPLE_FILES.items():
        file_path = test_files_dir / f"sample.{file_type}"
        
        # Download only if file doesn't exist
        if not file_path.exists():
            try:
                response = requests.get(url)
                response.raise_for_status()
                file_path.write_bytes(response.content)
            except Exception as e:
                pytest.skip(f"Failed to download {file_type} test file: {e}")
        
        downloaded_files[file_type] = file_path.read_bytes()
    
    return downloaded_files

class TestPDFProcessor:
    def test_process_pdf(self, sample_files):
        processor = PDFProcessor()
        meta = {'filename': 'test.pdf'}
        
        result = processor.process(sample_files['pdf'], meta)
        
        assert 'result' in result
        assert 'chunks' in result['result']
        assert isinstance(result['result']['chunks'], list)
        assert len(result['result']['chunks']) > 0
        
        # Verify chunk structure
        for chunk in result['result']['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('page ')
        
        assert 'title' in result['result']
        assert result['result']['filename'] == 'test.pdf'

class TestDocumentProcessor:
    def test_process_docx(self, sample_files):
        processor = DocumentProcessor()
        meta = {
            'filename': 'test.docx',
            'extension': '.docx'
        }
        
        result = processor.process(sample_files['docx'], meta)
        
        assert 'result' in result
        assert 'chunks' in result['result']
        assert isinstance(result['result']['chunks'], list)
        assert len(result['result']['chunks']) > 0
        
        # Verify chunk structure
        for chunk in result['result']['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'] in ['header', 'main content', 'table', 'inline shape', 'footer']
        
        assert 'title' in result['result']
        assert result['result']['filename'] == 'test.docx'

    def test_process_xlsx(self, sample_files):
        processor = DocumentProcessor()
        meta = {
            'filename': 'test.xlsx',
            'extension': '.xlsx'
        }
        
        result = processor.process(sample_files['xlsx'], meta)
        
        assert 'result' in result
        assert 'chunks' in result['result']
        assert isinstance(result['result']['chunks'], list)
        assert len(result['result']['chunks']) > 0
        
        # Verify chunk structure
        for chunk in result['result']['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('sheet ')
        
        assert 'title' in result['result']
        assert result['result']['filename'] == 'test.xlsx'

    def test_invalid_extension(self):
        processor = DocumentProcessor()
        meta = {
            'filename': 'test.invalid',
            'extension': '.invalid'
        }
        
        result = processor.process(b'content', meta)
        assert result['status'] == 'error'
        assert 'Unsupported document type' in result['error']

class TestPPTXProcessor:
    def test_process_pptx(self, sample_files):
        processor = PPTXProcessor()
        meta = {'filename': 'test.pptx'}
        
        result = processor.process(sample_files['pptx'], meta)
        
        assert 'result' in result
        assert 'chunks' in result['result']
        assert isinstance(result['result']['chunks'], list)
        assert len(result['result']['chunks']) > 0
        
        # Verify chunk structure
        for chunk in result['result']['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('slide ')
        
        assert 'title' in result['result']
        assert result['result']['filename'] == 'test.pptx'

    def test_process_empty_pptx(self):
        """Test handling of empty or corrupted PPTX files"""
        processor = PPTXProcessor()
        meta = {'filename': 'empty.pptx'}
        
        result = processor.process(b'invalid content', meta)
        assert result['status'] == 'error'
        assert 'error' in result

    @pytest.mark.parametrize("slide_content", [
        "# Title\nContent",
        "## Section\nBullet points", 
        "Table\tData\tMore Data"
    ])
    def test_text_extraction_patterns(self, slide_content):
        """Test different patterns of content extraction"""
        processor = PPTXProcessor()
        meta = {'filename': 'test.pptx'}
        
        # Create a valid PPTX file in memory
        prs = Presentation()
        slide = prs.slides.add_slide(prs.slide_layouts[0])
        shape = slide.shapes.title
        shape.text = slide_content
        
        # Save to bytes
        pptx_bytes = io.BytesIO()
        prs.save(pptx_bytes)
        pptx_content = pptx_bytes.getvalue()
        
        result = processor.process(pptx_content, meta)['result']
        
        assert 'chunks' in result
        assert isinstance(result['chunks'], list)
        assert len(result['chunks']) > 0
        
        # Verify chunk structure
        for chunk in result['chunks']:
            assert 'text' in chunk
            assert 'position' in chunk
            assert chunk['position'].startswith('slide ')
            assert any(text in chunk['text'] for text in slide_content.split('\n'))