import os
os.environ['MINIO_HOST'] = 'localhost'  # Mock ENV before importing config

import sys
import json
import logging
import asyncio

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the TelegramProcessor and TelegramHandler
from quepasa.crawler.processors.telegram_processor import TelegramProcessor
from quepasa.api.handlers.telegram_handler import TelegramHandler
from src.lib.files import QuepasaFiles

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Test configuration
TEST_CLIENT_ID = 'test_client'
TEST_DOMAIN = 'test_domain'

qp_files = QuepasaFiles(
    bucket_name='quepasa-files',
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin',
    debug_flag=False
)

class TelegramProcessorTester:
    def __init__(self):
        self.qp_files = QuepasaFiles()
        self.telegram_handler = TelegramHandler(files=self.qp_files)
        
    async def authenticate_telegram(self, client_id, phone_number):
        """
        Handle Telegram authentication flow (request code, input verification, sign-in)
        
        Args:
            client_id: Client identifier
            phone_number: Phone number for Telegram authentication
            
        Returns:
            bool: True if authentication was successful, False otherwise
        """
        try:
            # Request authentication code
            logger.info(f"Requesting authentication code for {phone_number}")
            code_result = await self.telegram_handler.request_code(client_id, phone_number)
            
            if code_result.get("status") == "error":
                logger.error(f"Failed to request code: {code_result.get('message')}")
                return False
            
            phone_code_hash = code_result.get("data", {}).get("phone_code_hash")
            logger.info(f"Phone code hash: {phone_code_hash}")

            if not phone_code_hash:
                logger.error("No phone code hash received")
                return False
                
            # If already authorized, no need to continue with verification
            if code_result.get("message") == "User is already authorized":
                logger.info("User is already authorized, no need for verification")
                return True
                
            # Get verification code from user input
            verification_code = input(f"Enter the verification code sent to {phone_number}: ")
            
            # Check if 2FA password might be needed
            password = None
            password_maybe_needed = input("Do you need to enter a 2FA password? (y/n): ").lower() == 'y'
            if password_maybe_needed:
                password = input("Enter your 2FA password: ")
            
            # Verify the code
            verify_result = await self.telegram_handler.verify_code(
                client_id, 
                phone_number, 
                verification_code,
                phone_code_hash,
                password
            )
            
            if verify_result.get("status") == "error":
                logger.error(f"Failed to verify code: {verify_result.get('message')}")
                return False
                
            logger.info("Telegram authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Authentication failed: {str(e)}", exc_info=True)
            return False
        
    async def run_test_async(self, chat_url: str, client_id: str, phone_number: str = None):
        """
        Run a test of the TelegramProcessor with async support
        
        Args:
            channel_id: Telegram channel ID or username to process
            client_id: Client ID for metadata lookup
            phone_number: Phone number for Telegram authentication
            
        Returns:
            Dict with test results
        """
        try:
            # Authenticate with Telegram if a phone number is provided
            if phone_number:
                auth_success = await self.authenticate_telegram(client_id, phone_number)
                if not auth_success:
                    return {
                        'status': 'error',
                        'error': 'Telegram authentication failed'
                    }
                
            # Create metadata for the processor
            meta = {
                'client_id': client_id,
                'domain': TEST_DOMAIN,
                'id': chat_url,
                'url': chat_url,
                'source': 'telegram',
                'phone_number': phone_number  # Pass phone number directly in metadata
            }
            
            # Initialize the processor
            processor = TelegramProcessor()
            
            # Process the channel - await the async process method
            logger.info(f"Process Telegram chat url: {chat_url}")
            result = await processor.process(chat_url, meta)
            
            # Display the result
            logger.info(f"Processing result: {result['status']}")
            if result['status'] == 'success':
                documents = result['results']
                logger.info(f"Total documents: {len(documents)}")
                
                # Save result to a file
                channel_id = chat_url.split('/')[-1]
                output_file = f"telegram_result_{channel_id}.json"
                with open(output_file, 'w') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)

                logger.info(f"Saved results to {output_file}")
                
            else:
                logger.error(f"Error: {result.get('error', 'Unknown error')}")
                
            return result
            
        except Exception as e:
            logger.error(f"Test failed: {str(e)}", exc_info=True)
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def run_test(self, channel_id, client_id, phone_number=None):
        """
        Run the async test function in an event loop
        
        Args:
            channel_id: Telegram channel ID or username to process
            client_id: Client ID for metadata lookup
            phone_number: Phone number for Telegram authentication
            
        Returns:
            Dict with test results
        """
        loop = asyncio.get_event_loop()
        if loop.is_closed():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
        try:
            return loop.run_until_complete(
                self.run_test_async(channel_id, client_id, phone_number)
            )
        finally:
            # Don't close the loop if it's the default one
            if loop != asyncio.get_event_loop():
                loop.close()

if __name__ == "__main__":
    # Get parameters from command line arguments or use defaults
    import argparse
    parser = argparse.ArgumentParser(description='Test the TelegramProcessor')
    parser.add_argument('--chat-url', '-c', help='Chat URL to process', required=True)
    parser.add_argument('--client-id', '-i', help='Client ID for metadata', default=TEST_CLIENT_ID)
    parser.add_argument('--phone', '-p', help='Phone number for Telegram authentication')
    args = parser.parse_args()
    
    # Create the tester and run the test
    tester = TelegramProcessorTester()
    result = tester.run_test(args.chat_url, args.client_id, args.phone)
    
    # Exit with status code based on result
    sys.exit(0 if result['status'] == 'success' else 1) 