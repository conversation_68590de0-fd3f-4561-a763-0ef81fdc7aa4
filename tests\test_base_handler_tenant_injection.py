import pytest
from unittest.mock import Mock, patch
from flask import Response

from quepasa.searcher.api.base import BaseAPIHandler
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.common.config_models import TenantConfig, TenantModel


class DummyHandler(BaseAPIHandler):
    def _handle_request_internal(self, method, url, headers):
        # Return a basic response tuple
        return ({'status': 'ok', 'data': {}}, 200, {})


def make_tenant_config(name="acme"):
    tenant = TenantModel(id=1, name=name, enabled=True, settings={})
    return TenantConfig(tenant=tenant, collections=[], areas=[], attributes=[], conversational_search_configs=[], conversational_prompt_configs=[])


def test_sets_tenant_config_on_request(monkeypatch):
    # Prepare config with request containing x_customer_id
    with patch('configuration.main.default.QuepasaConfigurationHub') as mock_hub:
        cfg = mock_hub.return_value
        req = QuepasaRequest(question="q", protocol="http")
        req.x_customer_id = "acme"
        cfg.request = req

        # Authenticate always ok
        with patch('quepasa.searcher.api.base.AuthManager') as auth_mgr:
            auth_mgr.return_value.authenticate.return_value = Mock(is_authorized=True)

            # Return tenant config for name
            with patch('quepasa.searcher.api.base.get_tenant_config', return_value=make_tenant_config("acme")):
                handler = DummyHandler(cfg)
                body, status, headers = handler.handle_request("POST", "/answer", {})

                assert status == 200
                assert isinstance(cfg.request.tenant_config, TenantConfig)
                assert cfg.request.tenant_config.tenant.name == "acme"


def test_handles_missing_tenant_config(monkeypatch):
    with patch('configuration.main.default.QuepasaConfigurationHub') as mock_hub:
        cfg = mock_hub.return_value
        req = QuepasaRequest(question="q", protocol="http")
        req.x_customer_id = "unknown"
        cfg.request = req

        with patch('quepasa.searcher.api.base.AuthManager') as auth_mgr:
            auth_mgr.return_value.authenticate.return_value = Mock(is_authorized=True)

            # Return None for missing tenant
            with patch('quepasa.searcher.api.base.get_tenant_config', return_value=None):
                handler = DummyHandler(cfg)
                # It should catch exception and return 500 error tuple
                resp = handler.handle_request("POST", "/answer", {})
                assert isinstance(resp, tuple)
                assert resp[1] == 500
                assert resp[0].get('error')
