import os
import time
import pytest
from src.lib.files import QuepasaFiles
from quepasa.crawler.main import CrawlerOrchestrator

from src.lib.batch_utils import ENV, BatchUtils, BatchState

# Test configuration
TEST_CONTAINER_ID = 'test_container'
TEST_CLIENT_ID = 'test_client'

qp_files = QuepasaFiles(
    bucket_name='quepasa-files',
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin',
    debug_flag=False
)

# Client ID for testing
TEST_CLIENT_ID = 'test_client_crawler_orchestrator'

base_path = f"test/storage/crawler/api-v1/{TEST_CLIENT_ID}"

def create_test_files():
    # CSV with sample data
    qp_files.set_text(f'{base_path}/sample.csv', """
Department,Employee,Position,Salary,Years,Office,Projects,Performance,Education,Skills
Sales,<PERSON>,Senior Manager,85000,8,<PERSON> York,Project A|Project B|Project C,Excellent,MBA,Leadership|Communication|Strategy
Marketing,<PERSON>,Director,95000,10,<PERSON> Angeles,Project X|Project Y,Outstanding,PhD,Marketing|Analytics|Management
Engineering,<PERSON>,Lead Developer,92000,6,San Francisco,Backend|Frontend|Mobile,Excellent,MSc,Python|Java|Cloud
    """.strip())
    
    qp_files.set_text(f'{base_path}/sample.md', """
# Comprehensive Guide to Modern Software Development

## Introduction

Software development has evolved significantly over the past decades, incorporating new methodologies, tools, and practices. This guide provides a detailed overview of modern software development practices and principles.

## Agile Development Methodology

### Core Principles

Agile development is based on several key principles:
- Iterative development
- Continuous feedback
- Adaptive planning
- Rapid delivery
    """.strip())
    
    qp_files.set_text(f'{base_path}/sample.txt', """
Introduction to Advanced Technology Systems

In the rapidly evolving landscape of modern technology, understanding the fundamental principles and advanced concepts of technological systems has become increasingly crucial. This comprehensive guide aims to explore various aspects of technology systems, their implementations, and their impact on different sectors.

Chapter 1: Basic Principles and Foundations

The foundation of any technological system lies in its basic principles. These principles encompass various aspects including data processing, system architecture, and interaction models.
    """.strip())

def create_test_batch():

    """Test the CrawlerOrchestrator functionality"""
    # Create test files
    create_test_files()

    # Test data with both URLs and local files
    test_batch = {
        'client_id': TEST_CLIENT_ID,
        'domain': 'test',
        'action': 'upsert',
        'urls': [
            'https://raw.githubusercontent.com/openai/openai-cookbook/main/examples/Question_answering_using_embeddings.md',
            'https://www.youtube.com/watch?v=GIg4HlSp_ZE',  # A real YouTube video about AI
            'https://www.pdf995.com/samples/pdf.pdf',  # A sample PDF
        ],
        'files': [
            f'{base_path}/sample.csv',
            f'{base_path}/sample.md',
            f'{base_path}/sample.txt'
        ]
    }

    batch_id = BatchUtils.create_batch(TEST_CLIENT_ID, BatchState.UPLOADED, test_batch)
    
    print("Created test batch with ID:", batch_id)
    return batch_id

def test_crawler_orchestrator():
    """Test the DataProcessorOrchestrator functionality"""
    
    # Create test batch
    batch_id = create_test_batch()

    # Initialize orchestrator
    orchestrator = CrawlerOrchestrator()
    
    # Test full orchestrator run
    orchestrator.run(TEST_CLIENT_ID)

    state, data = BatchUtils.get_batch_status(TEST_CLIENT_ID, batch_id)
    print("State", state)
    print("Data", data)
    assert state == BatchState.BACKLOG

if __name__ == '__main__':
    pytest.main([__file__]) 