from dataclasses import dataclass, field
from typing import List, Optional

@dataclass
class BatchAccess:
    user_ids: Optional[List[str]] = field(default_factory=list)
    groups: Optional[List[str]] = field(default_factory=list)
    roles: Optional[List[str]] = field(default_factory=list)

@dataclass
class BatchResult:
    """Result of batch operation"""
    batch_id: str
    processed_ids: List[str]
    skip_indexing: bool = False 
