from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

@dataclass
class BatchAccess:
    user_ids: Optional[List[str]] = field(default_factory=list)
    groups: Optional[List[str]] = field(default_factory=list)
    roles: Optional[List[str]] = field(default_factory=list)

@dataclass
class BatchResult:
    """Result of batch operation"""
    batch_id: str
    processed_ids: List[str]
    skip_indexing: bool = False

class RefreshConfigResponse(BaseModel):
    status: str = Field(..., description="Status of the operation, e.g., 'ok'")
    message: str = Field(..., description="Human-readable message about the refresh result")
    updated: str = Field(..., description="What was updated by the refresh process")
    summary: Optional[Dict[str, Any]] = Field(default=None, description="Optional summary of dynamic config changes")
