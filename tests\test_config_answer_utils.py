import pytest
import re
from configuration.base.answer_utils import AnswerUtils

def test_get_links_regexp():
    pattern = AnswerUtils._get_reference_regexp()
    assert isinstance(pattern, re.Pattern)
    
    # Test valid reference patterns
    assert pattern.search("[1]")
    assert pattern.search("[1][2]")
    assert pattern.search("[1] and [2]")
    assert pattern.search("[1],[2],[3]")
    
    # Test invalid patterns
    assert not pattern.search("no refs")
    assert not pattern.search("[a]")
    assert not pattern.search("[1a]")

def test_get_number_of_continuous_refs():
    # Test continuous references
    assert AnswerUtils._get_number_of_continuous_refs("[1][2][3]") == 3
    assert AnswerUtils._get_number_of_continuous_refs("[1, 2, 3]") == 3
    assert AnswerUtils._get_number_of_continuous_refs("[1] and [2]") == 2
    
    # Test non-continuous references
    assert AnswerUtils._get_number_of_continuous_refs("[1][3][4]") == 0
    assert AnswerUtils._get_number_of_continuous_refs("[2][1]") == 0
    
    # Test single reference
    assert AnswerUtils._get_number_of_continuous_refs("[1]") == 0
    
    # Test invalid input
    assert AnswerUtils._get_number_of_continuous_refs("") == 0
    assert AnswerUtils._get_number_of_continuous_refs("no refs") == 0

def test_has_bad_reference_by_regexp():
    # Test bad reference patterns
    assert AnswerUtils._has_bad_reference_by_regexp(
        r'\[\d+\](?:\s*,?\s*\[\d+\])*',
        "[1][2][3][4]",
        count=3
    )
    
    # Test acceptable patterns
    assert not AnswerUtils._has_bad_reference_by_regexp(
        r'\[\d+\](?:\s*,?\s*\[\d+\])*',
        "[1][2]",
        count=3
    )
    assert not AnswerUtils._has_bad_reference_by_regexp(
        r'\[\d+\](?:\s*,?\s*\[\d+\])*',
        "Text with [1] single reference",
        count=3
    )
