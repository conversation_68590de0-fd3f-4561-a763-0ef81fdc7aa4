from __future__ import annotations

import os
import j<PERSON>
from typing import List, Dict, <PERSON>, Async<PERSON><PERSON>ator, <PERSON><PERSON>
from anthropic import <PERSON>throp<PERSON>

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .base import BaseLLM
from .cache import LLMCache<PERSON><PERSON>in
from .providers import LL<PERSON>rovider
from src.lib.utils import get_tool_call_dict_list

logger = Que<PERSON>aLogger().get_instance(__name__)

class AnthropicLLM(BaseLLM, LLMCacheMixin):
    """Class for interacting with Anthropic's language models."""

    def __init__(self, api_key=None):
        """Initialize the Anthropic LLM.
        
        Args:
            api_key (str, optional): The API key for Anthropic. If not provided,
                will try to get it from ANTHROPIC_API_KEY environment variable.
        """
        super().__init__()
        self.api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
        if not self.api_key:
            raise ValueError("ANTHROPIC_API_KEY environment variable is required")
        self.client = Anthropic(api_key=self.api_key)

    @property
    def provider(self) -> <PERSON><PERSON><PERSON>ider:
        """Get the provider type."""
        return LLMProvider.ANTHROPIC

    def process_messages_for_anthropic(self, prompt_list: List[Dict[str, str]]) -> tuple:
        """
        Process messages for Anthropic API by extracting system message.
        
        Anthropic requires system messages to be passed separately, not in the messages list.
        
        Args:
            prompt_list: List of prompts with role and content
            
        Returns:
            Tuple of (system_prompt, user_assistant_messages)
        """
        system_prompt = None
        user_assistant_messages = []
        
        for message in prompt_list:
            if message["role"].lower() == "system":
                # Extract system message
                system_prompt = message["content"]
            else:
                # Keep other messages
                user_assistant_messages.append(message)
        
        return system_prompt, user_assistant_messages

    def get_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> str:
        """Get an answer from the model.
        
        Args:
            model_version: The model version to use (e.g. "claude-3-opus")
            prompt_list: List of prompts with role and content
            answer_prompt_size: Maximum size of the answer
            json_mode: Whether to force JSON output format
            
        Returns:
            The model's response text
        """
        try:
            # Process messages for Anthropic API
            system_prompt, messages = self.process_messages_for_anthropic(prompt_list)
            
            # Create API request with or without system message
            kwargs = {
                "model": model_version,
                "messages": messages,
                "max_tokens": answer_prompt_size
            }
            
            # Add system parameter only if a system message was found
            if system_prompt:
                kwargs["system"] = system_prompt
                
            response = self.client.messages.create(**kwargs)
            # Extract the text from the response content
            if response.content and len(response.content) > 0:
                if isinstance(response.content[0], dict) and 'text' in response.content[0]:
                    return response.content[0]['text']
                elif hasattr(response.content[0], 'text'):
                    return response.content[0].text
            return ""
        
        except Exception as e:
            logger.error(f"Failed to get answer from Anthropic: {e}")
            raise Exception(f"Failed to get answer from Anthropic: {e}")

    def get_streaming_answer(self, model_version: str, prompt_list: List[Dict[str, str]], answer_prompt_size: int, json_mode: bool = False) -> AsyncGenerator[str, None]:
        """Get a streaming answer from the model.
        
        Args:
            model_version: The model version to use (e.g. "anthropic:claude-3-opus")
            prompt_list: List of prompts with role and content
            answer_prompt_size: Maximum size of the answer
            json_mode: Whether to force JSON output format
            
        Yields:
            Chunks of the model's response text
        """
        try:
            # Process messages for Anthropic API
            system_prompt, messages = self.process_messages_for_anthropic(prompt_list)
            
            # Create API request with or without system message
            kwargs = {
                "model": model_version,
                "messages": messages,
                "max_tokens": answer_prompt_size
            }
            
            # Add system parameter only if a system message was found
            if system_prompt:
                kwargs["system"] = system_prompt
                
            # Create a streaming response
            with self.client.messages.stream(**kwargs) as stream:
                # Iterate through the stream events
                for text in stream.text_stream:
                    yield text

        except Exception as e:
            logger.error(f"Failed to get streaming answer from Anthropic: {e}")
            raise Exception(f"Failed to get streaming answer from Anthropic: {e}")

    def get_tools_answer(self, model_version: str, prompt_list: List[Dict[str, str]], tools: List[Dict[str,Any]], answer_prompt_size: int) -> Tuple[str, list]:
        """Get a completion from the LLM with function calling.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            tools: Tools used for Agentic/Function calling
            answer_prompt_size: Maximum response size
            
        Returns:
            The generated response
        """
        try:
            # Process messages for Anthropic API
            system_prompt, messages = self.process_messages_for_anthropic(prompt_list)
            
            # Create API request with or without system message
            kwargs = {
                "model": model_version,
                "messages": messages,
                "max_tokens": answer_prompt_size,
                "tools": tools
            }
            
            # Add system parameter only if a system message was found
            if system_prompt:
                kwargs["system"] = system_prompt
                
            response = self.client.messages.create(**kwargs)
            
            # Extract content and tool calls
            content = ""
            tool_calls = []
            
            if response.content and len(response.content) > 0:
                for content_block in response.content:
                    if content_block.type == "text":
                        content = content_block.text
                    elif content_block.type == "tool_use":
                        tool_calls.append(content_block)
            
            return content, get_tool_call_dict_list(tool_calls)
            
        except Exception as e:
            logger.error(f"Failed to get tools answer from Anthropic: {e}")
            raise Exception(f"Failed to get tools answer from Anthropic: {e}")

    async def get_streaming_tools_answer(self, model_version: str, prompt_list: List[Dict[str, str]], tools: List[Dict[str,Any]], answer_prompt_size: int) -> AsyncGenerator[Tuple[str, list], None]:
        """Get a streaming completion from the LLM with function calling.
        
        Args:
            model_version: Model version to use
            prompt_list: List of prompts with 'role' and 'content'
            tools: Tools used for Agentic/Function calling
            answer_prompt_size: Maximum response size
            
        Yields:
            Generated response chunks with tool calls
        """
        try:
            # Process messages for Anthropic API
            system_prompt, messages = self.process_messages_for_anthropic(prompt_list)
            
            # Create API request with or without system message
            kwargs = {
                "model": model_version,
                "messages": messages,
                "max_tokens": answer_prompt_size,
                "tools": tools
            }
            
            # Add system parameter only if a system message was found
            if system_prompt:
                kwargs["system"] = system_prompt
                
            # Create a streaming response
            with self.client.messages.stream(**kwargs) as stream:
                content = ""
                tool_calls = []
                
                # Iterate through the stream events
                for event in stream:
                    if event.type == "content_block_delta":
                        if event.delta.type == "text_delta":
                            content += event.delta.text
                            yield content, tool_calls
                        elif event.delta.type == "tool_use":
                            # Handle tool use deltas - convert to dictionary format
                            tool_call_dict = {
                                'id': getattr(event.delta, 'id', None),
                                'type': 'tool_use',
                                'function': {
                                    'name': getattr(event.delta, 'name', None),
                                    'arguments': getattr(event.delta, 'input', {})
                                }
                            }
                            tool_calls.append(tool_call_dict)
                            yield content, tool_calls
                            
                    elif event.type == "message_delta":
                        # Handle message deltas
                        if event.delta.content:
                            for content_block in event.delta.content:
                                if content_block.type == "text":
                                    content += content_block.text
                                    yield content, tool_calls
                                elif content_block.type == "tool_use":
                                    # Convert tool use to dictionary format
                                    tool_call_dict = {
                                        'id': getattr(content_block, 'id', None),
                                        'type': 'tool_use',
                                        'function': {
                                            'name': getattr(content_block, 'name', None),
                                            'arguments': getattr(content_block, 'input', {})
                                        }
                                    }
                                    tool_calls.append(tool_call_dict)
                                    yield content, tool_calls

        except Exception as e:
            logger.error(f"Failed to get streaming tools answer from Anthropic: {e}")
            raise Exception(f"Failed to get streaming tools answer from Anthropic: {e}") 