from typing import Dict, Any, List, Optional
import os
import requests

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ..models.request import Que<PERSON><PERSON>Request
from ..models.web import WebSearchResult
from ..sources.base import STATUS_ERROR, STATUS_SUCCESS
from ..models.response import QuepasaResponse

logger = QuepasaLogger().get_instance(__name__)

# Constants
BING_ENDPOINT = "https://api.bing.microsoft.com/v7.0/search"
WEB_MAXIMUM_SEARCHES = 50

class WebSearchManager:
    """Manager for web search results using Bing API"""
    
    def __init__(self, api_key=None):
        """Initialize web search manager"""
        self.api_key = api_key or os.getenv('BING_SUBSCRIPTION_API_KEY')
        if not self.api_key:
            logger.error("BING_SUBSCRIPTION_API_KEY environment variable not set")
            
    def search(self, request: QuepasaRequest) -> List[WebSearchResult]:
        """Execute web search and return results
        
        Args:
            request: Search request
            
        Returns:
            List of search results
            
        Raises:
            ValueError: If Bing API key not configured
            Exception: For other search errors
        """
        # Validate Bing API key
        if not self.api_key:
            raise ValueError("Bing API key not configured")
            
        try:
            # Get search results
            documents = self._search_web(request)
            return documents
            
        except Exception as e:
            logger.error(f"Error searching web: {str(e)}")
            raise Exception(f"Error searching web: {str(e)}")
            
    def _search_web(self, request: QuepasaRequest) -> List[WebSearchResult]:
        """Execute web search using Bing API
        
        Args:
            request: Search request
            
        Returns:
            List of search results
            
        Raises:
            Exception: For search errors
        """
        # Build search parameters
        params = {
            'q': request.question,
            'count': WEB_MAXIMUM_SEARCHES,
            'answerCount': WEB_MAXIMUM_SEARCHES
        }
        
        # Add country code if specified
        if request.country:
            country_code = request.country
            if country_code == 'irl':  # Ireland
                params['cc'] = 'IE'
            elif country_code == 'mne':  # Montenegro
                params['cc'] = 'ME'
            else:
                params['cc'] = country_code.upper()
            params['q'] += f" location:{params['cc']}"
            
        # Execute search
        headers = {'Ocp-Apim-Subscription-Key': self.api_key}
        response = requests.get(BING_ENDPOINT, headers=headers, params=params)
        response.raise_for_status()
        
        # Process results
        results = []
        response_json = response.json()
        if (
            'webPages' in response_json
            and 'value' in response_json['webPages']
            and response_json['webPages']['value']
        ):
            for item in response_json['webPages']['value']:
                results.append(self._format_search_result(item))
                
        return results
        
    def _format_search_result(self, result: Dict[str, Any]) -> WebSearchResult:
        """Format Bing search result
        
        Args:
            result: Raw search result
            
        Returns:
            Formatted search result
        """
        text = f"{result['name']}\n{result['snippet']}"
        return WebSearchResult(
            url=result['url'],
            title=result['name'],
            snippet=result['snippet'],
            text=text
        ) 