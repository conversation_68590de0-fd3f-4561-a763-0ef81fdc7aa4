import pytest
from unittest.mock import patch, MagicMock
from quepasa.crawler.orchestrator import CrawlerOrchestrator
from src.lib.batch_utils import BatchState

@pytest.fixture
def orchestrator():
    return CrawlerOrchestrator()

@pytest.fixture
def mock_batch_utils():
    with patch('quepasa.crawler.orchestrator.BatchUtils') as mock:
        mock.get_batch_files_by_client.return_value = {
            'test-client': [
                {'file': 'test/storage/batch/uploaded/12345.test-client.json', 'ts': 123456789},
                {'file': 'test/storage/batch/uploaded/67890.test-client.json', 'ts': 123456790}
            ]
        }
        mock.parse_batch_filename.return_value = ('test-client', '12345')
        yield mock

@pytest.fixture
def mock_process_batch():
    with patch('quepasa.crawler.orchestrator.process_batch') as mock:
        mock.apply_async.return_value = MagicMock()
        yield mock

def test_orchestrator_run(orchestrator, mock_batch_utils, mock_process_batch):
    """Test the main run method of the orchestrator"""
    # Configure mock to parse batch filenames correctly
    def mock_parse_batch_filename(filename):
        parts = filename.split('.')
        if len(parts) >= 2:
            return None, parts[0].split('/')[-1]
        return None, None
    mock_batch_utils.parse_batch_filename.side_effect = mock_parse_batch_filename
    
    # Call the run method
    orchestrator.run()
    
    # Verify that BatchUtils.get_batch_files_by_client was called with UPLOADED state
    mock_batch_utils.get_batch_files_by_client.assert_called_once_with(BatchState.UPLOADED)
    
    # Verify that process_batch.apply_async was called twice (once for each batch)
    assert mock_process_batch.apply_async.call_count == 2
    
    # Verify the arguments for the first call
    mock_process_batch.apply_async.assert_any_call(args=['test-client', '12345'])
    mock_process_batch.apply_async.assert_any_call(args=['test-client', '67890'])

def test_orchestrator_run_with_target(orchestrator, mock_batch_utils, mock_process_batch):
    """Test running the orchestrator with a target client and batch"""
    # Configure mock to return specific batch files
    mock_batch_utils.get_batch_files_by_client.return_value = {
        'target-client': [{'file': 'test/storage/batch/uploaded/12345.target-client.json'}]
    }
    
    # Configure mock to parse batch filenames correctly
    def mock_parse_batch_filename(filename):
        parts = filename.split('.')
        if len(parts) >= 2:
            return None, parts[0].split('/')[-1]
        return None, None
    mock_batch_utils.parse_batch_filename.side_effect = mock_parse_batch_filename
    
    # Call the run method
    orchestrator.run()
    
    # Verify that BatchUtils.get_batch_files_by_client was called with UPLOADED state
    mock_batch_utils.get_batch_files_by_client.assert_called_once_with(BatchState.UPLOADED)
    
    # Verify that process_batch.apply_async was called
    mock_process_batch.apply_async.assert_called_once_with(args=['target-client', '12345'])

def test_orchestrator_no_batches(orchestrator, mock_batch_utils, mock_process_batch):
    """Test orchestrator behavior when no batches are found"""
    # Configure mock to return empty dict
    mock_batch_utils.get_batch_files_by_client.return_value = {}
    
    # Call the run method
    orchestrator.run()
    
    # Verify that process_batch.apply_async was not called
    mock_process_batch.apply_async.assert_not_called() 