from typing import Optional, Dict, Tuple
from dataclasses import dataclass
from configuration.main.default import QuepasaConfigurationHub
from quepasa.common.config_client import get_tenant_config
from quepasa.common.config_models import TenantConfig
from src.lib.constants import AUTH_TYPE_BEARER, AUTH_TYPE_CLIENT_KEY, AUTH_TYPE_EXTERNAL

@dataclass
class AuthResult:
    """Result of authentication check"""
    is_authorized: bool
    client_id: Optional[str] = None
    error: Optional[str] = None

class RequestAuthenticatorManager:
    """Authentication manager for Bearer and client-key.
    
    Header formats:
    - Bearer (tenant-aware): "Bearer <client_id>:<token>"
    - Client-key: X-Customer-Id: <client_id>, Authorization: "client-key <key>"
    """

    def __init__(self):
        """Initialize auth manager"""
        pass

    def authenticate(self, headers: Dict[str, str]) -> AuthResult:
        """Authenticate an incoming request

        Args:
            headers: Request headers

        Returns:
            AuthResult with authorization status and client info
        """
        try:
            auth_header = headers.get('authorization') or headers.get('Authorization')
            if not auth_header:
                return AuthResult(
                    is_authorized=False,
                    error="Missing Authorization header"
                )

            # Check if this is client-key authentication (has X-Customer-Id header)
            x_customer_id = headers.get('x-customer-id') or headers.get('X-Customer-Id')
            if not x_customer_id and auth_header.lower().startswith("client-key "):
                return AuthResult(
                    is_authorized=False,
                    error="Invalid Authorization header format"
                )

            if x_customer_id and auth_header.lower().startswith('client-key '):
                # Handle client-key authentication
                # Extract key from the "client-key some-key" format
                auth_parts = auth_header.split(' ', 1)
                if len(auth_parts) == 2:
                    key = auth_parts[1]
                    return self._handle_client_key_auth(x_customer_id, key)
                else:
                    return AuthResult(
                        is_authorized=False,
                        error="Invalid Authorization header format"
                    )
            else:
                # Handle Bearer authentication
                client_id, auth_type, auth_token = self._parse_auth_header(auth_header)
                if not client_id or not auth_type or not auth_token:
                    return AuthResult(
                        is_authorized=False,
                        error="Invalid Authorization header format"
                    )

                if auth_type.lower() == AUTH_TYPE_BEARER:
                    return self._handle_bearer_auth(client_id, auth_token)
                else:
                    return AuthResult(
                        is_authorized=False,
                        error=f"Unsupported authorization type: {auth_type}"
                    )

        except Exception as e:
            return AuthResult(
                is_authorized=False,
                error=f"Authentication error: {str(e)}"
            )

    def _parse_auth_header(self, auth_header: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """Parse Authorization header into type, token and client ID

        Args:
            auth_header: Authorization header value

        Returns:
            Tuple of (client_id, auth_type, auth_token) or (None, None, None) if invalid
        """
        try:
            auth_parts = auth_header.split(' ', 1)
            if len(auth_parts) == 2:
                auth_type = auth_parts[0]
                full_token = auth_parts[1]
                token_parts = full_token.split(':')
                if len(token_parts) == 2:
                    client_id = token_parts[0]
                    auth_token = token_parts[1]
                    return client_id, auth_type, auth_token

        except:
            pass

        return None, None, None

    def _handle_bearer_auth(self, client_id: str, token: str) -> AuthResult:
        """Handle Bearer token authentication

        Args:
            client_id: Client identifier
            token: Bearer token

        Returns:
            AuthResult with authorization status
        """
        config = QuepasaConfigurationHub.from_client_code(client_id)
        if config.validate_auth_token(token):
            return AuthResult(
                is_authorized=True,
                client_id=client_id
            )

        return AuthResult(
            is_authorized=False,
            error="Invalid Bearer token"
        )

    def _handle_client_key_auth(self, client_id: str, key: str) -> AuthResult:
        """Handle client-key authentication
        
        This method expects the client_id and key to be extracted from headers:
        - X-Customer-Id header becomes client_id
        - Authorization: "client-key some-key" becomes key
        
        Args:
            client_id: Client identifier from X-Customer-Id header
            key: Client key from Authorization header
            
        Returns:
            AuthResult with authorization status and appropriate error messages
        """
        # Validate client_id is not empty
        if not client_id or not client_id.strip():
            return AuthResult(
                is_authorized=False,
                error="Missing X-Customer-Id header"
            )

        # Validate key is not empty
        if not key or not key.strip():
            return AuthResult(
                is_authorized=False,
                error="Invalid or missing client key"
            )

        try:
            tenant_config: Optional[TenantConfig] = get_tenant_config(tenant_name=client_id.strip())
        except Exception:
            tenant_config = None
        
        if tenant_config is None:
            return AuthResult(
                is_authorized=False,
                error="Invalid or missing client key"
            )
        
        if not tenant_config.client_key_is_valid(client_key=key):
            return AuthResult(
                is_authorized=False,
                error="Invalid or missing client key"
            )
        
        return AuthResult(
            is_authorized=True,
            client_id=client_id.strip()
        )