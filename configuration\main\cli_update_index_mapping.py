from elasticsearch import Elasticsearch
from src.lib.utils import get_elasticsearch_config, INDEX_NAME_PREFIX

# This script synchronizes existing Elasticsearch index mappings with the
# canonical mappings defined in configuration/main/cli_create_index.py.
# It attempts to apply the full mapping block (dynamic, dynamic_templates,
# properties). Elasticsearch will add missing fields/templates, but will not
# change incompatible existing field definitions.

from configuration.main.cli_create_index import index_settings, INDEX_SUFFIXES  # reuse canonical mappings

def update_mapping_for_index(es: Elasticsearch, index_name: str) -> None:
    try:
        if not es.indices.exists(index=index_name):
            print(f"[mapping-sync] Index does not exist, skipping: {index_name}")
            return

        mappings = index_settings.get("mappings", {})
        body = {}
        for key in ("dynamic", "dynamic_templates", "properties"):
            if key in mappings:
                body[key] = mappings[key]

        if not body:
            print("[mapping-sync] No mappings found in index_settings; nothing to apply")
            return

        es.indices.put_mapping(index=index_name, body=body)
        print(f"[mapping-sync] Synchronized mapping for index: {index_name}")
    except Exception as e:
        print(f"[mapping-sync] Error updating mapping for {index_name}: {e}")


def main():
    es = Elasticsearch(**get_elasticsearch_config())

    prefix = f"{INDEX_NAME_PREFIX}-v2"
    indices = [f"{prefix}-{suffix}" for suffix in INDEX_SUFFIXES]

    for idx in indices:
        update_mapping_for_index(es, idx)


if __name__ == "__main__":
    main()
