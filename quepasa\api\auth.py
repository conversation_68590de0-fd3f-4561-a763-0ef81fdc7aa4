from fastapi import HTTPException, Request
from quepasa.common.bearer import RequestAuthenticatorManager
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger.get_instance(__name__)

async def verify_auth(request: Request) -> str:
    """Verify request authentication and return client ID"""
    auth_manager = RequestAuthenticatorManager()
    result = auth_manager.authenticate(dict(request.headers.items()))
    if not result.is_authorized:
        raise HTTPException(401, result.error or "Unauthorized")
    return result.client_id