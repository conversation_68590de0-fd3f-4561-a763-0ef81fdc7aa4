from fastapi import HTTPException, Request
from quepasa.auth.bearer import <PERSON><PERSON><PERSON><PERSON>
from src.lib.logger import QuepasaLogger

logger = QuepasaLogger.get_instance(__name__)

async def verify_auth(request: Request) -> str:
    """Verify request authentication and return client ID"""
    auth_manager = BearerAuth()
    result = auth_manager.authenticate_request(dict(request.headers))
    if not result.is_authorized:
        raise HTTPException(401, result.error or "Unauthorized")
    return result.client_id