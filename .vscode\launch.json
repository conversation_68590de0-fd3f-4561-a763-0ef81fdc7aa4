{
  // VS Code debug configurations
  "version": "0.2.0",
  "configurations": [
    {
      "name": "API: Uvicorn (quepasa.api.app)",
      "type": "debugpy",
      "request": "launch",
      "module": "dev.run_uvicorn",
      "args": [],
      "jinja": true,
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}",
      "envFile": "${workspaceFolder}/.env",
      "env": {
        "PYTHONPATH": "${workspaceFolder}"
      },
      "justMyCode": false,
      "python": "${workspaceFolder}/.venv/Scripts/python.exe"
    },
    {
      "name": "Module: Searcher Service",
      "type": "debugpy",
      "request": "launch",
      "module": "quepasa.searcher.main",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}",
      "envFile": "${workspaceFolder}/local.env",
      "env": {
        "PYTHONPATH": "${workspaceFolder}",
        "REDIS_HOST": "localhost",
        "REDIS_PORT": "6379"
      },
      "justMyCode": false
    },
    {
      "name": "Module: Embedding Service",
      "type": "debugpy",
      "request": "launch",
      "module": "quepasa.embedding.app",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}",
      "envFile": "${workspaceFolder}/local.env",
      "env": {
        "PYTHONPATH": "${workspaceFolder}"
      },
      "justMyCode": false
    }
    {
      "name": "Worker: Data Processor (Celery solo)",
      "type": "debugpy",
      "request": "launch",
      "module": "celery",
      "args": [
        "-A",
        "quepasa.data_processor.tasks",
        "worker",
        "-Q",
        "data-processor",
        "-n",
        "data-processor@%h",
        "--loglevel",
        "info",
        "--pool",
        "solo",
        "--concurrency",
        "1"
      ],
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}",
      "envFile": "${workspaceFolder}/local.env",
      "env": {
        "PYTHONPATH": "${workspaceFolder}"
      },
      "justMyCode": false
    }
  ]
}