import os
from typing import Optional, List
from openai import OpenAI

from .base import <PERSON><PERSON>mbedding
from .cache import EmbeddingCacheMixin
from .providers import EmbeddingProvider
from src.lib.utils import get_tokenizer, get_max_embedding_model_token_length
from src.lib.logger import <PERSON><PERSON>aLogger

logger = QuepasaLogger().get_instance(__name__)

class MWSEmbedding(BaseEmbedding, EmbeddingCacheMixin):
    """MWS embedding provider.
    
    This class provides access to MWS's embedding models via their OpenAI-compatible API.
    """    
    def __init__(self):
        """Initialize MWS embedding provider."""
        super().__init__()
        self.client = OpenAI(
            base_url="https://api.gpt.mws.ru/v1/",
            api_key=os.getenv('MWS_API_KEY')
        )

    @property
    def provider(self) -> EmbeddingProvider:
        return EmbeddingProvider.MWS

    def _truncate_text(self, model_version: str, text: str) -> str:
        """Truncate text to fit within token limit.
        
        Args:
            text: Text to truncate
            
        Returns:
            Truncated text that fits within the model's token limit
        """
        max_tokens = get_max_embedding_model_token_length(model_version)
        if len(get_tokenizer().encode(text)) > max_tokens:
            new_text = ""
            for line in text.split("\n"):
                line_nl = "\n" + line
                if len(get_tokenizer().encode((new_text + line_nl).strip())) > max_tokens:
                    break
                new_text += line_nl
            return new_text.strip()
        return text

    def get_embedding(self, model_version: str, text: str) -> Optional[List[float]]:
        """Get embedding from MWS API.
        
        Args:
            model_version: The model version to use for embeddings
            text: The text to get embedding for
            
        Returns:
            List of floats representing the embedding, or None if the request fails
        """
        text = self._truncate_text(model_version, text)
        try:
            return self.client.embeddings.create(
                model=model_version,
                input=text,
                encoding_format="float"
            ).data[0].embedding
        
        except Exception as e:
            logger.error(f"Failed to get embedding from MWS: {str(e)}")
            return None 