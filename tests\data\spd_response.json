{"id": "c5b36d44-9956-4833-93a2-539368a7d995", "area": "Production", "query": "jeans", "correctedQuery": "", "filter": "(NOT productId: ANY(\"1138288168\")) AND ((NOT id:ANY(\"1099171\",\"1089277\",\"1088589\",\"1099662\",\"1089713\")))", "originalRequest": {"query": "jeans", "area": "Production", "collection": "productsEnriched", "refinements": [], "pageSize": 10, "skip": 0, "preFilter": "NOT productId: ANY(\"1138288168\")", "responseMask": ["*"], "debug": false, "enableTopsort": false, "enableTopsortV2": false}, "records": [{"_id": "a7ba61a07feeb5fabd7c4e123645d1bf", "_u": "http://shoeby1productsEnriched.com/1104787", "_t": "Slim Fit Jeans Mediumstone L34", "collection": "productsEnriched", "labels": ["BOOSTED", "PINNED"], "allMeta": {"images": [{"uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dw20fd8df7/products/1104787/06_1104787_100.jpg"}], "description": "The perfect basic! The SLim Jeans <PERSON> has a medium-high waist, a slim fit and a size 34. The jeans are characterized by a medium stone blue wash. The blue wash makes the jeans timeless, allowing you to combine the item endlessly. Complete the look with a sweater and sneakers and you're ready to go!", "variants": [{"id": "1104787-100-28", "type": "VARIANT", "priceInfo": {"originalPrice": 69.99, "price": 45.49}, "images": [{"uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dw20fd8df7/products/1104787/06_1104787_100.jpg"}], "sizes": ["28"], "description": "The perfect basic! The SLim Jeans <PERSON> has a medium-high waist, a slim fit and a size 34. The jeans are characterized by a medium stone blue wash. The blue wash makes the jeans timeless, allowing you to combine the item endlessly. Complete the look with a sweater and sneakers and you're ready to go!", "title": "Slim Fit Jeans Mediumstone L34", "colorInfo": {"colorFamilies": ["Blue"]}, "primaryProductId": "1104787"}, {"id": "1104787-100-29", "type": "VARIANT", "priceInfo": {"originalPrice": 69.99, "price": 45.49}, "images": [{"uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dw20fd8df7/products/1104787/06_1104787_100.jpg"}], "sizes": ["29"], "description": "The perfect basic! The SLim Jeans <PERSON> has a medium-high waist, a slim fit and a size 34. The jeans are characterized by a medium stone blue wash. The blue wash makes the jeans timeless, allowing you to combine the item endlessly. Complete the look with a sweater and sneakers and you're ready to go!", "title": "Slim Fit Jeans Mediumstone L34", "colorInfo": {"colorFamilies": ["Blue"]}, "primaryProductId": "1104787"}], "id": "1104787", "title": "Slim Fit Jeans Mediumstone L34", "primaryProductId": "1104787"}}, {"_id": "cbeef635ef57be06bc21fc1d82713d3a", "_u": "http://shoeby1productsEnriched.com/1104833", "_t": "Slim Fit Jeans Mediumstone L30", "collection": "productsEnriched", "labels": ["BURIED"], "allMeta": {"images": [{"uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwe476fe07/products/1104833/06_1104833_100.jpg"}], "description": "The perfect basic! The Slim Fit Jeans Mediumstone has a medium-high waist, a slim fit and a size 30. The jeans are characterized by a medium stone blue wash. The blue wash makes the jeans timeless, allowing you to combine the item endlessly. Complete the look with a sweater and sneakers and you're ready to go! The model has a height of 186 cm and wears size 32. The item has an inseam of 81 cm with size 30.", "variants": [{"id": "1104833-100-28", "type": "VARIANT", "priceInfo": {"originalPrice": 69.99, "price": 45.49}, "images": [{"uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwe476fe07/products/1104833/06_1104833_100.jpg"}], "sizes": ["28"], "description": "The perfect basic! The Slim Fit Jeans Mediumstone has a medium-high waist, a slim fit and a size 30. The jeans are characterized by a medium stone blue wash. The blue wash makes the jeans timeless, allowing you to combine the item endlessly. Complete the look with a sweater and sneakers and you're ready to go! The model has a height of 186 cm and wears size 32. The item has an inseam of 81 cm with size 30.", "title": "Slim Fit Jeans Mediumstone L30", "colorInfo": {"colorFamilies": ["Blue"]}, "primaryProductId": "1104833"}, {"id": "1104833-100-29", "type": "VARIANT", "priceInfo": {"originalPrice": 69.99, "price": 45.49}, "images": [{"uri": "https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dwe476fe07/products/1104833/06_1104833_100.jpg"}], "sizes": ["29"], "description": "The perfect basic! The Slim Fit Jeans Mediumstone has a medium-high waist, a slim fit and a size 30. The jeans are characterized by a medium stone blue wash. The blue wash makes the jeans timeless, allowing you to combine the item endlessly. Complete the look with a sweater and sneakers and you're ready to go! The model has a height of 186 cm and wears size 32. The item has an inseam of 81 cm with size 30.", "title": "Slim Fit Jeans Mediumstone L30", "colorInfo": {"colorFamilies": ["Blue"]}, "primaryProductId": "1104833"}], "id": "1104833", "title": "Slim Fit Jeans Mediumstone L30", "primaryProductId": "1104833"}}], "sponsoredRecords": [], "totalRecordCount": 365, "pageInfo": {"recordStart": 1, "recordEnd": 10}, "availableNavigation": [{"name": "attributes.gbi_L3", "displayName": "Category L3", "type": "Value", "refinements": [{"type": "Value", "count": 334, "value": "pants", "pinned": false}, {"type": "Value", "count": 30, "value": "shorts", "pinned": false}, {"type": "Value", "count": 1, "value": "jacket", "pinned": false}], "or": true, "pinned": false}, {"name": "attributes.gbi_L4", "displayName": "Category", "type": "Value", "refinements": [{"type": "Value", "count": 331, "value": "jeans", "pinned": false}], "or": true, "pinned": false}, {"name": "attributes.TargetAudience", "displayName": "Target Audience", "type": "Value", "refinements": [{"type": "Value", "count": 124, "value": "adults lady women", "pinned": false}, {"type": "Value", "count": 120, "value": "adults men man", "pinned": false}, {"type": "Value", "count": 48, "value": "boys", "pinned": false}, {"type": "Value", "count": 120, "value": "gentlemen", "pinned": false}, {"type": "Value", "count": 73, "value": "girls", "pinned": false}, {"type": "Value", "count": 121, "value": "kids children", "pinned": false}, {"type": "Value", "count": 124, "value": "ladies", "pinned": false}], "or": true, "pinned": false}, {"name": "patterns", "displayName": "Pattern", "type": "Value", "refinements": [{"type": "Value", "count": 39, "value": "bleached", "pinned": false}, {"type": "Value", "count": 17, "value": "destroyed", "pinned": false}, {"type": "Value", "count": 13, "value": "bleached wash", "pinned": false}, {"type": "Value", "count": 8, "value": "washed", "pinned": false}, {"type": "Value", "count": 6, "value": "damaged", "pinned": false}, {"type": "Value", "count": 4, "value": "heart", "pinned": false}, {"type": "Value", "count": 4, "value": "knitted denim", "pinned": false}, {"type": "Value", "count": 4, "value": "medium used wash", "pinned": false}, {"type": "Value", "count": 3, "value": "destroy", "pinned": false}, {"type": "Value", "count": 3, "value": "medium stone wash", "pinned": false}, {"type": "Value", "count": 3, "value": "rhinestones", "pinned": false}, {"type": "Value", "count": 3, "value": "solid", "pinned": false}, {"type": "Value", "count": 3, "value": "subtle wash", "pinned": false}, {"type": "Value", "count": 3, "value": "vintage", "pinned": false}, {"type": "Value", "count": 3, "value": "whiskers", "pinned": false}, {"type": "Value", "count": 3, "value": "zebra", "pinned": false}, {"type": "Value", "count": 2, "value": "destroys", "pinned": false}, {"type": "Value", "count": 2, "value": "ironed pleats", "pinned": false}, {"type": "Value", "count": 2, "value": "j<PERSON><PERSON><PERSON>", "pinned": false}, {"type": "Value", "count": 2, "value": "leopard", "pinned": false}, {"type": "Value", "count": 2, "value": "stone wash", "pinned": false}, {"type": "Value", "count": 1, "value": "all-over", "pinned": false}, {"type": "Value", "count": 1, "value": "all-over silver rhinestones", "pinned": false}, {"type": "Value", "count": 1, "value": "biker", "pinned": false}, {"type": "Value", "count": 1, "value": "bleached structure", "pinned": false}, {"type": "Value", "count": 1, "value": "block print", "pinned": false}, {"type": "Value", "count": 1, "value": "bold bleached", "pinned": false}, {"type": "Value", "count": 1, "value": "checkers", "pinned": false}, {"type": "Value", "count": 1, "value": "decorative cracks", "pinned": false}, {"type": "Value", "count": 1, "value": "denim", "pinned": false}, {"type": "Value", "count": 1, "value": "embroidered hearts", "pinned": false}, {"type": "Value", "count": 1, "value": "flare", "pinned": false}, {"type": "Value", "count": 1, "value": "floral", "pinned": false}, {"type": "Value", "count": 1, "value": "foil", "pinned": false}, {"type": "Value", "count": 1, "value": "jog denim", "pinned": false}, {"type": "Value", "count": 1, "value": "lasered hearts", "pinned": false}, {"type": "Value", "count": 1, "value": "loose fit", "pinned": false}, {"type": "Value", "count": 1, "value": "metallic foil", "pinned": false}, {"type": "Value", "count": 1, "value": "paperbag", "pinned": false}, {"type": "Value", "count": 1, "value": "pearl", "pinned": false}, {"type": "Value", "count": 1, "value": "pearls", "pinned": false}, {"type": "Value", "count": 1, "value": "pinstripe", "pinned": false}, {"type": "Value", "count": 1, "value": "pleats", "pinned": false}, {"type": "Value", "count": 1, "value": "repaired destroys", "pinned": false}, {"type": "Value", "count": 1, "value": "repared destroys", "pinned": false}, {"type": "Value", "count": 1, "value": "ribbed", "pinned": false}, {"type": "Value", "count": 1, "value": "ripped", "pinned": false}, {"type": "Value", "count": 1, "value": "ripped and repair", "pinned": false}, {"type": "Value", "count": 1, "value": "stones", "pinned": false}, {"type": "Value", "count": 1, "value": "stonewash", "pinned": false}, {"type": "Value", "count": 1, "value": "studs", "pinned": false}, {"type": "Value", "count": 1, "value": "subtle small tears", "pinned": false}, {"type": "Value", "count": 1, "value": "swirl", "pinned": false}, {"type": "Value", "count": 1, "value": "two different medium stone blue washes", "pinned": false}, {"type": "Value", "count": 1, "value": "two-tone", "pinned": false}, {"type": "Value", "count": 1, "value": "wahed", "pinned": false}, {"type": "Value", "count": 1, "value": "washed effect", "pinned": false}, {"type": "Value", "count": 1, "value": "wiskers", "pinned": false}], "or": true, "pinned": false}, {"name": "colorFamilies", "displayName": "Color", "type": "Value", "refinements": [{"type": "Value", "count": 208, "value": "Blue", "pinned": false}, {"type": "Value", "count": 80, "value": "Black", "pinned": false}, {"type": "Value", "count": 75, "value": "<PERSON>", "pinned": false}, {"type": "Value", "count": 7, "value": "Multicolour", "pinned": false}, {"type": "Value", "count": 5, "value": "White", "pinned": false}, {"type": "Value", "count": 2, "value": "Beige", "pinned": false}, {"type": "Value", "count": 2, "value": "Pink", "pinned": false}, {"type": "Value", "count": 2, "value": "Purple", "pinned": false}, {"type": "Value", "count": 1, "value": "<PERSON>", "pinned": false}, {"type": "Value", "count": 1, "value": "Green", "pinned": false}, {"type": "Value", "count": 1, "value": "Silver", "pinned": false}], "or": true, "pinned": false}, {"name": "genders", "displayName": "Gender", "type": "Value", "refinements": [{"type": "Value", "count": 197, "value": "female", "pinned": false}, {"type": "Value", "count": 168, "value": "male", "pinned": false}], "or": true, "pinned": false}, {"name": "ageGroups", "displayName": "Age Groups", "type": "Value", "refinements": [{"type": "Value", "count": 244, "value": "adult", "pinned": false}, {"type": "Value", "count": 121, "value": "kids", "pinned": false}], "or": true, "pinned": false}, {"name": "attributes.gbi_L1", "displayName": "gbi L1", "type": "Value", "refinements": [{"type": "Value", "count": 365, "value": "apparel", "pinned": false}], "or": true, "source": "Dynamic", "pinned": false}, {"name": "attributes.product_collection", "displayName": "product collection", "type": "Value", "refinements": [{"type": "Value", "count": 1, "value": "jacket", "pinned": false}, {"type": "Value", "count": 334, "value": "pants", "pinned": false}, {"type": "Value", "count": 30, "value": "shorts", "pinned": false}], "or": true, "source": "Dynamic", "pinned": false}, {"name": "attributes.gbi_L2", "displayName": "gbi L2", "type": "Value", "refinements": [{"type": "Value", "count": 364, "value": "bottoms", "pinned": false}, {"type": "Value", "count": 1, "value": "outerwear", "pinned": false}], "or": true, "source": "Dynamic", "pinned": false}, {"name": "attributes.OnlineFlag", "displayName": "OnlineFlag", "type": "Value", "refinements": [{"type": "Value", "count": 365, "value": "True", "pinned": false}], "or": true, "source": "Dynamic", "pinned": false}, {"name": "attributes.style", "displayName": "style", "type": "Value", "refinements": [{"type": "Value", "count": 1, "value": "animal print", "pinned": false}, {"type": "Value", "count": 1, "value": "animal prints", "pinned": false}, {"type": "Value", "count": 3, "value": "baggy jeans", "pinned": false}, {"type": "Value", "count": 4, "value": "best basics", "pinned": false}, {"type": "Value", "count": 2, "value": "by mieke collection", "pinned": false}, {"type": "Value", "count": 6, "value": "cargo", "pinned": false}, {"type": "Value", "count": 1, "value": "clean lines", "pinned": false}, {"type": "Value", "count": 142, "value": "denim days", "pinned": false}, {"type": "Value", "count": 2, "value": "diamonds", "pinned": false}, {"type": "Value", "count": 14, "value": "editorials", "pinned": false}, {"type": "Value", "count": 8, "value": "flared fit", "pinned": false}, {"type": "Value", "count": 9, "value": "flared jeans", "pinned": false}, {"type": "Value", "count": 2, "value": "fred van leer", "pinned": false}, {"type": "Value", "count": 2, "value": "fred van leer collection", "pinned": false}, {"type": "Value", "count": 2, "value": "hearts print", "pinned": false}, {"type": "Value", "count": 16, "value": "high waist", "pinned": false}, {"type": "Value", "count": 2, "value": "i must have", "pinned": false}, {"type": "Value", "count": 3, "value": "iconic blue", "pinned": false}, {"type": "Value", "count": 9, "value": "influencer", "pinned": false}, {"type": "Value", "count": 7, "value": "influencers", "pinned": false}, {"type": "Value", "count": 6, "value": "into the blue", "pinned": false}, {"type": "Value", "count": 1, "value": "leopard print", "pinned": false}, {"type": "Value", "count": 3, "value": "lizzy van der l<PERSON> collection", "pinned": false}, {"type": "Value", "count": 1, "value": "lonneke nooteboom collection", "pinned": false}, {"type": "Value", "count": 18, "value": "mid waist", "pinned": false}, {"type": "Value", "count": 1, "value": "monica geuze collection", "pinned": false}, {"type": "Value", "count": 47, "value": "party collection", "pinned": false}, {"type": "Value", "count": 1, "value": "personal stylist favorites", "pinned": false}, {"type": "Value", "count": 4, "value": "pink selections", "pinned": false}, {"type": "Value", "count": 1, "value": "purple selections", "pinned": false}, {"type": "Value", "count": 1, "value": "retro deluxe", "pinned": false}, {"type": "Value", "count": 1, "value": "retro glam", "pinned": false}, {"type": "Value", "count": 2, "value": "rocky romance", "pinned": false}, {"type": "Value", "count": 69, "value": "skinny fit", "pinned": false}, {"type": "Value", "count": 52, "value": "slim fit", "pinned": false}, {"type": "Value", "count": 67, "value": "straight fit", "pinned": false}, {"type": "Value", "count": 40, "value": "tapered fit", "pinned": false}, {"type": "Value", "count": 1, "value": "the essentials", "pinned": false}, {"type": "Value", "count": 39, "value": "trends", "pinned": false}, {"type": "Value", "count": 33, "value": "wide leg fit", "pinned": false}, {"type": "Value", "count": 3, "value": "zebra print", "pinned": false}], "or": true, "source": "Dynamic", "pinned": false}, {"name": "attributes.special_offer", "displayName": "special offer", "type": "Value", "refinements": [{"type": "Value", "count": 5, "value": "2 for 55", "pinned": false}, {"type": "Value", "count": 3, "value": "2 for 59", "pinned": false}, {"type": "Value", "count": 74, "value": "2 for 109", "pinned": false}, {"type": "Value", "count": 6, "value": "2 for action", "pinned": false}, {"type": "Value", "count": 1, "value": "2 shorts for 69.-", "pinned": false}, {"type": "Value", "count": 98, "value": "2nd jeans half price", "pinned": false}, {"type": "Value", "count": 4, "value": "30% discount", "pinned": false}, {"type": "Value", "count": 2, "value": "40% discount", "pinned": false}, {"type": "Value", "count": 1, "value": "50% discount", "pinned": false}, {"type": "Value", "count": 2, "value": "60% discount", "pinned": false}, {"type": "Value", "count": 14, "value": "all and skinny jeans 25.-", "pinned": false}, {"type": "Value", "count": 15, "value": "all skinny jeans 40.-", "pinned": false}, {"type": "Value", "count": 18, "value": "always advantage", "pinned": false}, {"type": "Value", "count": 13, "value": "bestsellers", "pinned": false}, {"type": "Value", "count": 44, "value": "black friday", "pinned": false}, {"type": "Value", "count": 1, "value": "fashion deals men", "pinned": false}, {"type": "Value", "count": 6, "value": "jeans 2 for 55", "pinned": false}, {"type": "Value", "count": 18, "value": "jeans 2 for 59", "pinned": false}, {"type": "Value", "count": 32, "value": "jeans 2 for 59.-", "pinned": false}, {"type": "Value", "count": 59, "value": "jeans 2 for 109.-", "pinned": false}, {"type": "Value", "count": 11, "value": "jeans for 15.-", "pinned": false}, {"type": "Value", "count": 6, "value": "jeans sale", "pinned": false}, {"type": "Value", "count": 5, "value": "new in sale", "pinned": false}, {"type": "Value", "count": 8, "value": "online exclusive", "pinned": false}, {"type": "Value", "count": 32, "value": "sale", "pinned": false}, {"type": "Value", "count": 5, "value": "sale extra", "pinned": false}, {"type": "Value", "count": 126, "value": "special deals", "pinned": false}, {"type": "Value", "count": 2, "value": "what's new now", "pinned": false}], "or": true, "source": "Dynamic", "pinned": false}, {"name": "attributes.occasion", "displayName": "occasion", "type": "Value", "refinements": [{"type": "Value", "count": 1, "value": "casual outfit goals", "pinned": false}, {"type": "Value", "count": 2, "value": "city escape", "pinned": false}, {"type": "Value", "count": 1, "value": "city essentials", "pinned": false}, {"type": "Value", "count": 11, "value": "festival looks", "pinned": false}, {"type": "Value", "count": 6, "value": "halloween items", "pinned": false}, {"type": "Value", "count": 1, "value": "holiday essentials", "pinned": false}, {"type": "Value", "count": 4, "value": "king's day", "pinned": false}, {"type": "Value", "count": 103, "value": "magazine items", "pinned": false}, {"type": "Value", "count": 3, "value": "summer senses", "pinned": false}, {"type": "Value", "count": 6, "value": "summer shop", "pinned": false}, {"type": "Value", "count": 1, "value": "text refe", "pinned": false}, {"type": "Value", "count": 2, "value": "warm wardrobe", "pinned": false}], "or": true, "source": "Dynamic", "pinned": false}, {"name": "attributes.collection", "displayName": "collection", "type": "Value", "refinements": [{"type": "Value", "count": 1, "value": "by fred", "pinned": false}, {"type": "Value", "count": 1, "value": "by fred van leer", "pinned": false}, {"type": "Value", "count": 1, "value": "by lonneke", "pinned": false}, {"type": "Value", "count": 1, "value": "by monica", "pinned": false}, {"type": "Value", "count": 1, "value": "fred van leer", "pinned": false}, {"type": "Value", "count": 1, "value": "organic cotton", "pinned": false}, {"type": "Value", "count": 1, "value": "shoeby x fred van leer", "pinned": false}], "or": true, "source": "Dynamic", "pinned": false}], "selectedNavigation": [], "empty": false, "siteParams": [], "warnings": [], "includeExpandedResults": true, "facetLimit": 300, "redirectMetadata": [], "rewrites": ["PRODUCT_CATALOG"], "requestServed": "GOOGLE"}