import os
import json
import hashlib
from abc import ABC
from typing import Op<PERSON>, Tuple, List, Dict, Any

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>ogger
from src.lib.files import QuepasaFiles

logger = QuepasaLogger().get_instance(__name__)
qp_files = QuepasaFiles()

class RerankerCacheMixin(ABC):
    """Mixin class providing caching functionality for reranker responses."""
    
    def __init__(self):
        """Initialize the mixin."""
        super().__init__()
        self._cache_dir = "prod/reranker"
        self._local_cache_dir = os.path.join(os.path.dirname(__file__), "/../../../cache/reranker")

    def _get_cache_paths(self, model_version: str, query: str, documents: List[Dict[str, Any]], top_n: Optional[int]) -> Tuple[str, str]:
        """Get local and remote cache paths for the given query and documents.
        
        Args:
            model_version: Model name/version used
            query: Search query
            documents: List of documents to rerank
            top_n: Number of results to return
            
        Returns:
            Tuple of (local_path, remote_path)
        """
        # Create a unique hash of the inputs
        input_str = json.dumps({
            'query': query,
            'documents': documents,
            'model_version': model_version,
            'top_n': top_n
        }, sort_keys=True)

        # Generate MD5 hash
        input_str_md5 = hashlib.md5(input_str.encode('utf-8')).hexdigest()
                
        # Create hierarchical path structure
        path_parts = []
        for i in range(3):
            if i + 1 <= len(input_str_md5):
                path_parts.append(input_str_md5[0:i + 1])
        path_parts.append(input_str_md5)
        
        # Local and remote paths
        local_path = f"{self._local_cache_dir}/{model_version}/{'/'.join(path_parts)}.txt"
        remote_path = f"{self._cache_dir}/{model_version}/{'/'.join(path_parts)}.txt"
        
        return local_path, remote_path

    def _load_local_cache(self, local_path: str) -> Optional[List[Dict[str, Any]]]:
        """Try to load response from local cache."""
        if os.path.isfile(local_path):
            try:
                with open(local_path, 'r') as f:
                    return json.load(f)['response']
            except Exception as e:
                logger.warning(f"Failed to load local cache {local_path}: {str(e)}")
        return None

    def _load_remote_cache(self, remote_path: str, local_path: str) -> Optional[List[Dict[str, Any]]]:
        """Try to load response from remote cache."""
        if qp_files.exists(remote_path):
            try:
                qp_files.download_file(remote_path, local_path)
                with open(local_path, 'r') as f:
                    return json.load(f)['response']
            except Exception as e:
                logger.warning(f"Failed to load remote cache {remote_path}: {str(e)}")
        return None

    def _save_response(self, response: List[Dict[str, Any]], local_path: str, remote_path: str) -> None:
        """Save response to both local and remote cache."""
        try:
            # Save locally
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            with open(local_path, 'w') as f:
                json.dump({'response': response}, f)
            
            # Save remotely
            qp_files.upload_file(local_path, remote_path)
        except Exception as e:
            logger.warning(f"Failed to save response cache: {str(e)}")

    def get_cached_results(self, model_version: str, query: str, documents: List[Dict[str, Any]], top_n: int = None) -> List[Dict[str, Any]]:
        """Get cached reranking results, computing if necessary."""
        # Get cache paths
        local_path, remote_path = self._get_cache_paths(model_version, query, documents, top_n)
        
        # Try local cache
        response = self._load_local_cache(local_path)
        if response is not None:
            return response
            
        # Try remote cache
        response = self._load_remote_cache(remote_path, local_path)
        if response is not None:
            return response
            
        # Compute new response
        response = self.get_results(model_version, query, documents, top_n)
        if response:
            self._save_response(response, local_path, remote_path)
            
        return response 