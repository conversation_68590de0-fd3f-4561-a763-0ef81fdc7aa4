import os
import io
from typing import Any, Dict, Optional

import replicate

from .base import BaseWhisper
from .cache import WhisperCacheMixin
from .providers import WhisperProvider
from .base import WhisperModelType

from src.lib.logger import QuepasaLogger
logger = QuepasaLogger().get_instance(__name__)

class ReplicateWhisper(BaseWhisper, WhisperCacheMixin):
    """Whisper transcription provider using Replicate API"""
    
    # Default models for different use cases
    MODELS = {
        WhisperModelType.DEFAULT: "vaibhavs10/incredibly-fast-whisper:3ab86df6c8f54c11309d4d1f930ac292bad43ace52d10c80d87eb258b3c9f79c",
        WhisperModelType.DIARIZATION: "thomasmol/whisper-diarization:1495a9cddc83b2203b0d8d3516e38b80fd1572ebc4bc5700ac1da56a9b3ed886"
    }
        
    def __init__(self):
        """Initialize the Replicate Whisper provider"""
        super().__init__()
        
        # Check for API token
        self.api_key = os.getenv("REPLICATE_API_KEY")
        if not self.api_key:
            raise ValueError("Replicate API key must be provided or set in REPLICATE_API_KEY environment variable")
        self.client = replicate.Client(api_token=self.api_key)

    @property
    def provider(self) -> WhisperProvider:
        return WhisperProvider.REPLICATE
    
    def _get_model_id(self, model: str) -> str:
        """
        Get the full model ID for a given model name
        
        Args:
            model: Model name or full model ID
            
        Returns:
            Full model ID
        """
        # If model is a known shorthand, use predefined model ID
        if model in self.MODELS:
            return self.MODELS[model]
        
        # Otherwise use provided model string as is
        return model
        
    def get_segments(self, model: str, audio_data: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get transcription segments from audio data
        
        Args:
            model: The model to use for transcription
            audio_data: Audio data as bytes
            meta: Dictionary containing metadata including language and file extension
            
        Returns:
            Dictionary with transcription segments and metadata
        """
        try:
            # Get full model ID
            model_id = self._get_model_id(model)
            logger.info(f"Using model: {model_id}")
            
            # Create a BytesIO object
            audio_buffer = io.BytesIO(audio_data)
            
            input_params = {}
            try:
                # Prepare input parameters
                if model == WhisperModelType.DEFAULT:
                    input_params = {
                        "audio": audio_buffer,
                    }
                
                elif model == WhisperModelType.DIARIZATION:
                    input_params = {
                        "file": audio_buffer,
                        "translate": False,
                    }

                else:
                    raise ValueError(f"Unsupported model: {model}")

                # Add language if specified
                language = meta.get('language', None)
                if language:
                    input_params["language"] = language
                
                # Run transcription using Replicate API
                result = self.client.run(
                    model_id,
                    input=input_params
                )
                
                # Format result to ensure consistent structure
                formatted_result = self._format_result(result)
                return formatted_result
            
            finally:
                # Close the BytesIO buffer
                audio_buffer.close()
                    
        except Exception as e:
            # Return error result
            return {
                "success": False,
                "error": str(e),
                "text": None,
                "segments": []
            }
    
    def _format_result(self, result: Any) -> Dict[str, Any]:
        """
        Format the result to ensure consistent structure
        
        Args:
            result: Raw result from Replicate API
            
        Returns:
            Formatted result dictionary
        """
        if not result:
            logger.error("No result returned from Replicate API")
            return {
                "success": False,
                "error": "No result returned from Replicate API",
                "text": None,
                "segments": []
            }
        
        # Handle the specific format returned by the diarization model
        if isinstance(result, dict) and "segments" in result:
            segments = result.get("segments", [])
            
            # Join all segment texts to create a full transcript
            text = " ".join([segment.get("text", "") for segment in segments])
            
            # Ensure segments are formatted correctly
            formatted_segments = []
            for segment in segments:
                formatted_segment = {
                    "text": segment.get("text", ""),
                    "start": segment.get("start", 0),
                    "end": segment.get("end", 0),
                    "speaker": segment.get("speaker", "UNKNOWN")
                }
                formatted_segments.append(formatted_segment)
            
            return {
                "success": True,
                "text": text,
                "segments": formatted_segments
            }
        
        # Handle case where result is a dict with 'text' key
        if isinstance(result, dict) and "text" in result:
            text = result.get("text", "")
            segments = result.get("segments", [])
            
            # Ensure segments are formatted correctly
            formatted_segments = []
            for segment in segments:
                formatted_segment = {
                    "text": segment.get("text", ""),
                    "start": segment.get("start", 0),
                    "end": segment.get("end", 0),
                    "speaker": segment.get("speaker", "UNKNOWN")
                }
                formatted_segments.append(formatted_segment)
            
            return {
                "success": True,
                "text": text,
                "segments": formatted_segments
            }
        
        # Handle case where result is just text
        if isinstance(result, str):
            return {
                "success": True,
                "text": result,
                "segments": []
            }
        
        # Handle case where result is a list
        if isinstance(result, list):
            # Try to extract segments from list
            if result and isinstance(result[0], dict):
                # For models that return a list of segment dictionaries
                text = " ".join([segment.get("text", "") for segment in result if "text" in segment])
                
                # Format segments
                formatted_segments = []
                for segment in result:
                    if isinstance(segment, dict):
                        formatted_segment = {
                            "text": segment.get("text", ""),
                            "start": segment.get("start", 0),
                            "end": segment.get("end", 0),
                            "speaker": segment.get("speaker", "UNKNOWN")
                        }
                        formatted_segments.append(formatted_segment)
                
                return {
                    "success": True,
                    "text": text,
                    "segments": formatted_segments
                }
            elif result and isinstance(result[0], str):
                # For models that return a list of strings
                text = " ".join(result)
                return {
                    "success": True,
                    "text": text,
                    "segments": []
                }
            
        # Handle case where result is any other format
        return {
            "success": False,
            "error": "Unexpected result format from Replicate API",
            "text": str(result) if result else None,
            "segments": []
        }
