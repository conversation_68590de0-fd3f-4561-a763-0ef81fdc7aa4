import os
import json
import tempfile
import subprocess
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any, List
from pathlib import Path

import replicate

from .base import BaseProcessor
from .audio_processor import AudioProcessor
from ..exceptions import ProcessorError

class VideoProcessor(BaseProcessor):
    """Processor for video files to extract transcription"""
    
    def __init__(self):
        """Initialize VideoProcessor."""
        super().__init__()
        
        # Initialize audio processor
        self.audio_processor = AudioProcessor()
        
    def _process_impl(self, content: bytes, meta: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process video file and extract transcription
        
        Args:
            content: Video file content
            meta: Metadata about the video file
            
        Returns:
            Dictionary containing extracted transcription
        """
        # Validate metadata
        error = self.validate_meta(meta, ['filename', 'extension'])
        if error:
            raise ProcessorError(error)
            
        # Check if extension is supported
        extension = meta['extension'].lower()
        supported_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv']
        if extension not in supported_extensions:
            raise ProcessorError(f"Unsupported video file type: {extension}")
            
        try:
            # Save content to temporary file
            with tempfile.NamedTemporaryFile(suffix=extension, delete=False) as temp_file:
                temp_file.write(content)
                temp_path = temp_file.name
                
            # Extract audio from video
            audio_path = self._extract_audio(temp_path)
            
            # Get audio/video information
            video_info = self._get_video_info(temp_path)
            self.logger.info(f"Video duration: {video_info.get('duration', 0)} seconds")
            
            # Read audio content
            with open(audio_path, 'rb') as audio_file:
                audio_content = audio_file.read()
            
            # Use the AudioProcessor to process the extracted audio
            audio_meta = meta.copy()
            audio_meta['extension'] = '.mp3'
            # Keep the language from the original metadata
            if 'language' in meta:
                audio_meta['language'] = meta['language']
            
            # Process audio content
            result = self.audio_processor._process_impl(audio_content, audio_meta)
            
            # Add video-specific information to the result
            result['filename'] = meta.get('filename', '')
            result['video_info'] = video_info
            
            # Clean up temporary files
            os.unlink(temp_path)
            os.unlink(audio_path)
            
            return result
            
        except Exception as e:
            # Clean up temporary files in case of error
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.unlink(temp_path)
            if 'audio_path' in locals() and os.path.exists(audio_path):
                os.unlink(audio_path)
            raise ProcessorError(f"Error processing video file: {str(e)}")
    
    def _extract_audio(self, video_path: str) -> str:
        """Extract audio from video file as MP3."""
        try:
            # Create output path for extracted audio
            audio_path = video_path + '.mp3'
            
            # Use ffmpeg to extract audio
            cmd = [
                'ffmpeg',
                '-y',  # Overwrite output
                '-i', video_path,
                '-vn',  # No video
                '-ac', '1',  # Convert to mono
                '-ar', '16000',  # Sample rate
                '-ab', '64k',  # Bitrate
                audio_path
            ]
            
            self.logger.info(f"Extracting audio from video")
            subprocess.run(cmd, capture_output=True, check=True)
            self.logger.info(f"Audio extraction complete")
            
            return audio_path
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"FFmpeg error: {e.stderr.decode() if e.stderr else str(e)}")
            raise ProcessorError(f"Error extracting audio: {str(e)}")
        except Exception as e:
            self.logger.error(f"Error extracting audio: {str(e)}")
            raise ProcessorError(f"Error extracting audio: {str(e)}")
    
    def _get_video_info(self, video_path: str) -> Dict:
        """Get video information using ffprobe."""
        try:
            # Get video duration
            probe_cmd = [
                'ffprobe', 
                '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                video_path
            ]
            duration = float(subprocess.check_output(probe_cmd).decode().strip())
            
            # Get video dimensions
            probe_cmd = [
                'ffprobe', 
                '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height',
                '-of', 'csv=s=x:p=0',
                video_path
            ]
            dimensions = subprocess.check_output(probe_cmd).decode().strip()
            width, height = map(int, dimensions.split('x')) if 'x' in dimensions else (0, 0)
            
            # Get video codec
            probe_cmd = [
                'ffprobe', 
                '-v', 'error',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=codec_name',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                video_path
            ]
            codec = subprocess.check_output(probe_cmd).decode().strip()
            
            return {
                'duration': duration,
                'width': width,
                'height': height,
                'codec': codec
            }
            
        except Exception as e:
            self.logger.error(f"Error getting video info: {str(e)}")
            return {'duration': 0, 'width': 0, 'height': 0, 'codec': 'unknown'} 