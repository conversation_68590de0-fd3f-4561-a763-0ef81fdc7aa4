from typing import List, Dict, Any
from pydantic import BaseModel, <PERSON>
from fastapi import APIRouter, HTTPException, Depends
import json

from quepasa.api.auth import verify_auth
from src.lib.llm.providers import LLMProvider
from src.lib.llm_utils import get_cached_llm_answer
from src.lib.logger import QuepasaLogger

# Logger
logger = QuepasaLogger().get_instance(__name__)

router = APIRouter()

class JudgementBaseRequest(BaseModel):
    llm: str = Field(default="openai:gpt-4o")
    prompt: str = Field(default=None)
    max_tokens: int = Field(default=None)

class JudgementScoresRequest(JudgementBaseRequest):
    messages: List[Dict[str,str]]

class JudgementSideBySideRequest(JudgementBaseRequest):
    messages: List[Dict[str,str]]
    golden_messages: List[Dict[str,str]]

class JudgementGoldenMatchRequest(JudgementBaseRequest):
    messages: List[Dict[str,str]]
    golden_messages: List[Dict[str,str]]

# todo: remove this class and endpoint
class JudgementRequest(JudgementBaseRequest):
    messages: List[Dict[str,str]]
    golden_messages: List[Dict[str,str]] = Field(default_factory=list)

class JudgementResponse(BaseModel):
    status: str
    response: Any # Todo: fix to when we will figure out the final format

JUDGEMENT_PROMPT_SCORE = """
You are an exceptional e-commerce LLM chatbot performance evaluator.
The e-commerce store being evaluated specialises in **clothing, footwear, and accessories for adults and children**.
Evaluate the AI assistant's responses using the methodology and scoring guide below.

Important rule: If a metric is **not applicable** to the given conversation (e.g., the customer never requests or hints at products, so product recommendations are irrelevant), assign "N/A" for that metric and state "Not applicable" in the justification.

Return your response **strictly** as a valid JSON object, with:
- a numeric score (0–5) or "N/A" for each metric, plus a short justification
- clear recommendations for improvement
Respond only in valid JSON format. Do NOT include explanations, formatting, or markdown.

---

Here's the dialog between the customer and the AI assistant:
{{MESSAGES}}

---

📏 Review Methodology (5-Part Assessment)
1. **Intent Understanding (0–5)**
- Does the AI accurately understand and track user goals?
- Does it manage ambiguous or shifting intents effectively?
- Does it respond to tone/emotion implicitly?
2. **Role Adherence (0–5)**
- Does it maintain its salesperson persona?
- Is the tone natural, empathetic, persuasive, and brand-aligned?
- Does it drive toward product discovery or purchase?
- Does it try and close the sale at the earliest reasonable stage?
3. **Product Recommendation Performance (0–5)**
- Are recommendations relevant and appropriate?
- Are they diverse and personalized?
- Are descriptions clear and aligned with industry norms?
4. **Fallback Handling & Conversational Repair (0–5)**
- Does it handle errors/misunderstandings gracefully?
- Are fallbacks natural and non-repetitive?
- Is coherence maintained during recovery?
5. **Knowledge Retention & Context Evaluation (0–5)**
- Does it recall and utilize earlier context accurately?
- Does it maintain and leverage conversation history?
- Is context applied smoothly across turns?

---

📊 Scoring Guide (0–5 Scale)
- 0 = Absolute failure
- 1 = Poor: significant gaps
- 2 = Fair: partial but insufficient
- 3 = Good: meets expectations, minor fixes
- 4 = Very Good: strong, minor flaws
- 5 = Excellent: outstanding, no issues
- "N/A" = Not applicable to the given conversation

---

:return: JSON format example
{{
  "Intent Understanding":{{
    "score":5,
    "justification":"Accurately understood the user's greeting and responded appropriately."}},
  "Role Adherence":{{
    "score":4,
    "justification":"Maintained salesperson persona with minor tone issues."}},
  "Product Recommendation Performance":{{
    "score":"N/A",
    "justification":"Not applicable — no product request detected."}},
  "Fallback Handling & Conversational Repair":{{
    "score":5,
    "justification":"Excellent fallback handling."}},
  "Knowledge Retention & Context Evaluation":{{
    "score":4,
    "justification":"Good use of context, minor lapses."}},
  "Recommendations":"Maintain tone consistency. No product suggestions needed for this interaction."}}
}}
"""

JUDGEMENT_PROMPT_SIDE_BY_SIDE = """
You are an expert evaluator of e-commerce AI assistants.
The e-commerce store being evaluated specialises in **clothing, footwear, and accessories for adults and children**.
Compare the **two dialogs** below (Dialog A and Dialog B) across 5 key performance metrics.

Important rule: If a metric is not applicable to either dialog (e.g., no product recommendations were expected or needed), assign "N/A" to the **better** field and write "Not applicable" in the **justification**. This metric should be excluded from the majority vote when determining the overall winner.

Return your answer strictly as a valid JSON object with:
- For each metric: which dialog performed better ("A", "B", "equal", or "N/A") and a brief justification
- A final overall winner ("A", "B", or "equal") based on majority vote excluding any "N/A" metrics
- Optional concise recommendations for improvement for the weaker dialog (only if there is a clear winner)
Do NOT include any explanations or markdown, just raw JSON.

---

Dialog A:
{{MESSAGES}}

---

Dialog B:
{{GOLDEN_MESSAGES}}

---

📏 Evaluation Criteria (Compare Dialog A vs. Dialog B)
1. **Intent Understanding**
- Which dialog better demonstrates accurate understanding and tracking of the user's goals?
- Which handles ambiguous or shifting intents more effectively?
- Which responds more appropriately to the user's tone/emotional implicitly?
2. **Role Adherence**
- Which dialog better maintains the assistant’s persona as a helpful e-commerce salesperson?
- Which uses a more natural, empathetic, persuasive, and brand-aligned tone?
- Which drives more effectively toward product discovery or purchase?
- Which attempts to close the sale at the earliest reasonable opportunity?
3. **Product Recommendation Performance**
- Which dialog offers more relevant and appropriate product suggestions?
- Which provides more diverse and personalized recommendations?
- Which includes clearer product descriptions that align better with industry norms?
4. **Fallback Handling & Repair**
- Which dialog handles user confusion/misunderstandings more gracefully?
- Which offers fallbacks that are more natural, non-repetitive, and informative?
- Which maintains better coherence and flow during recovery?
5. **Knowledge Retention & Context**
- Which dialog more accurately recalls and uses previous conversation context?
- Which maintains and uses conversation history more effectively?
- Which applies context more smoothly across dialog turns?

---

:return: JSON format example
{{
  "Intent Understanding": {{
    "better": "equal",
    "justification": "Both dialogs xxx xxxxx."
  }},
  "Role Adherence": {{
    "better": "B",
    "justification": "Dialog B xxx xxxxxx xxxx."
  }},
  "Product Recommendation Performance": {{
    "better": "A",
    "justification": "Dialog A xxx xx."
  }},
  "Fallback Handling & Repair": {{
    "better": "N/A",
    "justification": "Not applicable xxxxx xxx xx."
  }},
  "Knowledge Retention & Context": {{
    "better": "equal",
    "justification": "Both dialogs xxx xxx xxxx x."
  }},
  "Overall Winner": "equal",
  "Improvement Suggestions": "Xxxx xxx xx xxx xxxx."
}}
"""

JUDGEMENT_PROMPT_GOLDEN_MATCH = """
You are an expert evaluator of e-commerce AI assistants.
The e-commerce store being evaluated specialises in **clothing, footwear, and accessories for adults and children**.  
Compare the **current dialog** below to a **gold-standard reference dialog**, judging how well the current version matches or improves upon the golden one across 5 key performance metrics.

Your task is to compare the **current dialog** to a **gold-standard reference dialog** and assess whether the assistant’s decisions were appropriate across five key binary criteria.

Return your answer strictly as a valid JSON object with:
- For each criterion:
  - "ok": true or false — Was the assistant’s behaviour appropriate in this regard?
  - "justification": a brief explanation
- Final field "Overall Evaluation":
  - "good" if **all** "ok" values are true
  - "bad" if **any** "ok" is false
- If "Overall Evaluation" is "bad", include a field "Improvement Suggestions" with specific, concise advice
- Do NOT include any explanations or markdown — just raw JSON

---

Current Dialog:
{{MESSAGES}}

---

Golden Dialog:
{{GOLDEN_MESSAGES}}

---

📏 Evaluation Criteria (binary)
1. **Show Product List**  
Was it appropriate to show or not show a list of products at this point?

2. **Product Relevance**  
Were the shown products relevant and appropriate?

3. **Ask Clarification Before Showing**  
Was it appropriate to ask a clarification question *before* showing anything?

4. **Ask Clarification in General**  
Was the use of clarification questions overall appropriate and timely?

5. **Close the Sale**  
Was it appropriate to attempt to close the sale at this point?

---

:return: JSON format example
{{
  "Show Product List": {{
    "ok": false,
    "justification": "Xxx xxx xx."
  }},
  "Product Relevance": {{
    "ok": true,
    "justification": "X xxx xx."
  }},
  "Ask Clarification Before Showing": {{
    "ok": false,
    "justification": "Xxx x xxxx."
  }},
  "Ask Clarification in General": {{
    "ok": true,
    "justification": "Xxxxxx xx."
  }},
  "Close the Sale": {{
    "ok": false,
    "justification": "Xxx xxx."
  }},
  "Overall Evaluation": "bad",
  "Improvement Suggestions": "Xxx xx."
}}
"""

def get_formatted_dialog(messages):
    formatted_dialog = ""
    for turn in messages:
        role = turn['role'].capitalize()
        content = turn['content'].strip()
        formatted_dialog += f"{role}:\n{content}\n\n"
    return formatted_dialog

@router.post("/scores", response_model=JudgementResponse)
async def get_judgement_scores(
    request: JudgementScoresRequest,
    client_id: str = Depends(verify_auth)
) -> JudgementResponse:
    """Get Judgement response for the provided Judgement request"""
    try:
        # Detect llm provider
        provider_str, model_name = request.llm.split(':')
        provider = LLMProvider.from_str(provider_str)

        # Choose prompt
        eval_prompt = request.prompt if request.prompt != None else JUDGEMENT_PROMPT_SCORE

        # Replace conversations
        eval_prompt = eval_prompt.replace("{{MESSAGES}}", get_formatted_dialog(request.messages))

        response_text = get_cached_llm_answer(
            provider,
            model_name,
            [{"role": "user", "content": eval_prompt.strip()}],
            request.max_tokens,
            True
        )

        response_json = json.loads(response_text)
        try:
            scores = []
            for _, value in response_json.items():
                if (
                    'score' in value 
                    and value['score']
                    and (
                        isinstance(value['score'], int)
                        or isinstance(value['score'], str) and value['score'].isdigit()
                    )
                    and int(value['score']) in [0, 1, 2, 3, 4, 5]
                ):
                    scores.append(int(value['score']))
            
            response_json['Average Score'] = sum(scores) / len(scores)
            response_json['Total Score'] = sum(scores)

        except Exception as e:
            logger.info(f"Response JSON: {response_json}")
            logger.error(f"Failed to calculate average and total score: {str(e)}")

        return {
            'status': "OK",
            'response': response_json
        }
        
    except Exception as e:
        logger.error(f"Failed to get Judgement Scores response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Judgement Scores response: {str(e)}")

@router.post("/side-by-side", response_model=JudgementResponse)
async def get_judgement_side_by_side(
    request: JudgementSideBySideRequest,
    client_id: str = Depends(verify_auth)
) -> JudgementResponse:
    """Get Judgement response for the provided Judgement request"""
    try:
        # Detect llm provider
        provider_str, model_name = request.llm.split(':')
        provider = LLMProvider.from_str(provider_str)

        # Choose prompt
        eval_prompt = request.prompt if request.prompt != None else JUDGEMENT_PROMPT_SIDE_BY_SIDE

        # Replace conversations
        eval_prompt = eval_prompt.replace("{{MESSAGES}}", get_formatted_dialog(request.messages))
        eval_prompt = eval_prompt.replace("{{GOLDEN_MESSAGES}}", get_formatted_dialog(request.golden_messages))

        response_text = get_cached_llm_answer(
            provider,
            model_name,
            [{"role": "user", "content": eval_prompt.strip()}],
            request.max_tokens,
            True
        )

        response_json = json.loads(response_text)
        return {
            'status': "OK",
            'response': response_json
        }
        
    except Exception as e:
        logger.error(f"Failed to get Judgement Side by Side response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Judgement Side by Side response: {str(e)}")

@router.post("/golden-match", response_model=JudgementResponse)
async def get_judgement_golden_match(
    request: JudgementGoldenMatchRequest,
    client_id: str = Depends(verify_auth)
) -> JudgementResponse:
    """Get Judgement response for the provided Judgement request"""
    try:
        # Detect llm provider
        provider_str, model_name = request.llm.split(':')
        provider = LLMProvider.from_str(provider_str)

        # Choose prompt
        eval_prompt = request.prompt if request.prompt != None else JUDGEMENT_PROMPT_GOLDEN_MATCH

        # Replace conversations
        eval_prompt = eval_prompt.replace("{{MESSAGES}}", get_formatted_dialog(request.messages))
        eval_prompt = eval_prompt.replace("{{GOLDEN_MESSAGES}}", get_formatted_dialog(request.golden_messages))

        response_text = get_cached_llm_answer(
            provider,
            model_name,
            [{"role": "user", "content": eval_prompt.strip()}],
            request.max_tokens,
            True
        )

        response_json = json.loads(response_text)
        return {
            'status': "OK",
            'response': response_json
        }
        
    except Exception as e:
        logger.error(f"Failed to get Judgement Golden Match response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Judgement Golden Match response: {str(e)}")

# todo: remove this endpoint
@router.post("/answer", response_model=JudgementResponse)
async def get_judgement(
    request: JudgementRequest,
    client_id: str = Depends(verify_auth)
) -> JudgementResponse:
    """Get Judgement response for the provided Judgement request"""
    try:
        # Detect llm provider
        provider_str, model_name = request.llm.split(':')
        provider = LLMProvider.from_str(provider_str)

        # Choose prompt
        eval_prompt = JUDGEMENT_PROMPT_SIDE_BY_SIDE if len(request.golden_messages) > 0 else JUDGEMENT_PROMPT_SCORE
        if request.prompt != None:
            eval_prompt = request.prompt

        # Replace conversations
        eval_prompt = eval_prompt.replace("{{MESSAGES}}", get_formatted_dialog(request.messages))
        if len(request.golden_messages) > 0:
            eval_prompt = eval_prompt.replace("{{GOLDEN_MESSAGES}}", get_formatted_dialog(request.golden_messages))

        response_text = get_cached_llm_answer(
            provider,
            model_name,
            [{"role": "user", "content": eval_prompt.strip()}],
            request.max_tokens,
            True
        )

        response_json = json.loads(response_text)
        if not request.golden_messages or len(request.golden_messages) == 0:
            try:
                scores = []
                for _, value in response_json.items():
                    if (
                        'score' in value 
                        and value['score']
                        and (
                            isinstance(value['score'], int)
                            or isinstance(value['score'], str) and value['score'].isdigit()
                        )
                        and int(value['score']) in [0, 1, 2, 3, 4, 5]
                    ):
                        scores.append(int(value['score']))
                
                response_json['Average Score'] = sum(scores) / len(scores)
                response_json['Total Score'] = sum(scores)

            except Exception as e:
                logger.info(f"Response JSON: {response_json}")
                logger.error(f"Failed to calculate average and total score: {str(e)}")

        return {
            'status': "OK",
            'response': response_json
        }
        
    except Exception as e:
        logger.error(f"Failed to get Judgement response: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get Judgement response: {str(e)}")
