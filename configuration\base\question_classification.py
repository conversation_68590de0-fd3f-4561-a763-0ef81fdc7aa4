import json
from typing import Dict, Any, <PERSON>, Tuple, Union, Optional
from quepasa.searcher.models.request import QuepasaRequest, UserHistoryItem
from src.lib.constants import (
    ANSWER_INTENT_ARCHITECTURE,
    ANSWER_INTENT_GENERAL,
    ANSWER_INTENT_SEARCH,
    ANSWER_INTENT_UNKNOWN
)

from .translation import TranslationConfig
from src.lib.llm_utils import get_cached_llm_answer
from src.lib.utils import get_think_and_answer_from_content
from src.lib.llm.providers import LLMProvider

class QuestionClassificationConfig(TranslationConfig):
    """Base configuration for question classification."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def get_classification_model_name(self, source: str) -> Tuple[str, str]:
        """Get model name to use for classification.
        
        Previously: get_classification_model_version()
        
        Args:
            source: Source type
            data: Request data
            
        Returns:
            Model identifier string
        """
        return LLMProvider.NEBIUS, "Qwen/Qwen3-32B-fast"
        
    def get_intents(self, source: str) -> List[str]:
        """Get available question intents.
        
        Previously: get_classes()
        
        Args:
            source: Source type
            data: Request data
            
        Returns:
            List of class names
        """
        return [
            ANSWER_INTENT_GENERAL,
            ANSWER_INTENT_SEARCH,
        ]
        
    def get_classification_messages(self, source: str) -> List[Dict[str, str]]:
        """Get messages for classification prompt.
        
        Previously: get_classification_messages()
        
        Args:
            source: Source type
            data: Request data
            
        Returns:
            List of message objects for prompt
        """
        return [
            {
                'role': 'system',
                'content': self.get_classification_instruction(source)
            },
            {
                'role': 'user',
                'content': "Hello"
            },
            {
                'role': 'assistant',
                'content': json.dumps({
                    'intent': ANSWER_INTENT_GENERAL,
                    'language_code': 'en',
                    'related': "no"
                })
            },
            {
                'role': 'user',
                'content': "Where can I buy it?"
            },
            {
                'role': 'assistant',
                'content': json.dumps({
                    'intent': ANSWER_INTENT_SEARCH,
                    'language_code': 'en',
                    'related': "no"
                })
            },
        ]
        
    def get_question_classification(self, source: str, history_list: List[UserHistoryItem] = []) -> Dict[str, Any]:
        """Get question classification.
        
        Previously: get_question_classification()
        
        Args:
            source: Source type
            data: Request data
            history_list: List of conversation history
            
        Returns:
            Classification result dictionary
        """
        if not self.request: 
            return {'intent': ANSWER_INTENT_UNKNOWN}
        
        # Handle empty questions
        if not self.request.question or not self.request.question.strip():
            return {'intent': ANSWER_INTENT_UNKNOWN}

        classification_provider, classification_model_version = self.get_classification_model_name(source)
        messages = self.get_classification_messages(source)

        classification = {}
        try:
            classification = self.get_base_question_analysis(
                source,
                (classification_provider, classification_model_version), 
                messages, 
                history_list
            )
        
        except Exception as e:
            return {'intent': ANSWER_INTENT_UNKNOWN}

        if 'intent' not in classification:
            classification['intent'] = ANSWER_INTENT_UNKNOWN

        if classification['intent'] not in self.get_intents(source):
            classification['intent'] = ANSWER_INTENT_SEARCH

        classification['intent'] = self.process_question_classification(source, classification['intent'])
        
        return classification
        
    def get_base_question_analysis(self, source: str, classification_tuple: Tuple[str, str], 
                                   messages: List[Dict[str, Any]], history_list: List[UserHistoryItem]) -> Dict[str, Any]:
        """Get base intent/analysis of question.
        
        Args:
            classification_model_version: Version of classification model to use
            messages: List of conversation messages
            history_list: List of conversation history
            
        Returns:
            Dict containing classification results
        """
        question = None
        if self.request.question:
            question = self.request.question

            for history_item in history_list:
                if (
                    'content' in history_item
                    and history_item['content'] != None
                    and history_item['content'].strip() != ""
                    and 'role' in history_item
                    and (
                        history_item['role'] == "user"
                        or
                        history_item['role'] == "assistant"
                        and 'classification' in history_item['request']
                        and history_item['request']['classification'] != None
                        and 'intent' in history_item['request']['classification']
                        and history_item['request']['classification']['intent'] != None
                    )
                ):
                    classification_item = ""
                    if history_item['role'] != "user":
                        classification_item = history_item['request']['classification']
                        for key in ['llm', 'model_version']:
                            if key in classification_item:
                                classification_item.pop(key)

                    messages.append({
                        'role': history_item['role'],
                        'content': history_item['content'] if history_item['role'] == "user" else json.dumps(classification_item),
                    })

            messages.append({
                'role': 'user',
                'content': question
            })

            classification_provider, classification_model_version = classification_tuple
            _, classification_json = get_think_and_answer_from_content(get_cached_llm_answer(
                classification_provider, 
                classification_model_version, 
                messages, 
                self.get_classificator_max_tokens(source)
            ).strip())

            try:
                classification = json.loads( classification_json )
                for key in list(classification.keys()):
                    if (
                        classification[ key ] != None
                        and classification[ key ].strip().lower() in ["", "не указан"]
                    ):
                        del classification[ key ]

            except Exception as e:
                classification = {}

            classification['model_version'] = classification_model_version

            if (
                'show_classification_prompt' in self.request
                and self.request.show_classification_prompt != None
                and self.request.show_classification_prompt == True
            ):
                classification['prompt'] = "\n\n-----\n\n".join([ m['content'] for m in messages ])

        else:
            classification = {}

        return classification
        
    def process_question_classification(self, source: str, class_name: str) -> str:
        """Process complete question classification.
        
        Previously: process_question_classification()
        
        Args:
            question: Question text to process
            source: Source type
            
        Returns:
            Complete question analysis
        """
        return class_name
        
    def get_classification_instruction(self, source: str) -> str:
        """Get instruction for classification model.
        
        Previously: get_classification_instruction()
        
        Args:
            source: Source type
            
        Returns:
            Classification instruction string
        """
        return f"""
You are a bot that the user asks questions to. Your task is only to classify the questions, not answer them.

Perform the following actions in the step by step way:

1. Identify the user question language.
Choose one of the possible options:
{self.get_language_options()}

2. Analyze the question and choose one of the following intents:
	a. "{ANSWER_INTENT_GENERAL}":
		— the question contains only a greeting;
	b. "{ANSWER_INTENT_SEARCH}":
		— all other questions.

3. Analyze the previous question from the context. Add the "related" field to your response. Set the "related" field to "yes" if and only if:
	— the current question was asked as a continuation of the previous question;
	— the current question reformulates or retells the previous question;
	— the current question clarifies, narrows or expands the previous question;
	— the current question coincides in topic and keywords with the previous question.
Otherwise, set the "related" field to "no".

4. Output in valid JSON format:
	— "intent",
	— "language_code",
	— "related".

Your answer should contain only a valid JSON object and nothing more.
        """.strip()
        
    def get_classificator_max_tokens(self, source: str) -> Optional[int]:
        """Get maximum tokens for classification response.
        
        Previously: get_classificator_max_tokens()
        
        Args:
            source: Source type
            
        Returns:
            Maximum token count
        """
        return None
