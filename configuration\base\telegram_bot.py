import os
import re
from typing import Optional, List, Dict, Any, Union
from quepasa.searcher.models.request import QuepasaRequest
from src.lib.files import QuepasaFiles
from .telegram_config import TelegramConfig

qp_files = QuepasaFiles()

class TelegramBotConfig(TelegramConfig):
    """Base configuration for Telegram bot functionality."""
    
    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

    def get_telegram_access_token(self) -> Optional[str]:
        """Get Telegram bot access token.
        
        Previously: get_access_token()
        """
        config = self._get_telegram_config()
        if config and 'token' in config:
            token = config['token']
            if token and token.strip():
                return token.strip()
        return None

    def get_telegram_bot_name(self) -> Optional[str]:
        """Get Telegram bot name from config.
        
        Previously: get_bot_name()
        """
        config = self._get_telegram_config()
        if config and 'name' in config:
            name = config['name']
            if name and name.strip():
                return name.strip()
        return None

    def should_stream_telegram_responses(self) -> bool:
        """Whether to stream responses in Telegram.
        
        Previously: is_stream_reply()
        """
        return True

    def allows_telegram_direct_questions(self, message: Dict[str, Any]) -> bool:
        """Whether questions without /ask command are allowed in regular chats.
        
        Previously: is_allowed_without_ask()
        """
        return False

    def allows_telegram_direct_questions_in_topic(self, topic_name: str) -> bool:
        """Whether questions without /ask command are allowed in forum topics.
        
        Previously: is_allowed_without_ask_in_topic()
        """
        return False
