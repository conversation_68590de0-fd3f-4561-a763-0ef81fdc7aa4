import time
from datetime import datetime
from typing import List, Dict, Any, Optional, Union, Tuple
from quepasa.searcher.models.request import QuepasaRequest
from src.lib.constants import (
    DOCUMENT_TYPE_DOCUMENT, 
    DOCUMENT_TYPE_PRODUCT,
    KIND_SUMMARY,
    KIND_TEXT,
    KIND_ALL,
    SOURCE_LIVEWIKI
)
from src.lib.embedding.providers import EmbeddingProvider
from .localization import LocalizationConfig
from .search_extensions import SearchExtensionsConfig
from src.lib.files import QuepasaFiles
from src.lib.llm.providers import LLMProvider

class IndexerConfig(LocalizationConfig, SearchExtensionsConfig):
    """Base configuration for indexing functionality."""

    def __init__(self, request_or_client_code: Union[QuepasaRequest, str]):
        super().__init__(request_or_client_code)

        # Global properties to track document languages and update times
        self.document_languages = None
        self.document_languages_last_updated_at = None
        self.qp_files = QuepasaFiles()

    def get_indexed_languages(self, use_cache: bool = True) -> Dict[str, List[str]]:
        """Get mapping of supported language codes to names.
        
        Previously: get_document_language_config()
        
        Returns:
            Dict mapping language codes to language names
        """
        if (
            self.document_languages_last_updated_at == None
            or time.time() - self.document_languages_last_updated_at > 600  # 10 min
            or not use_cache
        ):
            languages_list_file = f"prod/storage/indexer/api-v1/{self.client_code}/list_languages.json"
            try:
                self.document_languages = self.qp_files.get_json(languages_list_file)

            except:
                self.document_languages = {}

            self.document_languages_last_updated_at = time.time()

        languages = set()

        if not self.request:
            return {
                DOCUMENT_TYPE_DOCUMENT: [self.get_fallback_language()],
                DOCUMENT_TYPE_PRODUCT: [self.get_fallback_language()]
            }

        if (
            hasattr(self.request, 'kind') and self.request.kind in [KIND_SUMMARY, KIND_ALL]
            or self.request.source == SOURCE_LIVEWIKI
        ):
            languages.add("en")

        if (
            not hasattr(self.request, 'kind')
            or hasattr(self.request, 'kind') and self.request.kind in [KIND_TEXT, KIND_ALL]
        ):
            domains = []
            if hasattr(self.request, 'domain') and self.request.domain:
                if isinstance(self.request.domain, list) and len(self.request.domain) > 0:
                    domains = self.request.domain
                    
                elif isinstance(self.request.domain, str) and self.request.domain.strip() != "":
                    domains = self.request.domain.strip().split(',')

            if len(domains) > 0 and self.document_languages:
                for domain in domains:
                    if (
                        domain in self.document_languages
                        and self.document_languages[domain]
                    ):
                        languages.update(self.document_languages[domain])

            elif self.document_languages:
                for domain_langs in self.document_languages.values():
                    if domain_langs:
                        languages.update(domain_langs)

        filtered_languages = []
        language_mapping = self.get_language_mapping()
        for language in languages:
            language_short_code = language[0:2]
            if (
                language_short_code in language_mapping
                and language_short_code not in filtered_languages
            ):
                filtered_languages.append(language_short_code)

        if not filtered_languages:
            filtered_languages.append(self.get_fallback_language())

        return {
            DOCUMENT_TYPE_DOCUMENT: filtered_languages,
            DOCUMENT_TYPE_PRODUCT: filtered_languages
        }

    def get_embedding_model_versions(self, document_type: str) -> List[str]:
        """Get embedding model versions to use for document type.
        
        Previously: get_indexer_embedding_versions()
        
        Args:
            document_type: Type of document being indexed
            
        Returns:
            List of embedding model identifiers
        """
        return [
            (EmbeddingProvider.SBERT, "sentence-transformers/multi-qa-mpnet-base-dot-v1")
        ]
    
    def get_llm_summary_model_name(self, domain: Optional[str] = None) -> Tuple[str, str]:
        """Get model to generate summaries for documents.
        
        Args:
            domain: Domain of the document
            
        Returns:
            Model identifier string
        """

        return LLMProvider.NEBIUS, "Qwen/Qwen2.5-32B-Instruct-fast"