from enum import Enum

class RerankerProvider(Enum):
    """Enum for supported reranker providers."""
    TOGETHER = "together"
    
    @classmethod
    def from_str(cls, provider: str) -> "RerankerProvider":
        """Convert string to provider enum.
        
        Args:
            provider: Provider name as string
            
        Returns:
            Provider enum value
            
        Raises:
            ValueError: If provider is not supported
        """
        try:
            return cls(provider.lower())
        except ValueError:
            raise ValueError(f"Unknown reranker provider: {provider}. "
                           f"Supported providers: {[p.value for p in cls]}") 