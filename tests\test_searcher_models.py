import pytest
from datetime import datetime
from quepasa.searcher.models.request import QuepasaRequest, UserInfo, WebhookConfig
from quepasa.searcher.models.response import QuepasaResponse, QuepasaAnswer

def test_request_model():
    request = QuepasaRequest(
        question="test query",
        protocol="http"
    )
    
    assert request.question == "test query"
    assert request.protocol == "http"
    assert request.engine == "answer"  # default value

def test_request_model_with_optional_fields():
    request = QuepasaRequest(
        question="test query",
        protocol="webhook",
        language="es",
        webhook={"endpoint": "https://example.com/webhook"},
        user_info={"id": "test_user", "name": "Test User"}
    )
    
    assert request.language == "es"
    assert request.webhook.endpoint == "https://example.com/webhook"
    assert request.user_info.id == "test_user"
    assert request.user_info.name == "Test User"

def test_response_model():
    response = QuepasaResponse(
        status="ok",
        data={},
        error=None
    )
    
    assert response.status == "ok"
    assert response.data == {}
    assert response.error is None

def test_response_model_with_answers():
    response = QuepasaResponse(
        status="ok",
        data={
            "text": "test answer",
            "markdown": "test answer",
            "references": []
        },
        error=None
    )
    
    assert response.status == "ok"
    assert response.data["text"] == "test answer"
    assert response.data["markdown"] == "test answer"
    assert len(response.data["references"]) == 0

def test_invalid_request():
    with pytest.raises(ValueError):
        QuepasaRequest(
            question="",  # empty question
            protocol="invalid"  # invalid protocol not in VALID_PROTOCOLS
        )

def test_invalid_response():
    with pytest.raises(ValueError):
        QuepasaResponse(
            status="error",  # error status requires error message
            data=QuepasaAnswer(text="test"),  # valid data
            error=None  # missing required error message for error status
        ) 