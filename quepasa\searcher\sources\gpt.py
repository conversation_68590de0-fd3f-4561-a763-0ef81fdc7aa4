import re
from typing import Dict, <PERSON>, Optional, <PERSON>, Generator
from dataclasses import dataclass
import time
import tiktoken

from src.lib.logger import <PERSON><PERSON><PERSON><PERSON>ogger
from src.lib.constants import SOURCE_GPT, ANSWER_TYPE_STRICT
from configuration.main.default import QuepasaConfigurationHub
from src.lib.llm_utils import get_cached_streaming_chunks, get_cached_llm_answer

from .base import (
    BaseSource,
    STATUS_SUCCESS,
    ERROR_MESSAGE_SEARCH_NOT_SUPPORTED,
    ERROR_MESSAGE_PROMPT_TOO_LONG
)

from ..utils import (
    get_encoding,
    get_llm_model_name,
    get_llm_prompt_template,
    get_think_and_answer_from_content
)

from ..models.request import QuepasaRequest
from ..models.response import QuepasaResponse, QuepasaStreamAnswer
from ..models.answer import AnswerFormatter
from ..utils import get_serializable_request

logger = QuepasaLogger().get_instance(__name__)

class GPTSource(BaseSource):
    """GPT source for direct LLM queries"""
    
    def __init__(self):
        """Initialize GPT source"""
        super().__init__()
        
    def search(self, request: QuepasaRequest) -> QuepasaResponse:
        """Execute search request
        
        Args:
            request: Search request
            client_config: Client configuration
            
        Returns:
            Search response
        """
        # GPT source doesn't support search, only direct answers
        return self._handle_error(request, ERROR_MESSAGE_SEARCH_NOT_SUPPORTED)
        
    def get_answer(self, request: QuepasaRequest, stream: bool = False) -> Union[QuepasaResponse, Generator[QuepasaStreamAnswer, None, None]]:
        """Get answer for request
        
        Args:
            request: Search request
            client_config: Client configuration
            stream: Whether to stream the response
            
        Returns:
            Search response or generator of stream responses
        """
        logger.info(f"GPT get_answer request: {request}")
        if stream:
            return self._get_streaming_answer(request)
        else:
            return self._get_non_streaming_answer(request)
    
    def _get_non_streaming_answer(self, request: QuepasaRequest) -> QuepasaResponse:
        """Get non-streaming answer"""
        config = QuepasaConfigurationHub.from_request(request)
        formatter = AnswerFormatter()
        source = request.source

        try:
            # Get prompt template
            prompt_template = get_llm_prompt_template(request.prompt) if request.prompt else config.get_llm_prompt_template(SOURCE_GPT)

            # Get LLM configuration from client
            provider, model = get_llm_model_name(request.answer_llm) if request.answer_llm else config.get_llm_model_name(SOURCE_GPT)
            logger.info(f"Retrieved LLM configuration: provider: {provider}, model: {model}, request.answer_llm: {request.answer_llm}")

            llm_params = config.get_llm_parameters(SOURCE_GPT)
            
            # Initialize tokenizer based on model
            encoding = get_encoding(model)
                
            # Calculate max context length
            max_response_tokens = config.get_max_response_tokens(SOURCE_GPT, model)
            max_prompt_tokens = config.get_max_prompt_tokens(SOURCE_GPT)
            
            # Process prompt template and check tokens
            if isinstance(prompt_template, str):
                prompt_messages = [{'role': 'user', 'content': prompt_template}]
                prompt_tokens = len(encoding.encode(prompt_template))

            else:
                prompt_messages = prompt_template
                prompt_tokens = 0
                for msg in prompt_template:
                    prompt_tokens += len(encoding.encode(msg['content']))
            
            if prompt_tokens > max_prompt_tokens:
                return self._handle_non_streaming_error(request, ERROR_MESSAGE_PROMPT_TOO_LONG)

            # Get answer from LLM
            answer = get_cached_llm_answer(
                provider,
                model,
                prompt_messages,
                max_response_tokens,
                llm_params.get('json_mode', False)
            )
            
            think, answer = get_think_and_answer_from_content(answer)
            logger.info(f"Think: {think}")
            logger.info(f"Answer: {answer}")

            answer, _ = config.process_answer(source, False, answer, {})
            
            # Format answer using the new formatter
            formatted_answer = formatter.format_answer(request, answer)
            
            # Format response
            response = {
                'type': f"{formatted_answer.type.value}.{source}",
                'text': formatted_answer.text,
                'markdown': formatted_answer.markdown,
                'references': formatted_answer.references,
                'metadata': {
                    'request': get_serializable_request(request.to_dict()),
                    'prompt': prompt_messages,
                }
            }
            
            # Process response
            processed_response = config.process_response(response)
                
            return QuepasaResponse(
                status=STATUS_SUCCESS,
                data=processed_response
            )
            
        except Exception as e:
            return self._handle_non_streaming_error(request, f"Error getting answer: {str(e)}")
    
    def _get_streaming_answer(self, request: QuepasaRequest) -> Generator[QuepasaStreamAnswer, None, None]:
        """Get streaming answer"""
        config = QuepasaConfigurationHub.from_request(request)
        formatter = AnswerFormatter()
        source = request.source

        try:
            # Get prompt template
            prompt_template = get_llm_prompt_template(request.prompt) if request.prompt else config.get_llm_prompt_template(SOURCE_GPT)

            # Get LLM configuration from client
            provider, model = get_llm_model_name(request.answer_llm) if request.answer_llm else config.get_llm_model_name(SOURCE_GPT)
            logger.info(f"Retrieved LLM configuration: provider: {provider}, model: {model}, request.answer_llm: {request.answer_llm}")

            llm_params = config.get_llm_parameters(SOURCE_GPT)
            
            # Initialize tokenizer based on model
            encoding = get_encoding(model)
                
            # Calculate max context length
            max_response_tokens = config.get_max_response_tokens(SOURCE_GPT, model)
            max_prompt_tokens = config.get_max_prompt_tokens(SOURCE_GPT)
            
            # Process prompt template and check tokens
            if isinstance(prompt_template, str):
                prompt_messages = [{'role': 'user', 'content': prompt_template}]
                prompt_tokens = len(encoding.encode(prompt_template))

            else:
                prompt_messages = prompt_template
                prompt_tokens = 0
                for msg in prompt_template:
                    prompt_tokens += len(encoding.encode(msg['content']))
            
            if prompt_tokens > max_prompt_tokens:
                for error_chunk in self._handle_streaming_error(request, ERROR_MESSAGE_PROMPT_TOO_LONG):
                    yield error_chunk
                return

            # If streaming, yield initial state
            yield QuepasaStreamAnswer(
                loading=True,
                streaming=False,
                created_at=int(time.time())
            )
            
            collected_answer = []
            for chunk in get_cached_streaming_chunks(
                provider,
                model,
                prompt_messages,
                max_response_tokens,
                llm_params.get('json_mode', False)
            ):
                collected_answer.append(chunk)

                # Process answer
                think, answer = get_think_and_answer_from_content(''.join(collected_answer))
                answer, _ = config.process_answer(source, True, answer, {})

                formatted_answer = formatter.format_answer(request, answer)
                yield QuepasaStreamAnswer(
                    type=f"{formatted_answer.type.value}.{source}",
                    think=think,
                    text=formatted_answer.text,
                    markdown=formatted_answer.markdown,
                    references=formatted_answer.references,
                    loading=False,
                    streaming=True,
                    created_at=int(time.time())
                )
            
            # Process final answer for streaming
            think, answer = get_think_and_answer_from_content(''.join(collected_answer))
            logger.info(f"Think: {think}")
            logger.info(f"Answer: {answer}")

            answer, _ = config.process_answer(source, False, answer, {})
            
            # Format answer using the new formatter
            formatted_answer = formatter.format_answer(request, answer)
            
            # Format response
            response = {
                'type': f"{formatted_answer.type.value}.{source}",
                'text': formatted_answer.text,
                'markdown': formatted_answer.markdown,
                'references': formatted_answer.references,
                'metadata': {
                    'request': get_serializable_request(request.to_dict()),
                    'prompt': prompt_messages,
                }
            }
            
            # Process response
            processed_response = config.process_response(response)
            
            # Yield final response
            yield QuepasaStreamAnswer(
                loading=False,
                streaming=False,
                created_at=int(time.time()),
                **processed_response
            )
            
        except Exception as e:
            for error_chunk in self._handle_streaming_error(request, f"Error getting answer: {str(e)}"):
                yield error_chunk
