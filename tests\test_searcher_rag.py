import pytest
from unittest.mock import Mock, patch
from datetime import datetime
from elasticsearch import Elasticsearch

from quepasa.searcher.core.rag_search import Rag<PERSON>earchManager
from quepasa.searcher.models.request import QuepasaRequest
from quepasa.searcher.models.document import QuepasaDocument
from src.lib.constants import (
    FACTOID_LEVEL_DOCUMENT,
    FACTOID_LEVEL_CHUNK,
    SOURCE_DOCUMENTS
)

from quepasa.searcher.sources.base import STATUS_SUCCESS, STATUS_ERROR

@pytest.fixture
def mock_embedding(mocker):
    """Mock embedding model for tests"""
    with patch('src.lib.embedding_utils.get_cached_embedding') as mock:
        instance = mock.return_value
        instance.encode.return_value = [0.1] * 384
        yield instance

@pytest.fixture
def mock_reranker(mocker):
    """Mock reranker model for tests"""
    with patch('src.lib.reranker_utils.get_cached_reranker') as mock:
        instance = mock.return_value
        instance.rerank.return_value = [(0, 0.8)]
        yield instance

@pytest.fixture
def mock_elasticsearch(mocker):
    """Mock Elasticsearch for tests"""
    mock_es = mocker.Mock(spec=Elasticsearch)
    mock_es.search.return_value = {
        "hits": {
            "hits": [
                {
                    "_id": "doc1",
                    "_score": 0.8,
                    "_source": {
                        "id": "doc1",
                        "root_id": "doc1",
                        "chunk_index": 0,
                        "client": "test",
                        "domain": "test.com",
                        "provider": "test_provider",
                        "type": "DOCUMENT",
                        "kind": "text",
                        "level": FACTOID_LEVEL_DOCUMENT,
                        "url": "https://test.com/doc1",
                        "language": "en",
                        "title": "Test Document",
                        "keywords": ["test"],
                        "text": "Sample text content",
                        "tokens": 100,
                        "chunks": [],
                        "start_position": 0,
                        "end_position": 100,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat(),
                        "embeddings": {"test_model": [0.1] * 384}
                    }
                }
            ],
            "total": {
                "value": 1,
                "relation": "eq"
            }
        },
        "aggregations": {
            "total_chunks": {"value": 1},
            "total_documents": {"value": 1}
        }
    }
    mocker.patch('quepasa.searcher.core.rag_search.Elasticsearch', return_value=mock_es)
    mocker.patch('quepasa.searcher.core.rag_search.es', mock_es)
    return mock_es

@pytest.fixture
def mock_config(mocker):
    """Mock config for RAG search tests"""
    mock = mocker.Mock()
    mock.get_client_code.return_value = "test"
    mock.get_index_name.return_value = "test_index"
    mock.get_language_code.return_value = "en"
    mock.get_max_results.return_value = 10
    mock.get_indexed_languages.return_value = {"DOCUMENT": ["en"]}
    mock.get_fuzzy_match_prefix_length.return_value = 3
    mock.get_document_relevance_weights.return_value = {"text": 0.5, "semantic": 0.5}
    mock.get_chunk_relevance_weights.return_value = {"text": 0.5, "semantic": 0.5}
    mock.should_filter_by_date.return_value = True
    mock.get_search_embedding_model.return_value = ("test_provider", "test_model")
    mock.get_search_reranker_model.return_value = (None, None)
    mock.expand_question.return_value = "expanded test query"
    mock.get_translated_question_hash.return_value = {"DOCUMENT": {"en": "test query"}}
    mock.get_relevance_weights.return_value = {"text": 0.5, "semantic": 0.5}
    return mock

@pytest.fixture
def rag_manager(mock_config, mock_elasticsearch, mocker):
    """Create a RAG search manager with mocked config"""
    mocker.patch('configuration.main.default.QuepasaConfigurationHub', return_value=mock_config)
    mocker.patch('quepasa.searcher.core.rag_search.get_elasticsearch_config', return_value={
        "hosts": ["http://localhost:9200"],
        "verify_certs": False
    })
    manager = RagSearchManager()
    return manager

@pytest.fixture
def request_with_metadata():
    """Create a request with metadata for testing"""
    return QuepasaRequest(
        client="test",
        question="test query",
        protocol="http",
        source=SOURCE_DOCUMENTS
    )

@pytest.fixture
def mock_document():
    """Create a mock document for testing"""
    return QuepasaDocument(
        id="doc1",
        root_id="doc1",
        chunk_index=0,
        client="test",
        domain="test.com",
        provider="test_provider",
        type="DOCUMENT",
        kind="text",
        level=FACTOID_LEVEL_DOCUMENT,
        url="https://test.com/doc1",
        language="en",
        title="Test Document",
        keywords=["test"],
        text="Sample text content",
        tokens=100,
        chunks=[],
        start_position=0,
        end_position=100,
        created_at=datetime.now().isoformat(),
        updated_at=datetime.now().isoformat(),
        embeddings={"test_model": [0.1] * 384},
        score=0.8
    )

def test_search_basic(rag_manager, request_with_metadata, mock_embedding, mock_reranker, mock_elasticsearch, mock_document):
    """Test basic search functionality"""
    with patch('quepasa.searcher.core.rag_search.QuepasaDocument', return_value=mock_document):
        results = rag_manager.search(request_with_metadata)
        assert len(results) == 1
        assert results[0].id == "doc1"

def test_search_error_handling(rag_manager, request_with_metadata, mock_embedding, mock_reranker, mock_elasticsearch):
    """Test error handling during search"""
    mock_elasticsearch.search.side_effect = RuntimeError("Test error")
    with pytest.raises(RuntimeError) as exc_info:
        rag_manager.search(request_with_metadata)
