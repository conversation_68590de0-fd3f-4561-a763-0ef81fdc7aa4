import json
from datetime import datetime
from unittest.mock import patch

import pytest
from elasticsearch import Elasticsearch

from quepasa.searcher.core.rag_search import RagSearchManager
from quepasa.searcher.models.document import QuepasaDocument
from quepasa.searcher.models.request import QuepasaRequest
from src.lib.constants import (
    FACTOID_LEVEL_DOCUMENT,
    FACTOID_LEVEL_CHUNK,
    SOURCE_DOCUMENTS,
    SOURCE_PRODUCTS
)


@pytest.fixture
def mock_embedding(mocker):
    """Mock embedding model for tests"""
    with patch('src.lib.embedding_utils.get_cached_embedding') as mock:
        instance = mock.return_value
        instance.encode.return_value = [0.1] * 384
        yield instance

@pytest.fixture
def mock_reranker(mocker):
    """Mock reranker model for tests"""
    with patch('src.lib.reranker_utils.get_cached_reranker') as mock:
        instance = mock.return_value
        instance.rerank.return_value = [(0, 0.8)]
        yield instance

@pytest.fixture
def mock_elasticsearch(mocker):
    """Mock Elasticsearch for tests"""
    mock_es = mocker.Mock(spec=Elasticsearch)
    mock_es.search.return_value = {
        "hits": {
            "hits": [
                {
                    "_id": "doc1",
                    "_score": 0.8,
                    "_source": {
                        "id": "doc1",
                        "root_id": "doc1",
                        "chunk_index": 0,
                        "client": "test",
                        "domain": "test.com",
                        "provider": "test_provider",
                        "type": "DOCUMENT",
                        "kind": "text",
                        "level": FACTOID_LEVEL_DOCUMENT,
                        "url": "https://test.com/doc1",
                        "language": "en",
                        "title": "Test Document",
                        "keywords": ["test"],
                        "text": "Sample text content",
                        "tokens": 100,
                        "chunks": [],
                        "start_position": 0,
                        "end_position": 100,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat(),
                        "embeddings": {"test_model": [0.1] * 384}
                    }
                }
            ],
            "total": {
                "value": 1,
                "relation": "eq"
            }
        },
        "aggregations": {
            "total_chunks": {"value": 1},
            "total_documents": {"value": 1}
        }
    }
    mocker.patch('quepasa.searcher.core.rag_search.Elasticsearch', return_value=mock_es)
    mocker.patch('quepasa.searcher.core.rag_search.es', mock_es)
    return mock_es

@pytest.fixture
def mock_elasticsearch_with_product(mocker):
    """Mock Elasticsearch for product document tests"""
    mock_es = mocker.Mock(spec=Elasticsearch)
    mock_es.search.return_value = {
        "hits": {
            "hits": [
                {
                    "_id": "SKU123",
                    "_score": 0.9,
                    "_source": {
                        "id": "SKU123",
                        "root_id": "SKU123",
                        "chunk_index": 0,
                        "client": "test",
                        "domain": "test.com",
                        "provider": "test_provider",
                        "type": "product",
                        "kind": "text",
                        "level": FACTOID_LEVEL_CHUNK,
                        "url": "https://test.com/product/SKU123",
                        "language": "en",
                        "title": "Product Title",
                        "keywords": ["product", "test"],
                        "text": "Product description text",
                        "tokens": 50,
                        "chunks": [],
                        "start_position": 0,
                        "end_position": 50,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat(),
                        "sku": "SKU123",
                        "metadata": '{"name": "Product Name", "price": 99.99, "category": "electronics"}'
                    }
                }
            ],
            "total": {
                "value": 1,
                "relation": "eq"
            }
        },
        "aggregations": {
            "total_chunks": {"value": 1},
            "total_documents": {"value": 1}
        }
    }
    mocker.patch('quepasa.searcher.core.rag_search.Elasticsearch', return_value=mock_es)
    mocker.patch('quepasa.searcher.core.rag_search.es', mock_es)
    return mock_es

@pytest.fixture
def mock_config(mocker):
    """Mock config for RAG search tests"""
    mock = mocker.Mock()
    mock.get_client_code.return_value = "test"
    mock.get_index_name.return_value = "test_index"
    mock.get_language_code.return_value = "en"
    mock.get_max_results.return_value = 10
    mock.get_indexed_languages.return_value = {"DOCUMENT": ["en"]}
    mock.get_fuzzy_match_prefix_length.return_value = 3
    mock.get_document_relevance_weights.return_value = {"text": 0.5, "semantic": 0.5}
    mock.get_chunk_relevance_weights.return_value = {"text": 0.5, "semantic": 0.5}
    mock.should_filter_by_date.return_value = True
    mock.get_search_embedding_model.return_value = ("test_provider", "test_model")
    mock.get_search_reranker_model.return_value = (None, None)
    mock.expand_question.return_value = "expanded test query"
    mock.get_translated_question_hash.return_value = {"DOCUMENT": {"en": "test query"}}
    mock.get_relevance_weights.return_value = {"text": 0.5, "semantic": 0.5}
    return mock

@pytest.fixture
def rag_manager(mock_config, mock_elasticsearch, mocker):
    """Create a RAG search manager with mocked config"""
    mocker.patch('configuration.main.default.QuepasaConfigurationHub', return_value=mock_config)
    mocker.patch('quepasa.searcher.core.rag_search.get_elasticsearch_config', return_value={
        "hosts": ["http://localhost:9200"],
        "verify_certs": False
    })
    manager = RagSearchManager()
    return manager

@pytest.fixture
def request_with_metadata():
    """Create a request with metadata for testing"""
    return QuepasaRequest(
        client="test",
        question="test query",
        protocol="http",
        source=SOURCE_DOCUMENTS
    )

@pytest.fixture
def request_for_products():
    """Create a request specifically for product search testing"""
    return QuepasaRequest(
        client="test",
        question="test product query",
        protocol="http",
        source=SOURCE_PRODUCTS
    )

@pytest.fixture
def mock_document():
    """Create a mock document for testing"""
    return QuepasaDocument(
        id="doc1",
        root_id="doc1",
        chunk_index=0,
        client="test",
        domain="test.com",
        provider="test_provider",
        type="DOCUMENT",
        kind="text",
        level=FACTOID_LEVEL_DOCUMENT,
        url="https://test.com/doc1",
        language="en",
        title="Test Document",
        keywords=["test"],
        text="Sample text content",
        tokens=100,
        chunks=[],
        start_position=0,
        end_position=100,
        created_at=datetime.now().isoformat(),
        updated_at=datetime.now().isoformat(),
        embeddings={"test_model": [0.1] * 384},
        score=0.8,
        sku=None,
        metadata=None
    )

@pytest.fixture
def mock_product_document():
    """Create a mock product document with sku and metadata for testing"""
    return QuepasaDocument(
        id="SKU123",
        root_id="SKU123",
        chunk_index=0,
        client="test",
        domain="test.com",
        provider="test_provider",
        type="product",
        kind="text",
        level=FACTOID_LEVEL_CHUNK,
        url="https://test.com/product/SKU123",
        language="en",
        title="Product Title",
        keywords=["product", "test"],
        text="Product description text",
        tokens=50,
        chunks=[],
        start_position=0,
        end_position=50,
        created_at=datetime.now().isoformat(),
        updated_at=datetime.now().isoformat(),
        embeddings={"test_model": [0.1] * 384},
        score=0.9,
        sku="SKU123",
        metadata='{"name": "Product Name", "price": 99.99, "category": "electronics"}'
    )

def test_search_basic(rag_manager, request_with_metadata, mock_embedding, mock_reranker, mock_elasticsearch, mock_document):
    """Test basic search functionality"""
    with patch('quepasa.searcher.core.rag_search.QuepasaDocument', return_value=mock_document):
        results = rag_manager.search(request_with_metadata)
        assert len(results) == 1
        assert results[0].id == "doc1"

def test_search_error_handling(rag_manager, request_with_metadata, mock_embedding, mock_reranker, mock_elasticsearch):
    """Test error handling during search"""
    mock_elasticsearch.search.side_effect = RuntimeError("Test error")
    with pytest.raises(RuntimeError) as exc_info:
        rag_manager.search(request_with_metadata)

def test_rag_search_product_documents_with_metadata(rag_manager, request_for_products, mock_embedding, mock_reranker, mock_elasticsearch_with_product):
    """Test RAG search retrieval of product documents with sku and metadata fields"""
    # Execute search
    results = rag_manager.search(request_for_products)
    
    # Verify results
    assert len(results) == 1
    product_doc = results[0]
    
    # Verify product document fields
    assert product_doc.id == "SKU123"
    assert product_doc.type == "product"
    assert product_doc.sku == "SKU123"
    assert product_doc.metadata == '{"name": "Product Name", "price": 99.99, "category": "electronics"}'
    assert product_doc.score == 0.9
    
    # Verify metadata integrity (JSON string should be valid)
    metadata_dict = json.loads(product_doc.metadata)
    assert metadata_dict["name"] == "Product Name"
    assert metadata_dict["price"] == 99.99
    assert metadata_dict["category"] == "electronics"

def test_search_backward_compatibility_missing_fields(rag_manager, request_with_metadata, mock_embedding, mock_reranker, mocker):
    """Test backward compatibility when sku and metadata fields are missing"""
    # Mock Elasticsearch response without sku/metadata fields
    mock_es = mocker.Mock(spec=Elasticsearch)
    mock_es.search.return_value = {
        "hits": {
            "hits": [
                {
                    "_id": "doc2",
                    "_score": 0.7,
                    "_source": {
                        "id": "doc2",
                        "root_id": "doc2",
                        "chunk_index": 0,
                        "client": "test",
                        "domain": "test.com",
                        "provider": "test_provider",
                        "type": "DOCUMENT",
                        "kind": "text",
                        "level": FACTOID_LEVEL_CHUNK,
                        "url": "https://test.com/doc2",
                        "language": "en",
                        "title": "Document without new fields",
                        "text": "Document content",
                        "tokens": 80,
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                        # Note: No sku or metadata fields
                    }
                }
            ],
            "total": {"value": 1, "relation": "eq"}
        },
        "aggregations": {
            "total_chunks": {"value": 1},
            "total_documents": {"value": 1}
        }
    }
    mocker.patch('quepasa.searcher.core.rag_search.es', mock_es)
    
    # Execute search
    results = rag_manager.search(request_with_metadata)
    
    # Verify results
    assert len(results) == 1
    doc = results[0]
    
    # Verify document fields exist
    assert doc.id == "doc2"
    assert doc.type == "DOCUMENT"
    
    # Verify sku and metadata default to None when missing
    assert doc.sku is None
    assert doc.metadata is None
    
    # Verify other fields are populated correctly
    assert doc.title == "Document without new fields"
    assert doc.text == "Document content"
    assert doc.score == 0.7
