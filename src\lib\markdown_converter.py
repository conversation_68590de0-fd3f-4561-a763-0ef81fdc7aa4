import logging
from typing import List, Dict, Any, Optional, Set
import copy

# Maximum number of product variants per markdown chunk.
# If a product has more, variants are split into multiple chunks of this size.
CHUNK_LIST_LEN = 8

# Get logger for this module
logger = logging.getLogger(__name__)

# Default exclusion sets
# TODO: move to config for each customer
DEFAULT_EXCLUDE_KEYS = {
    "_id", "indexables", "samplePrecision", "gtin", "dynamicFacets", "conditions",
    "fulfillmentInfo", "availableTime", "type", "id", "name", "attributes", "variants"
}

DEFAULT_EXCLUDE_ATTR_KEYS = {
    "gbi_L1", "gbi_L2", "gbi_L3", "gbi_L4",
    "OnlineFlag", "sddEligibleFlag", "PRIMARY_CATEGORY", "BRAND_ID",
    "MARKETPLACE_ITEM_FLAG", "SKU_FOR_SWATCH_UPC", "SKU_FOR_SWATCH_SKU_SIZE",
    "PERSONALIZATION_TYPE", "CUSTOMIZATION_OFFERED_FLAG"
}

DEFAULT_EXCLUDE_VAR_KEYS = {
    "samplePrecision", "type", "primaryProductId", "gtin", "fulfillmentInfo",
    "availableTime", "name", "id", "attributes"
}

def _format_value_in_markdown(value: Any) -> str:
    """Format a value for markdown output"""
    if isinstance(value, list):
        return ", ".join(_format_value_in_markdown(v) for v in value)
    if isinstance(value, dict):
        return ", ".join(f"{k}: {_format_value_in_markdown(v)}" for k, v in value.items())
    return str(value)


def _preprocess_product_to_markdown(product: Dict[str, Any]) -> str:
    """
    Convert a preprocessed product dictionary into a well-formatted Markdown string.
    Expects product to have the 'SKU' field (preprocessed from 'id').
    """
    sku = product.get("SKU")
    description = product.get("description", "").strip()
    availability = product.get("availability", "")
    attributes = product.get("attributes", {})
    link = product.get("uri", "")
    images = [img.get("uri") for img in product.get("images", []) if isinstance(img, dict)]
    variants = product.get("variants", [])
    title = product.get('title', sku)

    # --- main fields ---
    md = [f"# {title}", f"**SKU:** {sku}", f"**Availability:** {availability}"]

    # --- other fields ---
    ignore_keys = {"SKU", "description", "availability", "attributes", "uri", "images", "variants", "title"}
    extra = {k: v for k, v in product.items() if k not in ignore_keys}
    if extra:
        for k, v in extra.items():
            md.append(f"**{k}:** {_format_value_in_markdown(v)}")

    # link + image
    if link or images:
        md.append("")
        if link:
            md.append(f"[View product]({link})")
        if images:
            for img in images:
                md.append(f"![image]({img})")

    # description
    if description:
        md.append("")
        md.append("## Description")
        md.append(description)

    # attributes
    if attributes:
        md.append("")
        md.append("## Attributes")
        for key, val in attributes.items():
            md.append(f"**{key}:** {_format_value_in_markdown(val)}")

    # variants
    if variants:
        md.append("")
        md.append("## Variants")

        keys = []
        for v in variants:
            for k in v.keys():
                if k not in keys:
                    keys.append(k)

        # table
        header = "| " + " | ".join(keys) + " |"
        sep = "|" + "|".join(["-" * (len(k) + 2) for k in keys]) + "|"
        rows = []
        for v in variants:
            row = "| " + " | ".join(_format_value_in_markdown(v.get(k, "")) for k in keys) + " |"
            rows.append(row)

        md.append(header)
        md.append(sep)
        md.extend(rows)

    return "\n".join(md)


def _delete_unnecessary_fields(product: Dict[str, Any],
                               exclude_keys: Optional[Set[str]] = None,
                               exclude_attr_keys: Optional[Set[str]] = None,
                               exclude_var_keys: Optional[Set[str]] = None
                               ) -> Dict[str, Any]:
    """Delete unnecessary fields and attributes from the product"""

    # Use default exclusion sets if not provided
    if exclude_keys is None:
        exclude_keys = DEFAULT_EXCLUDE_KEYS

    if exclude_attr_keys is None:
        exclude_attr_keys = DEFAULT_EXCLUDE_ATTR_KEYS

    if exclude_var_keys is None:
        exclude_var_keys = DEFAULT_EXCLUDE_VAR_KEYS

    # Create base product with SKU from id
    md_ready_product = {"SKU": product.get("id")}
    md_ready_product.update({k: v for k, v in product.items() if k not in exclude_keys})

    # Process attributes
    if 'attributes' in product and product['attributes']:
        md_ready_product['attributes'] = {
            k: v for k, v in product['attributes'].items()
            if k not in exclude_attr_keys and 
                not k.startswith("gbi_") and 
                not k.startswith("SKU_FOR_SWATCH_") and 
                not k.startswith("MARKETPLACE_")
        }
    else:
        md_ready_product['attributes'] = {}

    # Process variants
    md_ready_product['variants'] = []
    if 'variants' in product and product['variants']:
        for var in product['variants']:
            md_ready_variant = {"SKU": var.get("id")}
            md_ready_variant.update(
                (k, v) for k, v in var.items()
                if k not in exclude_var_keys
            )

            # Process variant attributes
            if 'attributes' in var and var['attributes']:
                var_attributes = {
                    k: v for k, v in var['attributes'].items() if (
                            k not in exclude_attr_keys and 
                            not k.startswith("gbi_") and
                            not k.startswith("SKU_FOR_SWATCH_") and 
                            not k.startswith("MARKETPLACE_") and
                            (k not in md_ready_product['attributes'] or
                                md_ready_product['attributes'][k] != v)
                    )
                }

                if var_attributes:
                    md_ready_variant['attributes'] = var_attributes

            md_ready_product['variants'].append(md_ready_variant)

    return md_ready_product


def _get_product_variants_keys(md_ready_product: Dict[str, Any]) -> Set[str]:
    """Get all unique keys from product variants"""
    variant_keys = set()
    for variant in md_ready_product.get('variants', []):
        variant_keys.update(variant.keys())
    return variant_keys


def _get_product_variants_attributes_keys(md_ready_product: Dict[str, Any]) -> Set[str]:
    """Get all unique attribute keys from product variants"""
    variant_attr_keys = set()
    for variant in md_ready_product.get('variants', []):
        if 'attributes' in variant:
            variant_attr_keys.update(variant['attributes'].keys())
    return variant_attr_keys


def _delete_common_fields_from_variants(md_ready_product: Dict[str, Any]) -> Dict[str, Any]:
    """Move common variant fields to product level"""
    for key in _get_product_variants_keys(md_ready_product):
        if key in [
            'attributes',
            'availability',
            'SKU',
            'sizes',
            #'priceInfo'
        ]:
            continue

        check_diff_value = False
        common_value = None
        for variant in md_ready_product.get('variants', []):
            if common_value is None:
                common_value = variant.get(key)
            elif common_value != variant.get(key):
                check_diff_value = True
                break

        if not check_diff_value:
            md_ready_product[key] = common_value

            for variant in md_ready_product.get('variants', []):
                variant.pop(key, None)

    return md_ready_product


def _delete_common_attributes_from_variants(md_ready_product: Dict[str, Any]) -> Dict[str, Any]:
    """Remove common attributes from variants"""
    for key in _get_product_variants_attributes_keys(md_ready_product):
        check_diff_value = False
        common_value = None
        for variant in md_ready_product.get('variants', []):
            if 'attributes' not in variant:
                continue
            if common_value is None:
                common_value = variant['attributes'].get(key)
            elif common_value != variant['attributes'].get(key):
                check_diff_value = True
                break

        if not check_diff_value:
            md_ready_product['attributes'][key] = common_value

            for variant in md_ready_product.get('variants', []):
                variant['attributes'].pop(key, None) 

    return md_ready_product

def _preprocess_product(product: Dict[str, Any],
                        exclude_keys: Optional[Set[str]] = None,
                        exclude_attr_keys: Optional[Set[str]] = None,
                        exclude_var_keys: Optional[Set[str]] = None
                        ) -> Dict[str, Any]:
    """Complete preprocessing pipeline for a single product"""

    md_ready_product = _delete_unnecessary_fields(product, exclude_keys, exclude_attr_keys, exclude_var_keys)
    md_ready_product = _delete_common_fields_from_variants(md_ready_product)
    md_ready_product = _delete_common_attributes_from_variants(md_ready_product)

    # Clean up empty attributes in variants
    for variant in md_ready_product.get('variants', []):
        if 'attributes' in variant and len(variant['attributes']) == 0:
            variant.pop('attributes', None)

    # Ensure field order (attributes and variants at the end)
    if 'attributes' in md_ready_product:
        md_ready_product["attributes"] = md_ready_product.pop("attributes")
    if 'variants' in md_ready_product:
        md_ready_product["variants"] = md_ready_product.pop("variants")

    return md_ready_product

def _create_product_chunks(md_ready_product: Dict[str, Any]) -> List[Dict[str, str]]:
    """Split product variants into chunks and generate markdown for each chunk."""
    variants = md_ready_product['variants']
    variant_groups = [variants[i:i+CHUNK_LIST_LEN] for i in range(0, len(variants), CHUNK_LIST_LEN)]
    chunks = []
    for group in variant_groups:
        chunk = copy.deepcopy(md_ready_product)
        chunk['variants'] = group
        chunks.append({'language': 'en', 'text': _preprocess_product_to_markdown(chunk)})
    if not chunks:
        chunks.append({'language': 'en', 'text': _preprocess_product_to_markdown(md_ready_product)})
    return chunks

def _calculate_price_range(md_ready_product: Dict[str, Any]) -> (float, float):
    """Calculate price_from and price_to from product and variants."""
    price_from = 0
    price_to = 0
    if 'priceInfo' in md_ready_product:
        price_from = md_ready_product['priceInfo'].get('price', 0)
        price_to = md_ready_product['priceInfo'].get('price', 0)
    for variant in md_ready_product.get('variants', []):
        variant_price = variant.get('priceInfo', {}).get('price', 0)
        if price_from > 0 and variant_price > 0:
            price_from = min(price_from, variant_price)
        elif variant_price > 0:
            price_from = variant_price
        price_to = max(price_to, variant_price)
    return price_from, price_to

def product_to_markdown(product: Dict[str, Any],
                        exclude_keys: Optional[Set[str]] = None,
                        exclude_attr_keys: Optional[Set[str]] = None,
                        exclude_var_keys: Optional[Set[str]] = None
                        ) -> str:
    """
    Convert a product dictionary into a well-formatted Markdown string.
    
    Args:
        product (Dict[str, Any]): The product data to convert, represented as a dictionary.
        exclude_keys: Set of product fields to exclude during preprocessing
        exclude_attr_keys: Set of attribute keys to exclude during preprocessing  
        exclude_var_keys: Set of variant fields to exclude during preprocessing

    Returns:
        str: A Markdown-formatted string representing the product.
    """    
    md_ready_product = _preprocess_product(product, exclude_keys, exclude_attr_keys, exclude_var_keys)
    md_product = _preprocess_product_to_markdown(md_ready_product)
    return md_product

def products_to_documents(products: List[Dict[str, Any]],
                         exclude_keys: Optional[Set[str]] = None,
                         exclude_attr_keys: Optional[Set[str]] = None,
                         exclude_var_keys: Optional[Set[str]] = None
                         ) -> List[Dict[str, Any]]:
    """
    Convert an array of raw product JSON objects to dictionaries ready for Document creation.
    
    Args:
        products: List of raw product dictionaries (with 'id' field)
        exclude_keys: Set of product fields to exclude during preprocessing
        exclude_attr_keys: Set of attribute keys to exclude during preprocessing  
        exclude_var_keys: Set of variant fields to exclude during preprocessing
        
    Returns:
        List of dictionaries with processed product data ready for Document creation
    """
    documents = []
    for i, product in enumerate(products):
        try:
            # Validate required fields
            if not isinstance(product, dict):
                logger.error(f"Product at index {i}: Not a dictionary, skipping")
                continue

            product_id = product.get('id')
            if not product_id:
                logger.error(f"Product at index {i}: Missing 'id' field, skipping")
                continue

            # Preprocess the product
            md_ready_product = _preprocess_product(product, exclude_keys, exclude_attr_keys, exclude_var_keys)
            chunks = _create_product_chunks(md_ready_product)
            price_from, price_to = _calculate_price_range(md_ready_product)

            # Create document dictionary
            document_data = {
                'id': md_ready_product['SKU'],
                'url': md_ready_product.get('uri', ''),
                'title': md_ready_product.get('title', ''),
                'sku': str(md_ready_product['SKU']),
                'price_from': price_from,
                'price_to': price_to,
                'chunks': chunks
            }

            documents.append(document_data)

        except Exception as e:
            product_id = product.get('id', f'index-{i}') if isinstance(product, dict) else f'index-{i}'
            logger.error(f"Error processing product {product_id}: {str(e)}, skipping")
            continue

    return documents
