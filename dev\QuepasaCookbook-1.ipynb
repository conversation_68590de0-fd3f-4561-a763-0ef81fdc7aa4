{"cells": [{"cell_type": "markdown", "id": "9a8dc60e", "metadata": {}, "source": ["# NOTICE"]}, {"cell_type": "markdown", "id": "23230d0a", "metadata": {}, "source": ["Please make sure that you have created and updated .env file.\n", "\n", "You are required to set up this API KEYS:\n", "- `OPENAI_API_KEY`\n", "- `NEBIUS_API_KEY`"]}, {"cell_type": "markdown", "id": "3d785018", "metadata": {}, "source": ["## Check API endpoint"]}, {"cell_type": "code", "id": "73c4ea42", "metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:01:24.477900Z", "start_time": "2025-09-08T19:01:24.334809Z"}}, "source": ["!curl http://localhost:8000"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"message\":\"Welcome to QuePasa API\"}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100    36  100    36    0     0   1485      0 --:--:-- --:--:-- --:--:--  1565\n"]}], "execution_count": 1}, {"cell_type": "markdown", "id": "6db9f389", "metadata": {}, "source": ["## Check Searcher endpoint"]}, {"cell_type": "code", "id": "7c07af79", "metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:01:49.231495Z", "start_time": "2025-09-08T19:01:49.059503Z"}}, "source": ["!curl http://localhost:8080/health"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"status\":\"healthy\"}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "  0    21    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100    21  100    21    0     0    187      0 --:--:-- --:--:-- --:--:--   187\n"]}], "execution_count": 2}, {"cell_type": "markdown", "id": "20d506bb", "metadata": {}, "source": ["## Init"]}, {"cell_type": "code", "id": "3f87e686", "metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:01:51.773626Z", "start_time": "2025-09-08T19:01:51.655064Z"}}, "source": ["import requests\n", "\n", "API_ENDPOINT = \"http://localhost:8000\"\n", "SEARCHER_ENDPOINT = \"http://localhost:8080\"\n", "\n", "DOMAIN = \"default-test\"\n", "\n", "YOUR_SECRET_TOKEN = \"default:ujtiDZWTGjqlZ5Aa0ZQy/W9DQ+risY57nASbBA6PZE0Jt4+1gSGBapjfGrUnSZxm\""], "outputs": [], "execution_count": 3}, {"cell_type": "markdown", "id": "a04c4cdc", "metadata": {}, "source": ["### Upload document"]}, {"cell_type": "code", "id": "c4a043ab", "metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:01:53.784776Z", "start_time": "2025-09-08T19:01:53.466397Z"}}, "source": ["INDEX = \"001\"\n", "\n", "response = requests.post(\n", "    f\"{API_ENDPOINT}/data/v1/documents/{DOMAIN}\",\n", "    headers={\n", "        \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Bearer {YOUR_SECRET_TOKEN}\",\n", "    },\n", "    json=[\n", "        {\n", "            # Required fields\n", "            \"id\": f\"llm{INDEX}\",  # string\n", "            \"url\": \"https://www.shoeby.nl/shoeby-ladies-t-shirts-en-polo-s-basis-tanktop-zwart/1017012.html\",\n", "            \"title\": \"Basic Tank Top Black\",\n", "            #             'chunks': [\n", "            #                 {\n", "            #                     'language': \"en\", # two-char language code in lowercase\n", "            #                     'text': f\"\"\"\n", "            # #{INDEX}\n", "            # A large language model (LLM) is a computational model capable of language generation or other natural language processing tasks. As language models, LLMs acquire these abilities by learning statistical relationships from vast amounts of text during a self-supervised and semi-supervised training process.\n", "            # The largest and most capable LLMs, as of August 2024, are artificial neural networks built with a decoder-only transformer-based architecture, which enables efficient processing and generation of large-scale text data. Modern models can be fine-tuned for specific tasks or can be guided by prompt engineering.\n", "            # These models acquire predictive power regarding syntax, semantics, and ontologies inherent in human language corpora, but they also inherit inaccuracies and biases present in the data they are trained on.\n", "            # Some notable LLMs are OpenAI's GPT series of models (e.g., GPT-3.5, GPT-4 and GPT-4o; used in ChatGPT and Microsoft Copilot), Google's Gemini (the latter of which is currently used in the chatbot of the same name), Meta's LLaMA family of models, IBM's Granite models initially released with Watsonx, Anthropic's Claude models, and Mistral AI's models.\n", "            # \"\"\".strip(),\n", "            #                 },\n", "            #                 {\n", "            #                     'language': \"en\", # two-char language code in lowercase\n", "            #                     'text': f\"\"\"\n", "            # #{INDEX}\n", "            # NEW CHUNK\n", "            # \"\"\".strip(),\n", "            #                 },\n", "            #             ],\n", "            \"text_content\": '{\\\"id\\\":\\\"1017012\\\",\\\"name\\\":\\\"1017012\\\",\\\"title\\\":\\\"Basic Tank Top Black\\\",\\\"type\\\":\\\"PRIMARY\\\",\\\"brands\\\":[\\\"Shoeby\\\"],\\\"categories\\\":[\\\"apparel > tops > tank > tank top\\\"],\\\"uri\\\":\\\"https://www.shoeby.nl/shoeby-ladies-t-shirts-en-polo-s-basis-tanktop-zwart/1017012.html\\\",\\\"images\\\":[{\\\"uri\\\":\\\"https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dw5dfa7267/products/1017012/06_1017012_10.jpg\\\"}],\\\"description\\\":\\\"You can never have enough good basics! The Basic Tank Top is a perfect item to add to your wardrobe. The top has a low round neck, a fitted fit and wide spaghetti straps. The item is characterized by the black color and the ideal fit. You can also combine the top in many ways. It is made of elastane and cotton. This mix of fabrics makes the top strong in quality, subtly stretches and is comfortable to wear. Combine it with a blazer, wide leg jeans and sneakers for a stylish look! The model has a height of 173 cm and wears size S. The item has a total length of 52 cm in size S.\\\",\\\"colorInfo\\\":{\\\"colorFamilies\\\":[\\\"Black\\\"]},\\\"patterns\\\":[\\\"solid\\\"],\\\"materials\\\":[\\\"cotton\\\",\\\"elastic\\\"],\\\"audience\\\":{\\\"genders\\\":[\\\"female\\\"],\\\"ageGroups\\\":[\\\"adult\\\"]},\\\"attributes\\\":[{\\\"key\\\":\\\"gbi_L1\\\",\\\"value\\\":{\\\"text\\\":[\\\"apparel\\\"]}},{\\\"key\\\":\\\"gbi_L2\\\",\\\"value\\\":{\\\"text\\\":[\\\"tops\\\"]}},{\\\"key\\\":\\\"gbi_L3\\\",\\\"value\\\":{\\\"text\\\":[\\\"tank\\\"]}},{\\\"key\\\":\\\"gbi_L4\\\",\\\"value\\\":{\\\"text\\\":[\\\"tank top\\\"]}},{\\\"key\\\":\\\"OnlineFlag\\\",\\\"value\\\":{\\\"text\\\":[\\\"True\\\"]}},{\\\"key\\\":\\\"TargetAudience\\\",\\\"value\\\":{\\\"text\\\":[\\\"ladies\\\",\\\"adults lady women\\\"]}},{\\\"key\\\":\\\"product_collection\\\",\\\"value\\\":{\\\"text\\\":[\\\"tank\\\"]}}]}',\n", "            # 'html_content': \"\", # or send text\n", "            # 'markdown_content': \"\", # or send markdown\n", "            # Optional fields:\n", "            # - 'keywords': document keywords, string, by default empty\n", "            # - 'created_at': \"2024-05-20T07:26:06Z\", # document creation datetime, by default datetime of first creation of this document via API\n", "            # - 'updated_at': \"2024-05-20T07:26:06Z\", # document last update datetime, by default datetime of last update of this document via API\n", "        },\n", "    ],\n", ")\n", "\n", "print(response)\n", "response.json()\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Response [200]>\n"]}, {"data": {"text/plain": ["{'batch_id': '1757358113.584907',\n", " 'processed_ids': ['llm001'],\n", " 'skip_indexing': False}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"cell_type": "markdown", "id": "e2cebfae", "metadata": {}, "source": ["### Get Document"]}, {"cell_type": "code", "id": "97e17a85", "metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:01:56.874868Z", "start_time": "2025-09-08T19:01:56.842679Z"}}, "source": ["# Fetch a single indexed document by ID\n", "\n", "doc_id = f\"llm{INDEX}\"\n", "response_doc = requests.get(\n", "    f\"{API_ENDPOINT}/data/v1/documents/{DOMAIN}/{doc_id}\",\n", "    headers={\n", "        \"Content-Type\": \"application/json\",\n", "        \"Authorization\": f\"Bearer {YOUR_SECRET_TOKEN}\",\n", "    },\n", ")\n", "\n", "print(f\"Status: {response_doc.status_code}\")\n", "try:\n", "    response_doc.raise_for_status()\n", "    data = response_doc.json()\n", "    import json as _json\n", "    print(_json.dumps(data, indent=2, ensure_ascii=False))\n", "except requests.exceptions.HTTPError as e:\n", "    print(\"HTTP error:\", e)\n", "    print(\"Body:\")\n", "    print(response_doc.text)\n", "except ValueError:\n", "    # Response isn't JSON\n", "    print(\"Non-JSON response body:\")\n", "    print(response_doc.text)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Status: 200\n", "{\n", "  \"domain\": \"default-test\",\n", "  \"id\": \"llm001\",\n", "  \"url\": \"https://www.shoeby.nl/shoeby-ladies-t-shirts-en-polo-s-basis-tanktop-zwart/1017012.html\",\n", "  \"title\": \"Basic Tank Top Black\",\n", "  \"keywords\": \"\",\n", "  \"chunks\": [\n", "    {\n", "      \"language\": \"en\",\n", "      \"text\": \"{\\\"id\\\":\\\"1017012\\\",\\\"name\\\":\\\"1017012\\\",\\\"title\\\":\\\"Basic Tank Top Black\\\",\\\"type\\\":\\\"PRIMARY\\\",\\\"brands\\\":[\\\"Shoeby\\\"],\\\"categories\\\":[\\\"apparel > tops > tank > tank top\\\"],\\\"uri\\\":\\\"https://www.shoeby.nl/shoeby-ladies-t-shirts-en-polo-s-basis-tanktop-zwart/1017012.html\\\",\\\"images\\\":[{\\\"uri\\\":\\\"https://staging.shoebyfashion.com/on/demandware.static/-/Sites-master-catalog/default/dw5dfa7267/products/1017012/06_1017012_10.jpg\\\"}],\\\"description\\\":\\\"You can never have enough good basics! The Basic Tank Top is a perfect item to add to your wardrobe. The top has a low round neck, a fitted fit and wide spaghetti straps. The item is characterized by the black color and the ideal fit. You can also combine the top in many ways. It is made of elastane and cotton. This mix of fabrics makes the top strong in quality, subtly stretches and is comfortable to wear. Combine it with a blazer, wide leg jeans and sneakers for a stylish look! The model has a height of 173 cm and wears size S. The item has a total length of 52 cm in size\"\n", "    },\n", "    {\n", "      \"language\": \"en\",\n", "      \"text\": \"total length of 52 cm in size S.\\\",\\\"colorInfo\\\":{\\\"colorFamilies\\\":[\\\"Black\\\"]},\\\"patterns\\\":[\\\"solid\\\"],\\\"materials\\\":[\\\"cotton\\\",\\\"elastic\\\"],\\\"audience\\\":{\\\"genders\\\":[\\\"female\\\"],\\\"ageGroups\\\":[\\\"adult\\\"]},\\\"attributes\\\":[{\\\"key\\\":\\\"gbi_L1\\\",\\\"value\\\":{\\\"text\\\":[\\\"apparel\\\"]}},{\\\"key\\\":\\\"gbi_L2\\\",\\\"value\\\":{\\\"text\\\":[\\\"tops\\\"]}},{\\\"key\\\":\\\"gbi_L3\\\",\\\"value\\\":{\\\"text\\\":[\\\"tank\\\"]}},{\\\"key\\\":\\\"gbi_L4\\\",\\\"value\\\":{\\\"text\\\":[\\\"tank top\\\"]}},{\\\"key\\\":\\\"OnlineFlag\\\",\\\"value\\\":{\\\"text\\\":[\\\"True\\\"]}},{\\\"key\\\":\\\"TargetAudience\\\",\\\"value\\\":{\\\"text\\\":[\\\"ladies\\\",\\\"adults lady women\\\"]}},{\\\"key\\\":\\\"product_collection\\\",\\\"value\\\":{\\\"text\\\":[\\\"tank\\\"]}}]}\"\n", "    }\n", "  ],\n", "  \"languages\": [\n", "    \"en\"\n", "  ],\n", "  \"updated_at\": \"2025-09-08T19:01:54Z\",\n", "  \"created_at\": \"2025-09-03T21:17:08Z\"\n", "}\n"]}], "execution_count": 5}, {"cell_type": "markdown", "id": "3334e4aa", "metadata": {}, "source": ["### Check batch status"]}, {"cell_type": "code", "id": "6dd39f50", "metadata": {"ExecuteTime": {"end_time": "2025-09-08T19:02:01.264911Z", "start_time": "2025-09-08T19:02:01.220359Z"}}, "source": ["batch_id = response.json()['batch_id']\n", "response_batch = requests.get(\n", "    f\"{API_ENDPOINT}/data/v1/batches/{batch_id}\",\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'<PERSON><PERSON> {YOUR_SECRET_TOKEN}',\n", "    },\n", ")\n", "\n", "print( response_batch )\n", "response_batch.json()"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Response [200]>\n"]}, {"data": {"text/plain": ["{'status': 'Batch state: BatchState.DONE',\n", " 'state': 'done',\n", " 'data': {'domain': 'default-test', 'processed_ids': ['default-test:llm001']}}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"cell_type": "markdown", "id": "fa7fbc86", "metadata": {}, "source": ["### Make a RAG request"]}, {"cell_type": "code", "id": "8d4b018d", "metadata": {"scrolled": true}, "source": ["response = requests.post(\n", "    f\"{SEARCHER_ENDPOINT}/retrieve/answer\",\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'<PERSON><PERSON> {YOUR_SECRET_TOKEN}',\n", "    },\n", "    json = {\n", "        'question': \"What products do you have?\",\n", "        'source': \"documents\",\n", "        'domain': DOMAIN,\n", "    }\n", ")\n", "\n", "print(response)\n", "response.json()"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "5a8a5043", "metadata": {}, "source": ["## Upload file"]}, {"cell_type": "code", "id": "4abaa41a", "metadata": {}, "source": ["!wget https://publish.uwo.ca/~csmeenk2/files/TTTMWeb.pdf"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "9cc7154b", "metadata": {}, "source": ["file_path = \"TTTMWeb.pdf\"\n", "\n", "# Open the file in binary mode\n", "response = None\n", "with open( file_path, 'rb' ) as f:\n", "    # Send the POST request with the file\n", "    response = requests.post(\n", "        f\"{API_ENDPOINT}/data/v1/files/{DOMAIN}\",\n", "        headers = {\n", "            'Authorization': f'<PERSON><PERSON> {YOUR_SECRET_TOKEN}',\n", "        },\n", "        data = {\n", "            'language': \"en\", # Optional, Two-character language code (e.g., 'en').\n", "        },\n", "        files = {\n", "            'file': f,\n", "        },\n", "    )\n", "    \n", "response.json()"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "78ce4f72", "metadata": {}, "source": ["### Check batch status"]}, {"cell_type": "code", "id": "53632284", "metadata": {}, "source": ["batch_id = response.json()['batch_id']\n", "response_batch = requests.get(\n", "    f\"{API_ENDPOINT}/data/v1/batches/{batch_id}\",\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'<PERSON><PERSON> {YOUR_SECRET_TOKEN}',\n", "    },\n", ")\n", "\n", "print( response_batch )\n", "response_batch.json()"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "a5c153cc", "metadata": {}, "source": ["### Make a RAG request"]}, {"cell_type": "code", "id": "95068fd8", "metadata": {}, "source": ["response = requests.post(\n", "    f\"{SEARCHER_ENDPOINT}/retrieve/answer\",\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'Authorization': f'<PERSON><PERSON> {YOUR_SECRET_TOKEN}',\n", "    },\n", "    json = {\n", "        'question': \"What is Time machine?\",\n", "        'source': \"documents\",\n", "        'domain': DOMAIN,\n", "    }\n", ")\n", "\n", "print(response)\n", "response.json()"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "c224fc2f", "metadata": {}, "source": [], "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}